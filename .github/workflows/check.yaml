name: Check
on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  check:
    runs-on: ubuntu-22.04
    timeout-minutes: 30
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ hashFiles('poetry.lock') }}
      - name: Install poetry dependencies
        run: |
          make init
      - name: Start docker images
        uses: isbang/compose-action@v2.0.0
        with:
          compose-file: "docker/docker-compose.yml"
          services: |
            clickhouse-server
            postgres
            trino-server
      - name: Waiting for trino server
        run: |
          poetry run python3 scripts/wait_for_trino.py
      - name: Testing and validation
        run: |
          make check
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: html-cov
          path: htmlcov
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: coverage-xml
          path: coverage.xml
      - name: "Updating summary report"
        if: success()
        run: cat htmlcov/index.html >> $GITHUB_STEP_SUMMARY
      - name: Create coverage markdown
        run: |
          python scripts/coverage_report.py \
            --report-xml coverage.xml \
            --output code-coverage-results.md
      - name: Post coverage to the PRs
        uses: marocchino/sticky-pull-request-comment@v2
        if: github.event_name == 'pull_request'
        with:
          recreate: true
          path: code-coverage-results.md
      - name: Upload coverage reports to Codecov with GitHub Action
        uses: codecov/codecov-action@v4.5.0
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'push' }}
        env:
          SLACK_CHANNEL: alerts-dev
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Main branch is broken
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot
