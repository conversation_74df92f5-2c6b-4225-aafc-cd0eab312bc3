name: Poetry lock
on:
  workflow_dispatch:

jobs:
  poetry:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Lock poetry dependencies
        run: |
          rm poetry.lock
          poetry lock
      - name: Saving poetry lock
        uses: actions/upload-artifact@v4
        with:
          name: poetry.lock
          path: poetry.lock
