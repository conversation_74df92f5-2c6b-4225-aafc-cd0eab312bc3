name: Check saved metrics
on:
  repository_dispatch:
    types: [mitzubot_metrics]
  workflow_dispatch:
    inputs:
      env:
        required: true
        default: "dev"
        description: "Environment"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  publish-test-docker-image:
    name: Publish test docker image
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Install poetry dependencies
        run: |
          make init
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: test-build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: mitzu-metric-test
          TAG: ${{ github.sha }}
        run: |-
          make prepare_metric_test_requirements
          docker image build \
            -t $ECR_REGISTRY/$ECR_REPOSITORY:$TAG \
            --platform "linux/amd64" \
            --build-arg MITZU_VERSION=$(poetry version -s) \
            --build-arg ENVIRONMENT="cloud-dev" \
            tests/metrics
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$TAG

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: 1.3.5
          terraform_wrapper: false  # needed for jq compatibility

      - name: Terraform Init
        id: "init"
        working-directory: ./infra/metric-test/terraform/
        run: terraform init

      - name: Terraform Apply
        id: terraform
        working-directory: ./infra/metric-test/terraform/
        env:
          ECR_REGISTRY: "531968921201.dkr.ecr.eu-west-1.amazonaws.com"
          ECR_REPOSITORY: mitzu-metric-test
          TAG: ${{ github.sha }}
        run: |
          set -x
          terraform apply -no-color -auto-approve \
          -var "rds_username=${{ secrets.DEV_RDS_USERNAME }}" \
          -var "rds_password=${{ secrets.DEV_RDS_PASSWORD }}" \
          -var "container_image=$ECR_REGISTRY/$ECR_REPOSITORY:$TAG" \
          -var 'bigquery_credentials=${{ secrets.MATRIX_BIGQUERY_CREDENTIALS }}' \
          -var 's3_target_path=s3://mitzu-debug-dataframe/metric_test/${{ github.sha }}'
          echo "Task outputs:"
          terraform output -json task_arns
          TASK_ID=$(terraform output -json task_arns | jq -r .[0] | cut -d "/" -f 3)
          echo "parsed task id: $TASK_ID"
          echo "task_id=$TASK_ID" >> $GITHUB_OUTPUT

      - name: Wait for task to finish
        run: |
          echo "Waiting ECS task to finish: ${{ steps.terraform.outputs.task_id }}"
          aws ecs wait tasks-stopped --cluster mitzu-pro-ecs-prod --tasks ${{ steps.terraform.outputs.task_id }}

      - name: Download result and generate report
        run: |
          aws s3 cp s3://mitzu-debug-dataframe/metric_test/${{ github.sha }} metric_results.xml
          poetry run python scripts/format_saved_metrics_result.py --result-path metric_results.xml > metric_results.html

      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: metric_results_html
          path: metric_results.html

      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: metric_results_xml
          path: metric_results.xml

      - name: Update summary
        run: |
          cat metric_results.html >> $GITHUB_STEP_SUMMARY
