name: Test Matrix
on:
  repository_dispatch:
    types: [mitzubot_matrix]
  workflow_dispatch:
    inputs:
      test_docker:
        description: Data warehouses running in docker (clickhouse, postgres, trino)
        type: boolean
        default: true
      test_athena:
        description: "Athena"
        required: true
        type: boolean
        default: true
      test_bigquery:
        description: "Bigquery"
        required: true
        type: boolean
        default: true
      test_databricks:
        description: "Databricks"
        required: true
        type: boolean
        default: true
      test_redshift:
        description: "Redshift"
        required: true
        type: boolean
        default: false
      test_trino:
        description: "Trino (starburst)"
        required: true
        type: boolean
        default: true
      test_snowflake:
        description: "Snowflake"
        required: true
        type: boolean
        default: true
      test_filter:
        description: "Test filter (pytest -k)"
        type: string
        default: ""

jobs:
  test_matrix_plan:
    runs-on: ubuntu-22.04
    outputs:
      dwhs: ${{ steps.dwhs_step.outputs.dwhs }}
    name: Matrix test plan
    steps:
      - name: Parse matrix job inputs
        id: dwhs_step
        run: |
          selected="[]"

          if [[ "${{ github.event.inputs.test_docker }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"docker"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_athena }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"athena"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_bigquery }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"bigquery"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_databricks }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"databricks"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_redshift }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"redshift"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_trino }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"trino"}]' -c)
          fi

          if [[ "${{ github.event.inputs.test_snowflake }}" == "true" ]]; then
            selected=$(echo $selected | jq '. += [{"dwh":"snowflake"}]' -c)
          fi

          echo "Selected data warehouses: $selected"
          echo "dwhs={\"include\": $selected}" >> $GITHUB_OUTPUT
          echo $GITHUB_OUTPUT

  test_matrix_env:
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    needs: test_matrix_plan
    continue-on-error: true
    strategy:
      fail-fast: false
      matrix: ${{fromJson(needs.test_matrix_plan.outputs.dwhs)}}
    name: ${{ matrix.dwh }}
    steps:
      - name: Workflow/job inputs
        run: |
          echo "${{ toJSON(github.event.inputs) }}"
          echo "${{ toJSON(matrix) }}"
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ hashFiles('poetry.lock') }}
      - name: Install poetry dependencies
        run: |
          make init
      - name: Start docker images
        if: ${{ matrix.dwh == 'docker' }}
        uses: isbang/compose-action@v2.0.0
        with:
          compose-file: "docker/docker-compose.yml"
          services: |
            clickhouse-server
            postgres
            trino-server
      - name: Waiting for trino server
        if: ${{ matrix.dwh == 'docker' }}
        run: |
          poetry run python3 scripts/wait_for_trino.py
      - name: Configure Athena password
        if: ${{ matrix.dwh == 'athena' }}
        run: |
          echo "ATHENA_PASSWORD=${{ secrets.MATRIX_ATHENA_PASSWORD }}" >> $GITHUB_ENV
          echo "AWS_REGION=eu-west-1" >> $GITHUB_ENV
      - name: Configure Bigquery credentials
        if: ${{ matrix.dwh == 'bigquery' }}
        run: |
          echo BIGQUERY_CREDENTIALS='${{ secrets.MATRIX_BIGQUERY_CREDENTIALS }}' >> $GITHUB_ENV
      - name: Configure Databricks password
        if: ${{ matrix.dwh == 'databricks' }}
        run: |
          echo "DATABRICKS_PASSWORD=${{ secrets.MATRIX_DATABRICKS_PASSWORD }}" >> $GITHUB_ENV
      - name: Configure Redshift password
        if: ${{ matrix.dwh == 'redshift' }}
        run: |
          echo "REDSHIFT_PASSWORD=${{ secrets.MATRIX_REDSHIFT_PASSWORD }}" >> $GITHUB_ENV
      - name: Configure Trino password
        if: ${{ matrix.dwh == 'trino' }}
        run: |
          echo "TRINO_PASSWORD=${{ secrets.MATRIX_TRINO_PASSWORD }}" >> $GITHUB_ENV
      - name: Configure Snowflake password
        if: ${{ matrix.dwh == 'snowflake' }}
        run: |
          {
            echo "SNOWFLAKE_PRIVATE_KEY<<EOF"
            echo "${{ secrets.MATRIX_SNOWFLAKE_PRIVATE_KEY }}"
            echo EOF
          } >> "$GITHUB_ENV"
      - name: Testing and validation
        run: |
          filter="${{ github.event.inputs.test_filter }}"
          if [[ "${{ matrix.dwh }}" != "docker" ]]; then
            not_docker_filter="(not clickhouse and not psql and not trinolocal)"
            if [[ "$filter" != "" ]]; then
              filter+=" and $not_docker_filter"
            else
              filter=$not_docker_filter
            fi
          fi

          poetry run pytest \
            tests/integration \
            -k "$filter" \
            --junit-xml=matrix_result.xml \
            --dist loadgroup
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: matrix_result_${{ matrix.dwh }}
          path: matrix_result.xml

  generate_report:
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    needs: test_matrix_env
    name: Report generation
    steps:
      - name: Workflow inputs
        run: echo "${{ toJSON(github.event.inputs) }}"
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Load outputs
        uses: actions/download-artifact@v4
        with:
          pattern: matrix_result_*
          path: outputs
      - run: |
          for folder in `ls outputs`
          do
            echo "Copying result xml from outputs/$folder"
            cp "outputs/$folder/matrix_result.xml" ./$folder.xml
          done
      - name: Generating html report
        if: success() || failure()
        run: |
          python3 scripts/format_matrix_result.py \
           --result-path matrix_result_*.xml \
           --format html > matrix_result.html
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: matrix-report
          path: matrix_result.html
      - name: Generating Github markdown report
        if: success() || failure()
        run: |
          python3 scripts/format_matrix_result.py \
          --result-path matrix_result_*.xml \
          --format github-markdown >> $GITHUB_STEP_SUMMARY