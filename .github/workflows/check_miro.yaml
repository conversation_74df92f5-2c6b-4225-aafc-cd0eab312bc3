name: Check Miro JS
on:
  pull_request:
    paths:
      - 'integrations/miro/**'
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  check:
    runs-on: ubuntu-22.04
    timeout-minutes: 15
    defaults:
      run:
        working-directory: ./integrations/miro
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install nodejs
        uses: actions/setup-node@v3
        with:
          node-version: '20.11.1'
      - name: Load cached packages
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: npm-${{ hashFiles('package-lock.json') }}
      - name: Install js dependencies
        run: |
          npm install
      - name: lint and check formatting
        run: |
          npm run prettier:check && npm run lint
      - name: Testing and validation
        run: |
          npm run test
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: html-cov
          path: coverage/lcov-report
      - name: "Updating summary report"
        if: success()
        run:
          cat coverage/lcov-report/index.html >> $GITHUB_STEP_SUMMARY
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'push' }}
        env:
          SLACK_CHANNEL: alerts-dev
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Main branch is broken
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot