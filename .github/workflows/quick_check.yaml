name: Quick check
on:
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  quick_check:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ hashFiles('poetry.lock') }}
      - name: Install poetry dependencies
        run: |
          make init
      - name: Lint
        run: |
          make lint
      - name: Start docker images
        uses: isbang/compose-action@v2.0.0
        with:
          compose-file: "docker/docker-compose.yml"
          services: |
            clickhouse-server
            postgres
            trino-server
      - name: Waiting for trino server
        run: |
          poetry run python3 scripts/wait_for_trino.py
      - name: Testing and validation
        env:
          GITHUB_QUICK_CHECK_TOKEN: ${{ secrets.QUICK_CHECK_TOKEN }}
          PYTHONUNBUFFERED: "1"
        run: |
          echo "Coverage xml not found" > code-coverage-results.md
          poetry run python3 scripts/quick_check.py \
            --pr-number ${{ github.event.number }} \
            --cov-report-output code-coverage-results.md
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: html-cov
          path: htmlcov
      - uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: coverage-xml
          path: coverage.xml
      - name: "Updating summary report"
        if: success()
        run: |
          if [ -f "htmlcov/index.html" ]; then
            cat htmlcov/index.html >> $GITHUB_STEP_SUMMARY
          else
            echo "Report not found"
          fi
      - name: Upload coverage reports to Codecov with GitHub Action
        uses: codecov/codecov-action@v4.5.0
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
