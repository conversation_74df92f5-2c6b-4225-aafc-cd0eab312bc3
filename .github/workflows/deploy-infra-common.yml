name: Deploy Common Infra
on:
  push:
    paths:
      - "infra/common/**"
    branches:
      - main
  workflow_dispatch:
    inputs:
      tags:
        required: false
        default: "Manually triggered"
        description: "Comment"

jobs:
  deploy:
    name: Deploy Common Infra
    runs-on: ubuntu-22.04
    defaults:
      run:
        working-directory: ./infra/common/terraform/
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: 1.3.5

      - name: Terraform Init
        id: "init"
        run: terraform init

      - name: Terraform Validate
        id: validate
        run: terraform validate -no-color

      - name: Terraform Apply
        id: plan
        run: terraform apply -no-color -auto-approve

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'push' }}
        env:
          SLACK_CHANNEL: alerts-dev
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Main branch is broken
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot
