name: Deploy Mitzu-Service-Dev Infra
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      tags:
        required: false
        default: "Manually triggered"
        description: "Comment"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  publish-mitzu-docker-image:
    name: Publish mitzu docker image
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
      - name: Install poetry dependencies
        run: |
          make init
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: prod-build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: mitzu-pro-ecr-dev
          TAG: ${{ github.sha }}
        run: |-
          make prepare_release_requirements
          docker image build \
            -t $ECR_REGISTRY/$ECR_REPOSITORY:$TAG \
            --platform "linux/amd64" \
            --build-arg MITZU_VERSION=$(poetry version -s) \
            --build-arg EXPOSED_PORT=8081 \
            --build-arg TRACKING_HOST=${{ secrets.TRACKING_HOST }} \
            --build-arg TRACKING_API_KEY=${{ secrets.TRACKING_API_KEY }} \
            --build-arg RUDDER_TRACKING_HOST=${{ secrets.RUDDER_TRACKING_HOST }} \
            --build-arg RUDDER_TRACKING_API_KEY=${{ secrets.RUDDER_TRACKING_API_KEY }} \
            --build-arg ENVIRONMENT="cloud-dev" \
            release/
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$TAG

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: 1.3.5

      - name: Terraform Init
        id: "init"
        working-directory: ./infra/mitzu-service-dev/terraform/
        run: terraform init

      - name: Terraform Validate
        id: validate
        working-directory: ./infra/mitzu-service-dev/terraform/
        run: terraform validate -no-color

      - name: Terraform Apply
        id: plan
        working-directory: ./infra/mitzu-service-dev/terraform/
        env:
          ECR_REGISTRY: "531968921201.dkr.ecr.eu-west-1.amazonaws.com"
          ECR_REPOSITORY: mitzu-pro-ecr-dev
          TAG: ${{ github.sha }}
        run: |-
          terraform apply -no-color -auto-approve \
          -var "container_image=$ECR_REGISTRY/$ECR_REPOSITORY:$TAG"

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'push' }}
        env:
          SLACK_CHANNEL: alerts-dev
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Main branch is broken
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot