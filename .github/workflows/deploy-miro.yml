name: Deploy Miro
on:
  push:
    paths:
      - "integrations/miro/**"
    branches:
      - main
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tags:
        required: false
        default: "Manually triggered"
        description: "Comment"

jobs:
  deploy:
    name: Deploy Miro app
    runs-on: ubuntu-22.04
    defaults:
      run:
        working-directory: ./integrations/miro/
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Check github pages repo
        uses: actions/checkout@v3
        with:
          repository: mitzu-io/mitzu-miro-app
          path: ./integrations/miro/github_pages
          ssh-key: ${{ secrets.DEPLOY_MIRO_PRIVATE_KEY }}
      
      - name: Update github pages repo config
        run: |
          cd github_pages
          git config --global user.email "<EMAIL>"
          git config --global user.name "MitzuBot"

      - name: Install npm packages
        run: npm install

      - name: Release and deploy dev env
        if: ${{ github.event_name == 'push' }}
        run: |
          npm run build:dev
          rm -rf github_pages/dev || true
          mkdir -p github_pages/dev
          cp -r dist/* github_pages/dev

          cd github_pages
          git add .
          git commit -m "Deploy Dev app ${{ github.ref }}"
          git push

      - name: Release and deploy prod env
        if: ${{ github.event_name == 'release' }}
        run: |
          npm run build:prod
          rm -rf github_pages/prod || true
          mkdir -p github_pages/prod
          cp -r dist/* github_pages/prod

          cd github_pages
          git add .
          git commit -m "Deploy Prod app ${{ github.ref }}"
          git push

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'push' }}
        env:
          SLACK_CHANNEL: alerts-dev
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Failed to deploy Miro app to DEV env
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() && github.event_name == 'release' }}
        env:
          SLACK_CHANNEL: alerts
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Failed to deploy Miro app to PROD env
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://avatars.slack-edge.com/2024-05-08/7085795532437_55f790a35df984f0d149_192.png
          SLACK_USERNAME: MitzuBot
