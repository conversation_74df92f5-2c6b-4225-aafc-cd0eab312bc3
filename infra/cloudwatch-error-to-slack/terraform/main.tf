
resource "aws_iam_role" "lambda_cw_error_to_slack_execution_role" {
  name               = "lambda_cw_error_to_slack_execution_role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "lambda_cw_error_to_slack_execution_policy" {
  name   = "lambda_cw_error_to_slack_execution_policy"
  role   = aws_iam_role.lambda_cw_error_to_slack_execution_role.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": [
        "arn:aws:logs:*:*:*"
      ]
    }
  ]
}
EOF
}


data "archive_file" "zip" {
  source_file = "main.py"
  type        = "zip"
  output_path = "${path.module}/lambda.zip"
}

resource "aws_lambda_function" "cloudwatch_to_cw_error_to_slack" {
  filename         = data.archive_file.zip.output_path
  function_name    = "cloudwatch-error-to-slack"
  role             = aws_iam_role.lambda_cw_error_to_slack_execution_role.arn
  handler          = "main.lambda_handler"
  runtime          = "python3.11"
  source_code_hash = data.archive_file.zip.output_base64sha256

  layers = [
    "arn:aws:lambda:eu-west-1:770693421928:layer:Klayers-p311-requests:6",
  ]

  reserved_concurrent_executions = 1
}

resource "aws_lambda_permission" "cw_error_to_slack_allow" {
  for_each = var.log_groups

  statement_id  = "cw_error_to_slack_allow_${each.key}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.cloudwatch_to_cw_error_to_slack.arn
  principal     = var.cwl_endpoint
  source_arn    = each.value
}

resource "aws_cloudwatch_log_subscription_filter" "cloudwatch_errors_to_slack" {
  for_each = var.log_groups

  depends_on      = [aws_lambda_permission.cw_error_to_slack_allow]
  name            = "errors_to_slack"
  log_group_name  = each.key
  filter_pattern  = "{ ($.level = \"ERROR\") || ($.message = \"subscription updated based on stripe event\") }"
  destination_arn = aws_lambda_function.cloudwatch_to_cw_error_to_slack.arn
}