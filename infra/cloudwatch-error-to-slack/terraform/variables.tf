variable "region" {
  type    = string
  default = "eu-west-1"
}

variable "cwl_endpoint" {
  type    = string
  default = "logs.eu-west-1.amazonaws.com"
}

variable "log_groups" {
  type = map(string)
  default = {
    mitzu-pro-service-task-def-prod     = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-task-def-prod:*"
    mitzu-pro-service-dev-task-def-prod = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-dev-task-def-prod:*"
    mitzu-pro-service-pub-task-def-prod = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-pub-task-def-prod:*"
  }
}