import base64
import gzip
import json
from datetime import datetime, timedelta
from io import Bytes<PERSON>
from typing import Any, Dict, Optional

import requests

LAST_SUMMARY = None  # used to avoid flooding the slack channel with repeated errors
LAST_SEEN = None


def send_slack_message(webhook_url: str, message: str) -> None:
    payload = {"text": message}
    headers = {"Content-Type": "application/json"}

    response = requests.post(webhook_url, data=json.dumps(payload), headers=headers)

    if response.status_code != 200:
        print("Failed to send message. Status code:", response.status_code)
    print(response)


def get_webhook_url(log_group: str, user_email: str) -> str:
    if user_email == "<EMAIL>":
        return "*********************************************************************************"
    if log_group == "mitzu-pro-service-dev-task-def-prod":
        return "*********************************************************************************"
    return "*********************************************************************************"


def get_cloudwatch_url(log_group: str, error_dt: datetime, user_email: str) -> str:
    start_date = int(
        error_dt.replace(hour=0, minute=0, second=0, microsecond=0).timestamp() * 1000
    )
    return (
        "https://eu-west-1.console.aws.amazon.com/cloudwatch/home?region=eu-west-1#logsV2:log-groups"
        f"/log-group/{log_group}/log-events$3Fstart$3D{start_date}$26end$3D{start_date + 86399000}"
        "$26filterPattern$3D$257B+$2524.user_email+$253D+$2522{user_email.replace('@', '$2540')}$2522+$257D"
    )


def get_elasticksearch_url(log_group: str, error_dt: datetime, user_id: str) -> str:
    start_date = error_dt.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)

    indexes = {
        "mitzu-pro-service-task-def-prod": "73ab83ba-ba96-430e-bfaf-ba440b025e0b",
        "mitzu-pro-service-pub-task-def-prod": "414f0820-8fe8-4d7d-aa78-04df6c3ae0d4",
        "mitzu-pro-service-dev-task-def-prod": "b76b28a3-f17f-4a2b-bfc4-3f23fa15e74a",
    }
    index_id = indexes[log_group]

    """
    To update URL just open the elastic search, set the desired filters, columns and copy the URL from the browser.
    After pasting it here replace th index_id, start_date, end_date, user_id parts with a variable.
    """
    return (
        "https://mitzu.kb.eu-west-1.aws.found.io:9243/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:60000),"
        f"time:(from:'{start_date.isoformat()}',to:'{end_date.isoformat()}'))&_a=(columns:!(level,message,exception_value,"
        "ui_error_message,exception_traceback,user_email,user_id,org_id,org_name,mitzu_version),filters:!(('$state':(store:appState),"
        f"meta:(alias:!n,disabled:!f,field:level,index:'{index_id}',key:level,negate:!f,params:(query:ERROR),type:phrase),query:"
        f"(match_phrase:(level:ERROR))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'{index_id}',key:user_id,negate:!f,"
        f"params:(query:'{user_id}'),type:phrase),query:(match_phrase:(user_id:'{user_id}')))),index:'{index_id}',interval:auto,query:"
        "(language:kuery,query:''),sort:!(!('@timestamp',desc)))"
    )


def handle_error(env: str, log_group: str, message: Dict[str, Any]) -> Optional[str]:
    if "VIEW_IS_STALE" in message.get("exception_value", ""):
        return None

    if "Project not found with id" in message.get("exception_value", ""):
        return None

    if "Callback failed: the server did not respond." in message.get(
        "exception_value", ""
    ):
        return None

    if "unsupported operand type(s) for *: 'NoneType' and 'float'" in message.get(
        "exception_value", ""
    ):
        return None

    if "Project not found with project_id" in message.get(
        "exception_value", ""
    ) and "@mitzu.io" in message.get("user_email", ""):
        return None

    if "Bad Request" in message.get("exception_value", ""):
        return None

    message_time = str(datetime.fromtimestamp(message["timestamp"]))

    backend_version = message.get("mitzu_version")
    frontend_cookie_version = message.get("mitzu_version_cookie")
    current_frontend_version = message.get("ui_version")

    # ignore all errors having different frontend/backend versions
    if (
        frontend_cookie_version is not None
        and backend_version != frontend_cookie_version
    ):
        return None

    error_dt = datetime.fromtimestamp(message["timestamp"])
    cloudwatch_url = get_cloudwatch_url(log_group, error_dt, message["user_email"])
    elasticsearch_url = get_elasticksearch_url(log_group, error_dt, message["user_id"])
    message_summary = message.get(
        "exception_value",
        message.get("ui_error_message", message.get("message", "<no message>")),
    )[0:250]
    if "ui_error_message" in message:
        message_summary = f"UI error: \n{message_summary}"
    global LAST_SUMMARY
    global LAST_SEEN
    if (
        message_summary == LAST_SUMMARY  # type: ignore[unreachable]
        and LAST_SEEN is not None
        and (datetime.now() - LAST_SEEN).total_seconds() < 5  # type: ignore[unreachable]
    ):
        LAST_SEEN = datetime.now()  # type: ignore[unreachable]
        return None

    LAST_SUMMARY = message_summary
    LAST_SEEN = datetime.now()

    referer = message.get("referer")
    org_id = message.get("org_id", "No org_id")
    repro_url = f"\n<{referer}|Reproduce URL ({org_id})>" if referer else ""

    version_summary = f"(backend v: {backend_version}, cookie v: {frontend_cookie_version}, ui v: {current_frontend_version})"
    return f"""Error in the *{env}* environment {version_summary}:
  {message['user_email']} ({message['org_name']}) {message_time} <{cloudwatch_url}|Cloudwatch logs> <{elasticsearch_url}|Elasticsearch logs>
  {message.get('message', message.get("ui_error_message", "<no message>"))[0:250]}{repro_url}
```
{message_summary}
```"""


def handle_subscription_updated(env: str, message: Dict[str, Any]) -> str:
    return (
        f"Subscription updated in the *{env}* environment:\n"
        f"""*{message["org_name"]}* new expiration date: {message["org_subscription_expire_date"]} """
        f"""(expiration in stripe: {message["stripe_subscription_expire_date"]})"""
    )


def lambda_handler(event: Dict[str, Any], context: Any) -> None:
    log_group = "unkown log group"
    try:
        cw_data = str(event["awslogs"]["data"])
        cw_logs = gzip.GzipFile(
            fileobj=BytesIO(base64.b64decode(cw_data, validate=True))
        ).read()
        log_events = json.loads(cw_logs)
        log_group = log_events["logGroup"]
        env = "DEV" if log_group == "mitzu-pro-service-dev-task-def-prod" else "APP"
        for log_event in log_events["logEvents"]:
            print(log_event)
            message = json.loads(log_event["message"])

            if message["level"] == "ERROR":
                slack_message = handle_error(env, log_group, message)
            elif message.get("message") == "subscription updated based on stripe event":
                slack_message = handle_subscription_updated(env, message)
            else:
                raise ValueError(f"Unknown log level: {message['level']}")

            if slack_message is None:
                continue

            send_slack_message(
                get_webhook_url(log_group, message["user_email"]), slack_message
            )
    except Exception as exc:
        print(exc)
        send_slack_message(
            get_webhook_url(log_group, "xxx"),
            f"Failed to parse cloudwatch error log: {type(exc)} {exc}",
        )
    return
