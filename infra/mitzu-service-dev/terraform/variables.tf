variable "application_name" {
  description = "the name of your stack, e.g. \"demo\""
  default     = "mitzu-pro-service-dev"
}

variable "short_name" {
  description = "the name of your stack, e.g. \"demo\""
  default     = "dev"
}

variable "alarm_sns_topic" {
  type    = string
  default = "arn:aws:sns:eu-west-1:531968921201:Mitzu_Prod_Alarms"
}

variable "environment" {
  description = "the name of your environment, e.g. \"prod\""
  default     = "prod"
}

variable "secret_name" {
  type    = string
  default = "mitzu-cloud-dev"
}

variable "secret_arn" {
  type    = string
  default = "arn:aws:secretsmanager:eu-west-1:531968921201:secret:mitzu-cloud-dev-x3R0rs"
}

variable "infra_name" {
  description = "the name the common stack, e.g. \"data-platform-infra\""
  default     = "mitzu-pro"
}

variable "region" {
  type    = string
  default = "eu-west-1"
}

variable "task_def_cpu" {
  description = "The fargate task definition CPU size 256, 512, 1024, 2048 ,etc."
  default     = 1024
}

variable "task_def_mem" {
  description = "The fargate task definition MEM size  512, 1024, 2048 ,etc."
  default     = 2048
}

variable "container_image" {
  description = "The container image to deploy"
  default     = "531968921201.dkr.ecr.eu-west-1.amazonaws.com/mitzu-pro-ecr-dev:LATEST"
}

variable "ecs_max_capacity" {
  description = "The maximum capacity for ECS to autoscale for"
  default     = 1
}

variable "exposed_port" {
  default = 8081
}

variable "health_check" {
  default = "/ping"
}

variable "dns_names" {
  type = map(string)
  default = {
    main        = "dev.mitzu.io"
    cognito_sso = "dev-cognito.mitzu.io"
    google_sso  = "dev-google.mitzu.io"
    okta_sso    = "dev-okta.mitzu.io"
  }
}

variable "certificate_arn" {
  default = "arn:aws:acm:eu-west-1:531968921201:certificate/80f46b8f-f098-434b-ac4f-bb28c9984f74"
}

variable "dns_zone_id" {
  default = "Z0102514LRY95QZ6U201"
}

variable "debug_s3_arn" {
  type    = string
  default = "arn:aws:s3:::mitzu-debug-dataframe"
}
