
resource "aws_elasticache_subnet_group" "private_subnet_group" {
  name       = "${var.application_name}-subnet-group-${var.environment}"
  subnet_ids = [data.aws_subnet.private_a.id, data.aws_subnet.private_b.id]
}

resource "aws_security_group" "redis" {
  name   = "${var.application_name}-sg-redis-${var.environment}"
  vpc_id = data.aws_vpc.main.id

  ingress {
    protocol         = "TCP"
    from_port        = 6379
    to_port          = 6379
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_elasticache_cluster" "storage" {
  cluster_id         = "${var.application_name}-redis-${var.environment}"
  engine             = "redis"
  node_type          = "cache.t3.micro"
  num_cache_nodes    = 1
  port               = 6379
  apply_immediately  = true
  subnet_group_name  = aws_elasticache_subnet_group.private_subnet_group.name
  security_group_ids = [aws_security_group.redis.id]
}

resource "aws_cloudwatch_metric_alarm" "redis_memory_usage_80" {
  alarm_name                = "${var.short_name}_redis_memory_usage_80"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = 1
  metric_name               = "DatabaseMemoryUsagePercentage"
  namespace                 = "AWS/ElastiCache"
  period                    = 60
  statistic                 = "Average"
  threshold                 = 80
  alarm_description         = ""
  insufficient_data_actions = []
  dimensions = {
    CacheClusterId = aws_elasticache_cluster.storage.cluster_id
  }
  alarm_actions = [
    var.alarm_sns_topic,
  ]
  ok_actions = [
    var.alarm_sns_topic,
  ]
}

resource "aws_cloudwatch_metric_alarm" "redis_memory_usage_90" {
  alarm_name                = "${var.short_name}_redis_memory_usage_90"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = 1
  metric_name               = "DatabaseMemoryUsagePercentage"
  namespace                 = "AWS/ElastiCache"
  period                    = 60
  statistic                 = "Average"
  threshold                 = 90
  alarm_description         = ""
  insufficient_data_actions = []
  dimensions = {
    CacheClusterId = aws_elasticache_cluster.storage.cluster_id
  }
  alarm_actions = [
    var.alarm_sns_topic,
  ]
  ok_actions = [
    var.alarm_sns_topic,
  ]
}