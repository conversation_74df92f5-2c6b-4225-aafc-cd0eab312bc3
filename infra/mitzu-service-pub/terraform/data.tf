
# Dynamicly importing necessary resources from the infra/terraform

data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = ["${var.infra_name}-vpc-${var.environment}"]
  }
}

data "aws_subnet" "private_a" {
  filter {
    name   = "tag:Name"
    values = ["${var.infra_name}-sn-private-a-${var.environment}"]
  }
}

data "aws_subnet" "public_a" {
  filter {
    name   = "tag:Name"
    values = ["${var.infra_name}-sn-public-a-${var.environment}"]
  }
}

data "aws_subnet" "private_b" {
  filter {
    name   = "tag:Name"
    values = ["${var.infra_name}-sn-private-b-${var.environment}"]
  }
}

data "aws_lb" "main" {
  name = "${var.infra_name}-alb-${var.environment}"
}

data "aws_ecs_cluster" "main" {
  cluster_name = "${var.infra_name}-ecs-${var.environment}"
}

data "aws_security_group" "alb" {
  filter {
    name   = "group-name"
    values = ["${var.infra_name}-sg-alb-${var.environment}"]
  }
}

data "aws_secretsmanager_secret" "secrets" {
  arn = var.secret_arn
}

data "aws_secretsmanager_secret_version" "current" {
  secret_id = data.aws_secretsmanager_secret.secrets.id
}
