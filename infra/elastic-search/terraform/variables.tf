variable "region" {
  type    = string
  default = "eu-west-1"
}

variable "es_endpoint" {
  type    = string
  default = "https://3bb85f5b0a0d47a7b9e3eb33ec9e59b5.eu-west-1.aws.found.io"
}

variable "es_api_key" {
  type = string
}

variable "cwl_endpoint" {
  type    = string
  default = "logs.eu-west-1.amazonaws.com"
}

variable "log_groups" {
  type = map(string)
  default = {
    mitzu-pro-service-task-def-prod     = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-task-def-prod:*"
    mitzu-pro-service-dev-task-def-prod = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-dev-task-def-prod:*"
    mitzu-pro-service-pub-task-def-prod = "arn:aws:logs:eu-west-1:531968921201:log-group:mitzu-pro-service-pub-task-def-prod:*"
  }
}