
resource "aws_iam_role" "lambda_elasticsearch_execution_role" {
  name               = "lambda_elasticsearch_execution_role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "lambda_elasticsearch_execution_policy" {
  name   = "lambda_elasticsearch_execution_policy"
  role   = aws_iam_role.lambda_elasticsearch_execution_role.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": [
        "arn:aws:logs:*:*:*"
      ]
    }
  ]
}
EOF
}

resource "null_resource" "npm_dependencies" {
  provisioner "local-exec" {
    command = "cd ${path.module}/src && npm install"
  }

  triggers = {
    index   = sha256(file("${path.module}/src/exports.js"))
    package = sha256(file("${path.module}/src/package.json"))
    lock    = sha256(file("${path.module}/src/package-lock.json"))
    node    = sha256(join("", fileset(path.module, "src/**/*.js")))
  }
}

data "null_data_source" "wait_for_lambda_exporter" {
  inputs = {
    lambda_dependency_id = null_resource.npm_dependencies.id
    source_dir           = "${path.module}/src/"
  }
}

data "archive_file" "zip" {
  source_dir  = data.null_data_source.wait_for_lambda_exporter.outputs["source_dir"]
  type        = "zip"
  output_path = "${path.module}/lambda.zip"
}

resource "aws_lambda_function" "cloudwatch_to_elasticsearch" {
  filename         = data.archive_file.zip.output_path
  function_name    = "cloudwatch-to-elasticsearch"
  role             = aws_iam_role.lambda_elasticsearch_execution_role.arn
  handler          = "exports.handler"
  runtime          = "nodejs20.x"
  source_code_hash = data.archive_file.zip.output_base64sha256
  timeout          = 60

  reserved_concurrent_executions = 1

  environment {
    variables = {
      es_endpoint = "${var.es_endpoint}"
      es_api_key  = "${var.es_api_key}"
    }
  }
}

resource "aws_lambda_permission" "cloudwatch_allow" {
  for_each = var.log_groups

  statement_id  = "cloudwatch_allow_${each.key}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.cloudwatch_to_elasticsearch.arn
  principal     = var.cwl_endpoint
  source_arn    = each.value
}

resource "aws_cloudwatch_log_subscription_filter" "cloudwatch_logs_to_es" {
  for_each = var.log_groups

  depends_on      = [aws_lambda_permission.cloudwatch_allow]
  name            = "cloudwatch_logs_to_elasticsearch"
  log_group_name  = each.key
  filter_pattern  = ""
  destination_arn = aws_lambda_function.cloudwatch_to_elasticsearch.arn
}