
resource "aws_iam_role" "ecs_task_role" {
  name = "${var.application_name}-ecs-task-role"

  assume_role_policy = <<EOF
{
 "Version": "2012-10-17",
 "Statement": [
   {
     "Action": "sts:AssumeRole",
     "Principal": {
       "Service": "ecs-tasks.amazonaws.com"
     },
     "Effect": "Allow",
     "Sid": ""
   }
 ]
}
EOF
}

resource "aws_iam_policy" "logging_role_policy" {
  name        = "${var.application_name}-ecs-logging-policy"
  description = "Policy that allows access to DynamoDB"
  policy      = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DescribeLogStreams"
    ],
      "Resource": [
        "arn:aws:logs:*:*:*"
    ]
  }
 ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "ecs-task-role-policy-attachment" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.logging_role_policy.arn
}

resource "aws_iam_role_policy" "debug_s3_access_policy" {
  name   = "${var.application_name}-debug_s3_access_policy"
  role   = aws_iam_role.ecs_task_role.name
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Action": [
          "s3:PutObject"
      ],
      "Resource": "${var.debug_s3_arn}/*"
    }
 ]
}
EOF
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name               = "${var.application_name}-ecs-task-execution-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "ecs-task-execution-role-policy-attachment-1" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"

}

resource "aws_iam_role_policy_attachment" "ecs-task-execution-role-policy-attachment-2" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.logging_role_policy.arn
}

resource "aws_ecs_task_definition" "main" {
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_def_cpu
  memory                   = var.task_def_mem
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn
  family                   = "${var.application_name}-task-def"
  skip_destroy             = true
  container_definitions = jsonencode([{
    name      = "${var.application_name}-container"
    image     = var.container_image
    essential = true
    cpu       = var.task_def_cpu
    memory    = var.task_def_mem
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-create-group  = "true"
        awslogs-group         = "${var.application_name}-task-def"
        awslogs-region        = "${var.region}"
        awslogs-stream-prefix = "ecs"
      }
    }
    environment = [
      { "name" : "DB_CONNECTION_STRING", "value" : "postgresql+psycopg2://${var.rds_username}:${var.rds_password}@${data.aws_db_instance.default.endpoint}/mitzu_dev" },
      { "name" : "S3_TARGET_PATH", "value" : "${var.s3_target_path}" },
      { "name" : "BIGQUERY_CREDENTIALS", "value" : "${var.bigquery_credentials}" },
    ]
  }])
}
