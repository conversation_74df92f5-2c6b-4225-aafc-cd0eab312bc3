resource "aws_security_group" "ecs_task" {
  name   = "${var.application_name}-sg-task"
  vpc_id = data.aws_vpc.main.id

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}


data "aws_ecs_task_execution" "metric_test" {
  cluster         = data.aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.main.arn
  desired_count   = 1
  launch_type     = "FARGATE"

  network_configuration {
    security_groups  = [aws_security_group.ecs_task.id]
    subnets          = [data.aws_subnet.private_a.id]
    assign_public_ip = false
  }
}
