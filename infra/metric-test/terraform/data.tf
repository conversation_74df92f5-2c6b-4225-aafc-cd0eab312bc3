
# Dynamicly importing necessary resources from the infra/terraform

data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = ["mitzu-pro-vpc-prod"]
  }
}

data "aws_subnet" "private_a" {
  filter {
    name   = "tag:Name"
    values = ["mitzu-pro-sn-private-a-prod"]
  }
}

data "aws_subnet" "public_a" {
  filter {
    name   = "tag:Name"
    values = ["mitzu-pro-sn-public-a-prod"]
  }
}

data "aws_subnet" "private_b" {
  filter {
    name   = "tag:Name"
    values = ["mitzu-pro-sn-private-b-prod"]
  }
}

data "aws_ecs_cluster" "main" {
  cluster_name = "mitzu-pro-ecs-prod"
}

data "aws_security_group" "alb" {
  filter {
    name   = "group-name"
    values = ["mitzu-pro-sg-alb-prod"]
  }
}

data "aws_db_instance" "default" {
  db_instance_identifier = "mitzu-mitzu-pro-service-dev-rds-prod"
}