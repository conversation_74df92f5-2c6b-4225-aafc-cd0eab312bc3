variable "region" {
  type    = string
  default = "eu-west-1"
}

variable "application_name" {
  default = "metric-test"
}

variable "task_def_cpu" {
  description = "The fargate task definition CPU size 256, 512, 1024, 2048 ,etc."
  default     = 1024
}

variable "task_def_mem" {
  description = "The fargate task definition MEM size  512, 1024, 2048 ,etc."
  default     = 2048
}

variable "container_image" {
  description = "The container image to deploy"
  default     = "531968921201.dkr.ecr.eu-west-1.amazonaws.com/mitzu-metric-test:LATEST"
}

variable "ecs_max_capacity" {
  description = "The maximum capacity for ECS to autoscale for"
  default     = 1
}

variable "debug_s3_arn" {
  type    = string
  default = "arn:aws:s3:::mitzu-debug-dataframe"
}

variable "bigquery_credentials" {
  type = string
}

variable "rds_username" {
  type = string
}

variable "rds_password" {
  type = string
}

variable "s3_target_path" {
  type = string
}