resource "aws_cloudwatch_log_group" "request_log_group" {
  name = "api-gw-request-logs-${var.short_name}"

  retention_in_days = 60
}

// API Gateway
resource "aws_apigatewayv2_api" "main" {
  name          = "${var.application_name}-api-gw-${var.environment}"
  protocol_type = "HTTP"
}

resource "aws_apigatewayv2_stage" "api_default_stage" {
  api_id      = aws_apigatewayv2_api.main.id
  name        = "$default"
  auto_deploy = true

  default_route_settings {
    // https://github.com/hashicorp/terraform-provider-aws/issues/14742
    throttling_burst_limit = 5000
    throttling_rate_limit  = 10000
  }

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.request_log_group.arn
    format = jsonencode(
      {
        status         = "$context.status"
        ip             = "$context.identity.sourceIp"
        path           = "$context.path"
        httpMethod     = "$context.httpMethod"
        protocol       = "$context.protocol"
        requestId      = "$context.requestId"
        requestTime    = "$context.requestTime"
        responseLength = "$context.responseLength"
        routeKey       = "$context.routeKey"
      }
    )

  }
}

resource "aws_apigatewayv2_route" "tf-prod-api-gw-default-route" {
  api_id    = aws_apigatewayv2_api.main.id
  route_key = "ANY /{proxy+}"
  target    = "integrations/${aws_apigatewayv2_integration.vpc_link_api_integration.id}"
}

// API GW integrations per target group
resource "aws_apigatewayv2_vpc_link" "main_vpc_link" {
  # There is no data support for this resource so we need to declare it per service
  name = "${var.infra_name}-vpclink-sg-${var.environment}"
  security_group_ids = [
    aws_security_group.vpclink_sg.id
  ]
  subnet_ids = [
    data.aws_subnet.private_a.id
  ]
}

resource "aws_apigatewayv2_integration" "vpc_link_api_integration" {
  api_id             = aws_apigatewayv2_api.main.id
  connection_id      = aws_apigatewayv2_vpc_link.main_vpc_link.id
  integration_type   = "HTTP_PROXY"
  integration_uri    = aws_alb_listener.http.arn
  integration_method = "ANY"
  connection_type    = "VPC_LINK"
}

resource "aws_security_group" "vpclink_sg" {
  name        = "${var.application_name}-vpclink-sg-${var.environment}"
  description = "Allow all outbound traffic"
  vpc_id      = data.aws_vpc.main.id

  egress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    cidr_blocks = [
    "0.0.0.0/0"]
  }
}

resource "aws_security_group_rule" "vpclink_alb" {
  type                     = "ingress"
  security_group_id        = data.aws_security_group.alb.id
  from_port                = var.exposed_port
  protocol                 = "TCP"
  to_port                  = var.exposed_port
  source_security_group_id = aws_security_group.vpclink_sg.id
}

resource "aws_security_group_rule" "main_ingress_alb" {
  type                     = "ingress"
  security_group_id        = aws_security_group.ecs_task.id
  from_port                = var.exposed_port
  protocol                 = "TCP"
  to_port                  = var.exposed_port
  source_security_group_id = data.aws_security_group.alb.id
}



resource "aws_apigatewayv2_domain_name" "default" {
  for_each = var.dns_names

  domain_name = each.value
  domain_name_configuration {
    certificate_arn = var.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }
}

resource "aws_apigatewayv2_api_mapping" "default" {
  for_each = aws_apigatewayv2_domain_name.default

  api_id      = aws_apigatewayv2_api.main.id
  domain_name = each.value.domain_name
  stage       = aws_apigatewayv2_stage.api_default_stage.id
}

resource "aws_route53_record" "default" {
  for_each = aws_apigatewayv2_domain_name.default

  zone_id = var.dns_zone_id
  name    = each.value.domain_name
  type    = "A"

  alias {
    name                   = each.value.domain_name_configuration[0].target_domain_name
    zone_id                = each.value.domain_name_configuration[0].hosted_zone_id
    evaluate_target_health = false
  }
}


# avoid recreating the same resources with a different name
moved {
  from = aws_route53_record.dns_record_a
  to   = aws_route53_record.default["beta"]
}

moved {
  from = aws_apigatewayv2_domain_name.main_doman
  to   = aws_apigatewayv2_domain_name.default["beta"]
}

moved {
  from = aws_apigatewayv2_api_mapping.main_domain_mapping
  to   = aws_apigatewayv2_api_mapping.default["beta"]
}