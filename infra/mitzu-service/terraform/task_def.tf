
resource "aws_iam_role" "ecs_task_role" {
  name = "${var.application_name}-ecs-task-role"

  assume_role_policy = <<EOF
{
 "Version": "2012-10-17",
 "Statement": [
   {
     "Action": "sts:AssumeRole",
     "Principal": {
       "Service": "ecs-tasks.amazonaws.com"
     },
     "Effect": "Allow",
     "Sid": ""
   }
 ]
}
EOF
}

resource "aws_iam_policy" "logging_role_policy" {
  name        = "${var.application_name}-ecs-logging-policy-${var.environment}"
  description = "Policy that allows access to DynamoDB"
  policy      = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DescribeLogStreams"
    ],
      "Resource": [
        "arn:aws:logs:*:*:*"
    ]
  }
 ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "ecs-task-role-policy-attachment" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.logging_role_policy.arn
}

resource "aws_iam_role_policy" "debug_s3_access_policy" {
  name   = "${var.application_name}-debug_s3_access_policy-${var.environment}"
  role   = aws_iam_role.ecs_task_role.name
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Action": [
          "s3:PutObject"
      ],
      "Resource": "${var.debug_s3_arn}/*"
    }
 ]
}
EOF
}

resource "aws_iam_role_policy" "secret_read_only_policy" {
  name   = "${var.application_name}-secret_read_only_policy-${var.environment}"
  role   = aws_iam_role.ecs_task_role.name
  policy = <<EOF
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "VisualEditor0",
			"Effect": "Allow",
			"Action": "secretsmanager:GetSecretValue",
			"Resource": "${var.secret_arn}"
		}
	]
}
EOF
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name               = "${var.application_name}-ecs-task-execution-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "ecs-task-execution-role-policy-attachment-1" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"

}

resource "aws_iam_role_policy_attachment" "ecs-task-execution-role-policy-attachment-2" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.logging_role_policy.arn
}


resource "aws_ecs_task_definition" "main" {
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_def_cpu
  memory                   = var.task_def_mem
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn
  family                   = "${var.application_name}-task-def-${var.environment}"
  skip_destroy             = true
  container_definitions = jsonencode([{
    name      = "${var.application_name}-container-${var.environment}"
    image     = var.container_image
    essential = true
    cpu       = var.task_def_cpu
    memory    = var.task_def_mem
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-create-group  = "true"
        awslogs-group         = "${var.application_name}-task-def-${var.environment}"
        awslogs-region        = "${var.region}"
        awslogs-stream-prefix = "ecs"
      }
    }
    environment = [
      { "name" : "LOG_LEVEL", "value" : "INFO" },
      { "name" : "LOG_HANDER", "value" : "json" },
      { "name" : "AUTH_BACKEND", "value" : "cognito" },
      { "name" : "COGNITO_DOMAIN", "value" : "signin.mitzu.io" },
      { "name" : "COGNITO_REGION", "value" : "eu-west-1" },
      { "name" : "COGNITO_POOL_ID", "value" : "eu-west-1_QkZu6BnVD" },
      { "name" : "HOME_URL", "value" : "https://${var.dns_names["main"]}" },
      { "name" : "REDIS_URL", "value" : "redis://${aws_elasticache_cluster.storage.cache_nodes[0].address}:${aws_elasticache_cluster.storage.cache_nodes[0].port}" },
      { "name" : "GUNICORN_WORKERS", "value" : "4" },
      { "name" : "SMTP_HOST", "value" : "email-smtp.${var.region}.amazonaws.com" },
      { "name" : "EVENT_SCORING_INPUT_VIEW", "value" : "app.event_scoring_input_view" },
      { "name" : "AWS_SECRET_NAME", "value" : "${var.secret_name}" },
    ]
    portMappings = [{
      containerPort = var.exposed_port
      hostPort      = var.exposed_port
    }]
  }])
}
