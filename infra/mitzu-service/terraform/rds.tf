resource "aws_db_subnet_group" "default" {
  name       = "${var.application_name}-rds-sng-${var.environment}"
  subnet_ids = [data.aws_subnet.private_a.id, data.aws_subnet.private_b.id]
}

resource "aws_security_group" "default" {
  name   = "${var.application_name}-rds-sg-${var.environment}"
  vpc_id = data.aws_vpc.main.id

  ingress {
    protocol         = "TCP"
    from_port        = 5432
    to_port          = 5432
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}


resource "aws_db_instance" "default" {
  identifier                 = "mitzu-${var.application_name}-rds-${var.environment}"
  engine                     = "postgres"
  parameter_group_name       = "default.postgres14"
  db_subnet_group_name       = aws_db_subnet_group.default.name
  vpc_security_group_ids     = [aws_security_group.default.id]
  engine_version             = "14.17"
  auto_minor_version_upgrade = false
  instance_class             = "db.t3.large"
  allocated_storage          = 30
  username                   = local.rds_username
  password                   = local.rds_password
  maintenance_window         = "Mon:00:00-Mon:03:00"
  backup_window              = "03:00-04:00"
  apply_immediately          = true # wont apply changes in the maintenance window
  backup_retention_period    = 14   # we can restore backups for 14 days before they are being deleted
  skip_final_snapshot        = true
  lifecycle {
    ignore_changes  = [password]
    prevent_destroy = true
  }
  performance_insights_enabled = true
  deletion_protection          = true
}
