
resource "aws_iam_role" "lambda_mitzu_slack_bot_role" {
  name               = "lambda_mitzu_slack_bot_role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_sqs_queue" "mitzu_slack_bot_commands" {
  name                        = "mitzu_slack_bot_commands.fifo"
  fifo_queue                  = true
  deduplication_scope         = "queue"
  visibility_timeout_seconds  = 10
  content_based_deduplication = true
}

resource "aws_iam_role_policy" "lambda_mitzu_slack_bot_execution_policy" {
  name   = "lambda_mitzu_slack_bot_execution_policy"
  role   = aws_iam_role.lambda_mitzu_slack_bot_role.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": [
        "arn:aws:logs:*:*:*"
      ]
    },
    {
      "Sid": "",
      "Effect": "Allow",
      "Action": [
          "sqs:*"
      ],
      "Resource": "${aws_sqs_queue.mitzu_slack_bot_commands.arn}"
    }
  ]
}
EOF
}


data "archive_file" "zip" {
  source_file = "main.py"
  type        = "zip"
  output_path = "${path.module}/lambda.zip"
}

resource "aws_lambda_function" "mitzu_slack_bot" {
  filename      = data.archive_file.zip.output_path
  function_name = "mitzu-slack-bot"
  role          = aws_iam_role.lambda_mitzu_slack_bot_role.arn
  handler       = "main.lambda_handler"
  runtime       = "python3.11"
  timeout       = 60

  reserved_concurrent_executions = 1

  source_code_hash = data.archive_file.zip.output_base64sha256

  layers = [
    "arn:aws:lambda:eu-west-1:770693421928:layer:Klayers-p311-requests:6",
    "arn:aws:lambda:eu-west-1:770693421928:layer:Klayers-p311-boto3:9",
  ]

  environment {
    variables = {
      GITHUB_PAT       = var.github_pat
      SLACK_BOT_TOKEN  = var.slack_bot_token
      COMMAND_SQS_NAME = aws_sqs_queue.mitzu_slack_bot_commands.name
    }
  }
}

resource "aws_lambda_function_url" "mitzu_slack_bot" {
  function_name      = aws_lambda_function.mitzu_slack_bot.function_name
  authorization_type = "NONE"
}


resource "aws_lambda_event_source_mapping" "event_source_mapping" {
  event_source_arn = aws_sqs_queue.mitzu_slack_bot_commands.arn
  enabled          = true
  function_name    = aws_lambda_function.mitzu_slack_bot.arn
  batch_size       = 1
}


resource "aws_cloudwatch_event_rule" "every_weekday_morning" {
  name        = "every_weekday_morning"
  description = "trigger lambda every weekday morning"

  schedule_expression = "cron(0 7 ? * MON-FRI *)"
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.every_weekday_morning.name
  target_id = "SendToLambda"
  arn       = aws_lambda_function.mitzu_slack_bot.arn
}

resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.mitzu_slack_bot.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.every_weekday_morning.arn
}
