from __future__ import annotations

import hashlib
import json
import math
import os
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import boto3
import requests
from six.moves import urllib  # type: ignore[import]

GITHUB_PAT = os.getenv("GITHUB_PAT")
SLACK_BOT_TOKEN = os.getenv("SLACK_BOT_TOKEN")
COMMAND_SQS_NAME = os.getenv("COMMAND_SQS_NAME")


TEST_MATRIX_ADAPTERS = [
    "docker",
    "athena",
    "bigquery",
    "databricks",
    "trino",
    "snowflake",
    # "redshift",  # disabled
]


@dataclass(frozen=True)
class Command:
    message: str
    channel_id: str
    user: str

    def as_json(self) -> str:
        return json.dumps(
            {
                "message": self.message,
                "channel_id": self.channel_id,
                "user": self.user,
            }
        )

    @classmethod
    def from_json(cls, json_str: str) -> Command:
        json_dict = json.loads(json_str)
        return Command(
            message=json_dict["message"],
            channel_id=json_dict["channel_id"],
            user=json_dict["user"],
        )


def get_release_notes() -> str:
    response = requests.post(
        "https://api.github.com/repos/mitzu-io/mitzu/releases/generate-notes",
        headers={
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {GITHUB_PAT}",
            "X-Github-Api-Version": "2022-11-28",
        },
        data=json.dumps(
            {
                "tag_name": "v.next",
            }
        ),
    )
    if response.status_code != 200:
        raise ValueError(f"Failed to call github api: {response.text}")
    return response.json()["body"]


def get_changed_files_from_github(pr_number: str) -> List[str]:
    response = requests.get(
        f"https://api.github.com/repos/mitzu-io/mitzu/pulls/{pr_number}/files?per_page=100",
        headers={
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {GITHUB_PAT}",
            "X-GitHub-Api-Version": "2022-11-28",
        },
    )
    if response.status_code != 200:
        raise ValueError(f"failed to fetch github api: {response.text}")

    data = response.json()
    changed_files = [record["filename"] for record in data]
    return changed_files


def parse_pr_number(url: str) -> str:
    return url.split("/")[-1]


def get_release_plan() -> str:
    notes = get_release_notes()

    blocks: Dict[str, List[str]] = {
        "app_change": [],
        "doc_only": [],
        "other": [],
    }
    for line in notes.split("\n"):
        parts = line.strip().split(" ")

        msg = " ".join(
            [
                part if part != "*" else " - "
                for part in parts
                if not part.startswith("https://github.com")
            ]
        )
        links = [part for part in parts if part.startswith("https://github.com")]

        if len(links) == 1:
            link = links[0]
            if "Full Changelog" not in line:
                link = f" <{link}|PR>"

                pr_number = parse_pr_number(link)
                changed_files = get_changed_files_from_github(pr_number)

                has_migration = False
                has_app_terraform_change = False
                has_app_change = False
                has_adapter_change = False
                has_docs_change = False
                has_serialization_change = False

                for filename in changed_files:
                    if filename.startswith("release/app/migrations/"):
                        has_migration = True

                    if filename.startswith("docs/"):
                        has_docs_change = True

                    if filename.startswith("mitzu"):
                        has_app_change = True

                    if filename.startswith("mitzu/adapters") or filename.startswith(
                        "vendor"
                    ):
                        has_adapter_change = True

                    if filename == "mitzu/webapp/serialization.py":
                        has_serialization_change = True

                    if filename.startswith("infra"):
                        is_helper_lambda_change = False
                        for helper_lambda in [
                            "cloudwatch-error-to-slack",
                            "elastic-search",
                            "mitzu-slack-bot",
                        ]:
                            if filename.startswith(f"infra/{helper_lambda}"):
                                is_helper_lambda_change = True

                        if not is_helper_lambda_change:
                            has_app_terraform_change = True

                has_app_change = (
                    has_app_change or has_migration or has_app_terraform_change
                )

                if not has_app_change:
                    link += " :approved: no app change"

                if has_migration or has_app_terraform_change or has_adapter_change:
                    link += " :warning:"

                if has_migration:
                    link += " :database: contains migration"

                if has_app_terraform_change:
                    link += " :terraform: contains terraform changes"

                if has_adapter_change:
                    link += " :database-blue: contains adapter changes"

                if has_docs_change:
                    link += " :bookmark_tabs: contains doc change"

                if has_serialization_change:
                    link += " :floppy_disk: contains serialization change"

                line = "  " + msg + link
                if has_app_change:
                    blocks["app_change"].append(line)
                elif has_docs_change:
                    blocks["doc_only"].append(line)
                else:
                    blocks["other"].append(line)

    slack_msg = ":mitzu: *App changes*:\n"
    slack_msg += "\n".join(blocks["app_change"])

    if len(blocks["doc_only"]) > 0:
        slack_msg += "\n\n:bookmark_tabs: *Doc only changes*:\n"
        slack_msg += "\n".join(blocks["doc_only"])

    if len(blocks["other"]) > 0:
        slack_msg += "\n\n:gear: *Other changes*:\n"
        slack_msg += "\n".join(blocks["other"])

    return slack_msg


def get_test_matrix(text: str) -> str:
    parts = text.split(" ")
    index = parts.index("test_matrix") + 1
    parts = parts[index:]
    branch = "main"

    if len(parts) > 0:
        branch = parts.pop(0)

    if len(parts) == 0:
        adapters = {f"test_{adapter}": True for adapter in TEST_MATRIX_ADAPTERS}
    else:
        adapters = {f"test_{adapter}": False for adapter in TEST_MATRIX_ADAPTERS}
        for adapter in parts:
            adapters[f"test_{adapter}"] = True
    payload = {
        "ref": branch,
        "inputs": adapters,
    }

    return trigger_github_actions("matrix", "matrix.yaml", payload, branch)


def trigger_github_actions(
    name: str, yaml_name: str, payload: Optional[Dict[str, Any]], branch: str
) -> str:
    triggered_at = datetime.now()
    resp = requests.post(
        f"https://api.github.com/repos/mitzu-io/mitzu/actions/workflows/{yaml_name}/dispatches",
        headers={
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {GITHUB_PAT}",
            "X-GitHub-Api-Version": "2022-11-28",
        },
        data=json.dumps(payload) if payload is not None else None,
    )
    if resp.status_code != 204:
        return f"Failed to trigger {name} workflow: {resp.status_code} {resp.text} {json.dumps(payload)}"

    for i in range(1, 5):
        resp = requests.get(
            f"https://api.github.com/repos/mitzu-io/mitzu/actions/workflows/{yaml_name}/runs?branch={branch}",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": f"Bearer {GITHUB_PAT}",
                "X-GitHub-Api-Version": "2022-11-28",
            },
        )
        if resp.status_code != 200:
            return f"Failed to list running {name} workflows: {resp.status_code} {resp.text} {json.dumps(payload)}"

        workflow_runs = resp.json()["workflow_runs"]
        for run in workflow_runs:
            started_at = datetime.strptime(
                run["run_started_at"], "%Y-%m-%dT%H:%M:%SZ"
            )  # it should be timezone less datetime
            if started_at > triggered_at:
                return f"{name.capitalize()} workflow started: {run['html_url']}"

        time.sleep(1)

    return (
        f"{name.capitalize()} workflow started but could not find it in the list: "
        f"https://github.com/mitzu-io/mitzu/actions/workflows/{yaml_name}"
    )


def list_pull_requests() -> str:
    response = requests.get(
        "https://api.github.com/repos/mitzu-io/mitzu/pulls?state=open",
        headers={
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {GITHUB_PAT}",
            "X-Github-Api-Version": "2022-11-28",
        },
    )
    if response.status_code != 200:
        raise ValueError(f"Failed to call github api: {response.text}")

    pr_lines = []
    draft_lines = []
    now = datetime.now()
    for pr in response.json():
        pr_number = pr["number"]
        url = pr["html_url"]
        title = pr["title"]
        author = pr["user"]["login"]
        is_draft = pr["draft"]
        last_sha = pr["head"]["sha"]

        last_updated_at = datetime.strptime(pr["updated_at"], "%Y-%m-%dT%H:%M:%SZ")
        time_diff = now - last_updated_at
        total_seconds = time_diff.total_seconds()
        days = math.floor(total_seconds / (24 * 60 * 60))
        total_seconds -= days * (24 * 60 * 60)
        hours = math.floor(total_seconds / (60 * 60))
        total_seconds -= hours * (60 * 60)
        minutes = math.floor(total_seconds / 60)

        time_diff_str = "updated"
        if days > 0:
            time_diff_str += f" {days} days"
        if hours > 0:
            time_diff_str += f" {hours} hours"
        time_diff_str += f" {minutes} minutes"

        time_diff_str += " ago"

        review_response = requests.get(
            f"https://api.github.com/repos/mitzu-io/mitzu/pulls/{pr_number}/reviews",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": f"Bearer {GITHUB_PAT}",
                "X-Github-Api-Version": "2022-11-28",
            },
        )
        if review_response.status_code != 200:
            raise ValueError(f"Failed to call github api: {review_response.text}")
        reviewers = {}
        for review in review_response.json():
            reviewers[review["user"]["login"]] = review["state"]

        has_comments = (
            "REQUEST_CHANGES" in reviewers.values() or "COMMENT" in reviewers.values()
        )
        is_approved = "APPROVED" in reviewers.values()

        review_state = ":warning: reviewer wanted"
        if has_comments and not is_approved:
            review_state = ":speech_balloon: reviewed"
        elif is_approved:
            review_state = ":white_check_mark: approved"

        if is_draft:
            review_state = ":building_construction: draft"

        checks_response = requests.get(
            f"https://api.github.com/repos/mitzu-io/mitzu/commits/{last_sha}/check-runs",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": f"Bearer {GITHUB_PAT}",
                "X-Github-Api-Version": "2022-11-28",
            },
        )
        if checks_response.status_code != 200:
            raise ValueError(f"Failed to call github api: {checks_response.text}")

        in_progress = False
        is_passing_checks = True
        for status in checks_response.json()["check_runs"]:
            if in_progress:
                continue
            if status["status"] == "pending":
                in_progress = True
            elif status["status"] != "completed":
                in_progress = True
            elif status["conclusion"] != "success":
                is_passing_checks = False

        if in_progress:
            check_state = ":large_yellow_circle: checks are running"
        elif is_passing_checks:
            check_state = ":large_green_circle: checks passed"
        else:
            check_state = ":red_circle: checks failed"

        line = f"<{url}|{title}> | by {author} | {time_diff_str} | {check_state} | {review_state}"
        if is_draft:
            draft_lines.append(line)
        else:
            pr_lines.append(line)
    return "\n".join(pr_lines) + "\n" + ("\n".join(draft_lines))


def deploy_docs() -> str:
    branch = "main"
    return trigger_github_actions(
        "deploy docs",
        "deploy_docs.yaml",
        {
            "ref": branch,
        },
        branch,
    )


def send_text_response(command: Command, response_text: str) -> None:
    SLACK_URL = "https://slack.com/api/chat.postMessage"
    data = urllib.parse.urlencode(
        {
            "token": SLACK_BOT_TOKEN,
            "channel": command.channel_id,
            "text": response_text,
            "user": command.user,
            "link_names": True,
        }
    )
    data = data.encode("ascii")
    request = urllib.request.Request(SLACK_URL, data=data, method="POST")
    request.add_header("Content-Type", "application/x-www-form-urlencoded")
    res = urllib.request.urlopen(request).read()
    print("res:", res)


def check_message(text: str) -> str:
    try:
        if text.endswith("release plan"):
            return get_release_plan()
        if text.endswith("deploy docs"):
            return deploy_docs()
        if text.endswith("pr"):
            return list_pull_requests()
        if text.endswith("rocket"):
            return ":mitzu::rocket:"
        if "test_matrix" in text:
            return get_test_matrix(text)
    except Exception as exc:
        return str(exc)

    message = ""
    if not text.endswith("help"):
        message = f"Unknown command: '{text}'\n"
    return f"""{message}
 choose either:
  - deploy docs
  - pr
  - release plan
  - rocket
  - test_matrix [branch] [{" ".join(TEST_MATRIX_ADAPTERS)}]
  - help"""


def is_bot(event: Dict[str, str]) -> bool:
    return "bot_profile" in event["event"]


def url_verification(event: Dict[str, str]) -> bool:
    return event.get("type") == "url_verification"


def lambda_handler(event: Dict[str, Any], context: Any) -> Optional[Dict[str, Any]]:
    if "body" in event.keys():
        # slack webhook, reply fast to avoid being triggered again
        print("slack event", event)
        event = json.loads(event["body"])

        if url_verification(event):  # needed only for slack integration
            return {"statusCode": 200, "body": event.get("challenge")}

        if is_bot(event):  # ignore messages from the bot itself
            return {"statusCode": 200, "body": "OK"}

        if (
            event["event"].get("type") == "app_mention"
            or event["event"].get("channel_type") == "im"
        ):
            message = Command(
                message=event["event"]["text"],
                channel_id=event["event"]["channel"],
                user=event["event"]["user"],
            )

            sqs = boto3.client("sqs")
            queue_url = sqs.get_queue_url(QueueName=COMMAND_SQS_NAME)["QueueUrl"]

            message_json = message.as_json()
            sqs.send_message(
                QueueUrl=queue_url,
                MessageBody=message_json,
                MessageGroupId="mitzu",
                MessageDeduplicationId=hashlib.md5(
                    message_json.encode("utf-8")
                ).hexdigest()
                if "release plan" in message_json
                else event["event"]["ts"],
            )

        return {"statusCode": 200, "body": "OK"}

    elif "Records" in event.keys():
        for record in event["Records"]:
            if record["eventSource"] == "aws:sqs":
                print("sqs event", event)

                # clear message to avoid retrigger
                sqs = boto3.client("sqs")
                sqs.delete_message(
                    QueueUrl=COMMAND_SQS_NAME, ReceiptHandle=record["receiptHandle"]
                )

                command = Command.from_json(record["body"])
                slack_message = check_message(command.message)
                send_text_response(command, slack_message)
            else:
                print("Unknown event in records", record)

    # daily schedule
    elif event.get("source") == "aws.events":
        message = Command(
            message="<@U072HM8BYAZ> release plan",
            channel_id="C072GF708NN",
            user="U072QBXA1RP",
        )

        sqs = boto3.client("sqs")
        queue_url = sqs.get_queue_url(QueueName=COMMAND_SQS_NAME)["QueueUrl"]

        message_json = message.as_json()
        sqs.send_message(
            QueueUrl=queue_url,
            MessageBody=message_json,
            MessageGroupId="mitzu",
            MessageDeduplicationId=hashlib.md5(
                message_json.encode("utf-8")
            ).hexdigest(),
        )

        return {"statusCode": 200, "body": "OK"}

    else:
        print("Unknown event", event)

    return None
