from __future__ import annotations

import json
import os
from datetime import datetime
from typing import Any, Dict, Optional

import boto3

STRIPE_ENV = os.getenv("STRIPE_ENV")
BUCKET_NAME = os.getenv("BUCKET_NAME")


def lambda_handler(event: Dict[str, Any], context: Any) -> Optional[Dict[str, Any]]:
    print("stripe event", event)

    s3_client = boto3.client("s3")

    try:
        stripe_event = json.loads(event["body"])
    except json.decoder.JSONDecodeError as e:
        print("Webhook error while parsing basic request." + str(e))
        return {"statusCode": 400, "body": ""}

    event_id = stripe_event["id"]
    event_name = stripe_event["type"].replace(".", "_")
    event_time = str(datetime.fromtimestamp(stripe_event["created"]).date())

    stripe_event["table_name"] = event_name
    stripe_event["date"] = event_time

    s3_key = f"raw/{STRIPE_ENV}/{event_name}/date={event_time}/{event_id}.json"

    s3_client.put_object(
        Body=json.dumps(stripe_event),
        Bucket=BUCKET_NAME,
        Key=s3_key,
    )
    print(s3_key)

    return {"statusCode": 200, "body": "OK"}
