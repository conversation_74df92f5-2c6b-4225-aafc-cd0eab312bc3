
# data persistency
resource "aws_s3_bucket" "stripe_bucket" {
  bucket = "mitzu-stripe-data"
}

resource "aws_s3_bucket_ownership_controls" "stripe_bucket_acl_ownership" {
  bucket = aws_s3_bucket.stripe_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}

resource "aws_s3_bucket_acl" "stripe_bucket_acl" {
  bucket     = aws_s3_bucket.stripe_bucket.id
  acl        = "private"
  depends_on = [aws_s3_bucket_ownership_controls.stripe_bucket_acl_ownership]
}

# webhook lambda

resource "aws_iam_role" "lambda_stripe_webhook_role" {
  for_each           = var.stripe_env
  name               = "lambda_stripe_webhook_role_${each.key}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "lambda_stripe_webhook_execution_policy" {
  for_each = var.stripe_env
  name     = "lambda_stripe_webhook_execution_policy_${each.key}"
  role     = aws_iam_role.lambda_stripe_webhook_role[each.key].id
  policy   = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": [
        "arn:aws:logs:*:*:*"
      ]
    },
    {
      "Sid": "",
      "Effect": "Allow",
      "Action": [
          "s3:PutObject"
      ],
      "Resource": "${aws_s3_bucket.stripe_bucket.arn}/*"
    }
  ]
}
EOF
}


data "archive_file" "zip" {
  source_file = "stripe_webhook.py"
  type        = "zip"
  output_path = "${path.module}/lambda.zip"
}

resource "aws_lambda_function" "stripe_webhook" {
  for_each      = var.stripe_env
  filename      = data.archive_file.zip.output_path
  function_name = "stripe-webhook-${each.key}"
  role          = aws_iam_role.lambda_stripe_webhook_role[each.key].arn
  handler       = "stripe_webhook.lambda_handler"
  runtime       = "python3.11"
  timeout       = 60

  reserved_concurrent_executions = 1

  source_code_hash = data.archive_file.zip.output_base64sha256

  layers = [
    "arn:aws:lambda:eu-west-1:770693421928:layer:Klayers-p311-requests:6",
    "arn:aws:lambda:eu-west-1:770693421928:layer:Klayers-p311-boto3:9",
  ]

  environment {
    variables = {
      STRIPE_ENV = each.key
      BUCKET_NAME = aws_s3_bucket.stripe_bucket.bucket
    }
  }
}

resource "aws_lambda_function_url" "stripe_webhook" {
  for_each           = var.stripe_env
  function_name      = aws_lambda_function.stripe_webhook[each.key].function_name
  authorization_type = "NONE"
}
