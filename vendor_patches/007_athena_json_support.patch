diff --git a/mitzu/vendor/pyathena/sqlalchemy_athena.py b/mitzu/vendor/pyathena/sqlalchemy_athena.py
index 8edc4ee91..d0feaa0bf 100644
--- a/mitzu/vendor/pyathena/sqlalchemy_athena.py
+++ b/mitzu/vendor/pyathena/sqlalchemy_athena.py
@@ -109,6 +109,9 @@ class AthenaTypeCompiler(GenericTypeCompiler):
     def visit_REAL(self, type_, **kw):
         return "DOUBLE"
 
+    def visit_DOUBLE(self, type_, **kw):
+        return "DOUBLE"
+
     def visit_NUMERIC(self, type_, **kw):
         return self.visit_DECIMAL(type_, **kw)
 
@@ -178,6 +181,9 @@ class AthenaTypeCompiler(GenericTypeCompiler):
     def visit_BOOLEAN(self, type_, **kw):
         return "BOOLEAN"
 
+    def visit_JSON(self, type_, **kw):
+        return "JSON"
+
 
 class AthenaDDLCompiler(DDLCompiler):
     @property
