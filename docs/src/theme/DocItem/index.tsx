import React from 'react';
import { HtmlClassNameProvider } from '@docusaurus/theme-common';
import { DocProvider } from '@docusaurus/plugin-content-docs/client';
import DocItemMetadata from '@theme/DocItem/Metadata';
import DocItemLayout from '@theme/DocItem/Layout';
import type { Props } from '@theme/DocItem';
import Navbar from '@theme/Navbar';

export default function DocItem(props: Props): JSX.Element {
    const docHtmlClassName = `docs-doc-id-${props.content.metadata.id}`;
    const MDXComponent = props.content;
    return (
        <DocProvider content={props.content}>
            <HtmlClassNameProvider className={docHtmlClassName}>
                <DocItemMetadata />
                <Navbar />
                <DocItemLayout>
                    <MDXComponent />
                </DocItemLayout>
            </HtmlClassNameProvider>
        </DocProvider>
    );
}
