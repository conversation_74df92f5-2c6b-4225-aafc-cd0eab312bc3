@media (min-width: 997px) {
  .sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    width: var(--doc-sidebar-width);
  }

  .logo {
    height: var(--ifm-navbar-height) !important;
    min-height: var(--ifm-navbar-height) !important;
  }

  .sidebarWithHideableNavbar {
    padding-top: 0;
  }

  .sidebarHidden {
    opacity: 0;
    visibility: hidden;
  }

  .sidebarLogo {
    display: flex !important;
    align-items: center;
    margin: 0 var(--ifm-navbar-padding-horizontal);
    min-height: var(--ifm-navbar-height);
    max-height: var(--ifm-navbar-height);
    color: inherit !important;
    text-decoration: none !important;
  }

  .sidebarLogo img {
    margin-right: 0.5rem;
    height: 2rem;
  }
}

.sidebarLogo {
  display: none;
}
