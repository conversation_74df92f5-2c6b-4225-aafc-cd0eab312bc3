import React from 'react';
import clsx from 'clsx';
import { useThemeConfig } from '@docusaurus/theme-common';
import Logo from '@theme/Logo';
import CollapseButton from '@theme/DocSidebar/Desktop/CollapseButton';
import Content from '@theme/DocSidebar/Desktop/Content';
import type { Props } from '@theme/DocSidebar/Desktop';

import styles from './styles.module.css';
import Link from '@docusaurus/Link';

function DocSidebarDesktop({ path, sidebar, onCollapse, isHidden }: Props) {
    const {
        navbar: { hideOnScroll },
        docs: {
            sidebar: { hideable },
        },
    } = useThemeConfig();

    return (
        <div
            className={clsx(
                styles.sidebar,
                hideOnScroll && styles.sidebarWithHideableNavbar,
                isHidden && styles.sidebarHidden,
                'h-fit'
            )}
        >
            <Link to="/" style={{ textDecoration: 'none' }}>
                <div
                    className={clsx(
                        'flex flex-row h-[3.75rem] items-center py-2 px-[1.5rem] space-x-3'
                    )}
                >
                    <img src="/img/logo.svg" alt="Logo" />
                    <h1 className="m-0 text-black">Mitzu</h1>
                </div>
            </Link>
            {/* {hideOnScroll && <Logo tabIndex={-1} className={styles.sidebarLogo} />} */}
            <Content path={path} sidebar={sidebar} />
            {hideable && <CollapseButton onClick={onCollapse} />}
        </div>
    );
}

export default React.memo(DocSidebarDesktop);
