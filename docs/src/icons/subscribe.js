import React from "react"

function Subscribe({ className }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="128"
      height="128"
      fill="none"
      viewBox="0 0 128 128"
      id="subscribe-email"
    >
      <g clipPath="url(#clip0_241_711)">
        <path
          stroke="currentColor"
          strokeWidth="7"
          d="M107 25.5L97.0269 35.4731C81.6936 50.8064 74.0269 58.4731 64.5 58.4731C54.9731 58.4731 47.3064 50.8064 31.9731 35.4731L22 25.5"
        ></path>
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeWidth="7"
          d="M64.5 103H59.5C40.236 103 30.604 103 24.1825 97.5909 23.1807 96.7471 22.2529 95.8193 21.4091 94.8175 16 88.396 16 78.764 16 59.5V59.5C16 40.236 16 30.604 21.4091 24.1825 22.2529 23.1807 23.1807 22.2529 24.1825 21.4091 30.604 16 40.236 16 59.5 16H69.5C88.764 16 98.396 16 104.818 21.4091 105.819 22.2529 106.747 23.1807 107.591 24.1825 113 30.604 113 40.236 113 59.5V59.5 81.25M101 96H83M92 105L92 87"
        ></path>
      </g>
      <defs>
        <clipPath id="clip0_241_711">
          <rect width="128" height="128" fill="#fff"></rect>
        </clipPath>
      </defs>
    </svg>
  )
}

export default Subscribe
