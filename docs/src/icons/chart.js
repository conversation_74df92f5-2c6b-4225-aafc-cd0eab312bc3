import React from "react"

function Chart({ props }) {
  return (
    <svg
      {...props}
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.61523 0.691406C1.61523 0.558798 1.56256 0.431621 1.46879 0.337853C1.37502 0.244085 1.24784 0.191406 1.11523 0.191406C0.982626 0.191406 0.855449 0.244085 0.761681 0.337853C0.667913 0.431621 0.615234 0.558798 0.615234 0.691406V10.3581C0.615234 11.0021 1.1379 11.5247 1.7819 11.5247H11.4486C11.5812 11.5247 11.7084 11.4721 11.8021 11.3783C11.8959 11.2845 11.9486 11.1573 11.9486 11.0247C11.9486 10.8921 11.8959 10.765 11.8021 10.6712C11.7084 10.5774 11.5812 10.5247 11.4486 10.5247H1.7819C1.7377 10.5247 1.69531 10.5072 1.66405 10.4759C1.63279 10.4447 1.61523 10.4023 1.61523 10.3581V0.691406ZM7.61523 2.35807C7.61523 2.22546 7.66791 2.09829 7.76168 2.00452C7.85545 1.91075 7.98263 1.85807 8.11523 1.85807H11.4499C11.5825 1.85807 11.7097 1.91075 11.8035 2.00452C11.8972 2.09829 11.9499 2.22546 11.9499 2.35807V5.69474C11.9499 5.82735 11.8972 5.95452 11.8035 6.04829C11.7097 6.14206 11.5825 6.19474 11.4499 6.19474C11.3173 6.19474 11.1901 6.14206 11.0963 6.04829C11.0026 5.95452 10.9499 5.82735 10.9499 5.69474V3.56407L7.46857 7.04474C7.37482 7.13837 7.24774 7.19097 7.11523 7.19097C6.98273 7.19097 6.85565 7.13837 6.7619 7.04474L5.44857 5.73141L3.3019 7.87807C3.20712 7.96639 3.08175 8.01447 2.95222 8.01219C2.82269 8.0099 2.6991 7.95743 2.60749 7.86582C2.51588 7.77421 2.4634 7.65062 2.46112 7.52109C2.45883 7.39155 2.50691 7.26619 2.59523 7.17141L5.09523 4.67141C5.18898 4.57777 5.31607 4.52518 5.44857 4.52518C5.58107 4.52518 5.70815 4.57777 5.8019 4.67141L7.11523 5.98474L10.2419 2.85807H8.11523C7.98263 2.85807 7.85545 2.80539 7.76168 2.71163C7.66791 2.61786 7.61523 2.49068 7.61523 2.35807Z"
        fill="currentColor"
      ></path>
    </svg>
  )
}

export default Chart
