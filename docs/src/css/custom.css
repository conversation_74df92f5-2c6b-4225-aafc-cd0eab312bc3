@tailwind base;
@tailwind components;
@tailwind utilities;

/* You can override the default Infima variables here. */
:root {
    --ifm-color-primary: #f77180;
    --ifm-color-primary-dark: #f54f62;
    --ifm-color-primary-darker: #f43e52;
    --ifm-color-primary-darkest: #ef0d27;
    --ifm-color-primary-light: #f9939e;
    --ifm-color-primary-lighter: #faa4ae;
    --ifm-color-primary-lightest: #fdd7db;

    --ifm-code-font-size: 95%;
    --ifm-hover-overlay: #c5caf53d;
    --ifm-arrow: url('data:image/svg+xml;utf8,<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.95142 14L12.0486 10.9096L15.1457 14L16.0972 13.0486L12.0486 9L8 13.0486L8.95142 14Z" fill="black"/></svg>');
    --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
    --ifm-color-primary: #f77180;
    --ifm-color-primary-dark: #f54f62;
    --ifm-color-primary-darker: #f43e52;
    --ifm-color-primary-darkest: #ef0d27;
    --ifm-color-primary-light: #f9939e;
    --ifm-color-primary-lighter: #faa4ae;
    --ifm-color-primary-lightest: #fdd7db;
    --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

@font-face {
    font-family: 'Roboto';
    src: url('../../static/fonts/Roboto-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Roboto';
    src: url('../../static/fonts/Roboto-Medium.ttf') format('truetype');
    font-weight: 450 500;
}

/* sidebar */

.menu__link--sublist-caret:before {
    content: '';
    margin-right: 8px;
    min-width: 1.25rem;
    background: var(--ifm-arrow) 50% / 2rem 2rem;
    filter: var(--ifm-menu-link-sublist-icon-filter);
    height: 1.25rem;
    transform: rotate(180deg);
    width: 1.25rem;
    transition: transform var(--ifm-transition-fast) linear;
}
.menu__list-item--collapsed .menu__link--sublist:before {
    transform: rotateZ(90deg);
}
aside {
    @apply !border-r-0;
}

aside > div {
    @apply !mt-[60px];
}
/* navbar */
.navbar__link--active {
    @apply relative after:content-[''] after:h-1 after:bg-[var(--ifm-color-primary)] after:absolute after:bottom-0 after:left-0 after:w-full;
}
a.navbar__item.navbar__link {
    @apply h-full items-center hidden lg:flex flex-row;
}

/* containers */

/* .alert {
  --alert-background-color: #f8d7da;
  --alert-border-color: #c6f5ce;
  background-color: var(--alert-background-color);
  border: var(--ifm-alert-border-width) solid var(--alert-border-color);
  border-left-width: var(--ifm-alert-border-left-width);
} */

.alert--secondary {
    --alert-background-color: #ebebeb;
    --alert-border-color: #9c9c9c;
    background-color: var(--alert-background-color);
    border: var(--ifm-alert-border-width) solid var(--alert-border-color);
    border-left-width: var(--ifm-alert-border-left-width);
}
[data-theme='dark'] .alert--secondary {
    --alert-background-color: #2b2a29;
    --alert-border-color: #797877;
}
.alert--info {
    --alert-background-color: #f0f6ff;
    --alert-border-color: #3880ff;
    background-color: var(--alert-background-color);
    border: var(--ifm-alert-border-width) solid var(--alert-border-color);
    border-left-width: var(--ifm-alert-border-left-width);
}
[data-theme='dark'] .alert--info {
    --alert-background-color: #000d24;
    --alert-border-color: #3880ff;
}

/* .alert--warning {
  --alert-background-color: #f2fc666e;
  --alert-border-color: #e4f658;
}
.alert--danger {
  --alert-background-color: #f7465470;
  --alert-border-color: #fc5869;
}
.alert--success {
  --alert-background-color: #97f79775;
  --alert-border-color: #2df65c;
} */

/* images in the docs markdown, NOTE: got rid of it after using the zoomed imgz component inside the docs */
/* .theme-doc-markdown.markdown img {
  width: auto !important;
  height: auto !important;
  margin-bottom: -4px;
  object-fit: contain !important;
  margin-left: auto !important;
  margin-right: auto !important;
} */
/* tables */
.theme-doc-markdown.markdown table {
    width: fit-content;
    margin: 0 auto;
}

/* navbar links */
.navbar__items.navbar__items--right > .navbar__item.navbar__link {
    /* hide text and show * sign */
    /* @apply content-none before:w-auto   before:content-['*'] before:text-blue-400 after:ml-2; */
}

/* SEARCH BUTTON */
.DocSearch.DocSearch-Button {
    @apply rounded-sm px-2;
}

.DocSearch-Button {
    border: 1px solid var(--ifm-color-primary) !important;
    @apply xl:min-w-[15rem]  !bg-inherit;
}

.img_node_modules-\@docusaurus-theme-classic-lib-theme-MDXComponents-Img-styles-module {
    border-radius: 0.5rem;
    -webkit-box-shadow: 5px 5px 9px 0px rgba(112, 112, 112, 0.5);
    -moz-box-shadow: 5px 5px 9px 0px rgba(112, 112, 112, 0.5);
    box-shadow: 5px 5px 9px 0px rgba(112, 112, 112, 0.5);
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    max-height: 800px;
    width: auto;
    height: auto;
}

p:has(.img_node_modules-\@docusaurus-theme-classic-lib-theme-MDXComponents-Img-styles-module) {
    text-align: center;
}

#connection-table {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

div.dwh-logo {
    text-align: center;
    width: 260px;
    height: 130px;
    margin: 20px;
}

div.dwh-logo img {
    max-width: 260px;
    height: 100px;
}


.video-iframe {
    width: 100%;
    max-width: 800px;
    height: 450px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin: 0 auto; 
    display: block;
  }
  