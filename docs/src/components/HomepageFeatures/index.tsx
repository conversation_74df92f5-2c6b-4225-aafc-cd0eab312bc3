import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';
import all_insigft_from_dwh_png from '../../../static/img/all_insights_from_dwh.png';
import unlimited_events from '../../../static/img/unlimited_events.png';
import secure_user_profiles from '../../../static/img/secure_user_profiles.png';

type FeatureItem = {
    title: string;
    src: string;
    description: JSX.Element;
};

const FeatureList: FeatureItem[] = [
    {
        title: 'All insights directly from your data warehouse',
        src: all_insigft_from_dwh_png.src.src,
        description: (
            <>
                Warehouse native product analytics enables you to understand the entire business
                with a simple and clear interface. First-party usage data, 3rd-party data from
                services, or custom data models accessible in one place without SQL knowledge.
            </>
        ),
    },
    {
        title: 'Unlimited events and users',
        src: unlimited_events.src.src,
        description: (
            <>
                Traditional 3rd party, product analytics costs, can quickly escalate due to charging
                based on tracked users or events. Warehouse native product analytics enables 90%
                savings. Purchase seats and analyize the unlimited events and monthly tracked users
                in your data warehouse.{' '}
                <a href="https://www.mitzu.io/pricing" target="_blank">
                    See our seat based pricing
                </a>
            </>
        ),
    },
    {
        title: 'Secure user profiles',
        src: secure_user_profiles.src.src,
        description: (
            <>
                Don’t want to expose your customer information to 3rd party apps, or is it hard to
                keep them in sync? Don’t worry, Mitzu got you covered! Single-click integration of
                user profile tables to Mitzu.
            </>
        ),
    },
];

function Feature({ title, src, description }: FeatureItem) {
    console.log(src);
    return (
        <div className={clsx('col col--4')}>
            <div className="text--center">
                <img className={styles.featureSvg} src={src} role="img" />
            </div>
            <div className="text--center padding-horiz--md">
                <Heading as="h3">{title}</Heading>
                <p>{description}</p>
            </div>
        </div>
    );
}

export default function HomepageFeatures(): JSX.Element {
    return (
        <section className={styles.features}>
            <div className="container">
                <div className="row">
                    {FeatureList.map((props, idx) => (
                        <Feature key={idx} {...props} />
                    ))}
                </div>
            </div>
        </section>
    );
}
