import Link from '@docusaurus/Link';
import React from 'react';

type Props = {
    data: {
        title: string;
        description: string;
        image: string;
        link: string;
    };
};

const Card = ({ data }: Props) => {
    return (
        <Link
            style={{
                textDecoration: 'none',
                color: 'inherit',
            }}
            to={data.link}
        >
            <div
                className="w-full p-4 lg:p-8 rounded-xl border-solid border-gray-200 dark:border-gray-500 border-[3px] hover:shadow-md cursor-pointer shadow-sm
      transition duration-300 ease-in-out transform 
      "
            >
                <img src={data.image} className="max-w-16 max-h-16" alt={data.title} />
                <h3 className="mt-2 mb-1 lg:mb-2 lg:text-xl text-base">{data.title}</h3>
                <p className="m-0 lg:text-base text-sm ">{data.description}</p>
            </div>
        </Link>
    );
};

export default Card;
