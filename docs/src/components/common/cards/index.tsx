import React from 'react';
import Card from './card';

type Props = {
    data: {
        title: string;
        description: string;
        image: string;
        link: string;
    }[];
};

const Cards = ({ data }: Props) => {
    return (
        <div className="w-full gap-3 lg:gap-6 grid grid-cols-1 py-6  xl:grid-cols-2">
            {data.map((item, index) => (
                <Card key={index} data={item} />
            ))}
        </div>
    );
};

export default Cards;
