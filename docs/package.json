{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "^3.5.2", "@docusaurus/plugin-google-gtag": "^3.5.2", "@docusaurus/plugin-ideal-image": "^3.5.2", "@docusaurus/preset-classic": "^3.5.2", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "docusaurus-lunr-search": "^3.4.0", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-medium-image-zoom": "^5.2.8"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.5.2", "@docusaurus/tsconfig": "^3.5.2", "@docusaurus/types": "^3.5.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "tailwindcss": "^3.4.9", "typescript": "~5.2.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}