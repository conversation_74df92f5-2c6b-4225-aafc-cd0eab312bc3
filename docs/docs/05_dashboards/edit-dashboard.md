---
title: Edit Dashboard
description: Edit Dashboard
sidebar_label: Edit Dashboard
---

import thumbnail from '/img/application/edit-dashboard/image.png';

# Edit Dashboard

The **Edit Dashboard** page allows you to group saved insights into a single view. This page offers a great option to organize your insights into groups.

<Imgz  src='/img/edit-dashboard/edit_dashboard.png'/>

## Use Cases

Here are a couple of example use cases for grouping your insights:

-   **Business Problem-Based** Grouping: Examples include a Product Activation dashboard, User Retention dashboard, etc.
-   **Function-Based** Grouping: Examples include an Engineering dashboard, Sales dashboard, etc.

## All Features

### Renaming the Dashboard

You can rename your dashboard on the top left side of the page.

<Imgz  src='/img/edit-dashboard/rename_dashboard.png'/>

### Adding a Saved Insight

There are multiple ways to add insights to a dashboard:

-   From the Explore page while saving an insight.

<Imgz src='/img/edit-dashboard/add_insight_to_dashboard.png'/>

-   With the **Add Insight** or **Add Existing Insight** Button from **Edit Dashboards** page.

    <Imgz src='/img/edit-dashboard/add_insight_to_dashboard_2.png'/>

If you choose to add an existing insight, you can select it from the list of saved insights. Clicking on the insight will take you to the Explore page, where you can add it to your dashboard.

If you choose to add a new insight, you will be taken to the Explore page to create a new insight and add it to your dashboard.

### Refreshing the Dashboard

To refresh the entire dashboard, use the Refresh button. This will trigger a parallel refresh of all saved insights in the dashboard, executing up to 5 SQL queries in your data warehouse simultaneously. You can cancel the refresh at any time.

### Global annotations

The **+ Global annotations** option allows you to include any global annotations you've added. When this option is enabled, viewers of the dashboard will be able to see the additional context or notes provided through global annotations. This settings is persistent.

#### Auto Refresh

You can enable Auto Refresh to refresh the dashboard every time the dashboard page is opened. You can set the auto-refresh frequency. The dashboard will only refresh if a specified period has passed since the last refresh.
<Imgz src='/img/edit-dashboard/refresh_dashboard_1.png'/>
<Imgz src='/img/edit-dashboard/refresh_dashboard_2.png'/>

A button will appear in the top right corner of the dashboard page showing the auto-refresh frequency. You can change the frequency by clicking on this button.

### Duplicating the Dashboard

You can create a copy of the dashboard by clicking the **Duplicate** button on the top right corner of the menu bar on the dashboard page.

#### Duplicating the Dashboard Only

If you choose to duplicate the dashboard only, a new dashboard will be created containing the same insights as the original. Changes made to insights in the new dashboard will also affect the insights in the original dashboard, and vice versa.

#### Duplicating the Dashboard with Insights

If you choose to duplicate the dashboard along with the insights, copies of the insights will be created in the new dashboard. Changes made to the insights in the new dashboard will not affect the original dashboard.

### Creating a Snapshot

You can take a snapshot of the dashboard by clicking the **Snapshot** button on the top right corner of the menu bar on the dashboard page.

<Imgz src='/img/edit-dashboard/snapshot_dashboard.png'/>

The difference between creating a snapshot and duplicating the dashboard is that a snapshot will lock the date for all the insights. This ensures that when you return to the snapshot dashboard, you will see the exact same data. Snapshots can be useful for static reports, such as those in Notion.

<Imgz src='/img/edit-dashboard/snapshot_dashboard_1.png'/>

### Managing the layout

You can move the insights inside a dashboard by clicking and dragging the "grab area" on top of the insights.

<div align="center" data-full-width="false">

<Imgz src='/img/application/edit-dashboard/image-4.png'/>

</div>

You can grab an insight and move it around, which will reorder other insights on the dashboard as well.

Resizing the charts is also easy. Click the side menu on the top right part of the chart, and then select **Toggle Size**. This will toggle the chart between full width and default width. Full-width charts will display as default width on mobile platforms.
<Imgz src='/img/application/edit-dashboard/image-5.png'/>

### Dashboard time filter

The insights shown in the dashboard have their default time window set as they were saved. Currently, Mitzu offers an override option for this. You can choose a different time horizon for all the insights in the dashboard. This is done by selecting an option from the top time window chooser.

You need to click the Refresh button to update the charts or set Auto Refresh to automatically update them after a certain time interval when the dashboard is opened.

<Imgz src='/img/edit-dashboard/last_refresh.png'/>

### Removing insights

You can find the **Remove** button in the same menu where the **Toggle Size** button is located (top right part of the charts). This will remove the saved insight from the dashboard but will not delete the saved insight itself.
<Imgz src='/img/edit-dashboard/delete_insight.png'/>

## Video tutorial

<iframe width="560" height="315" src="https://www.youtube.com/embed/P5BXqzhgHz8?si=ckK3rjI6B1w64Vtq" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen class="video-iframe"></iframe>