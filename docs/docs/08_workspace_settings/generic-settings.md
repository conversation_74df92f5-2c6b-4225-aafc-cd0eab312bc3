---
title: Generic settings
description: Generic settings
sidebar_label: Generic settings
slug: /generic-settings
---

# Generic settings

The generic settings tab is where you manage the most common configs for your workspace.

<Imgz src='/img/application/general-settings/image.png'/>

### Options

-   **Workspace name**: The name of the workspace as seen by every member.
-   **Workspace description**: The description for your workspace.

Changes are saved automatically.

### Actions

-   **Set as default**: New users will see the default workspace after their first login.

-   **Clear caches**: To improve performance, <PERSON><PERSON><PERSON> keeps some metadata (e.g., a list of data warehouse schemas, tables, fields, event and property catalog) in a cache. After clearing the cache, these data will be reloaded on demand.

-   **Delete workspace**: After a second confirmation, this action will permanently the workspace and all assets associated with it:
    -   Event and property catalog
    -   Saved insights
    -   Saved dashboards
    -   Cohorts
    -   All workspace settings
