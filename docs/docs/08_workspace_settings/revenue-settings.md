---
title: Revenue settings
description: Revenue settings
sidebar_label: Revenue settings
slug: /revenue-settings
---

# Revenue Settings

In the Revenue settings tab, you will find all the configurations for the revenue calculation.

<Imgz src='/img/revenue-settings/image.png'/>

## Options

Before configuring the revenue calculation, make sure that the event data table containing your payment events is configured in the [Event tables](/event-tables) and that this event includes the following mandatory fields:
  * `Amount paid` (it's a negative number for paybacks)
  * `Period start` and `Period end` showing the paid period

First, toggle the `Enable revenue calculation` checkbox, then configure the following fields:

* **Invoice paid event**: Select the event in your data warehouse representing the payment events.

* **Amount paid field**: The amount of property for the selected event.

* **Amount precision**: The revenue calculation will multiply the amounts with the selected value. For example, choose `100` if you store the paid values in cents.

* **Period start field**: Start date property of the selected event.

* **Period end field**: End date property of the selected event.

* **Revenue currency**: Enter your currency. This will be used only on charts; <PERSON><PERSON><PERSON> won't convert any currencies.
