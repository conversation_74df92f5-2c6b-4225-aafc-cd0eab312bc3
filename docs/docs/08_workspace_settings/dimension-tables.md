---
title: Dimension tables
description: Dimension tables
sidebar_label: Dimension tables
slug: /dimension-tables
---

# Dimensions Tables

## Overview

Dimensions are a common concept in data analytics. By dimensions, we often mean a set of properties the users, product, or session may have. Examples of user properties: 

* Country of origin
* Email addresses
* Primary email address
* Phone number
* Is paying
* First event time
* Last event time
* Sign-up time
* etc.

In addition to the user profiles, you can configure a dimension table for any entities you've added on the [Entities](/entities) page.

Most companies store these properties in a data warehouse in separate tables, where each row holds information about a single entity. Later, during analysis, they join these tables to the event tables for further filtering and segmentation.

<PERSON><PERSON><PERSON> supports joining one or more dimension tables to event tables.

## Add new dimension table(s)

Click the `Add dimension table` button.

<Imgz src='/img/dimension-tables/image.png'/>

The modal will load the available schemas of the configured data warehouse connection. Once the loading finishes, select the schema containing the event table(s) you want to add. After selecting the schema, <PERSON><PERSON><PERSON> will load all table names in that schema. Once the tables are loaded, you can choose one or multiple tables to add or choose the ` - ADD ALL <N> TABLES - ` to select all tables (here, `<N>` equals the number of tables in the specified schema).


Once the tables are selected, you can choose the entity and enter the `Primary key column`.

For the `Primary key field,` you need to set the field that can be used as a joining key when joining the event tables. For example, if you've selected the `User` entity, you should set the column containing the user ID field. Type the column name for this input and select it from the dropdown.

If unsure what columns are available, click the `Fetch columns` button. It will load the available columns of all selected tables and populate the dropdown fields with the most common column names.

:::warning
Fetching columns can take a long time if too many tables are selected.
:::

Click the `Add tables` button to close the modal and add the new tables configured with the selected entity and primary key.

## Configure dimension tables

<Imgz src='/img/dimension-tables/image-2.png'/>

You can update the `Entity` and the `Primary key column` in the table. Optionally, you can set the `Ignored fields` to list the table fields that you don't want to be indexed.

Changes are saved automatically.

:::warning
The newly added and reconfigured event tables are not indexed automatically. You need to select them and click the `Index selected` button to index them.
:::


## Removing user profile tables

If you decide to remove any dimension table, select it with the checkbox in the table and click the `Remove tables` button. It will remove the table from the list of tables and the user property catalog. All saved Insights using one of the removed tables will break.

