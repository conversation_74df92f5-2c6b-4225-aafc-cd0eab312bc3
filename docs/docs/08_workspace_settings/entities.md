---
title: Entities
description: Entities
sidebar_label: Entities
slug: /entities
---

# Entities

In your data model an event may be enriched with additional information, such as additional properties about the user, about the user's session, its team, its organization, etc. These additional set of properties are called entities. You can list the entities of your data model in the `Entities` tab.

<Imgz src='/img/entities/image.png'/>

## Update entity

The table contains all of the configured entities. You can update an entity name or icon by entering a different name or selecting a different icon. Changes are saved automatically.

## Add new entity

Click the `Add entity` button to create a new entity. The new entity will be added to the table where you can set its display name and select its icon.

## Remove an entity

To remove one or more entities, toggle the checkboxes in the table of those entities which you want to remove and click the `Remove entities` button.

:::warning
The User entity cannot be removed.
:::

:::warning
Entities referenced by either and event data table or a dimension data table cannot be removed.
:::

## Video tutorial

Video how to setup your entities and dimension tables in 7 mins.

<iframe width="560" height="315" src="https://www.youtube.com/embed/SShz1nZ_Fhg?si=7cJOQIl_OXGw1zL5" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen class="video-iframe"></iframe>
