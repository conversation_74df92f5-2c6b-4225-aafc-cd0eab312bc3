---
title: Event tables
description: Event tables
sidebar_label: Event tables
slug: /event-tables
---

# Event Tables

Event tables are tables in the data warehouse that contain user events. You must select specific columns for different purposes for each event table.

You can select a column for these purposes:

- **Identifier columns**
  - **User ID Column** (mandatory): This column of the event table contains the User ID.
  - **Entity ID columns**: For all entities you've added on the [Entities](/entities) page, you can select the column containing the entity ID.

- **Event time columns**
  - **Event time Column** (mandatory): This column of the event table contains the event time.
  - **Date Partition Column** (optional): Queries can take a long time on large tables, and to speed up the queries, some data warehouses support partitioning. You can configure the partitioning column if you've partitioned the table by the event time.

- **Other settings**:
  - **Event Name Column** (optional): If your event table contains multiple events, you can set the column that distinguishes them.
  - **Ignore Columns** (optional): You can configure columns of the event table that you want <PERSON><PERSON><PERSON> to ignore. <PERSON><PERSON><PERSON> won't index or list these fields on the `Insights` page.

## List of the event tables

You can browse the already configured and recently added event tables in a table showing all configured columns for each event table.

:::warning
The content of this table list changes while you add, remove, or configure event data tables. It shows only the current state, and you must click on the `Save` button to persist your changes and index the updated tables.
:::

<Imgz src='/img/event-tables/image.png'/>

## Adding new event table(s)

Click the `Add event tables` button to open a new modal in which you can select the event tables to add.

<Imgz src='/img/event-tables/image-1.png'/>

First, select the proper tab. If the event tables contain only a single event in each table, choose `Single event table`. If the event tables contain more events distinguished by a column containing the event name, choose `Multi event table`.

The modal will load the available schemas of the configured data warehouse connection. Once the loading finishes, select the schema containing the event table(s) you want to add. After selecting the schema, Mitzu will load all table names in that schema. Once the tables are loaded, you can choose one or multiple tables to add or choose the ` - ADD ALL <N> TABLES - ` to select all tables (here, `<N>` equals the number of tables in the specified schema).


Once the tables are selected, you can configure the fields for

 - User ID column (mandatory)
 - Event time column (mandatory)
 - Date partition column (optional)
 - Event name column (visible and mandatory only on the `Multi event table` tab)

For each column, type the column name and select it from the dropdown. If you are unsure what columns are available, click the `Fetch columns` button. It will load the available columns of all selected tables and populate the dropdown fields with the most common column names.

:::warning
Fetching columns can take a long time if too many tables are selected.
:::

Click the `Add tables` button to close the modal and add the new tables configured with the selected fields.

:::warning
If you miss some tables from the list and recently updated the connection settings or the Mitzu permissions in your data warehouse, click on the refresh button next to the tables dropdown to reload the list of tables in that schema.
:::

## Configure event tables

Toggling the checkboxes in the list allows you to select one or more tables to configure. Then, click the `Configure tables` button to open a new modal.

<Imgz src='/img/event-tables/image-2.png'/>

For each column, type the column name and select it from the dropdown. If you are unsure what columns are available, click the `Fetch columns` button. It will load the available columns of all selected tables and populate the dropdown fields with the most common column names.

:::warning
Fetching columns can take a long time if too many tables are selected.
:::

Click on the `Configure` button to update the list of the event tables.

:::warning
The newly added and reconfigured event tables are not indexed automatically. You need to select them and click the `Index selected` button to index them.
:::


## Enable / Disable

You can disable a table if you don't want to use it as an event table. To do it, toggle the checkbox of an event table and click on the `Enable / Disable` button.

:::warning
When a table gets disabled, all events and catalog records belonging to that table will be removed.  
:::


## Re-indexing

If the content of your table changes (e.g., a new event in a multi-event table, new event property columns, etc.), you need to re-index them. First, select one or more event tables in the table by toggling the checkboxes and clicking on the `Re-index selected` button to start the indexing. You can track the progress and see the result below the table. You can read more about this process on the [indexing](/indexing) page.

## Remove an event table

You can remove an event table if you don't need it anymore.

:::warning
All saved insights using an event from the removed event table will break.
:::
