---
title: Insight settings
description: Insight settings
sidebar_label: Insight settings
slug: /insight-settings
---

# Insight Settings

You will find all the configurations regarding the Insight page.

<Imgz src='/img/application/insight-settings/image.png'/>

## Options

-   **Auto Refresh**: Automatic refreshing will trigger an SQL query execution on every change on the Insights page. This may cause throttling in the data warehouse, so enable this setting with caution.

-   **Default End Date Config**: You can set the default end date for every insight created in the workspace. You can choose from the following options:

    -   **Start of the current day**: Use this option if your daily pipeline creates the events daily.
    -   **End of the current day**: Use this option if you update your events close to real-time or in frequent batches.
    -   **Now**: Use this option if you update your events close to real-time and you want to filter out future events that usually are stamped with incorrect timestamps.
    -   **Custom Date**: Use this option if your dataset is not updated anymore. The `Custom Default End Date` should be set for this option.

-   **Custom End Date**: This date selector is only available if you select the `custom date` option for the Default End Date Config.

-   **Event Resolution Scale**: Event sampling reduces the number of events considered in funnel and retention insights. It can significantly improve the calculation time of the funnel and retention insights; however, the result will be less precise. Resolution will always keep every user or other entity in the calculations.

-   **Event Auto Sampling Scale**: Sampling reduces the number of users considered in segmentation, funnel, and retention insights. It can significantly improve the calculation time of the segmentation, funnel, and retention insights; however, the result will be less precise. Sampling may remove users or other entities from calculations.

-   **Conversion Attribution**: Will set the default conversion attribution mode for the funnel insights. "First" event conversion attribution will only match the first event on every funnel step for each user. "Every" event attribution will match every user event with every other user event in a funnel.

-   **Retention Attribution**: Will set the default retention attribution mode for the retention insights. "First" event retention attribution will only match the first event on the retaining step for each user. "Every" event attribution will match user events in the retaining step.

-   **Gather Cluster Info**: You can enable monitoring of the data warehouse's cluster status. This information helps you troubleshoot slow queries and track the progress of your insights calculation.

-   **Consider First Event As Retained**: Considering first event as retained will change retention calculation. If the user or group has a single event it will be still considered as retained on the first time period. E.g. a user with a single page view will be considered retained on their first day.

-   **Cut off incomplete data**: Enabling this setting will remove users (and other entities) from retention calculations that didn't have the chance to complete their retention events. By enabling this setting, this feature will be turned on by default. You can still turn it off for each individual retention insight.

-   **Case insensitive filtering**: Enables case-insensitive filtering for events. When enabled, filters will ignore differences in letter cases.

-   **Apply First Event Period Filter**: Select this option to apply the first event period filter to the funnel and retention insights by default.

-   **Fill missing values with zero**: Enable this option to fill missing values with zero. This option only applies to segmentation charts.

Changes are saved automatically.