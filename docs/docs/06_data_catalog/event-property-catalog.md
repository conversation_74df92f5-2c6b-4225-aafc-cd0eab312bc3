---
title: Event Property Catalog
description: Event property catalog
sidebar_label: Event Property Catalog
slug: /event-property-catalog
---

# Event Property Catalog

The event property catalog helps you track and document all events' properties in your data warehouse.

Event properties are added and removed right after you add or remove a [event table](/event-tables). You cannot create or remove them manually.

<Imgz src='/img/event-property-catalog/image.png'/>

## Use cases

Maintaining the display name and the description fields of the event property records can help you to

-   map the technical abbreviations and raw property names to human-readable labels
-   explain the meaning of an event property with a description

## All features

### Listing event properties in the catalog

The Events Properties tab on the catalog page will list the stored event properties in the catalog of the selected workspace.

All event properties are listed only once, even if multiple events have properties with the same name.

The table contains the following columns:

-   **Property name:** This is the column name of the event data table. The column name is in the `<column name>.<field key>` format if the event property is a nested data structure (eg. struct, json).
-   **Display name:** You will see the display name referencing the event property on the Insights page, charts, dashboards, and tooltips.
-   **Description:** The description can help you find a suitable event when browsing the Insights page.
-   **Visibility:** You can control which event properties should be visible on the Insights page. Select the value `Hidden` for an event; you won't see it listed in any dropdowns. Insights already having this field won't be broken but cannot be recreated.
-   **Events:** You can see which event has that property. Click on the event link to navigate the [Event catalog](event-catalog) page with a predefined filter showing the clicked event. Only the first five events are listed.

You can filter the table using the free text search input filter or select an event from the dropdown. If you use the free text search, the list will show the most recent results as you type your search expression. Click the `Reset filters` button next to the search bar to clear all filters.

You can see only 20 event properties on the page. To see the remaining events, narrow the results with a search expression or browse the other pages in the pagination bar. The results are in alphabetical order by the property name column.

### Updating display name, description

To change the display name or the description of an event property catalog record, click the input field and enter the desired value. The catalog is updated once you stop typing.

### Refreshing filter values and change property data types

You can also refresh the filter values in the event properties tab.
Simply, choose an event and click the "Manage values" button for any of the properties.

<Imgz src='/img/event-property-catalog/image-1.png'/>

Here, you can refresh the possible filter values. Also, you can change the data type of any property.

:::warning

Changing the data type may cause that the generated SQL queries will not work anymore.
Proceed only if you are sure that the data type change is necessary.

:::
