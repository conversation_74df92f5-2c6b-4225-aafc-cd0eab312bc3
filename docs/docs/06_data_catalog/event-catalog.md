---
title: Event Catalog
description: Event catalog
sidebar_label: Event Catalog
slug: /event-catalog
---

# Event Catalog

The Event catalog helps you track and document the events stored in your data warehouse.

Events are added and removed right after you add or remove a [event table](/event-tables). You cannot create or remove them manually.

<Imgz src='/img/event-catalog/image.png'/>

## Use cases

Maintaining the display name and the description fields of the event catalog records can help you to

-   map the technical abbreviations and raw event names to human-readable labels
-   explain the circumstances of an event with a description

## All features

### Listing events in the catalog

The Events tab on the catalog page will list the stored events in the catalog of the selected workspace.

The table contains the following columns:

-   **Event source:** It is the source of that event in `<source table name>.<event name>` format. The event name is the event table name when the event comes from a single event table.
-   **Display name:** You will see the display name referencing the event on the Insights page, charts, dashboards, and tooltips.
-   **Description:** The description can help you find the right event when browsing the Insights page.
-   **Last discovered:** The elapsed time since the event last indexed.
-   **Visibility:** You can control which events should be visible on the Insights page. Select the value `Hidden` for an event; you won't see it listed in any dropdowns. Insights already having this event won't be broken but cannot be recreated.
-   **Manage properties:** Links to the [Event property catalog](event-property-catalog) with a predefined filter for this event.

You can filter the table using the free text search input filter or select an event from the dropdown. If you use the free text search, the list will show the most recent results as you type your search expression. Click the `Reset filters` button next to the search bar to clear all filters.

You can see only 20 events on the page. To see the remaining events, narrow the results with a search expression or browse the other pages in the pagination bar. The results are in alphabetical order by the event source.

<Imgz src='/img/event-catalog/image-1.png'/>

### Updating display name, description

To change the display name or the description of an event catalog record, click the input field and enter the desired value. The catalog is updated once you stop typing.

### Re-indexing all events

The event tables are indexed once you have configured them. If you added or removed events, you may not see the new events or still see the removed ones. To re-index all events, click the `Re-index all events` button to start the [indexing process](/indexing).
