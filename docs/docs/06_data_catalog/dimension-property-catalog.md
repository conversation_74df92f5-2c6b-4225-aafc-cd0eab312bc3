---
title: Dimension Property Catalog
description: Dimension property catalog
sidebar_label: Dimension Property Catalog
slug: /dimension-property-catalog
---

# Dimension Property Catalog

The dimension property catalog helps you track and document the properties from all configured [dimension tables](/dimension-tables).

Dimension properties are added and removed right after you add or remove a [dimension table](/dimension-tables). You cannot create or remove them manually.

<Imgz src='/img/dimension-property-catalog/image.png'/>

## Use cases

Maintaining the display name and the description fields of the user property records can help you to

-   map the technical abbreviations and raw property names to human-readable labels
-   explain the meaning of a user property with a description

## All features

### Listing dimension properties in the catalog

The Dimension Properties tab on the catalog page will list the stored dimension properties in the catalog of the selected workspace.

The table contains the following columns:

-   **Property name:** This is the column name of the dimension property field in `<source table name>.<column name>` format. The column names of nested data structures (e.g., struct, JSON) are in the `<source table name>.<column name>.<field key>` format.
-   **Property type:** This column shows if the property belongs to Users or Groups. You can change the property type for all properties from the same table on the [dimension tables page](/dimension-tables).
-   **Display name:** Dimension property will be referenced with this value on the Insights page, charts, dashboards, and tooltips.
-   **Description:** The property description can help you find the right property when browsing the Insights page.
-   **Last discovered:** The elapsed time since the dimension property was last time found while checking the dimension table for new properties.
-   **Visibility:** You can control which properties should be visible on the Insights page. Select the value `Hidden` for a property; you won't see it listed in any dropdowns. Insights already having this field won't be broken but cannot be recreated.

You can filter the table by using the free text search input filter. The list will show the most recent results as you type your search expression. Click the `Reset filters` button next to the search bar to clear all filters.

You can see only 20 dimension properties on the page. To see the remaining properties, narrow the results with a search expression or browse the other pages in the pagination bar. The results are in alphabetical order by the property name.

### Updating display name, description

To change the display name or the description of a dimension property catalog record, click the input field and enter the desired value. The catalog is updated once you stop typing.

### Re-indexing dimension properties

The dimension tables are indexed once you have configured them. If you added or removed properties, you may not see the new properties or still see the removed ones. To re-index the dimension properties, click the `Re-index all properties` button to start the [indexing process](/indexing).
