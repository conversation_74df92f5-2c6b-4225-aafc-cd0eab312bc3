---
sidebar_position: 4
slug: /retention
description: Learn the basics of retention
sidebar_label: Retention
---

# Retention

import data from "./read_the_basics.json"

<Cards data={data} />

## Use Cases

Maximizing retention of your users is a critical part of your business's success.
Mitzu's retention feature allows you to analyze how users or groups retain your product or marketing site.

## Quick Start

In this example, we will showcase how users retain in our application.
We assume users are "retained" if they have paid their monthly renewal fee.

The analysis aims to visualize our users' overall monthly retention rate for the last year.
Besides that, we want to see how the retention rate changes over time with monthly granularity. (Cohort retention)

### Step 1. Switch to retention and select events

First, switch to the retention insight tab and select the events you want to analyze.

As the `Initial event`, I will select `Subscription started`, as we are interested only in users that
started a subscription.

Remember that you can set a segment filter on this event. For example, `first event` reduces the scope of analysis for the users' first subscription.

We will now apply a property filter to the `Plan interval`. We will select only the users who performed `Subscription started` events with a monthly plan interval.

As the second event, we can pick `Payment received.`

<Imgz src='/img/retention/image.png'/>

### Step 2. Select the retention window

I will select the retention period as `All groups` for this example.

<Imgz src='/img/retention/image-1.png'/>

The retention period is the period you want to analyze the retention for.
`All groups` means we will analyze **month over month** retention of our user base for the last year.
Alternatively, I could pick `Month 1` retention, which would show me the retention rate of users after their first month.

### Step 3. Select trends vs. overall measurement configuration

Let's select the `1Y` time window for our retention analysis.

By default, Mitzu shows the change in retention rate over time. However, you can also visualize the retention rate as a whole. (Overall retention rate)

<Imgz src='/img/retention/image-2.png'/>

### The results

By selecting the `Overall retention` configuration and having `All groups` as the retention period configuration, we will see the typical retention curve on our graph.

<Imgz src='/img/retention/image-3.png'/>

This shows the retention rate month over month for our users, regardless of which month they started their subscription, as long as the subscription was started within the last year.

### Cohort retention / retention change over time

You can select the `Monthly retention change` configuration for the trends vs. overall measurement configuration.
This will show you the change in retention rate over time for each month in the last 12 months.

This chart can get very noisy if you have a long time window.

<Imgz src='/img/retention/image-4.png'/>

It is often better to visualize this as a heatmap.

<Imgz src='/img/retention/image-5.png'/>

However, if this is still too noisy, you can remove the `All groups` retention period config, select only months one and four, and visualize this as a line chart.

<Imgz src='/img/retention/image-6.png'/>

## Retention features

### Retention period

The retention period is the configuration that will enable you to compare the retention rates for different periods.

In this dropdown component, you can select multiple values simultaneously.
By selecting, for example, `Month 1` and `Month 2`, you will see the retention rate for the first and second months after the initial step.

The retention period `<1` means `Month 0`, which will measure the retention rate of users in the same `Month` as the initial step.

<Imgz src='/img/retention/image-7.png'/>

:::caution
Mitzu calculates retention in relative periods. For example, if the retention period is set to `4 weeks,` users will be considered retained if they perform the retaining event between 21 and 28 days after the initial step.

In other words, the retention period doesn't refer to calendar months or weeks.
:::

### Custom holding constant

Similarly to the Funnels, you can set a custom holding constant for the retention analysis.
To continue our example, we will set the `Custom holding constant` to `subscription_id`.

This will measure the retention rate of users who have performed the `payment_received` event with the same `subscription_id` as the subscription started. This is crucial for correct attribution of the retaining events.

:::info
Similarly to the funnels, the `Analyze uniques by` config will change the target entity for the retention analysis.
You can switch to `Groups` to measure the retention rate of Groups that have performed the `payment_received` event after a single user from the group performs the `subscription_started` event.
:::

### Measurement types

By default, Mitzu measures the retention rate of users who performed the initial and retaining events.
However, you can change this behavior by selecting a different measurement type.

<Imgz src='/img/retention/image-8.png'/>

Mitzu supports the `Aggregate property` measurement type. As in the funnels, this configuration makes Mitzu aggregate any property of the last step (retaining step).

For example, in our case above, we can select the `Aggregate property` measurement type and select the `Sum` aggregation function with the `Price` event property of the `payment_received` event.

<Imgz src='/img/retention/image-9.png'/>

Visualizing the total retained revenue month over month

<Imgz src='/img/retention/image-10.png'/>

### Returning on or after vs. returning on specific days

This configuration will manipulate how Mitzu considers users retained.
If you select the `Returning on or after` configuration, Mitzu will consider users as retained if they performed the retaining event during **or after** the retention period.

:::info
Let's consider an app where the usage is sporadic. Every user opens it once a month.
If I visualize the week-over-week retention rate of users who open the app on the last day of the month for three weeks, I would see a drop in the retention rate. To reduce this noise, you can apply the `Returning on or after` configuration. This will make Mitzu consider each week before the retaining event as returned.
:::

In contrast, the `Returning on specific days` configuration will make Mitzu consider users as retained only for the exact time period (week, month, etc.) during which they performed the retaining event.

### Retention attribution

This setting allows you to choose the attribution of the retention.
This setting doesn't have a visible implication for "Retention rate" aggregation types (which is the default aggregation type).

Currently, we support two types of conversion attribution:

-   Every event - each event is considered a "retaining" event of the retention insight.
-   First event - only the first event of the retaining step (segment) is considered a retaining event.

### Trend vs. overall measurement configuration

As discussed in our example above, retention insights also support trends and overall measurement configurations.

Retention trends are called `daily/weekly/monthly retention change`, which, once selected, helps you understand how your retention rate changes over time.

Overall retention will show how the passage of time affects the retention rate for users who performed the initial event and repeatedly performed the retaining event.

### Data sampling

Data sample is a feature that allows you to reduce the amount of data that Mitzu will analyze.
This feature will be discussed in a different section.
