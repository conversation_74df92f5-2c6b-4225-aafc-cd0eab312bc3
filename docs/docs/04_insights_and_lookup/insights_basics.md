---
sidebar_position: 1
title: Insights Basics
slug: /insights-basics
description: Insights basics
sidebar_label: Insights basics
---

# Insights Basics

Now, let's dive into the basics of insight creation with Mitzu.
We recommend reading this section before creating segmentation, funnel, retention, or journey insights.

## Mitzu insights

Insights are the main building blocks of Mitzu. Currently, we support four categories of insights:

-   Segmentation
-   Funnel
-   Retention
-   Journey

You may be familiar with these from your marketing, sales, or other product analytics tools.
These insight categories will enable you to create a wide range of metrics and KPIs for your business.
Also, this is where you can dive into individual users, groups, or cohorts' behaviors.

<Imgz src='/img/insights_basics/image.png'/>

## Mitzu segment

In this section, we will cover the most important concepts of the segments panel on the insights page.

A segment in Mitzu is a set of events performed by users or groups. A segment is the main building block of
every insight in Mitzu. You construct insights by defining segments on the UI. A segment can be a single event that you want to measure. For example, how many users visited your landing page?

<Imgz src='/img/insights_basics/image-1.png'/>

However, filters are most often applied to segments. For example, how many users visited your landing page, and who came from a specific country?

<Imgz src='/img/insights_basics/image-2.png'/>

You can combine multiple events into the same segment, e.g., page visits or sign-ups.

<Imgz src='/img/insights_basics/image-4.png'/>

More about this later.

:::info
It is essential to understand segments to make the best use of Mitzu. You can interpret the segments as:
Find the users who performed X and Y actions using these filter criteria.
:::

:::note
You will often see different decorators on the segment panels, such as `breakdown` or `count uniques`.
These, in fact, are not part of the segment definition but are applied to the segments during analysis.
:::

### Subsegments

When we refer to a subsegment, we talk about the sub-definitions of users who make up the segment.
The best way to explain it is with an example. The following segment has two subsegments:

-   Users who performed a `Page viewed` event (visited the landing page).
-   Users who performed a `Sign up` event are currently paying.

<Imgz src='/img/insights_basics/image-5.png'/>

Using the same event twice to build two subsegments is very common.
For example, during experimentation (A/B test) analytics.

<Imgz src='/img/insights_basics/image-6.png'/>

As you will see later in funnels, retention insights will greatly benefit from subsegments.

:::info
You can add subsegments with the `+` button at the top of the segment panel.
You can add as many subsegments as you want.
:::

## Event filters

Event filters are also among Mitzu's most important concepts. They are used to narrow segment definitions.

<Imgz src='/img/insights_basics/image-7.png'/>

As you can see above, there are multiple types of filters. We will explore each of them in the following sections.

:::caution
When event filters are stacked in a subsegment, they are combined with an `AND` operator.
Between two **subsegments**, they are combined with an `OR` operator.
:::

<Imgz src='/img/insights_basics/image-9.png'/>

In the example above, this is how you can interpret the filters:

> Select me all users who `viewed` our site coming from one of the two `Acquisition Campaigns` **and** are located in the `United States`, **or** performed a `search` operation with the search text `LLMs` **and** their language settings are `English (US)`.

### Event property filters

The most common property filters are the event property filters.
These are marked with a `.` in the filter dropdown.

<Imgz src='/img/insights_basics/image-10.png'/>

Event property filters will exclude users who **didn't perform the events** with the given filter condition.

:::info
Mitzu collects event properties for each event during the table indexing process. If tables in your data warehouse change, you must re-index the event tables to reflect the changes.
:::

### Recent Event Usage Indicator

The blue circles in the event selection dropdown indicate recent usage frequency for that specific event. The number of times an event has been used recently is also displayed when you hover over the event.

<Imgz src='/img/insights_basics/image-52.png'/>


### Complex event properties

Mitzu supports complex event properties. Complex event properties are event properties that are nested inside other event properties. For example, you can have a `user_properties` event property containing nested `country_code` and `language` event properties.

Mitzu shows these properties in the following format in the property filter dropdown:

<Imgz src='/img/insights_basics/image-19.png'/>

In the example above, we have an `Event props` event property containing nested `Cost Usd` and `Number of items` event properties.

:::note
Properties can be renamed in the catalog page.
:::

### User and group property filters

User and group properties are not bound to specific events. User and group profile tables are the source of these properties. In the property filter dropdown, you will see the `User Property` and `Group Property` options with the following icons:

<Imgz src='/img/insights_basics/image-11.png'/>

If you filter on these properties, you directly reduce the users based on their properties.
Groups are associated with a set of users, so if you filter on a group property, you filter on the set of users.

### Cohort filters

Cohorts are a "cherry-picked" set of users. You can create cohorts in Mitzu in multiple ways:

-   Import from a CSV file
-   Create a cohort on the chart on the insights page

:::info
Cohort filters insight's scope to the "cherry-picked" set of users.
Or, filter on your insights for users that are **not** in the "cherry-picked" set.
:::

<Imgz src='/img/insights_basics/image-12.png'/>

:::info
**What is the difference between a cohort and a segment of users?**

Both mean a set of users. However, a segment of users that share a common property like:

-   Country
-   Language
-   Gender
-   Age
-   etc.

A cohort is a set of users that doesn't necessarily share a common property.
For example, two users who don't share a single property can be in the same cohort.
:::

You can stack cohort filters in the same subsegment. This is useful when you want to filter for users in two cohorts.

<Imgz src='/img/insights_basics/image-13.png'/>

Selecting users in one of two cohorts is identical to selecting users in two segments.

<Imgz src='/img/insights_basics/image-14.png'/>

### Filter operators

You will see different operators depending on the property filter type and the type of property data.

Event, user, and group property filters will have the following operators available:

1. **Scalar data types (integers, float, dates):**

-   `Is` - Property is **equal** to any of the values listed for the filter
-   `Is not` - Property is **not equal** to any of the values listed for the filter
-   `>` - Property is strictly greater than the single value selected for the filter
-   `<` - Property is strictly less than the single value selected for the filter
-   `>=` - Property is greater than or equal to the single value selected for the filter
-   `<=` - Property is less than or equal to the single value selected for the filter
-   `Present` - Property has some value in the event
-   `Missing` - Property has no value in the event

:::info
Date and time filters are also supported. If you need to type a specific date or time, use the `YYYY-MM-DD` or `YYYY-MM-DD HH:MM:SS` ISO formats.
:::

<Imgz src='/img/insights_basics/image-15.png'/>

:::note
The example above means we are selecting the users who performed a `Checkout` event, and their country codes are any of the following: `gb`, `br`, `de`, `in.`
:::

2. **String data type:**

In addition to the data types operators (above), you will see the following operators:

-   `Contains` - The value of the event property contains any of the values listed for the filter
-   `Not contains` - The value of the event property does not contain any of the values listed for the filter

:::info
`>`, `<`, `>=`, and `<=` operators are also available for string data types.
They compare the string values based on the ASCII code of the characters.
:::

<Imgz src='/img/insights_basics/image-16.png'/>

:::note
The example above means that we are selecting the users who performed a `Checkout` event, and their country codes contain any of the following letters: `a` or `b`.
:::

3. **Boolean data type:**

Therefore, the following operators are available for boolean data types:

-   `Is` - Property is **equal** to any of the values listed for the filter
-   `Is not` - Property is **not equal** to any of the values listed for the filter
-   `Present` - Property has some value in the event
-   `Missing` - Property has no value in the event

<Imgz src='/img/insights_basics/image-18.png'/>

1. **Array data type:**

Array data types in Mitzu are special data types. Mitzu currently supports arrays only with scalar and string element types.
Complex types are not supported inside arrays.
The following operators are available for array data types:

<Imgz src='/img/insights_basics/image-17.png'/>

-   `Contains` - The array value of the event property contains any of the values listed for the filter
-   `Not contains` - The array value of the event property does not contain any of the values listed for the filter
-   `Present` - Property has some value in the event
-   `Missing` - Property has no value in the event

## Segment filters

Segment filters reduce the events considered in the analysis.
They typically keep the number of users the same. However, later, you will see that they can also lead to a reduced number of users.

<Imgz src='/img/insights_basics/image-20.png'/>

Currently, there are four types of segment filters:

1. **First event**: Selects the first event of the users present in the segment.
2. **Last event**: Selects the last event of the users present in the segment.
3. **First event in window**: Selects the first event of the users present in the segment in the chosen time window. (See below)
4. **Last event in window**: Selects the last event of the users present in the segment in the chosen time window. (See below)
5. **First event per period**: Selects the first event per time period of the users present in the segment in the chosen time window. The time period is available for trend analysis.
6. **Last event per period**: Selects the last event per time period of the users present in the segment in the chosen time window. The time period is available for trend analysis.

:::info
Segment filters don't work only with the `user` as the subject. You can use them with any entity present in your data.
`Groups` are typical candidates for the segment filters, but so are `sessions`, etc.

We will cover this topic in more detail in the [**Analyze uniques by?**](#analyze-uniques-by) section.
:::

### Understanding Segment Filters

The following image should highlight which user event is going to be selected for the segment filters.
Consider the situation for our actor:

-   His activity happens during first four months in 2024
-   The time window we are looking at starts at 15th of January 2024 and ends at 15th of March 2024
-   We are looking at Segmentation with Monthly Trends.

<Imgz src='/img/insights_basics/first_filter.drawio.png'/>

-   **No segment filter**: all 6 events are considered inside the selected time window.
-   **First event**: none of the events are considered as the first event of the actor happened before the start of the time window.
-   **First event in window**: we consider only 1 event, which is the last event of the user in January.
-   **First event per period**: we consider only 3 events. The last event of the user in January, the first event in February, and the first event in March. These are the first events in each period that corresponds to our Months.

## Segment breakdowns

Breakdowns are powerful tools for understanding the behavior of your users in different subsegments that ultimately make up the segment.

<Imgz src='/img/insights_basics/image-21.png'/>

You can add as many breakdowns as you want. If you combine two or more events on the segment panel, you will see the combination of properties of all event properties.

<Imgz src='/img/insights_basics/image-22.png'/>

:::caution
You can use any event, user, or group property as a breakdown except properties with an `array` type.
:::

### Breakdown with subsegment index

In segments that contain multiple subsegments the breakdown dropdown will show the `subsegment` as an option.

<Imgz src='/img/insights_basics/image-37.png'/>

This option will breakdown you segment analysis based on the **index** of the subsegment. This is very useful when you want to compare the behaviour users that performed the same event with different property filters.

<Imgz src='/img/insights_basics/image-36.png'/>

A great example for this is funnel analysis for A/B test variants.

<Imgz src='/img/insights_basics/image-38.png'/>

## Analyze uniques by?

The "Analyze uniques by" is an essential concept in insight creation. You can set the entity to which Mitzu performs the analysis. The two most common entities are `users` and `groups`. However, you can select any entity in your data as the core entity.

By default, Mitzu selects the `users` entity for any analysis. This means that any insight is regarding the user.
In other words, you will analyze the behavior of the user segments.

If you select `groups` as the core entity, you will analyze group segment behavior.

:::info
Mitzu will perform segment filters and measurements on the selected entity in the `Analyze uniques by` dropdown.
:::

<Imgz src='/img/insights_basics/image-24.png'/>

Mitzu will also perform chart interactions on the selected entity.

<Imgz src='/img/insights_basics/image-25.png'/>

# Insight configuration

This section will cover the most important configuration options on the insights level.

## Choosing the time window

You will see the time window configuration component on the insights page's top center section.

<Imgz src='/img/insights_basics/image-51.png'/>

With this component, you can choose the time horizon for your analysis.
You can choose between a:

-   relative date range(e.g., `1 month`, `1 week`, `1 day`, etc.). Relative date ranges count the time horizon from the `default end date config`, which you can find on the `Inights settings` page.
-   calendar date range (e.g., `This week`, `This month`, `This year`, etc.).
-   custom date range (e.g., `1st of December 2023` to `1st of January 2024`).

### Relative time window

The relative time window counts the time horizon from the `default end date config`, which you can find on the `Workspace settings` page.

<Imgz src='/img/insights_basics/image-27.png'/>

Suppose you've set your default end date configuration to `Now`. Mitzu counts the time horizon for the analysis up until the current second. Analogically, if you set the default end date config to `Start of the current day`, the time horizon will be counted for the end of the current day. And so on.

#### Adjusting the time horizon for trends

If you choose to analyze your insights as "trends", the granularity of the trend will also affect the time horizon.
For example, if you choose to analyze your insights as "weekly trends," the start date for the analysis will be the first day of the week (Monday), which is regarded as the beginning of the time horizon based on the relative time window. Similarly, Mitzu truncates the start date of the time horizon for the `Month` and `Year` time window options.

:::info
Consider the following example:

-   The time horizon is set to `1M` - 1 month.
-   The default end date config is set to `Start of the current day`.
-   The insight type is set to `Weekly trend`.
-   The current date is `1st of September 2024`.

Normally, the time horizon should start on Thursday, the `1st of August 2024`. However, as we are looking at the weekly trend, the start date will be adjusted to Monday for the week of the `1st of August 2024`.
So, the final start date for the analysis will be `29th of July 2024`.
:::

Adjusting the time horizon for trends is important.
Without it, you will see misleading results at the start of your analysis.

### Custom time horizon

Clicking the `Custom` button opens a new modal window with the start and end dates of the custom time window for your analysis.

<Imgz src='/img/insights_basics/image-49.png'/>

:::info
Custom time window dates are inclusive, meaning the entire day will be considered in the analysis.

You can also select a single day in the custom time window calendar.
:::

:::caution
Adjusting the time horizon for trends is also supported for custom time windows.
For example, if you have chosen a start date other than Monday and choose to analyze your insights as "weekly trends," the start date for the analysis will be adjusted to Monday for the week of the start date.
:::

## Trend vs. overall measurement configuration

You can select the trend vs. overall measurement configuration at the top center dropdown of the insights page.

<Imgz src='/img/insights_basics/image-29.png'/>

Trend measurement types will visualize the analysis as a change in measurement over the time horizon.
In contrast, the `Overall` insight types will visualize the analysis as a single value (mostly) for the entire time horizon.

Currently, we support four trend measurement types:

-   hourly
-   daily
-   weekly
-   monthly

The default trend type is `daily`.

## Selecting the chart type

Mitzu supports multiple chart types for each insights type (segmentation, funnel, retention, journey) and trend vs. overall measurement types.

Trend measurement types support the following chart types:

-   Bar
-   Line
-   Stacked bar
-   Stacked area
-   Heatmap (only in retention insights)

Overall measurement types support the following chart types:

-   Bar
-   Stacked bar
-   GEO charts (only for breakdowns with ISO2 or ISO3 country codes)

### Percentage stacked charts

Percentage stacked charts will normalize the values in your analysis to 100%.

<Imgz src='/img/insights_basics/image-31.png'/>

The same insight with regular stacked charts will look like this:

<Imgz src='/img/insights_basics/image-32.png'/>

### GEO Charts

Mitzu supports GEO charts in (segmentation and funnel insights) with a single breakdown of `ISO2` or `ISO3` country codes`.

<Imgz src='/img/insights_basics/image-30.png'/>

## Chart interactions

You can see the chart tooltip by moving the mouse cursor above a data point on the chart.

<Imgz src='/img/insights_basics/image-39.png'/>

Depending on the insight and chart type, you can perform different operations on some data points on the chart. To open the menu, click on a data point on the chart.

<Imgz src='/img/insights_basics/image-40.png'/>

### Zooming in and seeing trends

If your chart shows a data trend, you can zoom in on a single data point by clicking on it and then clicking the `Zoom in` button.

If your chart shows the overall result of a time window, you can click on the `See trends` button to see the trends in a nearby time window.

### List users, groups

If you have selected either `Users` or `Groups` for [Analyze uniques by](#analyze-uniques-by), you can fetch the list of the users or groups of a given data point you've selected. Click the `List users` or `List groups` button to easily [focus on a selected entity](/user-and-group-focus) from the opened modal.

### Create a new cohort

If you've selected `Users` for [Analyze uniques by](#analyze-uniques-by), then you can create a new [cohort](/other_assets/cohorts) of a given data point.

### Create a new global annotation

If your chart type is not GEO chart or heatmap, then you can mark any dates from the tooltip menu with a [global annotation](other_assets/annotations)

## More options for insights

You can find options and actions for your insights in the `More` menu.

<Imgz src='/img/insights_basics/image-33.png'/>

### Show SQL

This will show the SQL query that Mitzu generates for the insight.

<Imgz src='/img/insights_basics/image-34.png'/>

### Annotations

This option will turn `global annotations` on or off for the insight.

<Imgz src='/img/insights_basics/image-35.png'/>

### Download chart

This button will download the currently visible chart as a PNG image to your computer.

### Table and CSV export

Below the chart, you can see the result in a data table. The table has different columns depending on your insight type and segments. Click on the `Export` button to download the table content in CSV format.

<Imgz src='/img/insights_basics/image-41.png'/>
