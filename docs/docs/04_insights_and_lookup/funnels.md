---
sidebar_position: 3
slug: /funnel
description: Learn the basics of funnels
sidebar_label: Funnels
---

# Funnels in Mitzu

import data from "./read_the_basics.json"

<Cards data={data} />

Mitzu's Funnels allow you to examine how end-users perform events in a series. Funnels calculate and display the number of users who convert from one event to another within a particular time window.

## Use Cases

Imagine your product is a B2B messaging application. You might use Funnels to answer these questions:

-   What percentage of users converted through my signup funnel within 7 days?
-   At what step of the signup funnel did most users drop off?
-   How did my A/B test impact conversions in the signup funnel?
-   How has the payment funnel conversion rate in the US changed over time?
-   How long does it take most users to complete my payment funnel?
-   What departments complete the payment funnel most often?
-   What flows do users take between opening an app and making a purchase?
-   What flows do users take that doesn't lead to a purchase?
-   How do these two paths differ? What actions should I nudge towards or against?
-   What did the users that dropped off do instead?

## Quick Start

Building a report in Funnels takes just a few clicks, and results arrive in seconds. Let's build a simple report together.

:::note
Let's measure the conversion rate of website visitors to your SaaS product. The typical question you would ask is, "How many users converted to start a trial?"
We should only consider valid conversions if the user started the trial within the first 7 days of their first visit.  
Also, we should look at the last 4 months of data to get reasonable results.
:::

### Step 1. Switch to funnels

Click on the `Funnels` tab on the left side of the screen.

<Imgz src='/img/funnels/image-2.png'/>

### Step 2. Select the events

Select the steps of the funnel you want to analyze:

-   The first step should be the website visit event.
-   In our case, the second step should be the conversion event "trial started".
-   For the `page viewed` event select the `First event` segment filter. (This will ensure we only consider the first visit of the user)

### Step 3. Conversion window

Select the correct conversion window,`1 week`, in the conversion window dropdown.

<Imgz src='/img/funnels/image.png'/>

The rest of the settings are left as default.

-   Entire funnel conversion window type
-   Every event conversion attribution

### Step 4. Time window

Let's choose the `4M` time window to achieve the desired results.
Switch to `Funnel steps` to see the overall conversion rate of the funnel.

<Imgz src='/img/funnels/image-3.png'/>

### The results

After the 4th step, you should see the following results:

<Imgz src='/img/funnels/image-4.png'/>

### Extra step: breakdown

You can break down the funnel by the `Campaign` event property as an extra step.
This will visualize how each campaign contributed to the conversion rate.

<Imgz src='/img/funnels/image-5.png'/>

## Conversion window

The conversion window is the time period during which the funnel steps must be completed.
The default value is `1 day`, this means all steps must be completed within the last 24 hours.

:::info
Mitzu currently doesn't support funnels without a conversion window. If you need an unbounded funnel, increase the conversion window to 10 years.
:::

:::caution
The conversion window can greatly affect the funnel's performance. With higher conversion windows, the SQL engine will join more events, resulting in a longer query execution time.
:::

## Conversion window type

This setting allows you to choose the type of the conversion window.
Currently, we support two types of conversion windows:

-   Entire funnel - all the steps must be completed within the conversion window.
-   Between each step - the maximum amount of time that can pass between each step must be within the conversion window.

## Conversion attribution

This setting allows you to choose the attribution of the conversion.
This setting doesn't have a visible implication for "Conversion rate" aggregation types. (which is the default aggregation type)  
It will affect "Custom aggregations" and "Time to convert" aggregation types.

Currently, we support two types of conversion attribution:

-   Every event - each event is considered a "converting" event of the funnel.
-   First event - only the first event of each step (segment) is considered a converting event.

### Practical examples

Let's consider the following funnel:

-   Landing page visit
-   Payment for an item in the e-commerce website

To measure the total revenue, you can use the "Every event" conversion attribution and the "Sum of revenue" aggregation type.

However, if you want to measure the average time it takes to reach the first payment, you can use the "First event" conversion attribution and the "Average time to convert" aggregation type.

### Performance implications of conversion attribution

Mitzu's SQL engine will use `LEFT OUTER JOIN` with the "every event" conversion attribution.
However, it will be a `WINDOW FUNCTION` with the "first event" conversion attribution.

:::info
Window functions are an of order magnitude faster than left outer joins. We advise defaulting to "first event" conversion attribution if you have a large dataset. You can change the default conversion attribution in the "Conversion attribution" setting on the [Insight settings](/insight-settings) page.  
:::

## Measurement types

Mitzu funnels have rich support for measurement (or aggregation) types.

<Imgz src='/img/funnels/image-7.png'/>

By default, Mitzu will use the "Conversion rate" measurement type.

These are the supported measurement types:

-   **Conversion rate** - counts the number of converted users divided by the number of users who started the funnel.
-   **Count converted users** - counts the number of users performing the funnel steps.
-   **Average time to convert** - the average time it takes for the users to convert.
-   **Median time to convert** - the median time it takes for users to convert.
-   **PXX time to convert** - percentile XX time it takes for users to convert.
-   **Min time to convert** - the minimum time it takes for the users to convert.
-   **Max time to convert** - the maximum time it takes for the users to convert.
-   **Aggregate property** - custom aggregation of any property value of the funnel's last segment (step).

### Trends vs. Overall measurement configuration

If you are visualizing a funnel trend, the measurements will be calculated between the first and last steps only.
However, users must complete each step in the funnel in order to be considered "converted" users.

If you want to visualize the funnel as an overall measurement, the measurement is calculated for all the steps in the funnel.
The exception is the "Aggregate property" measurement type, which will aggregate the values of the last step only in both cases.

## Custom holding constant

As previously mentioned, a user is considered a "converted" user if the same user completes all the steps in the funnel.
With custom holding constants, you can modify this behavior.

For example, consider successful conversion only if it happened in the same browser session.
For this purpose, we have introduced the `Custom holding constant` setting. You can pick any property present in all the steps of the funnel.
In the case of the example above, you can set the `Custom holding constant` to `session_id`.
This will ensure that Mitzu measures the conversion rate of users who have converted in the same browser session.

### Difference between "Custom holding constant" and "Analyze by"

If you change the `Custom holding constant` setting, the measurements will still calculate user conversion rates or any measurement regarding users.
However, if you change the `Analyze by` setting, the measurements' target entity will change. For example, if you set the `Analyze by` to `Groups`, the measurements will be calculated for the unique Groups.

You can, in fact, change the `Analyze by` to the `session_id` as well. In this case, you will measure the number of successful converting sessions.

:::note
This documentation uses the `session_id` as an example. However, your data in the data warehouse might not have `session_id` columns.
:::

## Funnel step breakdown

You can set a breakdown on any of the funnel steps. As long as the breakdown is set on a single step.
The most common way to break down the funnel is by one or more properties of the first step.

If you breakdown the funnel with a property from the second step or above. You will often notice breakdown value `<not set>`.
This indicates that segment of users that didn't convert between the step before and the step that contains the breakdown.

<Imgz src='/img/funnels/image-12.png'/>

## Trends vs. Overall measurement configuration

We have already discussed trends vs. overall measurement configuration in the [Insight basics](/insights-basics#trend-vs-overall-measurement-configuration) section.

In this section we consider this setting in the context of funnels.

### Funnel steps

The overall measurement type is called `Funnel steps,` and the visualization is a bar chart (or GEO Chart).
In the case of a bar chart, each group represents the associated measurements for each step of the funnel.

By default, each group in the bar chart shows the funnel's conversion rate (or any measurement) up until that step.

<Imgz src='/img/funnels/image-8.png'/>

Below, you can see the funnel steps of time to convert measurement.

<Imgz src='/img/funnels/image-9.png'/>

### Funnel trends

The primary thing to consider when visualizing funnel trends is that we attribute the conversion to the date of the first step of the funnel.

Consider the following funnel:

-   Page viewed
-   (Checkout) Payment for an item in the e-commerce

<Imgz src='/img/funnels/image-10.png'/>

In this case, the conversion window is set to 7 days.
The checkout may have been performed 6 days after the page view. However, we still attribute the conversion to the page view event date.

<Imgz src='/img/funnels/image-11.png'/>

This is true for any number of funnel steps.

## Dotted lines in funnel charts

<Imgz src='/img/funnels/image-13.png'/>

Dotted lines in funnel charts indicate that users entering the funnel at a given point have not yet had sufficient time to complete the funnel within the defined conversion window. The section with the dotted line is considered **incomplete**, meaning conversions beyond this point may still occur but are not yet recorded.

This helps distinguish between actual drop-offs and conversions that could still be in progress.

## Probability to be best (Bayesian)

With funnel insights you can also see the probability that a variant has the highest true conversion rate, based on Bayesian sampling from posterior distributions.
This feature is only available for funnel insights with overall measurements (Funnel steps).

This feature is mostly useful for A/B test analysis.

Bayesian A/B testing calculates the probability of a variant being the best by integrating prior beliefs with observed data. Given two variants (A and B), we model their conversion rates as Beta distributions (posterior beliefs) using Bayesian inference. The probability that one variant is better than the other is computed by simulating many samples from these posteriors and checking how often one exceeds the other. This Monte Carlo method estimates P(A > B) or P(B > A) directly, giving actionable probabilities instead of p-values. Unlike frequentist tests, Bayesian A/B testing continuously updates beliefs and allows stopping based on confidence thresholds.

<Imgz src='/img/funnels/image-6.png'/>


## Video tutorial

<iframe width="560" height="315" src="https://www.youtube.com/embed/M1nd9Y_VhSU?si=sEI1l8MGcv7owwzJ" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen class="video-iframe"></iframe>