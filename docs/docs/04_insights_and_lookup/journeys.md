---
sidebar_position: 5
slug: /journey
description: Journeys
sidebar_label: Journeys
---

# Journeys in Mitzu

import data from "./read_the_basics.json"

<Cards data={data} />

Mitzu's journey charts give you a visual map of every path users take through your product. Journeys calculate the user count number and conversion rates for each step in every path.

## Use Cases

Imagine your product is a B2C webshop, and you get your visitors to your webshop through different blog posts and marketing campaigns. You might use journeys to answer these questions:

-   Which marketing campaign results in the most income?
-   In a checkout flow, at which steps do the users drop off most?
-   How are the users segmented in the middle of a funnel?

## Quick Start

Building a report in journeys takes just a few clicks, and results arrive in seconds. Let's make a simple report together.

Imagine we have a webshop, and visitors to our landing page can subscribe to emails. Later, we will email them with the latest prices.

Let's say we ran a campaign called `20% off from all prices`, and we want to see in which countries these emails yielded a checkout and in which countries they were pointless to send.

### Step 1. Switch to journeys

Click on the `Journey` tab on the left side of the screen.

<Imgz src='/img/journeys/image.png'/>

### Step 2. Select the events

Select the steps of the journey you want to analyze:

-   The first step should be the `Page visit` event having a filter on the `Acquisition campaign` property with the value of `promo_20off`
-   Click on the `+ Add event` and add the following events `Email sent`, `Email opened`, and `Checkout`.
-   For the `Email opened` event, select the `User Country Code` for the breakdown.

### Step 3. Conversion window

Select the correct conversion window,`2 weeks`, in the conversion window dropdown.

<Imgz src='/img/journeys/image-1.png'/>

You can leave the rest of the settings by default.

-   Entire funnel conversion window type
-   First event conversion attribution

### Step 4. Time window

Let's choose the `1M` time window to achieve the desired results. The `<drop off>` nodes represent those users who performed the beginning of the user flow but did not finish it.

<Imgz src='/img/journeys/image-2.png'/>

### The results

After the 4th step, you should see the following results:

<Imgz src='/img/journeys/image-3.png'/>

On the chart, we can see all user paths with the selected events. By moving the cursor above the nodes and links, we can gather further details about that specific path.

For example, only 25% of the users did not open the emails sent within two weeks. In this case, the email tracking service may show a 75% open rate, but from the next step of the journey chart, we can see that the majority of these users dropped off before the checkout event.

<Imgz src='/img/journeys/image-4.png'/>

Another example is that users from the US, China, and Germany won't checkout after these emails, so maybe they should be targeted differently.

## Conversion window

The conversion window is the time period during which the journey steps must be completed.
The default value is `1 day`, this means all steps must be completed within the last 24 hours.

:::info
Mitzu currently only supports journeys with a conversion window. If you need an unbounded journey, increase the conversion window to 10 years.
:::

:::caution
The conversion window can significantly affect the journey's performance. With higher conversion windows, the SQL engine will join more events, resulting in a longer query execution time.
:::

## Conversion window type

This setting allows you to choose the type of the conversion window.
Currently, we support only one type of conversion window for journeys:

-   Entire funnel - all the steps must be completed within the conversion window.

## Conversion attribution

This setting allows you to choose the attribution of the conversion. Currently, we support only one type of conversion attribution for journeys:

-   First event - only the first event of each step (segment) is considered a converting event.

## Measurement types

Mitzu supports only one measurement (or aggregation) type for journeys:

-   **Conversion rate** - counts the number of converted users divided by the number of users who started the funnel.

## Custom holding constant

As previously mentioned, a user is considered a "converted" user if the same user completes all the steps in the journey.
With custom holding constants, you can modify this behavior.

For example, consider successful conversion only if it happened in the same browser session.
For this purpose, we have introduced the `Custom holding constant` setting. You can pick any property present in all the steps of the journey.
In the example above, you can set the `Custom holding constant` to `session_id`.
This will ensure that Mitzu measures the conversion rate of users who have converted in the same browser session.
