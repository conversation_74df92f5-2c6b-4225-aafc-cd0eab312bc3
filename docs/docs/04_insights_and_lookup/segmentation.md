---
sidebar_position: 2
slug: /segmentation
description: Segmentation
sidebar_label: Segmentation
---

# Segmentation

import data from "./read_the_basics.json"

<Cards data={data} />

Segmentation is a powerful and flexible tool for visualizing trends and compositions within your data. You can analyze events, cohorts, and user profiles and display the data in various chart types.

Advanced segmentation features allow you to create formulas and compare current and past data.

<Imgz src='/img/segmentation/image-1.png'/>

## Use cases

Here are some of the sample questions you can answer in segmentation:

1. **High-level product analytics**

    1. How is my WAU (weekly active users count) changing over time?
    2. How often are my users getting value?
    3. What is the distribution of my users across regions/devices, etc? (property breakdown)

2. **B2B (in this case, a messaging application)**

    1. How many messages were sent in the US in the past 30 days?
    2. How many users had a mobile app session yesterday? (unique events)
    3. How many messages are sent per session? (formulas)
    4. How much revenue was generated on plans purchased in the past year? (property aggregation)
    5. How has the power users cohort grown over the past 6 months? (cohort trends)

3. **Marketing**

    1. Which advertising campaigns generate the most checkouts? (property breakdown)
    2. Which advertising campaigns generate the most revenue (property aggregation)

4. **Revenue analytics**
    1. Show me the change in MRR from our US-based customers over the last year.
    2. What is our current MRR?

## Segmentation basics

You have learned the basics of [segments](/insights-basics) in Mitzu. This section will cover the most essential segment concepts regarding user (or any other entity) segmentation.

## Segmentation analysis

Segmentation analysis finds behavioral patterns in your user (or other entity) data. In Mitzu, segmentation focuses on comparing the behavior of different segments of users (or other entities) and identifying the differences between them.

### Examples of segmentation analysis

Let's see some examples of segmentation analysis:

:::note
I want to compare the number of users who visited our landing page to the number who started a trial in the last 30 days.
:::

This can be done by simply creating two user segments in the segments panel (left side of the page).
For the first segment, we choose the users who performed the `Page viewed` event. For the second segment, we select the users who performed the `Trial started` event. Both segment's definitions will be decorated with the `Count uniques` aggregation. This means we will count the unique users in each segment, as the `Analyze uniques by` option is set to `Users`. We will set the time window to `1M` and use the `Daily trend` measurement type. Finally, we will visualize this as a line chart.

<Imgz src='/img/segmentation/image-2.png'/>

:::note
Let's take this a step further and calculate the ratio of the two segments.
:::

You can do this by creating a simple formula under the segment panels. The formula should be `A / B` where `A` refers to the first segment and `B` refers to the second segment.

<Imgz src='/img/segmentation/image-3.png'/>

As you can see, there are, on average, 50 page visits per `Trial started` event, which can be great for our hypothetical SaaS business. However, I want to see the big picture. Let's zoom out and see how this measurement has changed over the last six months. For this, I need to change the time horizon to `6M` and use the weekly trend measurement type.

<Imgz src='/img/segmentation/image-4.png'/>

As you can see, the overall pattern stays the same. However, six months ago, we had a lot fewer page visits for every trial. This is something we should investigate further. In reality, for further investigation, I would switch to funnel analysis and see which marketing campaigns drove the most traffic to our landing page and which had the highest conversion rate for trial-start events.

For now, I will stop here. This example was intended to show you what an investigation would look like in Mitzu.

## Segmentation features

In this section, we will cover the features extending Mitzu insights' basics.
Segmentation is a special insight type, as you can select multiple segments of users for comparison. However, you can also choose how you measure the segments.

### Segment aggregation types

You can perform per-segment measurements with the help of the Aggregation type component.
By default, the aggregation type is set to `Count uniques,` which means we will count the number of unique users in each segment.

<Imgz src='/img/segmentation/image-6.png'/>

:::info
If the analyze uniques by setting is set to `Groups,` Mitzu will count the number of groups in each segment.
If the analyze uniques by is set to anything else, such as `session_id`, the count uniques will measure the number of unique sessions in each segment.
:::

:::info
The `$ MRR` and the `$ Subscriber count` aggregations are available only when you have [configured the revenue settings](/revenue-settings) and all events in the segment is the configured revenue event.
:::

#### Count event totals

This aggregation type will count the number of events in the segment.

<Imgz src='/img/segmentation/image-7.png'/>

In the example above, two segment definitions were defined with the same event, `Page viewed`.
The blue line shows the number of events performed by the segment's users, while the green shows the number of unique users in the segment.

#### Aggregate property

This aggregation type will aggregate the property values of the segment.

1. First, choose the aggregation type again. Currently, we support the following aggregation types:
    1. Count distinct
    2. Sum
    3. Average
    4. Min
    5. Max
    6. Median
    7. P75
    8. P90
    9. P95

Except for the `Count distinct` aggregation type, the aggregation types will only work with properties that are numeric.

:::note
How many different marketing campaigns we ran in the last 60 days?
:::

This question can be answered simply by choosing the `Count distinct` aggregation type and selecting the `Campaign` property. This will count the number of distinct campaigns for which at least a single page visit occurred.


#### MRR

This aggregation type will calculate the MRR based on the configured revenue event.

#### Subscriber count

This aggregation type will calculate the active subscriber count of every time period based on the configured revenue event.


<Imgz src='/img/segmentation/image-8.png'/>

### Formula calculations

With formula calculations, you can apply algebraic formulas to your segmentation analysis.
The most common use case is to calculate the average events per user.

<Imgz src='/img/segmentation/image-9.png'/>

In the example, we are visualizing the number of events per user per day as a trend.

Formula calculations support these algebraic operations:

1. `+` addition
2. `-` subtraction
3. `*` multiplication
4. `/` division

Mitzu also supports the parenthesis `()` in these expressions.

Letters refer to segment definitions, such as:

-   `A` - the first segment
-   `B` - the second segment
-   ... and so on ...

:::info
This is a valid formula for percentage calculations: `(A * 100) / B`.
:::

#### Formula breakdowns

In case you are using a formula and want to breakdown you results, based on a property.
The exact same property must be used in each segment definition, that is present in the formula.

:::note
What is the average page visit number per user per day, broken down by the `Campaign` property?
:::

<Imgz src='/img/segmentation/image-13.png'/>

### Post-processing

Post-processing options are only available in segmentation analysis. These operations are not performed with SQL in your data warehouse but in the Mitzu backend on the results dataset.

Currently, support these post-processing operations:

1. Rolling average of various window sizes
2. Cumulative sum

#### Rolling averages

Post-processing with rolling averages is useful to "smooth" your result charts.

Without post-processing, the chart will look like this:
<Imgz src='/img/segmentation/image-10.png'/>

With post-processing, the chart will look like this:
<Imgz src='/img/segmentation/image-11.png'/>

The overall trend is more readable with post-processing.

#### Cumulative sum

Cumulative sum helps calculate the integral of any measurement.
For example, how many total visits happened over the last 6 months?

<Imgz src='/img/segmentation/image-12.png'/>


## Video tutorial

<iframe width="560" height="315" src="https://www.youtube.com/embed/awl_qwnom8M?si=bRn2DlkQbtFlNXdD" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen class="video-iframe"></iframe>