---
title: Data Indexing
description: Explaining how <PERSON><PERSON><PERSON> indexes your data warehouse to transform your insights into SQL queries
sidebar_label: Data Indexing
slug: /indexing
---

# Indexing

Before creating the first insight, <PERSON><PERSON><PERSON> must collect information about how you structured the events in your data warehouse. We call this process indexing.

## Stored data

To generate the [insights](/insights-basics) and [Catalog](/event-catalog) pages, <PERSON><PERSON><PERSON> collects the following information during the indexing process:

-   **events:** Lists all events <PERSON><PERSON><PERSON> finds in the configured [event tables](/event-tables). <PERSON><PERSON><PERSON> will use it to generate the insights and catalog pages.
-   **event properties:** Lists all event properties assigned to each found event.
-   **event filter values:** Lists the possible values of each found event property.
-   **dimension properties:** Lists all dimension properties that <PERSON><PERSON><PERSON> finds in the configured [dimension tables](/dimension-tables).
-   **dimension filter values:** List of the possible values of each found event property.

:::success
<PERSON><PERSON><PERSON> will never copy data from your data warehouse or fetch data not required to generate the forms in the Mitzu web app.
:::

:::info
For performance reasons, the number of `Event filter values` and `Dimension filter values` is limited to 500. For example, <PERSON><PERSON><PERSON> won't store the possible values of the event time property due to their high cardinality. If you contact mitzu support, we will increase this limit.
:::

## Event table indexing

When you [add a new event table](/event-tables#adding-new-event-tables) or [re-index an existing table](/event-tables#re-indexing), then Mitzu will fetch a sample from the event table and store the events found in this sample. You can configure the sample size in several ways:

-   Mitzu calculates the time window of the sample that it will end on the [Default End Date Config](/insight-settings) and begin N days earlier, which you can set as the [Lookback Days](/indexing-settings).
-   You can configure the sample size on the [indexing settings](/indexing-settings) page.
-   You can enable [Data Scrambling](/indexing-settings) to randomize the order of the queried records.

:::info
You should configure the sample size to maximize the cardinality of your events in the sample; otherwise, Mitzu will not recognize some of your events. If you need help with this configuration, please contact <NAME_EMAIL>.
:::

Once Mitzu has identified your events, it will index each event's available properties. Like event indexing, Mitzu will fetch a sample of the events containing all event properties. This sampling uses the same configuration as event sampling. If your event tables contain many columns, consider enabling the [Bucketed table indexing](/indexing-settings). Then, you can configure how many columns Mitzu should index simultaneously, decreasing the indexing time of huge tables.

Mitzu can generate the insights page from the recognized `event` and `event properties`.

## Dimension table indexing

When you [add a new dimension table](/dimension-tables#setting-up-the-user-profile-tables), Mitzu will fetch a sample from the dimension table and store the dimension properties in this sample. You can configure the sample size in several ways:

-   You can configure the dimension sample size on the [indexing settings](/indexing-settings) page.
-   You can enable [Data Scrambling](/indexing-settings) to randomize the order of the queried records.
-   You can enable [Bucketed table indexing](/indexing-settings). This way, the sample will contain a randomized selection of rows from the source table.

:::info
You should configure the sample size to maximize the cardinality of your dimension properties in the sample; otherwise, Mitzu will not recognize some of your properties. If you need help with this configuration, please contact <NAME_EMAIL>.
:::

Mitzu can generate the insights page from the recognized 'dimension properties'. Mitzu indexes the dimension filter values only when needed. For example, when you add a new dimension filter on the insight page, Mitzu will index the possible values of that specific event property using the same sampling mechanism.
