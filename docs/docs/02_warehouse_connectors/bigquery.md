---
title: Big Query
description: Setup Google Big Query Integration To Mitzu
sidebar_label: Big Query
keywords:
    - data warehouse
    - integration
    - Google
    - Big Query
slug: /bigquery
---

import NextStep from './common.tsx';
import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';

# BigQuery

## Overview

Mitzu connects to Google Big Query using an IAM service account with the proper permissions to access your data. To connect Mitzu to Google Big Query, you must first create the user and then configure its credentials in Mitzu.

## Supported data types

<SupportedDataTypes
  string="STRING"
  number="FLOAT64, INT64 with alias INT, SMALLINT, INTEGER, BIGINT, TINYINT, BYTEINT"
  bool="BOOL"
  datetime="DATE, DATETIME, TIME, TIMESTAMP"
  map="JSON"
  struct="STRUCT"
  array="ARRAY"
/>

## Create a Service Account

You need to grant <PERSON><PERSON><PERSON> four permissions from your Google Cloud console for us to access your Big Query data.

1. In your GCP console, create a [Service account](https://console.cloud.google.com/iam-admin/serviceaccounts?project=mitzu-358611).
   <Imgz   src='/img/bigquery/image.png'/>
   <Imgz   src='/img/bigquery/image-1.png'/>
2. Add four roles to this account:
   <Imgz   src='/img/bigquery/image-2.png'/>

-   `BigQuery User`
-   `BigQuery Data Viewer`
-   `BigQuery Job User`
-   `BigQuery Read Session User`

3. Click Done\
   <Imgz   src='/img/bigquery/image-3.png'/>
4. Create BigQuery **JSON** Key by clicking on the `Manage Keys` button\
   <Imgz   src='/img/bigquery/image-4.png'/>
   <Imgz   src='/img/bigquery/image-5.png'/>
5. Save the key at a secure location

## Set the credentials in Mitzu

Set the Big Query project id and either copy the credentials or upload the credentials json from Google Big Query.

<Imgz   src='/img/bigquery/connection.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
