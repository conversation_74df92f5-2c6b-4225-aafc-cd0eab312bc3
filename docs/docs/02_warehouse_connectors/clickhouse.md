---
title: ClickHouse
description: Setup ClickHouse Integration To Mitzu
sidebar_label: ClickHouse
keywords:
    - data warehouse
    - integration
    - ClickHouse
    - self-hosted
slug: /clickhouse
---

import { MitzuIPAddress, SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';
import NextStep from './common.tsx';

# ClickHouse

## Overview

Mitzu connects to ClickHouse by using username/password authentication.

<MitzuIPAddress dwh_name="ClickHouse"/>

## Supported data types

<SupportedDataTypes
  string="String, FixedString"
  number="UInt8, UInt16, UInt32, UInt64, UInt128, UInt256, Int8, Int16, Int32, Int64, Int128, Int256, Float32, Float64"
  bool="Boolean"
  datetime="Date, Date32, DateTime, DateTime64"
  map="Map"
  struct="Tuple"
  array="Array"
/>

## Configure the connection details in Mitzu

<Imgz src='/img/clickhouse/image.png'/>

Please fill out the form with the details of your connections.

For an encrypted connection, please use the port numbers `9440` or `8443` and check the `Secure connection (SSL)` checkbox. For an unencrypted connection (not recommended), use the port `9000` or `8123`.

:::note
When entering URLs in our system, please include only the "Host" portion, omitting the protocol scheme like https.
:::

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
