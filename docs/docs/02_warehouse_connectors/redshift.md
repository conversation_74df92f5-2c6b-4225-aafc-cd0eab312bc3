---
title: AWS Redshift
description: Setup AWS Redshift Integration To Mitzu
sidebar_label: Redshift
keywords:
    - data warehouse
    - integration
    - AWS
    - redshift
slug: /redshift
---

import NextStep from './common.tsx';
import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';

# AWS Redshift

## Overview

Mitzu connects to AWS Redshift by using username/password authentication.

## Supported data types

<SupportedDataTypes
  string="CHAR, VARCHAR"
  number="SMALLINT, INTEGER, BIGINT, REAL, DOUBLE PRECISION"
  bool="BOOLEAN"
  datetime="DATE, TIME, TIMETZ,TIMESTAMP, TIMESTAMPTZ"
  map=""
  struct=""
  array=""
/>

## Getting Connection Information

1. Head to AWS Redshift under your Account in your AWS Region. Find the following information.

<Imgz src='/img/redshift/image.png'/>

<Imgz src='/img/redshift/image-1.png'/>

The service user's password should be available during user creation. Or you can allocate a new password on the same page.

## Configure the connection details in Mitzu

<Imgz src='/img/redshift/image-2.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
