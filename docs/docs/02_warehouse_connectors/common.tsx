import Admonition from '@theme/Admonition';

export function MitzuIPAddress({ dwh_name }): JSX.Element {
    return (
        <>
            <Admonition type="warning">
                If the {dwh_name} is hosted by you then please make sure the{' '}
                <code>************</code> ip is allowed inbound traffic to the specified port.
            </Admonition>
        </>
    );
}

export function DataWarehouseLogo({ logo, link, name }): JSX.Element {
    return (
        <div className="dwh-logo">
            <a href={link}>
                <img src={logo} />
                <br />
                {name}
            </a>
        </div>
    );
}

export function SupportedDataTypes({
    string,
    number,
    bool,
    datetime,
    map,
    struct,
    array,
}: SupportedDataTypesProps): JSX.Element {
    const not_supported = 'Currently not supported';
    return (
        <p>
            <PERSON><PERSON><PERSON> will map the types of the data warehouse based on the following table:
            <table>
                <thead>
                    <tr>
                        <th>Mitzu type</th>
                        <th>Data warehouse type</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>String</td>
                        <td>{string || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Number</td>
                        <td>{number || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Boolean</td>
                        <td>{bool || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Datetime</td>
                        <td>{datetime || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Map</td>
                        <td>{map || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Struct</td>
                        <td>{struct || not_supported}</td>
                    </tr>
                    <tr>
                        <td>Array</td>
                        <td>{array || not_supported}</td>
                    </tr>
                </tbody>
            </table>
            <br />
            <Admonition type="info">All unrecognized types will be handled as strings.</Admonition>
        </p>
    );
}

export function QueryParams(): JSX.Element {
    return (
        <p>
            You can configure the connection query parameters. To do so, click on the
            <code>Advanced settings</code> section and enter your parameters into the
            <code>URL Query Params</code> textbox. You must write each parameter in a new line in
            the <code>&lt;parameter name&gt;=&lt;parameter value&gt;</code> format.
        </p>
    );
}

export function TestConnection(): JSX.Element {
    return (
        <>
            <p>
                Click the <code>Test connection</code> button to check if Mitzu can connect to your
                data warehouse using the entered values.
            </p>
            <Admonition type="warning">
                Mitzu will try to connect to your data warehouse and execute a{' '}
                <code>SELECT 1;</code>
                command. You may need to grant further permission Mitzu to see and query your data
                tables.
            </Admonition>
            <p>
                To save the settings, click the <code>Test connection & Save</code> button.
            </p>
        </>
    );
}

export default function NextStep(): JSX.Element {
    return (
        <p>
            Once the connection is tested an saved the event end dimension tables can be configured.
            Please follow the <a href="/event-tables"> setting up event tables </a> guide.
        </p>
    );
}
