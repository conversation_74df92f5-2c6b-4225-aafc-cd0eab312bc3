---
title: Self-hosted Trino / Presto
description: Setup self-hosted Trino/Presto Integration To Mitzu
sidebar_label: Self-hosted Trino / Presto
keywords:
    - data warehouse
    - integration
    - trino
    - presto
    - self-hosted
slug: /trino
---

import { MitzuIPAddress, SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';
import NextStep from './common.tsx';

# Self-hosted Trino / Presto cluster

## Overview

Mitzu connects to Trino / Presto clusters by using username/password authentication.

<MitzuIPAddress dwh_name="Trino / Presto"/>

## Supported data types

<SupportedDataTypes
  string="CHAR, VARCHAR"
  number="INTEGER, TINYINT, SMALLINT, BIGINT, REAL, DOUBLE"
  bool="BOOLEAN"
  datetime="DATE, TIME, TIMESTAMP"
  map="MAP"
  struct="ROW"
  array="ARRAY"
/>

### Configuring a Trino / Presto connection to a self-hosted cluster

1. Create a principal in your Trino / Presto's authentication provider (e.g., LDAP, Kerberos, etc...)
2. Add the rule below to your System Access Control file to grant read-only access to certain catalogs. Update the `<user name>` to the newly created user's name, which Mitzu will use. The `<data catalog>` should be the catalog name you want to share with Mitzu. You can share multiple catalogs by listing them all, delimited by a `|` character (eg. `(catalog_1|catalog_2|catalog_)`\

```
{
"catalogs": [
...
{
"user": "<user name>",
"catalog": "<data catalog>",
"allow": "read-only"
}
...
]
}
```

## Configure the connection details in Mitzu

Add the connection information to Mitzu at the 3rd step in the new Workspace creation or the Manage Workspace / Connection tab. Using URL Query Params, you can add additional parameters to the connection. Each parameter should be in a separate row in the `<parameter_name>=<parameter_value>` format.

 <Imgz src='/img/self-hosted-trino-presto-cluster/image.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
