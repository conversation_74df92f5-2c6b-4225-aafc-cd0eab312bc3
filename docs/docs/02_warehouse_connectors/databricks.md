---
title: Databricks
description: Setup Databricks Integration To Mitzu
sidebar_label: Databricks
keywords:
    - data warehouse
    - integration
    - databricks
slug: /databricks
---

import NextStep from './common.tsx';
import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';

# Databricks

## Overview

Mitzu connects to Databricks by using User Access Token-based authentication.

## Databricks cluster support

Mitzu supports all Databricks SQL Warehouse and cluster types. The recommended engine is Serverless SQL Warehouse or PRO SQL Warehouse.

Mitzu supports Databricks with all three major cloud infrastructure providers:

-   AWS
-   Azure
-   GCP

Currently, Mitzu only supports User Access Token-based authentication.

## Supported data types

<SupportedDataTypes
  string="STRING"
  number="BIGINT, DOUBLE, FLOAT, INT, SMALLINT, TINYINT"
  bool="BOOLEAN"
  datetime="DATE, TIMESTAMP"
  map="MAP"
  struct="STRUCT"
  array="ARRAY"
/>

## Getting Connection Information

1. Create an access token with read access to your experiment data and write access to the staging database

2. Follow the [Databricks documentation](https://docs.databricks.com/integrations/jdbc-odbc-bi.html#get-connection-details-for-a-cluster) to get the hostname and HTTP path of the cluster you'll use to run your experimental analysis. You may consider creating a specific cluster for this use case.
   <Imgz src='/img/databricks/image.png'/>

3. Follow [these instructions](https://docs.databricks.com/dev-tools/auth.html#databricks-personal-access-tokens) to get the personal access token that Mitzu will use to calculate experiment results in your warehouse.
   <Imgz src='/img/databricks/image-1.png'/>

## Configure the connection details in Mitzu

Add the connection information to Mitzu.
<Imgz src='/img/databricks/image-2.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
