---
title: PostgreSQL
description: Setup PostgreSQL Integration To Mitzu
sidebar_label: PostgreSQL
keywords:
    - data warehouse
    - integration
    - PostgreSQL
    - psql
    - self-hosted
slug: /postgresql
---

import { MitzuIPAddress, SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';
import NextStep from './common.tsx';

# PostgreSQL

## Overview

Mitzu connects to PostgreSQL by using username/password authentication.

<MitzuIPAddress dwh_name="PostgreSQL"/>

## Supported data types

<SupportedDataTypes
  string="character, character varying, text, uuid"
  number="bigint, bigserial, integer, real, smallint, smallserial, serial"
  bool="boolean"
  datetime="date, time, timestamp"
  map="json, jsonb"
  struct=""
  array="array"
/>

## Configure the connection details in Mitzu

<Imgz src='/img/postgresql/image.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
