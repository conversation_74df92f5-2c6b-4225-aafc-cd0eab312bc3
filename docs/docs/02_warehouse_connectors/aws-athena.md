---
title: AWS Athena
description: Setup AWS Athena Integration To Mitzu
sidebar_label: AWS Athena
keywords:
    - data warehouse
    - integration
    - AWS
    - Athena
slug: /aws-athena
---

import NextStep from './common.tsx';
import { SupportedDataTypes, TestConnection } from './common.tsx';

# AWS Athena

## Overview

Mitzu connects to AWS Athena using an AWS user with the right permissions to access your data. To connect Mitzu to AWS Athena, first, this user should be created, and then its credentials need to be configured in Mitzu.

If you use other AWS services, we recommend creating a special AWS Service Account that only has the permissions required to run Athena and input the IAM credentials from that account to connect <PERSON><PERSON><PERSON> to Athena.

See [Identity and access management in Athena](https://docs.aws.amazon.com/athena/latest/ug/security-iam-athena.html).

## Supported data types

<SupportedDataTypes
  string="CHAR, CHAR(length), STRING, VARCHAR(length)"
  number="TINYINT, SMALLINT, INT, INTEGER, BIGINT, FLOAT, DOUBLE"
  bool="BOOLEAN"
  datetime="TIME, DATE, TIMESTAMP"
  map="MAP"
  struct="STRUCT"
  array="ARRAY"
/>

## Create an AWS Athena service user

Head to [AWS IAM](https://us-east-1.console.aws.amazon.com/iam/) and create a new user. This user should be able to access three primary resources:

-   `Files in S3`
-   `AWS Glue`
-   `AWS Athena`

[Here](https://docs.aws.amazon.com/IAM/latest/UserGuide/id_users_create.html), you can find more information about AWS users and how to create them.

Here is an example **IAM Policy** document containing the proper permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "Athena",
            "Effect": "Allow",
            "Action": [
                "athena:BatchGetNamedQuery",
                "athena:BatchGetQueryExecution",
                "athena:GetNamedQuery",
                "athena:GetQueryExecution",
                "athena:GetQueryResults",
                "athena:GetQueryResultsStream",
                "athena:GetWorkGroup",
                "athena:ListDatabases",
                "athena:ListDataCatalogs",
                "athena:ListNamedQueries",
                "athena:ListQueryExecutions",
                "athena:ListTagsForResource",
                "athena:ListWorkGroups",
                "athena:ListTableMetadata",
                "athena:StartQueryExecution",
                "athena:StopQueryExecution",
                "athena:CreatePreparedStatement",
                "athena:DeletePreparedStatement",
                "athena:GetPreparedStatement"
            ],
            "Resource": "*"
        },
        {
            "Sid": "Glue",
            "Effect": "Allow",
            "Action": [
                "glue:BatchGetPartition",
                "glue:GetDatabase",
                "glue:GetDatabases",
                "glue:GetPartition",
                "glue:GetPartitions",
                "glue:GetTable",
                "glue:GetTables",
                "glue:GetTableVersion",
                "glue:GetTableVersions"
            ],
            "Resource": "*"
        },
        {
            "Sid": "S3ReadAccess",
            "Effect": "Allow",
            "Action": ["s3:GetObject", "s3:ListBucket", "s3:GetBucketLocation"],
            "Resource": [
                "arn:aws:s3:::bucket1",
                "arn:aws:s3:::bucket1/*",
                "arn:aws:s3:::bucket2",
                "arn:aws:s3:::bucket2/*"
            ]
        },
        {
            "Sid": "AthenaResultsBucket",
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:AbortMultipartUpload",
                "s3:ListBucket",
                "s3:GetBucketLocation"
            ],
            "Resource": ["arn:aws:s3:::bucket2", "arn:aws:s3:::bucket2/*"]
        }
    ]
}
```

## Set the credentials in Mitzu

Find and copy the `AWS_ACCESS_KEY_ID` and `AWS_SECRET_KEY` to Mitzu. In the case of AWS Athena, the Catalog should stay AwsDataCatalog or leave the field empty. For `S3 Staging Dir`, make sure you have chosen the correct bucket for storing intermediary files.

<Imgz src='/img/aws-athena/image.png'/>

<TestConnection/>

## Next steps

<NextStep/>
