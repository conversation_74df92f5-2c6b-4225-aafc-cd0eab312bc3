---
title: Microsoft Fabric
description: Setup Microsoft Fabric Integration To Mitzu
sidebar_label: MS Fabric
keywords:
    - data warehouse
    - integration
    - Fabric
    - Microsoft
slug: /fabric
---

import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';
import NextStep from './common.tsx';

# Microsoft Fabric

## Overview

Mitzu connects to Microsoft Fabric by using email/password authentication.

## Supported data types

<SupportedDataTypes
  string="char, varchar"
  number="bit, smallint, int, bigint, decimal, numeric, float, real"
  bool=""
  datetime="date, time, datetime2"
  map=""
  struct=""
  array=""
/>

## Configure the connection details in Mitzu

<Imgz src='/img/fabric/image.png'/>

Please fill out the form with the details of your connections.

:::note
When entering URLs in our system, please include only the "Host" portion, omitting the protocol scheme like https.
:::

<TestConnection/>

## Next steps

<NextStep/>
