---
title: Starburst.io
description: Setup Starburst.io Integration To Mitzu
sidebar_label: Starburst.io
keywords:
    - data warehouse
    - integration
    - starburst.io
slug: /starburst
---

import NextStep from './common.tsx';
import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';

# Trino cluster hosted by Starburst.io

## Overview

Mitzu connects to Trino / Presto clusters by using username/password authentication.

## Supported data types

<SupportedDataTypes
  string="CHAR, VARCHAR"
  number="INTEGER, TINYINT, SMALLINT, BIGINT, REAL, DOUBLE"
  bool="BOOLEAN"
  datetime="DATE, TIME, TIMESTAMP"
  map="MAP"
  struct="ROW"
  array="ARRAY"
/>

### Configuring a Trino / Presto connection

1. Login to Starburst.io
2. (Recommended) Setup a new user and role having read-only privileges on your data catalog
3. Navigate to the Clusters tab and click on your data cluster's "Connection info" button. Be careful that a `/` character separates your username from your role in the User field.

 <Imgz src='/img/trino-cluster-hosted-by-starburst.io/image.png'/>

## Configure the connection details in Mitzu

Set the connection info to Mitzu and the role as a URL Query Params (e.g., `role=<your role>`). Besides the role, you can add other connection parameters in the same field. Each parameter should be in a separate row in the `<parameter_name>=<parameter_value>` format.

 <Imgz src='/img/trino-cluster-hosted-by-starburst.io/image-1.png'/>

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
