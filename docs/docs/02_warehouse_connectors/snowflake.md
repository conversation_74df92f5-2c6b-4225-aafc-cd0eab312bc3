---
title: Snowflake
description: Setup Snowflake Integration To Mitzu
sidebar_label: Snowflake
keywords:
    - data warehouse
    - integration
    - snowflake
slug: /snowflake
---

import NextStep from './common.tsx';
import { SupportedDataTypes, QueryParams, TestConnection } from './common.tsx';

# Snowflake

### Overview

Mitzu connects to Snowflake clusters by using username/password authentication.

## Supported data types

<SupportedDataTypes
  string="VARCHAR, CHAR, CHARACTER, STRING TEXT"
  number="INT, INTEGER, BIGINT, SMALLINT, TINYINT, BYTEINT, FLOAT, FLOAT4, FLOAT8, DOUBLE, DOUBLE PRECISION, REAL"
  bool="BOOLEAN"
  datetime="DATE, DATETIME, TIMESTAMP, TIMESTAMP_LTZ, TIMESTAMP_NTZ, TIMESTAMP_TZ"
  map="VARIANT"
  struct="OBJECT (not supported)"
  array="ARRAY"
/>

## Configure the connection details in Mitzu

<Imgz src='/img/snowflake/image.png'/>

You can find your account ID by following this guide [Account Identifier](https://docs.snowflake.com/en/user-guide/admin-account-identifier.html). For example, if you’re running Snowflake on AWS and your account URL is `https://IVMDAAM-DH09820.snowflakecomputing.com`:

-   `<account_identifier>`: `IVMDAAM-DH09820`

You’d enter `IVMDAAM-DH09820` as the account name in Mitzu.

:::info

Some regions require the cloud platform identifier. For the requirements per region, see [the official Snowflake documentation](https://docs.snowflake.com/en/user-guide/admin-account-identifier.html#non-vps-account-locator-formats-by-cloud-platform-and-region).

:::

For more information, please visit the [official documentation of Snowflake](https://docs.snowflake.com/en/user-guide/admin-account-identifier).

<QueryParams/>

<TestConnection/>

## Next steps

<NextStep/>
