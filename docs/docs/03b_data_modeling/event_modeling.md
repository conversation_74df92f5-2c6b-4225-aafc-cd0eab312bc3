---
sidebar_position: 2
title: Event Modeling
description: On this page you will find the three event data model architectures that <PERSON><PERSON><PERSON> supports
sidebar_label: Event Modeling
slug: /event-modeling
---

# Event modeling

On this page you will find the three event data model architectures that <PERSON><PERSON><PERSON> supports.
More information you can find at this [blog](https://www.mitzu.io/post/modeling-product-events-in-the-data-warehouse).

## Single event tables

This is the simplest event data model of all three. Each event you want to track should be a separate table.
Each table must contain the following columns:

-   **User ID:** The ID of the user that performed the event.
-   **Event time:** The time when the event occurred.

:::info
In case of single event tables the name of the table describes the event that the user performed.
:::

## Wide multi event Tables

This event table model contains each event in a single table.
The event properties for each event are stored in separate columns in the same table.
This makes this table "wide" as the properties for each event don't always overlap and 100+ columns are required.

Multi event table must contain the following columns:

-   **User ID:** The ID of the user that performed the event.
-   **Event time:** The time when the event occurred.
-   **Event name:** The name of the event that the user performed.

:::warning
Maintaining this table has some drawbacks.
This table often with 100+ columns, which can be hard to maintain.
:::

:::info
As this table also contains all events for the product or service, it can also contain a lot of data.
This can make this table slow to query. It is suggested to **partition** this table based on the `event_name` column
and index this table based on the `event_time` column.
:::

## Short multi event tables

This event table model contains each event in a single table.
The event properties for each event are stored MAP, JSON, or VARIANT types in the same table.
This makes this table "short" as with a single `event_properties` column, it is possible to store all the event properties.

Multi event table must contain the following columns:

-   **User ID:** The ID of the user that performed the event.
-   **Event time:** The time when the event occurred.
-   **Event name:** The name of the event that the user performed.

:::warning
Maintaining this table has some drawbacks.
The JSON, Map or Variant types can be cause performance issues.
:::

:::info
As this table also contains all events for the product or service, it can also contain a lot of data.
This can make this table slow to query. It is suggested to **partition** this table based on the `event_name` column
and index this table based on the `event_time` column.

If you are using Iceberg or Delta lake tables, make sure your tables are optimized for querying.
:::
