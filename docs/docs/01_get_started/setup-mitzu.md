---
sidebar_position: 1
slug: /setup-mitzu
sidebar_label: Setup Mitzu
description: Let's quickly set up Mi<PERSON><PERSON> on top of your data warehouse.
---

# Quick Start with Mitzu

Let's get started with <PERSON><PERSON><PERSON>. This page will help you set up your first workspace and connect your data.

## Sign up for Mitzu

Open the [https://app.mitzu.io](https://app.mitzu.io) website and click either the `Log in with <PERSON>ail` or `Log in with Google` button.

<Imgz src='/img/setup-mitzu/login.png'/>

:::warning
Login with company SSO is only available for already existing organizations with an Enterprise plan.
:::

## Create a new organization

After login, name your organization and click `Create Organisation.`

<Imgz src='/img/setup-mitzu/create-org.png'/>

## Connect your data warehouse to Mitzu

Your organization contains an empty workspace. To use Mitzu, you need to configure this workspace properly. The remaining steps to configure Mitzu are listed on the left sidebar of the main page.

<Imgz src='/img/setup-mitzu/onboarding-connection.png/'/>

Click the `Connect Mitzu with your data warehouse` button to configure your connection. The [warehouse integration](/warehouse-integrations) page provides more information about the connection settings.

:::info
Throughout this example, we will use BigQuery as a data warehouse.
Currently, Mitzu supports 8 data warehouse solutions:

- AWS Athena
- Google BigQuery
- ClickHouse
- DataBricks
- Postgres
- Redshift
- Snowflake
- Trino / Starburst
:::

<Imgz src='/img/bigquery/connection.png/'/>

## Add tables to your workspace

Add event tables on the `Event tables` tab. Event tables are regular tables in BigQuery (or in any data warehouse). The only requirement is to have some kind of `user ID` and `event time` columns. Optionally, they can have an `event name` field.

Event tables are the basis of Mitzu's event catalog. You can uncover new insights from your data without any SQL knowledge based on the event tables you define.

<Imgz src='/img/event-tables/image.png'/>

The [event tables](/event-tables) page provides more information about the event table configuration.


## Create your first insight

Having a single event table configured is enough to create your first insight. Click the `Create insight` button in the top left corner of the navbar; it loads the [Insights page](/insights-basics). When you click on the `Select an event` button, you should be able to see your events. If you choose an event, then the chart will appear.

<Imgz src='/img/setup-data-warehouse/image-19.png'/>


## Event tables for product analytics

Your application should track events that are ultimately stored in your data warehouse.
These events should be stored either as **separate tables** or as a **single table**.

- In the case of separate tables, Mitzu will automatically discover the events' names from the table names.
- In case of a **one big table**, please provide the `Event name` column in the workspace settings page.

:::note
More on this subject in the [event tables](/event-tables) section.
:::

## Dimension tables for product analytics

Your data warehouse may contain dimension tables containing additional information about certain entities, such as users, groups, or sessions. By configuring these tables, you can filter your events based on the dimension values.

:::note
More on this subject in the [dimension tables](/dimension-tables) section.
:::

## Event tables for marketing analytics

Your landing page should track events that are ultimately stored in your data warehouse.
The setup is similar to the one for product analytics.

:::info
Mitzu unifies marketing, product, and revenue analytics in a single platform.
It means that you can analyze the entire user life cycle in one place.

The key to this challenge is user ID unification.
You can learn about this [here](https://www.mitzu.io/post/identifying-users-in-the-data-warehouse).

More content on this subject is coming soon.
:::
