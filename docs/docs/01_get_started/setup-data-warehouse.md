---
sidebar_position: 2
slug: /setup-data-warehouse
description: Setup a data warehouse from scratch and centralize all your data in under 60 minutes.
sidebar_label: Setup your first data warehouse
---

# Setup a data warehouse

## Data warehouses for dummies

Historically, setting up a data warehouse **was** a complex task requiring a dedicated team of data engineers.
However, we live in an era where setting up a cloud data warehouse is a breeze.

This guide will walk you through the steps to set up a data warehouse for Mitzu.

:::info
The great thing about this guide is that it can serve you in the later stages of your data journey.
:::

## Step 1. Choose a data warehouse solution

For most companies, we suggest getting started with the simplest solutions:

-   Clickhouse
-   Snowflake
-   BigQuery
-   PostgreSQL

In this guide, we will use **BigQuery** as an example.

:::info
Google BigQuery is an excellent choice for most companies, starting with data warehouses.
It is easy to set up and has a free tier.

Please get in touch with us if you want to use another data warehouse.
Here you can find our [Slack community](https://join.slack.com/t/mitzu-io/shared_invite/zt-1h1ykr93a-_VtVu0XshfspFjOg6sczKg).
:::

## Step 2. Setup BigQuery (5-10 mins)

Starting with BigQuery is easy:

1. Go to the [BigQuery console](https://console.cloud.google.com/bigquery).
2. Create a new free account (it requires a credit card). But 10GB of data storage is free.
3. Go to the BigQuery console: [BQ Admin](https://console.cloud.google.com/bigquery). Here, you should see your default project.
4. Create a new dataset! <Imgz src='/img/setup-data-warehouse/image.png'/>
   I suggest keeping it as a single region dataset.
5. Once done, your BigQuery project should look like this: <Imgz src='/img/setup-data-warehouse/image-1.png'/>
6. Create a [service account](https://console.cloud.google.com/iam-admin/serviceaccounts) to access your data warehouse. Your service account should have the role `BigQuery Admin`. You can change this later if needed.
7. Create a new JSON access key under the `Manage keys` menu item. <Imgz src='/img/setup-data-warehouse/image-2.png'/> <Imgz src='/img/setup-data-warehouse/image-3.png'/>. Mitzu will use this JSON key to access your data warehouse later in the next steps.

:::success
**Congratulations!** You have successfully set up your data warehouse.
:::

## Step 3. Collect data to BigQuery (10-15 mins)

Moving data to your data warehouse is also very simple. You have multiple options available:

-   **Using a CDP** - (Customer Data Platform) like [Segment](https://segment.com), [RudderStack](https://rudderstack.com), [Jitsu](https://jitsu.com) or [Snowplow](https://snowplow.io)
-   **DIY** - build your solution that collects data (Not recommended)

This guide will use **Jitsu** as this is the simplest way to start.
:::note
Jitsu is an excellent choice for most companies that need data ingestion to the data warehouse.
It is easy to set up and has a very generous free tier.
:::

By the end of this guide, you will have a Jitsu account created and data from your landing page, your application, and Stripe data collected to BigQuery. You should see the result below:

<Imgz src='/img/setup-data-warehouse/image-6.png'/>

Let's get started!

Go to [Jitsu](https://jitsu.io) and create a new free account.

<Imgz src='/img/setup-data-warehouse/image-4.png'/>

### 3.1. Add BigQuery as a destination

Create your first destination by clicking on `+ Add` button at the destinations.
Choose `BigQuery` as a destination.

<Imgz src='/img/setup-data-warehouse/jitsu_dest.png'/>

### 3.2. Add your landing page visits as a data source

Create your first data source by clicking on `+ Add` button at the data sources.

Then, you need to name your data source and create a browser key. You can leave the rest as default.

<Imgz src='/img/setup-data-warehouse/image-7.png'/>

You must embed a JavaScript snippet into your website to finalize your setup.
This part is probably the easiest. Copy Jitsu's javascript snippet and paste it into your website's `<head>` section.
You can find the snipped under the `Setup instructions` menu item.

<Imgz src='/img/setup-data-warehouse/image-5.png'/>

Finally, connect your new data source to the BigQuery destination. You can do this by clicking on the `Connections` button in the middle of the overview page.

### 3.3. Add your application as a data source

Adding the application data source is similar to adding the landing page data source.
However, you will most likely use an SDK to collect the data. Jitsu currently supports multiple SDKs.
You can find the list of supported SDKs under the `Setup instructions` menu item.

<Imgz src='/img/setup-data-warehouse/image-9.png'/>

:::info
Remember to connect your data source to the BigQuery destination.
:::

### 3.4. Add Stripe as a data source

Now, we will add Stripe as a data source. This will be valuable in measuring revenue inside Mitzu.
Mitzu will allow you to analyze revenue based on user segments.

:::warning
Mitzu works best with Stripe data for consumer products. It is also required to have the product `userID` as Stripe customers' metadata.
Follow this [guide](https://docs.stripe.com/metadata) to add metadata to your Stripe customers.
:::

The best way to integrate Stripe with Mitzu is to follow the guide provided by Jitsu.
This guide will appear when you add your Stripe connector.

<Imgz src='/img/setup-data-warehouse/image-16.png'/>

Here is what it should look like:

<Imgz src='/img/setup-data-warehouse/image-12.png'/>

## Step 4. Verify your setup

As a final step, let's verify everything is working as expected.
You should see in BigQuery the following tables:

<Imgz src='/img/setup-data-warehouse/image-13.png'/>

## Step 5. Connect BigQuery to Mitzu (10 mins)

Follow the guide at [setup Mitzu](/setup-mitzu).
