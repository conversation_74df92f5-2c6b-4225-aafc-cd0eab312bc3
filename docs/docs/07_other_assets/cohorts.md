---
title: Cohorts
description: Cohorts
sidebar_label: Cohorts
---

import thumbnail from '/img/application/cohorts/image.png';

# Cohorts

In the world of product analytics, understanding user behavior is pivotal to driving engagement, retention, and overall success. Our Cohorts feature offers a powerful lens through which you can dissect and comprehend the diverse actions and characteristics of your users. By segmenting users into specific groups, or "Cohorts," based on shared attributes or behaviors, you gain the ability to tailor your strategies and create more personalized experiences.

## What are cohorts?

Cohorts are defined as clusters of users grouped together due to common characteristics or actions within a specified timeframe. This segmentation enables more granular analysis of user engagement, retention, and other critical metrics over time. By examining the nuances of each cohort, you can identify trends, patterns, and areas for improvement that are not apparent when looking at your user base as a whole.

### Example cohorts you can create

-   **Geographic Cohorts:** Understand the preferences and behaviors of users based on their geographic location. For example, you can create a cohort for users in a specific country or region to tailor your marketing strategies accordingly.
-   **Engagement-Level Cohorts:** Segment users by their engagement level, such as those who interact with your app or website frequently within a certain period. This helps in identifying your most active users and understanding what keeps them coming back.
-   **Acquisition Cohorts:** Track users based on when they first signed up or made a purchase. Analyzing these cohorts over time can reveal insights into user retention and the long-term value of different user segments.
-   **Behavior-Based Cohorts:** Group users by specific actions they've taken within your app or website, such as completing a purchase, sharing content, or reaching a particular milestone. This enables you to understand how different behaviors correlate with user retention and satisfaction.

### Benefits of Using Cohorts

-   **Enhanced User Understanding:** Gain a deeper insight into how different groups of users interact with your product, allowing for more personalized and effective engagement strategies.
-   **Improved Product Development:** Identify features and content that resonate with your users, guiding your development efforts towards what truly matters to them.
-   **Optimized Marketing Efforts:** Tailor your marketing campaigns to address the unique needs and preferences of each cohort, increasing their effectiveness and ROI.
-   **Data-Driven Decisions:** Make informed decisions based on clear insights into user behavior and preferences, steering your product and strategy in the right direction.

## All features

### Creating a cohort via Explore

After building an Insight on Explore, click on any data point on a Segmentation or Funnel chart (note that creating cohorts from Retention insights is not yet supported). Select the "Create cohort" option, name the cohort, and click on "Create cohort" to save it.

<Imgz src='/img/application/cohorts/image.png'/>

<Imgz src='/img/application/cohorts/image-5.png'/>

### Creating a cohort from CSV

If you have a list of users exported from a third-party tool in a CSV format, you can use it to create a static cohort in Mitzu. To do this, go to the homepage, click on "More," and then select "Import cohort".

:::info

Format Requirement: The CSV file must contain a single column labeled 'user_id', with each subsequent row containing one unique user_id.

:::

<Imgz src='/img/application/cohorts/image-1.png'/>

### Using Cohorts in Insights

Once you've created a cohort, you can use it to refine your insights. Start by navigating to the filter list where the 'Cohort' option is at the top. Select it and then choose the name of the cohort you want to apply. You can further refine your results by combining cohort filters with any other filters.

<Imgz src='/img/application/cohorts/image-2.png'/>

### Cohort Details

You can find the details of a cohort on the cohort’s page. Navigate to Cohorts under the Assets menu and click on the name of the cohort you would like to review. The following information will be displayed:

1. Owner
2. Size
3. Type (Dynamic or Imported)
4. Created at
5. Last updated at
6. _Refresh - only for Dynamic cohorts_
7. _View Insight - only for Dynamic cohorts_
8. _Users table - only for Imported cohorts_

<Imgz src='/img/application/cohorts/image-3.png'/>

### Update Cohorts

#### 1. Imported Cohorts

You can modify the name and description of an Imported cohort, however, the user list remains static. To update the users in an Imported cohort, a new CSV file can be imported into a new cohort.

#### 2. Dynamic Cohorts

A dynamic cohort is not a single users list, but the list of criteria that defines a cohort group. Each click on refresh will calculate the size of the cohort. You can also see the insight that was used to generate the cohort.

### Export Cohorts

To export a cohort to a CSV, go to the cohort's page and click 'Export' in the top-right corner of the page.

<Imgz src='/img/application/cohorts/image-4.png'/>


## Video tutorial

<iframe width="560" height="315" src="https://www.youtube.com/embed/NxjofNb0xiY?si=XQrolch0hgMe5xvN" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen class="video-iframe"></iframe>