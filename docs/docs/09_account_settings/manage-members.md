---
title: Users
description: Manage users and roles
sidebar_label: Users
slug: /manage-members
---
# Manage Members

You can invite your colleagues and business partners into your Mitzu account to share insights and dashboards or to let them analyze your company data.

## Listing users

The already invited members are listed in a table having the following columns:

-   **User email:** The email address of the member. This is used to log in Mitzu
-   **Login:** It can be either `basic authentication` or `private SSO` if [SSO](single-sign-on) is enabled and the member is expected to be authenticated by the configured identity provider
-   **Role:** The member's role can be either `member` or `admin`. Only admin users can change the account settings or invite other users.
-   **Invited:** elapsed time since the user has been invited
-   **First login:** elapsed time since the first login of the user
-   **Last login:** elapsed time since the user's last login.

<Imgz src='/img/application/manage-members/image.png'/>

## Invite new members

To invite new members to your account, click on the `Invite user` button. A modal will show up where you can enter a list of email addresses and the role to be assigned to the new members. Once the fields are filled out, click on the `Invite user` button in the modal to close it and invite the new members. An invitation email will be sent to the configured new email addresses.

Up to 10 email addresses can be invited at once.

If SSO is enabled, you don't need to invite the users from the configured Identity Provider.

:::warning
Only Admins can invite new members.
:::

<Imgz src='/img/application/manage-members/image-1.png'/>

## Update a member

A member's role can be changed. First, select the member by checking its checkbox in the table row, then click on the `Manage` button. A modal will show up where the member's role can be changed. Once the desired role is selected, click on the `Save` button.

You cannot change the membership of the account owner (the first user in the account) or your own role.

:::warning
Only Admins can update member roles, but other Admins can also do so. At least one member must have an Admin role in the account.
:::

<Imgz src='/img/application/manage-members/image-2.png'/>

## Remove a member

A member can be removed from the account. First, select the member by checking its checkbox in the table row, then click on the `Remove` button. A confirmation modal will show up, and if you are sure to remove the user, click on the `Delete` button.

All saved insights and dashboards will be transferred to the account's owner (the first member of the account).

:::warning
You cannot remove the account owner (the first member of the account) or your own user.
:::

:::warning
When SSO is enabled, members authenticated by your Identity Provider can be removed, but this won't prevent them from joining your account again on their next login.
:::

<Imgz src='/img/application/manage-members/image-3.png'/>
