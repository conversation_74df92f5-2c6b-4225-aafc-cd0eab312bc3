---
title: Single sign-on
description: SSO with AWS Cognito, Google SSO, or OIDC
sidebar_label: Single Sign-on (SSO)
keywords:
    - SSO
    - single sign-on
    - AWS
    - Cognito
slug: /single-sign-on
---

import VerifyLoginFlow from './common.tsx';

# Single sign-on

Users can use SSO to log in to Mitzu. For that, Mitz<PERSON> first needs to be configured with the company SSO. You can configure the following SSO providers:

-   [AWS Cognito](#aws-cognito)
-   [Google SSO](#google-sso)
-   Any [OIDC (OpenID Connect)](#oidc) compatible authentication backend like <PERSON><PERSON>, OneLogin

## AWS Cognito

### Create a new app client in AWS Cognito

1. Log in to Mitz<PERSON> and navigate the 'Account settings' page. Once SSO is enabled, you can configure the details of the integration:

<Imgz src='/img/application/aws-cognito/image.png'/>

2. Open the AWS Console and create a new app client to your AWS Cognito user pool with the following settings:
    - Auth type: Confidential client
    - Allowed callback URLs: copy the entire value of the Redirect URL input from the Mitzu SSO settings
    - OAuth 2.0 grant types: Authorization code grant
    - OpenID connect scopes: Email must be selected

### Configure Mitzu with the Cognito app client

1. Configure the client settings on the Mitzu SSO page
    - Client ID and Client Secret values can be found on the app client settings page
    - Pool ID, AWS Region, and AWS Cognito signing domain can be found on the user pool settings page
2. Click save

## Google SSO

### Create a new app client in Google

1. Log in to Mitzu and navigate the 'Account settings' page. Once SSO is enabled, you can configure the details of the integration:
   <Imgz src='/img/application/google-sso/image.png'/>

2. Open the Google Cloud Console and create a new OAuth 2.0 Client ID (on the APIs & Services -> Credentials page) with the following settings:
    - Application type: Web application
    - Authorized redirect URIs: copy the entire value of the Redirect URL input from the Mitzu SSO settings

### Configure Mitzu with the Cognito app client

1. Configure the client settings on the Mitzu SSO page
    - Client ID and Client Secret values can be found on the app client settings page
    - Project ID can be found on the Cloud Overview -> Dashboard page in the project info box
2. Click save

## OIDC

### Create a new app client in your Identity Provider

1. Log in to Mitzu and navigate the 'Account settings' page. Once SSO is enabled, you can configure the details of the integration:

<Imgz src='/img/application/oidc/image.png'/>

2. Open the web console of your Identity Provider and create a new client application with the following settings:
    - Application type: Web Application
    - Grant type: Authorization Code
    - Sign-in redirect URIs: copy the entire value of the Redirect URL input from the Mitzu SSO settings
    - Sign-out redirect URIs: copy the entire value of the Home URL and append \`/auth/unauthorized'
    - to initiate the login from the Identity Provider side, redirect the users to the Home URL appended '/auth/redirect-to-login'
    - Client authentication: client secret

### Configure Mitzu with the Cognito app client

1. Configure the client settings on the Mitzu SSO page
    - Client ID and Client Secret values can
    - Authorize endpoint, token endpoint, and JWKS URI can be configured manually or set the `<idp>/.well-known/openid-configuration` URL and click on the Fetch OIDC Settings button. It will fetch the configuration and fill out these fields.
2. Click save

<VerifyLoginFlow/>
