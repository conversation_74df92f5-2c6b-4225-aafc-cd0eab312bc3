import Admonition from '@theme/Admonition';

export default function VerifyLoginFlow(): JSX.Element {
    return (
        <>
            <Admonition type="warning" title="Verify login flow">
                In a different browser (or in an incognito window) verify the login flow. If it is
                not working as expected then please supervise your settings or contact Mitzu
                Support.
            </Admonition>
        </>
    );
}
