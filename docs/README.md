# Website

This website is built using [Docusaurus](https://docusaurus.io/), a modern static website generator.

### Installation

```
$ yarn
```

### Local Development

```
$ yarn start
```

This command starts a local development server and opens up a browser window. Most changes are reflected live without having to restart the server.

### Build

```
$ yarn build
```

This command generates static content into the `build` directory and can be served using any static contents hosting service.

### Deployment

Using SSH:

```
$ USE_SSH=true yarn deploy
```

Not using SSH:

```
$ GIT_USER=<Your GitHub username> yarn deploy
```

If you are using GitHub pages for hosting, this command is a convenient way to build the website and push to the `gh-pages` branch.

# Guide write docs

## Folder names

Folder follow the names of pages in the application. Or, they should follow names of tabs if the page is big.
Example:

-   Dashboards
-   Insights
-   Workspace settings
-   Assets

First letters of folders should be capitalized.

## Page names

Page names should follow the names of tabs or menu items mostly in the application. Or lower level concepts.
Examples:

-   Cohorts
-   Saved insights
-   Single sign on

First letters of page names should be capitalized.
