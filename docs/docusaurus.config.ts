import { themes as prismThemes } from 'prism-react-renderer';
import type { Config } from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';
import tailwindPlugin from './plugins/tailwind-config.cjs'; // add this
const slogan = 'Simple, Warehouse Native Product Analytics';

const config: Config = {
    title: 'Mitzu',
    tagline: slogan,
    favicon: 'img/favicon.ico',

    // Set the production url of your site here
    url: 'https://docs.mitzu.io',
    // Set the /<baseUrl>/ pathname under which your site is served
    // For GitHub pages deployment, it is often '/<projectName>/'
    baseUrl: '/',
    // GitHub pages deployment config.
    // If you aren't using GitHub pages, you don't need these.
    organizationName: 'mitzu-io', // Usually your GitHub org/user name.
    projectName: 'mitzu', // Usually your repo name.

    trailingSlash: false,
    onBrokenLinks: 'throw',
    onBrokenMarkdownLinks: 'throw',

    // Even if you don't use internationalization, you can use this field to set
    // useful metadata like html lang. For example, if your site is Chinese, you
    // may want to replace "en" with "zh-Hans".
    i18n: {
        defaultLocale: 'en',
        locales: ['en'],
    },

    scripts: [
        {
            src: 'https://cdn.counter.dev/script.js',
            'data-id': '06dfc979-e5eb-4f91-b798-8b51fd99fa55',
            'data-utcoffset': '2',
            async: true,
        },
    ],

    plugins: [
        [
            '@docusaurus/plugin-ideal-image',
            {
                quality: 70,
                max: 1030, // max resized image's size.
                min: 640, // min resized image's size. if original is lower, use that size.
                steps: 2, // the max number of images generated between min and max (inclusive)
                disableInDev: false,
            },
        ],
        tailwindPlugin,
        require.resolve('docusaurus-lunr-search'),
    ],

    presets: [
        [
            'classic',
            {
                docs: {
                    sidebarPath: './sidebars.ts',
                    routeBasePath: '/',
                },
                blog: {
                    showReadingTime: true,
                },
                gtag: {
                    trackingID: 'G-5R5Q881G52',
                    anonymizeIP: true,
                },
                theme: {
                    customCss: './src/css/custom.css',
                },
            },
        ],
    ],

    themeConfig: {
        // Replace with your project's social card
        image: 'img/docusaurus-social-card.jpg', // FIXME
        // algolia: {
        //   // TODO: create your own search index and API KEY in Algolia
        //   apiKey: "********************************",
        //   appId: "VUJWGKSNM6",
        //   indexName: "docs_index",
        // },
        tableOfContents: {
            minHeadingLevel: 2,
            maxHeadingLevel: 5,
        },
        navbar: {
            // title: "Mitzu",
            // logo: {
            //   alt: slogan,
            //   src: "img/logo.svg", // FIXME
            // },
            items: [
                // TODO: suggestion, let's keep minimalistic navbar for better looking in Tablet screens
                // NOTE: when you change a label or href here, please make sure to update the NavbarNavLink file
                { href: '/', label: 'Get Started', position: 'left' },
                {
                    href: '/warehouse-integrations',
                    label: 'Warehouse integrations',
                    position: 'left',
                },
                { href: 'https://mitzu.io/blog', label: 'Blog', position: 'left' },
                {
                    type: 'search',
                    position: 'right',
                },
                {
                    href: 'https://app.mitzu.io/',
                    label: 'Sign in to Mitzu',
                    position: 'right',
                },
            ],
        },
        footer: {
            style: 'light',
            links: [
                {
                    items: [
                        {
                            html: `
                <img src="/img/logo.svg" alt="logo"  height="51" />
                <p style="font-size: smaller;white-space: nowrap;">© ${new Date().getFullYear()} Mitzu Inc. All rights reserved</p>
              `,
                        },
                    ],
                },
                {},

                {
                    title: 'Docs',
                    items: [
                        {
                            label: 'Tutorial',
                            to: '/',
                        },
                    ],
                },
                {
                    title: 'Community',
                    items: [
                        {
                            label: 'LinkedIn',
                            href: 'https://www.linkedin.com/company/mitzu-io/',
                        },
                        {
                            label: 'Slack',
                            href: 'https://join.slack.com/t/mitzu-io/shared_invite/zt-1h1ykr93a-_VtVu0XshfspFjOg6sczKg',
                        },
                        {
                            label: 'YouTube',
                            href: 'https://www.youtube.com/channel/UC-6O5vL85xAtHLJy2-d6dEw',
                        },
                    ],
                },
                {
                    title: 'More',
                    items: [
                        {
                            label: 'Blog',
                            href: 'https://www.mitzu.io/blog',
                        },
                        {
                            label: 'Contact',
                            href: 'https://www.mitzu.io/contact-us',
                        },
                    ],
                },
            ],
            copyright: `Copyright © ${new Date().getFullYear()} Mitzu Inc. All rights reserved.`,
        },
        prism: {
            theme: prismThemes.github,
            darkTheme: prismThemes.dracula,
        },
    } satisfies Preset.ThemeConfig,
};

export default config;
