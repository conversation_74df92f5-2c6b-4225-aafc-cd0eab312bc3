import * as React from 'react';
import { ActionIcon, Button as MntButton } from '@mantine/core';

const Button = ({
    label,
    onClick,
    icon,
    variant,
}: {
    label: string;
    onClick: () => void;
    icon: string;
    variant: string | null;
}) => {
    return (
        <MntButton
            leftSection={icon}
            variant={variant || 'filled'}
            color="blue"
            onClick={onClick}
            size="xs"
        >
            {label}
        </MntButton>
    );
};

export default Button;

export const IconButton = ({
    onClick,
    icon,
    variant,
}: {
    onClick: () => void;
    icon: string;
    variant: string | null;
}) => {
    return (
        <ActionIcon variant={variant || 'filled'} color="blue" onClick={onClick} size="xs">
            {icon}
        </ActionIcon>
    );
};
