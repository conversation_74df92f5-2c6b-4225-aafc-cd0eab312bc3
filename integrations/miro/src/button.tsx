import { ActionIcon, Button as MntButton } from '@mantine/core';
// import { IconPhoto, IconDownload, IconArrowRight } from '@tabler/icons-react';
import { MdAdd } from 'react-icons/md';

const Button = ({ label, onClick, icon, variant }) => {
    return (
        <MntButton
            leftSection={icon}
            variant={variant || 'filled'}
            color="blue"
            onClick={onClick}
            size="xs"
        >
            {label}
        </MntButton>
    );
};

export default Button;

export const IconButton = ({ onClick, icon, variant }) => {
    return (
        <ActionIcon variant={variant || 'filled'} color="blue" onClick={onClick} size="xs">
            {icon}
        </ActionIcon>
    );
};
