export const MITZU_INSIGHT = 'mitzu_insight';
export const URL_ROOT = process.env.MIRO_APP_URL_ROOT || 'https://app.mitzu.io';
export const SRC_ROOT = process.env.MIRO_SRC_ROOT || '';
export const MITZU_ACCESS_TOKEN = 'mitzu_access_token';
export const MITZU_EMAIL = 'mitzu_email';
export const MITZU_ORG_NAME = 'mitzu_org_name';
export const MITZU_PROJECTS = 'mitzu_projects';

export type InsightData = {
    org_id: string;
    project_id: string;
    insight_id: string;
    title: string;
    image: string;
    error: string | null;
};

export type InsightMetaData = {
    org_id: string;
    project_id: string;
    insight_id: string;
};

export type Project = {
    id: string;
    name: string;
};

export type LoginData = {
    email: string;
    org_name: string;
    projects: Project[];
};

export type InsightTask = {
    task_id: string | null;
    state: string | null;
    error: string | null;
};

export const REFRESHING_INSIGHT_IMAGE =
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzUwIiBoZWlnaHQ9IjMwMCIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDcwMCA2MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IndoaXRlIi8+CjxzdHlsZT4KLmVycm9yIHsKICAgIGZvbnQ6IGJvbGQgMTFweCBzYW5zLXNlcmlmOwogICAgZmlsbDogcmVkOwp9Cjwvc3R5bGU+Cjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjUwJSIgeT0iNTAlIiBjbGFzcz0iZXJyb3IiPlJlZnJlc2hpbmcgaW5zaWdodDwvdGV4dD4KPC9zdmc+';
