export const MITZU_INSIGHT = 'mitzu_insight';
export const URL_ROOT = process.env.MIRO_APP_URL_ROOT || 'https://app.mitzu.io';
export const SRC_ROOT = process.env.MIRO_SRC_ROOT || '';
export const MITZU_ACCESS_TOKEN = 'mitzu_access_token';
export const MITZU_EMAIL = 'mitzu_email';
export const MITZU_ORG_NAME = 'mitzu_org_name';
export const MITZU_PROJECTS = 'mitzu_projects';

export type InsightData = {
    org_id: string;
    project_id: string;
    insight_id: string;
    title: string;
    image: string;
    error: string | null;
};

export type InsightMetaData = {
    org_id: string;
    project_id: string;
    insight_id: string;
};

export type Project = {
    id: string;
    name: string;
};

export type LoginData = {
    email: string;
    org_name: string;
    projects: Project[];
};
