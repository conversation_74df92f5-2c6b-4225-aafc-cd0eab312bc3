import { ImageProps } from '@mirohq/websdk-types';
import { InsightData, URL_ROOT } from './constants';
import { fetch_insight_data, get_insight_modal_link, duplicate_insight } from './mitzu_client';
import { MITZU_INSIGHT, InsightMetaData, MITZU_ACCESS_TOKEN } from '/src/constants.tsx';

export const addMitzuImage = async function (insight: InsightData, orig_image?: ImageProps) {
    const props = {
        url: insight.image,
        linkedTo:
            URL_ROOT + '/projects/' + insight.project_id + '/explore?sm=' + insight.insight_id,
        title: insight.title,
    };
    if (orig_image) {
        props.x = orig_image.x + 50;
        props.y = orig_image.y + 50;
    }
    const mitzuImage = await miro.board.createImage(props);
    await mitzuImage.setMetadata(MITZU_INSIGHT, {
        org_id: insight.org_id,
        project_id: insight.project_id,
        insight_id: insight.insight_id,
    });

    if (!orig_image) {
        await miro.board.viewport.zoomTo(mitzuImage);
    }
    return mitzuImage;
};

export const show_error_notification = async function (error: string) {
    await miro.board.notifications.showInfo(error);
};

export const reload_mitzu_image = async function (event) {
    const images = event.items;
    for (var i = 0; i < images.length; i++) {
        const metadata = await images[i].getMetadata();
        const meta_data: InsightMetaData = metadata[MITZU_INSIGHT];
        const insight_data = await fetch_insight_data(meta_data.insight_id);
        if (insight_data.error === null) {
            images[i].url = insight_data.image;
            images[i].title = insight_data.title;
            images[i].sync();
        } else {
            show_error_notification(insight_data.error);
        }
    }
};

export const open_in_modal = async function (event) {
    const image = event.items[0];
    const metadata = await image.getMetadata();
    const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];

    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    if (access_token === null) {
        show_error_notification('User is not logged in');
        return;
    }
    const url = get_insight_modal_link(insight_meta_data, access_token);
    const { waitForClose } = await miro.board.ui.openModal({
        url: url,
        fullscreen: true,
    });

    const reload_image_on_modal_close = async function () {
        await waitForClose();
        const insight_data = await fetch_insight_data(insight_meta_data.insight_id);
        image.url = insight_data.image;
        image.title = insight_data.title;
        image.sync();
    };

    reload_image_on_modal_close();
};

export const duplicate_insights = async function (event) {
    for (var i = 0; i < event.items.length; i++) {
        const orig_image = event.items[i];
        const metadata = await orig_image.getMetadata();
        const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];
        const new_insight = await duplicate_insight(insight_meta_data.insight_id);
        if (new_insight.error !== null) {
            show_error_notification(new_insight.error);
        } else {
            await addMitzuImage(new_insight, orig_image);
        }
    }
};
