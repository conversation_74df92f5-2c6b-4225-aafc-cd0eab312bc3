import { fetch_insight_data, get_insight_modal_link, get_insight_page_link, start_mitzu_session } from './mitzu_client';
import { MITZU_INSIGHT, InsightMetaData, MITZU_ACCESS_TOKEN } from '/src/constants.tsx';

const reload_mitzu_image = async function (event) {
  const images = event.items;
  for (var i = 0; i < images.length; i++) {
    const metadata = await images[i].getMetadata();
    const meta_data: InsightMetaData = metadata[MITZU_INSIGHT];
    const insight_data = await fetch_insight_data(meta_data.insight_id)
    images[i].url = insight_data.image;
    images[i].title = insight_data.title;
    images[i].sync();
  }
}

const open_in_mitzu = async function (event) {
  const image = event.items[0];
  const metadata = await image.getMetadata();
  const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];

  const url = get_insight_page_link(insight_meta_data)
  window.open(url, "_blank")
}

const open_in_modal = async function (event) {
  const image = event.items[0];
  const metadata = await image.getMetadata();
  const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];

  const url = get_insight_modal_link(insight_meta_data, MITZU_ACCESS_TOKEN)
  const { waitForClose } = await miro.board.ui.openModal({
    url: url,
    fullscreen: true,
  });

  const reload_image_on_modal_close = async function () {
    await waitForClose();
    const insight_data = await fetch_insight_data(insight_meta_data.insight_id)
    image.url = insight_data.image;
    image.title = insight_data.title;
    image.sync();
  }

  reload_image_on_modal_close();
}

export async function init() {
  await miro.board.ui.on('custom:reload-mitzu-insight', reload_mitzu_image);
  await miro.board.ui.on('custom:open-mitzu-insight', open_in_mitzu);
  await miro.board.ui.on('custom:open-mitzu-insight-modal', open_in_modal)

  await miro.board.experimental.action.register(
    {
      "event": "reload-mitzu-insight",
      "ui": {
        "label": {
          "en": "Reload result from Mitzu",
        },
        "icon": "arrow-clockwise-down-right",
        "description": "Loads the latest insight result stored in Mitzu",
      },
      "scope": "local",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );

  await miro.board.experimental.action.register(
    {
      "event": "open-mitzu-insight",
      "ui": {
        "label": {
          "en": "Open insight in new tab",
        },
        "icon": "arrow-box-out",
        "description": "Opens the original insight in a new tab",
      },
      "scope": "local",
      "selection": "single",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );

  await miro.board.experimental.action.register(
    {
      "event": "open-mitzu-insight-modal",
      "ui": {
        "label": {
          "en": "Open insight in modal",
        },
        "icon": "pen",
        "description": "Opens the original insight",
      },
      "scope": "local",
      "selection": "single",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );

  const auth_cookie = await window.cookieStore.get("mitzu-token-key");
  const user_info = await miro.board.getUserInfo();

  const data = await start_mitzu_session(user_info.email, auth_cookie.value);
  sessionStorage.setItem(MITZU_ACCESS_TOKEN, data.access_token);

  miro.board.ui.on('icon:click', async () => {
    await miro.board.ui.openPanel({ url: 'app.html' });
  });

}

init();
