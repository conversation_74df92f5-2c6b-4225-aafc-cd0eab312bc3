import { MITZU_INSIGHT, SRC_ROOT } from './constants';
import { open_in_modal, reload_mitzu_image, duplicate_insights } from './miro_actions';


export async function init() {
  await miro.board.ui.on('custom:reload-mitzu-insight', reload_mitzu_image);
  await miro.board.ui.on('custom:open-mitzu-insight-modal', open_in_modal)
  await miro.board.ui.on('custom:duplicate-mitzu-insight', duplicate_insights)

  await miro.board.experimental.action.register(
    {
      "event": "reload-mitzu-insight",
      "ui": {
        "label": {
          "en": "Reload result from Mitzu",
        },
        "icon": "arrow-clockwise-down-right",
        "description": "Loads the latest insight result stored in Mitzu",
      },
      "scope": "local",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );

  await miro.board.experimental.action.register(
    {
      "event": "open-mitzu-insight-modal",
      "ui": {
        "label": {
          "en": "Edit insight in modal",
        },
        "icon": "pen",
        "description": "Opens the original insight in a modal",
      },
      "scope": "local",
      "selection": "single",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );

  await miro.board.experimental.action.register(
    {
      "event": "duplicate-mitzu-insight",
      "ui": {
        "label": {
          "en": "Duplicate insight(s)",
        },
        "icon": "sticky-note-stack",
        "description": "Duplicates the insight(s) in Mitzu and adds them to this Miro board",
      },
      "scope": "local",
      "predicate": {
        "type": "image",
        ["metadata." + MITZU_INSIGHT]: {
          $exists: true,
        }
      },
      "contexts": {
        "item": {}
      }
    }
  );


  // Temporarily disable the auth flow with the iframes

  // const auth_cookie = await window.cookieStore.get("mitzu-token-key");
  // const user_info = await miro.board.getUserInfo();

  // const data = await start_mitzu_session(user_info.email, auth_cookie.value);
  // sessionStorage.setItem(MITZU_ACCESS_TOKEN, data.access_token);

  miro.board.ui.on('icon:click', async () => {
    await miro.board.ui.openPanel({ url: SRC_ROOT + 'app.html' });
  });

}

init();
