import {
    URL_ROOT,
    InsightData,
    InsightMetaData,
    InsightTask,
    MITZU_ACCESS_TOKEN,
} from './constants.tsx';

export const fetch_insight_data = async function (insight_id: string): Promise<InsightData> {
    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    const url = URL_ROOT + '/api/miro/insight?sm=' + insight_id + '&access_token=' + access_token;
    const options = {
        method: 'GET',
        headers: {
            Accept: 'application/json',
        },
    };
    try {
        const response = await fetch(url, options);
        return response.json().then((data) => {
            return {
                org_id: data.org_id,
                project_id: data.project_id,
                insight_id: insight_id,
                title: data.insight_title,
                image: data.insight_image,
                error: data.error,
            };
        });
    } catch (error) {
        let error_message: string;
        if (error instanceof Error) {
            error_message = error.message;
        } else {
            error_message = String(error);
        }
        return Promise.resolve({
            org_id: '',
            project_id: '',
            insight_id: insight_id,
            title: '',
            image: '',
            error: error_message,
        });
    }
};

export const create_new_insight = async function (
    project_id: string,
    metric_type: string
): Promise<InsightData> {
    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    const url =
        URL_ROOT +
        '/api/miro/create-insight?project_id=' +
        project_id +
        '&metric_type=' +
        metric_type +
        '&access_token=' +
        access_token;
    const options = {
        method: 'GET',
        headers: {
            Accept: 'application/json',
        },
    };
    try {
        const response = await fetch(url, options);
        return response.json().then((data) => {
            return {
                org_id: data.org_id,
                project_id: data.project_id,
                insight_id: data.insight_id,
                title: data.insight_title,
                image: data.insight_image,
                error: data.error,
            };
        });
    } catch (error) {
        let error_message: string;
        if (error instanceof Error) {
            error_message = error.message;
        } else {
            error_message = String(error);
        }
        return Promise.resolve({
            org_id: '',
            project_id: '',
            insight_id: '',
            title: '',
            image: '',
            error: error_message,
        });
    }
};

export const duplicate_insight = async function (insight_id: string): Promise<InsightData> {
    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    const url =
        URL_ROOT +
        '/api/miro/duplicate-insight?insight_id=' +
        insight_id +
        '&access_token=' +
        access_token;
    const options = {
        method: 'GET',
        headers: {
            Accept: 'application/json',
        },
    };
    try {
        const response = await fetch(url, options);
        return response.json().then((data) => {
            return {
                org_id: data.org_id,
                project_id: data.project_id,
                insight_id: data.insight_id,
                title: data.insight_title,
                image: data.insight_image,
                error: data.error,
            };
        });
    } catch (error) {
        let error_message: string;
        if (error instanceof Error) {
            error_message = error.message;
        } else {
            error_message = String(error);
        }
        return Promise.resolve({
            org_id: '',
            project_id: '',
            insight_id: '',
            title: '',
            image: '',
            error: error_message,
        });
    }
};

export const refresh_insight = async function (insight_id: string): Promise<InsightTask> {
    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    const url = URL_ROOT + '/api/miro/refresh-insight?access_token=' + access_token;
    const options = {
        method: 'POST',
        headers: {
            Accept: 'application/json',
        },
        body: JSON.stringify({
            insight_id: insight_id,
        }),
    };
    try {
        const response = await fetch(url, options);
        return response.json();
    } catch (error) {
        let error_message: string;
        if (error instanceof Error) {
            error_message = error.message;
        } else {
            error_message = String(error);
        }
        return Promise.resolve({
            task_id: null,
            state: null,
            error: error_message,
        });
    }
};

export const poll_task = async function (task_id: string): Promise<InsightTask> {
    const access_token = sessionStorage.getItem(MITZU_ACCESS_TOKEN);
    const url =
        URL_ROOT + '/api/miro/task-status?task_id=' + task_id + '&access_token=' + access_token;
    const options = {
        method: 'GET',
        headers: {
            Accept: 'application/json',
        },
    };
    try {
        const response = await fetch(url, options);
        return response.json();
    } catch (error) {
        let error_message: string;
        if (error instanceof Error) {
            error_message = error.message;
        } else {
            error_message = String(error);
        }
        return Promise.resolve({
            task_id: null,
            state: null,
            error: error_message,
        });
    }
};

export const get_insight_id_from_mitzu_link = function (link: string): string {
    const parts = link.split('?')[0].split('/');

    if (parts[parts.length - 2] == 'shared-insights') {
        return parts[parts.length - 1];
    }

    const query = link.split('?');
    if (query.length > 1) {
        const query_parts = query[1].split('&');
        for (let i = 0; i < query_parts.length; i++) {
            if (query_parts[i].startsWith('sm=')) {
                return query_parts[i].replace('sm=', '');
            }
        }
    }

    throw "It's not a saved insight link.";
};

export const get_insight_page_link = function (insight_meta_data: InsightMetaData): string {
    return (
        URL_ROOT +
        '/projects/' +
        insight_meta_data.project_id +
        '/explore?sm=' +
        insight_meta_data.insight_id
    );
};

export const get_insight_modal_link = function (
    insight_meta_data: InsightMetaData,
    access_token: string
): string {
    return (
        URL_ROOT +
        '/api/miro/embed-insight?insight_id=' +
        insight_meta_data.insight_id +
        '&access_token=' +
        access_token
    );
};

// temporarily commented out, it's not used right now
// export const start_mitzu_session = async function (email: string, token_key: string) {
//     const url =
//         URL_ROOT + '/api/miro/start-session?email=' + email + '&token_cache_key=' + token_key;
//     const options = {
//         method: 'GET',
//         headers: {
//             Accept: 'application/json',
//         },
//         credentials: 'same-origin',
//     };
//     try {
//         const response = await fetch(url, options);
//         return response.json();
//     } catch (error) {
//         let error_message: string;
//         if (error instanceof Error) {
//             error_message = error.message;
//         } else {
//             error_message = String(error);
//         }
//         return Promise.resolve({});
//     }
// };
