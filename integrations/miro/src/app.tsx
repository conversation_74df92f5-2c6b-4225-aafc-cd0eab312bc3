import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { InsightData, InsightMetaData, MITZU_INSIGHT, URL_ROOT } from './constants.tsx';
import Button from './button.tsx';
import {
    MantineProvider,
    TextInput,
    Alert,
    Title,
    Divider,
    Container,
    Stack,
    Group,
} from '@mantine/core';
import Icon from '../src/assets/logo.svg?react';
import '../src/assets/style.css';
import '@mantine/core/styles.css';
import { MdAdd, MdInfo, MdOpenInNew, MdRefresh, MdWarning } from 'react-icons/md';

import { fetch_insight_data, get_insight_id_from_shared_link } from './mitzu_client.jsx';

async function addMitzuImage(insight: InsightData) {
    const mitzuImage = await miro.board.createImage({
        url: insight.image,
        linkedTo:
            URL_ROOT + '/projects/' + insight.project_id + '/explore?sm=' + insight.insight_id,
        title: insight.title,
    });
    await mitzuImage.setMetadata(MITZU_INSIGHT, {
        org_id: insight.org_id,
        project_id: insight.project_id,
        insight_id: insight.insight_id,
    });

    await miro.board.viewport.zoomTo(mitzuImage);
}

const App: React.FC = () => {
    const [mitzuUrl, setMitzuURL] = React.useState('');
    const [addError, setAddError] = React.useState<string | null>(null);
    const [refreshError, setRefreshError] = React.useState<string[]>([]);

    const add_mitzu_to_board = async function () {
        try {
            const saved_metric_id = get_insight_id_from_shared_link(mitzuUrl);
            const insight_data = await fetch_insight_data(saved_metric_id);
            setAddError(insight_data.error);
            if (insight_data.error === null) {
                addMitzuImage(insight_data);
            }
        } catch (error) {
            setAddError(error);
            return;
        }
    };

    const reload_all_mitzu_insights = async function () {
        const images = await miro.board.get({ type: 'Image' });
        var errors = [];
        for (var i = 0; i < images.length; i++) {
            const metadata = await images[i].getMetadata();
            if (MITZU_INSIGHT in metadata) {
                const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];
                const insight_data = await fetch_insight_data(insight_meta_data.insight_id);

                if (insight_data.error === null) {
                    images[i].url = insight_data.image;
                    images[i].title = insight_data.title;
                } else {
                    images[i].url = insight_data.image; // should be an error image
                    errors.push(insight_data.error + ' (id: ' + insight_meta_data.insight_id + ')');
                }
                images[i].sync();
            }
        }
        setRefreshError(errors);
    };

    const renderErrorList = (errors: string[]) => {
        return (
            <Alert color="red">
                Could not refresh some insights:
                <ul>
                    {errors.map((error) => {
                        return <li>{error}</li>;
                    })}
                </ul>
                Please contact support at <a href="mailto:<EMAIL>"><EMAIL></a>.
            </Alert>
        );
    };

    const openMitzuApp = () => {
        window.open(URL_ROOT, '_blank');
    };

    return (
        <MantineProvider>
            <Container>
                <Group>
                    <Icon className="mitzu-icon" />
                    <Title order={1}>Mitzu Insight</Title>
                </Group>
                <Divider />
                <Stack style={{ marginTop: '10px' }}>
                    <Stack>
                        {/* <Title order={3}>Add insights</Title> */}
                        <TextInput
                            label="Paste public insight link"
                            value={mitzuUrl}
                            placeholder={URL_ROOT + '/shared-insights/.....'}
                            onChange={(event) => {
                                setMitzuURL(event.target.value);
                            }}
                            size="xs"
                        />
                        <Button
                            label="Add insight to board"
                            icon={<MdAdd />}
                            onClick={add_mitzu_to_board}
                        />
                        {addError ? <Alert color="red">{addError}</Alert> : <></>}
                    </Stack>

                    <Divider />
                    <Stack>
                        {/* <Title order={3}>Reload insights</Title> */}
                        <Button
                            label="Reload all insights"
                            icon={<MdRefresh />}
                            onClick={reload_all_mitzu_insights}
                        />
                        {refreshError.length > 0 ? renderErrorList(refreshError) : <></>}
                        <Alert variant="light" color="blue" icon={<MdInfo />}>
                            This won't execute any queries in your data warehouse! To recalculate
                            the results based on the most recent data please open the{' '}
                            <a href={URL_ROOT + '/'} target="_blank">
                                Mitzu app
                            </a>
                            .
                        </Alert>
                    </Stack>
                    <Divider />
                    <Button
                        label="Open Mitzu App"
                        icon={<MdOpenInNew />}
                        variant="white"
                        onClick={openMitzuApp}
                    />
                </Stack>
            </Container>
        </MantineProvider>
    );
};

const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);
