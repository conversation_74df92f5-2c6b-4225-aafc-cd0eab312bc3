import * as React from 'react';
import { createRoot } from 'react-dom/client';
import {
    InsightMetaData,
    LoginData,
    MITZU_ACCESS_TOKEN,
    MITZU_INSIGHT,
    URL_ROOT,
    MITZU_EMAIL,
    MITZU_ORG_NAME,
    MITZU_PROJECTS,
} from './constants.tsx';
import <PERSON><PERSON>, { IconButton } from './button.tsx';
import {
    MantineProvider,
    TextInput,
    Alert,
    Text,
    Title,
    Divider,
    Container,
    Stack,
    Group,
    Grid,
    GridCol,
    Select,
} from '@mantine/core';
import Icon from '../src/assets/logo.svg?react';
import '../src/assets/style.css';
import '@mantine/core/styles.css';
import {
    MdAdd,
    MdInfo,
    MdLogin,
    MdLogout,
    MdOpenInNew,
    MdRefresh,
    MdWarning,
} from 'react-icons/md';
import { Tb<PERSON>hartCohort, TbChartSankey } from 'react-icons/tb';

import { <PERSON><PERSON><PERSON>, ChartLine } from '@mynaui/icons-react';

import {
    create_new_insight,
    fetch_insight_data,
    get_insight_id_from_mitzu_link,
} from './mitzu_client.jsx';
import { addMitzuImage, open_in_modal } from './miro_actions.jsx';

const App: React.FC = () => {
    const [mitzuUrl, setMitzuURL] = React.useState('');
    const [addError, setAddError] = React.useState<string | null>(null);
    const [refreshError, setRefreshError] = React.useState<string[]>([]);

    const [loginData, setLoginData] = React.useState<LoginData | null>(null);
    const [loginError, setLoginError] = React.useState<string | null>(null);
    const [workspace, setWorkspace] = React.useState<string | null>(null);
    const [createInsightError, setCreateInsightError] = React.useState<string | null>(null);

    React.useEffect(() => {
        const email = sessionStorage.getItem(MITZU_EMAIL);
        const org_name = sessionStorage.getItem(MITZU_ORG_NAME);
        const projects = sessionStorage.getItem(MITZU_PROJECTS) || '[]';

        if (email && org_name && projects) {
            const parsed_projects = JSON.parse(projects);
            setLoginData({ email: email, org_name: org_name, projects: parsed_projects });
            if (parsed_projects.length > 0) {
                setWorkspace(parsed_projects[0].id);
            }
        }

        // this will catch and process the response from the login tab
        window.addEventListener('message', async function (event) {
            if (event.origin != URL_ROOT) {
                return;
            }

            const error = event.data.error;
            if (error) {
                setLoginError(error);
                return;
            }

            setLoginData({
                email: event.data.email,
                org_name: event.data.org_name,
                projects: event.data.projects,
            });
            if (event.data.projects.length > 0) {
                setWorkspace(event.data.projects[0].id);
            }
            sessionStorage.setItem(MITZU_EMAIL, event.data.email);
            sessionStorage.setItem(MITZU_ORG_NAME, event.data.org_name);
            sessionStorage.setItem(MITZU_PROJECTS, JSON.stringify(event.data.projects));
            sessionStorage.setItem(MITZU_ACCESS_TOKEN, event.data.access_token);
        });
    }, []);

    const add_mitzu_to_board = async function () {
        try {
            const saved_metric_id = get_insight_id_from_mitzu_link(mitzuUrl);
            const insight_data = await fetch_insight_data(saved_metric_id);
            setAddError(insight_data.error);
            if (insight_data.error === null) {
                addMitzuImage(insight_data);
            }
        } catch (error) {
            setAddError(error);
            return;
        }
    };

    const reload_all_mitzu_insights = async function () {
        const images = await miro.board.get({ type: 'Image' });
        var errors = [];
        for (var i = 0; i < images.length; i++) {
            const metadata = await images[i].getMetadata();
            if (MITZU_INSIGHT in metadata) {
                const insight_meta_data: InsightMetaData = metadata[MITZU_INSIGHT];
                const insight_data = await fetch_insight_data(insight_meta_data.insight_id);

                if (insight_data.error === null) {
                    images[i].url = insight_data.image;
                    images[i].title = insight_data.title;
                } else {
                    images[i].url = insight_data.image; // should be an error image
                    errors.push(insight_data.error + ' (id: ' + insight_meta_data.insight_id + ')');
                }
                images[i].sync();
            }
        }
        setRefreshError(errors);
    };

    const add_new_insight = async function (metric_type: string) {
        const insight_data = await create_new_insight(workspace, metric_type);

        setCreateInsightError(insight_data.error);
        if (insight_data.error === null) {
            const image = await addMitzuImage(insight_data);
            open_in_modal({ items: [image] });
        }
    };

    const renderErrorList = (errors: string[]) => {
        return (
            <Alert color="red">
                Could not refresh some insights:
                <ul>
                    {errors.map((error) => {
                        return <li>{error}</li>;
                    })}
                </ul>
                Please contact support at <a href="mailto:<EMAIL>"><EMAIL></a>.
            </Alert>
        );
    };

    const openMitzuApp = () => {
        window.open(URL_ROOT, '_blank');
    };

    const login = async () => {
        window.open(URL_ROOT + '/api/miro/login', '_blank');
    };

    const logout = () => {
        sessionStorage.removeItem(MITZU_EMAIL);
        sessionStorage.removeItem(MITZU_ORG_NAME);
        sessionStorage.removeItem(MITZU_ACCESS_TOKEN);
        setLoginData(null);
    };

    return (
        <MantineProvider>
            <Container>
                <Group>
                    <Icon className="mitzu-icon" />
                    <Title order={1}>Mitzu Insight</Title>
                </Group>
                <Divider />
                {loginData === null ? (
                    <Stack style={{ marginTop: '10px' }}>
                        <Button
                            label="Authorize with mitzu.io"
                            icon={<MdLogin />}
                            onClick={login}
                        />
                        {loginError ? (
                            <Alert color="red">
                                {loginError}
                                <Button
                                    label="Login at mitzu.io"
                                    icon={<MdOpenInNew />}
                                    variant="filled"
                                    onClick={openMitzuApp}
                                />
                            </Alert>
                        ) : (
                            <></>
                        )}
                        <Alert variant="light" color="yellow" icon={<MdWarning />}>
                            To add and update Mitzu insights on this board first you need to login
                            to Mitzu and then click the Login button.
                        </Alert>
                    </Stack>
                ) : (
                    <Stack style={{ marginTop: '10px' }}>
                        <Group>
                            <Text size="xs">
                                Logged in as {loginData?.email} ({loginData?.org_name})
                            </Text>
                            <IconButton icon={<MdLogout />} variant="subtle" onClick={logout} />
                        </Group>
                        <Divider />

                        <Stack>
                            <Text>Create new insight</Text>
                            <Select
                                label="Workspace"
                                data={(loginData.projects || []).map((p) => {
                                    return { value: p.id, label: p.name };
                                })}
                                value={workspace}
                                onChange={(selected_ws) => {
                                    if (selected_ws) {
                                        setWorkspace(selected_ws);
                                    }
                                }}
                                size="xs"
                                searchable={true}
                                clearable={false}
                                withCheckIcon={false}
                            />
                            <Grid>
                                <GridCol span={6}>
                                    <Button
                                        label="Segmentation"
                                        icon={
                                            <ChartLine
                                                className="insight-type-icon"
                                                style={{
                                                    backgroundColor: '#6eb363',
                                                }}
                                            />
                                        }
                                        variant="white"
                                        onClick={() => add_new_insight('segmentation')}
                                    />
                                </GridCol>
                                <GridCol span={6}>
                                    <Button
                                        label="Funnel"
                                        icon={
                                            <ChartBar
                                                className="insight-type-icon"
                                                style={{
                                                    backgroundColor: '#f76d7d',
                                                    transform: 'scaleX(-1)',
                                                }}
                                            />
                                        }
                                        variant="white"
                                        onClick={() => add_new_insight('conversion')}
                                    />
                                </GridCol>
                                <GridCol span={6}>
                                    <Button
                                        label="Retention"
                                        icon={
                                            <TbChartCohort
                                                className="insight-type-icon"
                                                style={{
                                                    backgroundColor: '#ea8756',
                                                }}
                                            />
                                        }
                                        variant="white"
                                        onClick={() => add_new_insight('retention')}
                                    />
                                </GridCol>
                                <GridCol span={6}>
                                    <Button
                                        label="Journey"
                                        icon={
                                            <TbChartSankey
                                                className="insight-type-icon"
                                                style={{
                                                    backgroundColor: '#ca91e8',
                                                }}
                                            />
                                        }
                                        variant="white"
                                        onClick={() => add_new_insight('journey')}
                                    />
                                </GridCol>
                            </Grid>
                            {createInsightError !== null ? (
                                <Alert color="red">{createInsightError}</Alert>
                            ) : (
                                <></>
                            )}
                        </Stack>

                        <Divider />
                        <Stack>
                            <Text>Add insight from link</Text>
                            <TextInput
                                value={mitzuUrl}
                                placeholder={URL_ROOT + '/.../explore?sm=...'}
                                onChange={(event) => {
                                    setMitzuURL(event.target.value);
                                }}
                                size="xs"
                            />
                            <Button
                                label="Add insight to board"
                                icon={<MdAdd />}
                                onClick={add_mitzu_to_board}
                            />
                            {addError ? <Alert color="red">{addError}</Alert> : <></>}
                        </Stack>

                        <Divider />
                        <Stack>
                            <Button
                                label="Reload all insights"
                                icon={<MdRefresh />}
                                onClick={reload_all_mitzu_insights}
                            />
                            {refreshError.length > 0 ? renderErrorList(refreshError) : <></>}
                            <Alert variant="light" color="blue" icon={<MdInfo />}>
                                This won't execute any queries in your data warehouse! To
                                recalculate the results based on the most recent data please open
                                the{' '}
                                <a href={URL_ROOT + '/'} target="_blank">
                                    Mitzu app
                                </a>
                                .
                            </Alert>
                        </Stack>
                        <Divider />
                        <Button
                            label="Open Mitzu App"
                            icon={<MdOpenInNew />}
                            variant="white"
                            onClick={openMitzuApp}
                        />
                    </Stack>
                )}
            </Container>
        </MantineProvider>
    );
};

const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);
