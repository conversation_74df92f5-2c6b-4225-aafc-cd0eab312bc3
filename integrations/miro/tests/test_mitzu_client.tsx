'use strict';
import { URL_ROOT } from '../src/constants.tsx';
import { get_insight_page_link, get_insight_id_from_mitzu_link } from '../src/mitzu_client.tsx';

describe('get_insight_page_link', () => {
    it('returns a proper url', () => {
        expect(
            get_insight_page_link({
                project_id: 'proj-a',
                org_id: 'org-a',
                insight_id: 'insight-id',
            })
        ).toBe(URL_ROOT + '/projects/proj-a/explore?sm=insight-id');
    });
});

describe('get_insight_id_from_mitzu_link', () => {
    it("throws error when it's not a public url", () => {
        expect(() => {
            get_insight_id_from_mitzu_link('https://google.com');
        }).toThrow("It's not a saved insight link.");
    });

    it('returns the insight id from the public url', () => {
        expect(
            get_insight_id_from_mitzu_link('https://dev.mitzu.io/shared-insights/80bebd117b80')
        ).toBe('80bebd117b80');
    });

    it('returns the insight id from the public url ignoring the url params', () => {
        expect(
            get_insight_id_from_mitzu_link(
                'https://dev.mitzu.io/shared-insights/80bebd117b80?embed=true'
            )
        ).toBe('80bebd117b80');
    });

    it('returns the insight id from the explore url', () => {
        expect(
            get_insight_id_from_mitzu_link(
                'http://localhost:8082/projects/sample_project_id/explore?sm=8d418ddd29a2'
            )
        ).toBe('8d418ddd29a2');
    });
});
