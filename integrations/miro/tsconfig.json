{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["esnext", "dom"], "jsx": "preserve", "moduleResolution": "node", "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "./node_modules/@mirohq"], "allowJs": true, "incremental": true, "isolatedModules": true}, "include": ["src", "pages", "*.ts"], "exclude": ["node_modules"]}