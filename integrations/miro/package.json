{"name": "mitzu-miro-app", "version": "0.1.0", "license": "MIT", "scripts": {"test": "jest", "start": "vite", "build:prod": "MIRO_APP_URL_ROOT='https://app.mitzu.io' MIRO_SRC_ROOT='/prod/' vite build --base=./", "build:dev": "MIRO_APP_URL_ROOT='https://dev.mitzu.io' MIRO_SRC_ROOT='/dev/' vite build --base=./", "serve": "vite preview"}, "dependencies": {"@mantine/core": "^8.0.0", "@mantine/hooks": "^8.0.0", "@mynaui/icons-react": "^0.3.3", "mirotone": "5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-typescript": "^7.27.1", "@mirohq/websdk-types": "latest", "@types/node": "^18.8.2", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@vitejs/plugin-react": "^2.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "4.9.5", "vite": "3.0.3"}}