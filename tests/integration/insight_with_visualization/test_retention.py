from dataclasses import replace
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from unittest.mock import MagicMock

import pandas as pd
import pytest

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.service.metric_converter as SMC
from mitzu.constants import N_PER_A
from mitzu.visualization.visualizer import Visualizer
from tests.helper import assert_dataframe_equals
from tests.integration.connection import get_connections
from tests.integration.data import (
    END_DATE,
    START_DATE,
    get_ephemeral_discovered_project,
)
from tests.integration.helper import assert_visualization

# register all tests with the matrix mark
pytestmark = pytest.mark.matrix

target_connections = get_connections(only=["psql"])


def ucc(val: str) -> Dict[str, str]:
    return {
        "ucc": val,
    }


retention_test_tables: Dict[str, List] = {
    "visualization_ret": [
        # user_id_1
        ("event_1", datetime(2023, 1, 1, 20, 0), "user_id_1", ucc("us")),
    ]
    + [
        ("event_2", datetime(2023, 1, d, 20, 0), "user_id_1", ucc("us"))
        for d in [7, 14, 21]
    ]
    + [
        # user_id_2 churned after total 2 events
        ("event_1", datetime(2023, 1, 2, 20, 0), "user_id_2", ucc("gb")),
    ]
    + [
        ("event_2", datetime(2023, 1, d, 20, 0), "user_id_2", ucc("gb"))
        for d in [7, 21]
    ]
    + [
        # user_id_3
        ("event_1", datetime(2023, 1, 4, 20, 0), "user_id_3", ucc("gb")),
    ]
    + [
        ("event_2", datetime(2023, 1, d, 20, 0), "user_id_3", ucc("gb"))
        for d in [5, 8, 11, 15]
    ],
}

event_1_path = M.EventNamePath("visualization_ret", "event_1")
event_2_path = M.EventNamePath("visualization_ret", "event_2")

event_1_segment = replace(
    WM.WebappSimpleSegment.create(event_1_path, None),
    aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
)

event_2_segment = replace(
    WM.WebappSimpleSegment.create(event_2_path, None),
    aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
)

complex_segment_with_group_by = replace(
    WM.WebappComplexSegment.create(
        event_1_segment, M.BinaryOperator.OR, event_2_segment
    ),
    aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
    group_by=[
        M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
        M.EventFieldPath("visualization_ret", "event_1", "ucc"),
    ],
)
metric_context = MC.MetricContext.empty()
comparison_1_day = M.ComparisonOptions(M.TimeWindow(1, M.TimeGroup.DAY))


def get_test_context(
    connection: M.Connection,
    segments: List[WM.WebappSegment],
    time_group: M.TimeGroup,
    chart_type: M.SimpleChartType,
    comparison: Optional[M.ComparisonOptions],
) -> Tuple[pd.DataFrame, Visualizer]:
    if time_group == M.TimeGroup.TOTAL and comparison is not None:
        raise ValueError("Unsupported scenario: total time group with comparison")

    if comparison is not None and chart_type == M.SimpleChartType.HEATMAP:
        raise ValueError("Unsupported scenario: heatmap with comparison")

    adapter, discovered_project = get_ephemeral_discovered_project(
        connection, retention_test_tables
    )
    webapp_metric = WM.WebappMetric(
        M.MetricType.RETENTION,
        segments=segments,
        config=WM.WebappMetricConfig.from_model(
            M.RetentionMetricConfig(
                start_dt=START_DATE,
                end_dt=END_DATE,
                time_group=time_group,
                chart_type=chart_type,
                comparison_options=comparison,
                retention_indices=[1, 2],
                aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
            )
        ),
        label_mapping={
            "Week 1": "First week",
            "Week 1 - Event 1 (1), gb": "First Event 1 in GB",
            "Week 1 - Event 1 (1), gb (previous)": "First Event 1 in GB (old)",
        },
    )
    metric_converter = SMC.MetricConverter(discovered_project, MagicMock(), MagicMock())
    metric = metric_converter.convert_metric(webapp_metric)

    result_df = adapter.get_df(metric)

    metric_context = MC.MetricContext.empty()
    visualizer = Visualizer.create_visualizer(
        result_df, webapp_metric, discovered_project.project, metric_context, metric
    )

    return result_df, visualizer


@pytest.mark.parametrize("connection", target_connections)
def test_retention_monthly_tg_no_group_by(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            event_1_segment,
            replace(event_2_segment, title="segment title"),
        ],
        time_group=M.TimeGroup.MONTH,
        chart_type=M.SimpleChartType.LINE,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 1, 1),
            ],
            "_group": 2 * [None],
            "_ret_index": [1, 2],
            "_user_count_1": [3, 3],
            "_user_count_2": [2, 2],
            "_agg_value": [66.6666, 66.6666],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [
                    datetime(2023, 1, 1),
                    datetime(2023, 1, 1),
                ],
                "_color": [
                    "First week",
                    "Week 2",
                ],
                "_text": ["66.7%", "66.7%"],
                "y": [66.67, 66.67],
                "_group": 2 * [N_PER_A],
                "_og_group": 2 * [None],
                "_user_count_1": [3, 3],
                "_user_count_2": [2, 2],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=None,
            event_value="First week",
            event_label="2023-01-01",
            title_label="Retention rate",
            title_value="66.67 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": ["First week", "Week 2"],
                "Retention period": ["Week 1", "Week 2"],
                "2023-01-01": [66.67, 66.67],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_monthly_tg_with_group_by(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            complex_segment_with_group_by,
            event_2_segment,
        ],
        time_group=M.TimeGroup.MONTH,
        chart_type=M.SimpleChartType.LINE,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 8 * [datetime(2023, 1, 1)],
            "_group": [
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###us",
                "_subsegment_index_0_1###us",
            ],
            "_ret_index": 4 * [1, 2],
            "_user_count_1": [2, 2, 1, 1, 2, 2, 1, 1],
            "_user_count_2": [1, 1, 1, 1, 2, 0, 1, 0],
            "_agg_value": [50, 50, 100, 100, 100, 0, 100, 0],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": 8 * [datetime(2023, 1, 1)],
                "_color": [
                    "First Event 1 in GB",
                    "Week 1 - Event 1 (1), us",
                    "Week 1 - Event 2 (2), gb",
                    "Week 1 - Event 2 (2), us",
                    "Week 2 - Event 1 (1), gb",
                    "Week 2 - Event 1 (1), us",
                    "Week 2 - Event 2 (2), gb",
                    "Week 2 - Event 2 (2), us",
                ],
                "_text": [
                    "50.0%",
                    "100.0%",
                    "100.0%",
                    "100.0%",
                    "50.0%",
                    "100.0%",
                    "",
                    "",
                ],
                "y": [50, 100, 100, 100, 50, 100, 0, 0],
                "_ret_index": [1, 1, 1, 1, 2, 2, 2, 2],
                "_group": [
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                ],
                "_og_group": [
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                ],
                "_user_count_1": [2, 1, 2, 1, 2, 1, 2, 1],
                "_user_count_2": [1, 1, 2, 1, 1, 1, 0, 0],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=["_subsegment_index_0_0", "gb"],
            event_value="First Event 1 in GB",
            event_label="2023-01-01",
            title_label="Retention rate",
            title_value="50 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [
                    "Week 2 - Event 2 (2), gb",
                    "Week 2 - Event 2 (2), us",
                    "First Event 1 in GB",
                    "Week 2 - Event 1 (1), gb",
                    "Week 1 - Event 1 (1), us",
                    "Week 1 - Event 2 (2), gb",
                    "Week 1 - Event 2 (2), us",
                    "Week 2 - Event 1 (1), us",
                ],
                "Retention period": [
                    "Week 2",
                    "Week 2",
                    "Week 1",
                    "Week 2",
                    "Week 1",
                    "Week 1",
                    "Week 1",
                    "Week 2",
                ],
                "2023-01-01": [0, 0, 50, 50, 100, 100, 100, 100],
                "Subsegment": [
                    "Event 2 (2)",
                    "Event 2 (2)",
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 2 (2)",
                    "Event 2 (2)",
                    "Event 1 (1)",
                ],
                "Ucc": ["gb", "us", "gb", "gb", "us", "gb", "us", "us"],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_total_tg_no_group_by(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            event_1_segment,
            replace(event_2_segment, title="segment title"),
        ],
        time_group=M.TimeGroup.TOTAL,
        chart_type=M.SimpleChartType.LINE,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 2 * [None],
            "_group": 2 * [None],
            "_ret_index": [1, 2],
            "_user_count_1": [3, 3],
            "_user_count_2": [2, 2],
            "_agg_value": [66.6666, 66.6666],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [1, 2],
                "_datetime": 2 * [None],
                "_color": 2 * [N_PER_A],
                "_text": ["66.7%", "66.7%"],
                "y": [66.67, 66.67],
                "_og_group": 2 * [None],
                "_user_count_1": [3, 3],
                "_user_count_2": [2, 2],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=None,
            event_label=None,
            event_value="Week 1",
            title_label="Retention rate",
            title_value="66.67 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [N_PER_A],
                "Week 1": [66.67],
                "Week 2": [66.67],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_total_tg_with_group_by(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            complex_segment_with_group_by,
            event_2_segment,
        ],
        time_group=M.TimeGroup.TOTAL,
        chart_type=M.SimpleChartType.LINE,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 8 * [None],
            "_group": [
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###us",
                "_subsegment_index_0_1###us",
            ],
            "_ret_index": 4 * [1, 2],
            "_user_count_1": [2, 2, 1, 1, 2, 2, 1, 1],
            "_user_count_2": [1, 1, 1, 1, 2, 0, 1, 0],
            "_agg_value": [50, 50, 100, 100, 100, 0, 100, 0],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [1, 1, 1, 1, 2, 2, 2, 2],
                "_color": [
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                ],
                "_text": [
                    "50.0%",
                    "100.0%",
                    "100.0%",
                    "100.0%",
                    "50.0%",
                    "100.0%",
                    "",
                    "",
                ],
                "y": [50, 100, 100, 100, 50, 100, 0, 0],
                "_datetime": 8 * [None],
                "_og_group": [
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                ],
                "_user_count_1": [2, 1, 2, 1, 2, 1, 2, 1],
                "_user_count_2": [1, 1, 2, 1, 1, 1, 0, 0],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=["_subsegment_index_0_0", "gb"],
            event_label="Event 1 (1), gb",
            event_value="Week 1",
            title_label="Retention rate",
            title_value="50 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                ],
                "Week 1": [50, 100, 100, 100],
                "Week 2": [50, 100, 0, 0],
                "Subsegment": [
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 2 (2)",
                    "Event 2 (2)",
                ],
                "Ucc": ["gb", "us", "gb", "us"],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_monthly_tg_no_group_by_heatmap(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            event_1_segment,
            replace(event_2_segment, title="segment title"),
        ],
        time_group=M.TimeGroup.MONTH,
        chart_type=M.SimpleChartType.HEATMAP,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 1, 1),
            ],
            "_group": 2 * [None],
            "_ret_index": [1, 2],
            "_user_count_1": [3, 3],
            "_user_count_2": [2, 2],
            "_agg_value": [66.6666, 66.6666],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [1, 2],
                "_color": [
                    "2023-01-01",
                    "2023-01-01",
                ],
                "_text": ["66.7%", "66.7%"],
                "y": [66.67, 66.67],
                "_group": 2 * [N_PER_A],
                "_og_group": 2 * [None],
                "_user_count_1": [3, 3],
                "_user_count_2": [2, 2],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=None,
            event_value="Week 1",
            event_label="2023-01-01",
            title_label="Retention rate",
            title_value="66.67 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Retention period": ["Week 1", "Week 2"],
                "2023-01-01": [66.67, 66.67],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_total_tg_no_group_by_heatmap(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            event_1_segment,
            replace(event_2_segment, title="segment title"),
        ],
        time_group=M.TimeGroup.TOTAL,
        chart_type=M.SimpleChartType.HEATMAP,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 2 * [None],
            "_group": 2 * [None],
            "_ret_index": [1, 2],
            "_user_count_1": [3, 3],
            "_user_count_2": [2, 2],
            "_agg_value": [66.6666, 66.6666],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [1, 2],
                "_datetime": 2 * [None],
                "_color": 2 * [N_PER_A],
                "_text": ["66.7%", "66.7%"],
                "y": [66.67, 66.67],
                "_og_group": 2 * [None],
                "_user_count_1": [3, 3],
                "_user_count_2": [2, 2],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=None,
            event_label=None,
            event_value="Week 1",
            title_label="Retention rate",
            title_value="66.67 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [N_PER_A],
                "Week 1": [66.67],
                "Week 2": [66.67],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_total_tg_with_group_by_heatmap(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            complex_segment_with_group_by,
            event_2_segment,
        ],
        time_group=M.TimeGroup.TOTAL,
        chart_type=M.SimpleChartType.HEATMAP,
        comparison=None,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 8 * [None],
            "_group": [
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###us",
                "_subsegment_index_0_1###us",
            ],
            "_ret_index": 4 * [1, 2],
            "_user_count_1": [2, 2, 1, 1, 2, 2, 1, 1],
            "_user_count_2": [1, 1, 1, 1, 2, 0, 1, 0],
            "_agg_value": [50, 50, 100, 100, 100, 0, 100, 0],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [1, 1, 1, 1, 2, 2, 2, 2],
                "_color": [
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                ],
                "_text": [
                    "50.0%",
                    "100.0%",
                    "100.0%",
                    "100.0%",
                    "50.0%",
                    "100.0%",
                    "",
                    "",
                ],
                "y": [50, 100, 100, 100, 50, 100, 0, 0],
                "_datetime": 8 * [None],
                "_og_group": [
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                    "_subsegment_index_0_0###gb",
                    "_subsegment_index_0_0###us",
                    "_subsegment_index_0_1###gb",
                    "_subsegment_index_0_1###us",
                ],
                "_user_count_1": [2, 1, 2, 1, 2, 1, 2, 1],
                "_user_count_2": [1, 1, 2, 1, 1, 1, 0, 0],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=["_subsegment_index_0_0", "gb"],
            event_label="Event 1 (1), gb",
            event_value="Week 1",
            title_label="Retention rate",
            title_value="50 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=None,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [
                    "Event 1 (1), gb",
                    "Event 1 (1), us",
                    "Event 2 (2), gb",
                    "Event 2 (2), us",
                ],
                "Week 1": [50, 100, 100, 100],
                "Week 2": [50, 100, 0, 0],
                "Subsegment": [
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 2 (2)",
                    "Event 2 (2)",
                ],
                "Ucc": ["gb", "us", "gb", "us"],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_monthly_tg_no_group_by_with_comparison(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            event_1_segment,
            replace(event_2_segment, title="segment title"),
        ],
        time_group=M.TimeGroup.MONTH,
        chart_type=M.SimpleChartType.LINE,
        comparison=comparison_1_day,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 1, 1),
            ],
            "_group": 2 * [N_PER_A],
            "_og_group": 2 * [None],
            "_ret_index": [1, 2],
            "_user_count_1": [3, 3],
            "_user_count_2": [2, 2],
            "_agg_value": [66.6666, 66.6666],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": [
                    datetime(2023, 1, 1),
                    datetime(2023, 1, 1),
                    datetime(2023, 1, 2),
                    datetime(2023, 1, 2),
                ],
                "_original_datetime": 4 * [datetime(2023, 1, 1)],
                "_color": [
                    "First week",
                    "Week 2",
                    "Week 1 (previous)",
                    "Week 2 (previous)",
                ],
                "_is_comparison": [False, False, True, True],
                "_text": 4 * ["66.7%"],
                "y": 4 * [66.67],
                "_group": 4 * [N_PER_A],
                "_og_group": 4 * [N_PER_A],
                "_user_count_1": 4 * [3],
                "_user_count_2": 4 * [2],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=None,
            event_value="Week 1 (previous)",
            event_label="2023-01-01",
            title_label="Retention rate",
            title_value="66.67 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=True,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": [
                    "First week",
                    "Week 2",
                    "Week 1 (previous)",
                    "Week 2 (previous)",
                ],
                "Retention period": [
                    "Week 1",
                    "Week 2",
                    "Week 1 (previous)",
                    "Week 2 (previous)",
                ],
                "2023-01-01": [66.67, 66.67, None, None],
                "2023-01-02": [None, None, 66.67, 66.67],
            }
        ),
    )


@pytest.mark.parametrize("connection", target_connections)
def test_retention_monthly_tg_with_group_by_with_comparison(connection: M.Connection):
    result_df, visualizer = get_test_context(
        connection,
        segments=[
            complex_segment_with_group_by,
            event_2_segment,
        ],
        time_group=M.TimeGroup.MONTH,
        chart_type=M.SimpleChartType.LINE,
        comparison=comparison_1_day,
    )
    expected_df = pd.DataFrame(
        {
            "_datetime": 8 * [datetime(2023, 1, 1)],
            "_group": [
                "Event 1 (1)###gb",
                "Event 1 (1)###gb",
                "Event 1 (1)###us",
                "Event 1 (1)###us",
                "Event 2 (2)###gb",
                "Event 2 (2)###gb",
                "Event 2 (2)###us",
                "Event 2 (2)###us",
            ],
            "_og_group": [
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###gb",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_0###us",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###gb",
                "_subsegment_index_0_1###us",
                "_subsegment_index_0_1###us",
            ],
            "_ret_index": 4 * [1, 2],
            "_user_count_1": [2, 2, 1, 1, 2, 2, 1, 1],
            "_user_count_2": [1, 1, 1, 1, 2, 0, 1, 0],
            "_agg_value": [50, 50, 100, 100, 100, 0, 100, 0],
        }
    )
    assert_dataframe_equals(result_df, expected_df)

    color_col = [
        "First Event 1 in GB",
        "Week 1 - Event 1 (1), us",
        "Week 1 - Event 2 (2), gb",
        "Week 1 - Event 2 (2), us",
        "Week 2 - Event 1 (1), gb",
        "Week 2 - Event 1 (1), us",
        "Week 2 - Event 2 (2), gb",
        "Week 2 - Event 2 (2), us",
        "First Event 1 in GB (old)",
        "Week 1 - Event 1 (1), us (previous)",
        "Week 1 - Event 2 (2), gb (previous)",
        "Week 1 - Event 2 (2), us (previous)",
        "Week 2 - Event 1 (1), gb (previous)",
        "Week 2 - Event 1 (1), us (previous)",
        "Week 2 - Event 2 (2), gb (previous)",
        "Week 2 - Event 2 (2), us (previous)",
    ]
    assert_visualization(
        visualizer,
        expected_chart_df=pd.DataFrame(
            {
                "x": 8 * [datetime(2023, 1, 1)] + 8 * [datetime(2023, 1, 2)],
                "_original_datetime": 16 * [datetime(2023, 1, 1)],
                "_color": color_col,
                "_text": 2
                * ["50.0%", "100.0%", "100.0%", "100.0%", "50.0%", "100.0%", "", ""],
                "y": 2 * [50, 100, 100, 100, 50, 100, 0, 0],
                "_ret_index": 2 * [1, 1, 1, 1, 2, 2, 2, 2],
                "_is_comparison": 8 * [False] + 8 * [True],
                "_group": [
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                ],
                "_og_group": [
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                    "Event 1 (1)###gb",
                    "Event 1 (1)###us",
                    "Event 2 (2)###gb",
                    "Event 2 (2)###us",
                ],
                "_user_count_1": 2 * [2, 1, 2, 1, 2, 1, 2, 1],
                "_user_count_2": 2 * [1, 1, 2, 1, 1, 1, 0, 0],
            }
        ),
        expected_tooltip_json=WM.TooltipCustomJson(
            segment_index=-1,
            break_down=[
                "Event 1 (1)",
                "gb",
            ],  # BUG: should be _subsegment_index_0_1
            event_value="First Event 1 in GB (old)",
            event_label="2023-01-01",
            title_label="Retention rate",
            title_value="50 %",
            subtitle=None,
            version=1,
            normalized_percentage=None,
            unique_field_label="Unique users",
            retention_index=1,
            is_comparison=True,
            direction=None,
        ),
        expected_table_df=pd.DataFrame(
            {
                "Label": color_col,
                "Retention period": [
                    "Week 1",
                    "Week 1",
                    "Week 1",
                    "Week 1",
                    "Week 2",
                    "Week 2",
                    "Week 2",
                    "Week 2",
                    "Week 1 (previous)",
                    "Week 1 (previous)",
                    "Week 1 (previous)",
                    "Week 1 (previous)",
                    "Week 2 (previous)",
                    "Week 2 (previous)",
                    "Week 2 (previous)",
                    "Week 2 (previous)",
                ],
                "2023-01-01": [
                    "50.0 %",
                    "100.0 %",
                    "100.0 %",
                    "100.0 %",
                    "50.0 %",
                    "100.0 %",
                    "0.0 %",
                    "0.0 %",
                ]
                + 8 * [None],
                "2023-01-02": 8 * [None]
                + [
                    "50.0 %",
                    "100.0 %",
                    "100.0 %",
                    "100.0 %",
                    "50.0 %",
                    "100.0 %",
                    "0.0 %",
                    "0.0 %",
                ],
                "Subsegment": 2
                * [
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 2 (2)",
                    "Event 2 (2)",
                    "Event 1 (1)",
                    "Event 1 (1)",
                    "Event 2 (2)",
                    "Event 2 (2)",
                ],
                "Ucc": 2 * ["gb", "us", "gb", "us", "gb", "us", "gb", "us"],
            }
        ),
    )
