WITH anon_5 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM public.funnel_agg AS t1
   WHERE t1.event_name = 'evt_2'),
     anon_4 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property
   FROM anon_5
   WHERE anon_5._cte_datetime >= '2023-01-01 00:00:00'
     AND anon_5._cte_datetime <= '2023-06-02 00:00:00'),
     anon_6 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM public.funnel_agg AS t1
   WHERE t1.event_name = 'evt_1'),
     anon_3 AS
  (SELECT anon_6._cte_user_id AS _cte_user_id,
          anon_6._cte_datetime AS _cte_datetime,
          anon_6._cte_group AS _cte_group,
          anon_6._cte_event_property AS _cte_event_property
   FROM anon_6
   WHERE anon_6._cte_datetime >= '2022-12-01 00:00:00'
     AND anon_6._cte_datetime <= '2023-06-02 00:00:00'),
     anon_7 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM public.funnel_agg AS t1
   WHERE t1.event_name = 'evt_1'),
     anon_2 AS
  (SELECT anon_7._cte_user_id AS _cte_user_id,
          anon_7._cte_datetime AS _cte_datetime,
          anon_7._cte_group AS _cte_group,
          anon_7._cte_event_property AS _cte_event_property
   FROM anon_7
   WHERE anon_7._cte_datetime >= '2022-12-01 00:00:00'
     AND anon_7._cte_datetime <= '2023-06-02 00:00:00'),
     anon_1 AS
  (SELECT NULL AS _cte_group,
          anon_2._cte_user_id AS _cte_user_id_0,
          anon_2._cte_datetime AS _cte_datetime_0,
          anon_3._cte_user_id AS _cte_user_id_1,
          anon_3._cte_datetime AS _cte_datetime_1,
          anon_4._cte_user_id AS _cte_user_id_2,
          anon_4._cte_datetime AS _cte_datetime_2
   FROM anon_4
   LEFT OUTER JOIN anon_3 ON anon_4._cte_user_id = anon_3._cte_user_id
   AND anon_3._cte_datetime < anon_4._cte_datetime
   AND anon_3._cte_datetime >= anon_4._cte_datetime + interval '-1 month'
   LEFT OUTER JOIN anon_2 ON anon_3._cte_user_id = anon_2._cte_user_id
   AND anon_2._cte_datetime < anon_3._cte_datetime
   AND anon_2._cte_datetime >= anon_4._cte_datetime + interval '-1 month')
SELECT NULL AS _datetime,
       NULL AS _group,
       count(DISTINCT anon_1._cte_user_id_0) AS _user_count_1,
       (count(DISTINCT anon_1._cte_user_id_0) * 100.0) / nullif(count(DISTINCT anon_1._cte_user_id_2), 0) AS _agg_value_1,
       count(DISTINCT anon_1._cte_user_id_1) AS _user_count_2,
       (count(DISTINCT anon_1._cte_user_id_1) * 100.0) / nullif(count(DISTINCT anon_1._cte_user_id_2), 0) AS _agg_value_2,
       count(DISTINCT anon_1._cte_user_id_2) AS _user_count_3,
       (count(DISTINCT anon_1._cte_user_id_2) * 100.0) / nullif(count(DISTINCT anon_1._cte_user_id_2), 0) AS _agg_value_3
FROM anon_1
GROUP BY 1,
         2