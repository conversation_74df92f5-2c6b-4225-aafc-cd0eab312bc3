WITH anon_5 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          coalesce(CAST(t1.user_country_code AS VARCHAR), '<n/a>') AS _cte_group,
          NULL AS _cte_event_property
   FROM public.tbl_1 AS t1
   WHERE t1.event_name = 'evt_1'
     AND t1.user_country_code IN ('us',
                                  'gb')),
     anon_3 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property
   FROM anon_5
   WHERE anon_5._cte_datetime >= date_trunc('MONTH', '2023-01-01 00:00:00')
     AND anon_5._cte_datetime <= '2023-06-02 00:00:00'),
     anon_6 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM public.tbl_1 AS t1
   WHERE t1.event_name = 'evt_2'),
     anon_4 AS
  (SELECT anon_6._cte_user_id AS _cte_user_id,
          anon_6._cte_datetime AS _cte_datetime,
          anon_6._cte_group AS _cte_group,
          anon_6._cte_event_property AS _cte_event_property
   FROM anon_6
   WHERE anon_6._cte_datetime >= date_trunc('MONTH', '2023-01-01 00:00:00')
     AND anon_6._cte_datetime <= '2023-06-09 00:00:00'),
     anon_2 AS
  (SELECT anon_3._cte_group AS _cte_group,
          anon_3._cte_user_id AS _cte_user_id_0,
          anon_3._cte_datetime AS _cte_datetime_0,
          anon_4._cte_user_id AS _cte_user_id_1,
          anon_4._cte_datetime AS _cte_datetime_1
   FROM anon_3
   LEFT OUTER JOIN anon_4 ON anon_3._cte_user_id = anon_4._cte_user_id
   AND anon_4._cte_datetime > anon_3._cte_datetime
   AND anon_4._cte_datetime <= anon_3._cte_datetime + interval '1 week'),
     anon_1 AS
  (SELECT CASE
              WHEN (count(anon_2._cte_user_id_1) OVER (PARTITION BY anon_2._cte_user_id_0) = 0) THEN anon_2._cte_user_id_0
              ELSE NULL
          END AS _agg_value_1
   FROM anon_2)
SELECT anon_1._agg_value_1 AS ___dim_id
FROM anon_1
WHERE anon_1._agg_value_1 IS NOT NULL
LIMIT 20001