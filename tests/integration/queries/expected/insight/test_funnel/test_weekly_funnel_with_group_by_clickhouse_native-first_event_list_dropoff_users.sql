WITH anon_4 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          coalesce(CAST(t1.user_country_code AS Nullable(VARCHAR())), '<n/a>') AS _cte_group,
          NULL AS _cte_event_property,
          0 AS _cte_segment_identifier
   FROM "default".tbl_1 AS t1
   WHERE t1.event_name = 'evt_1'
     AND t1.user_country_code IN ('us',
                                  'gb')),
     anon_3 AS
  (SELECT anon_4._cte_user_id AS _cte_user_id,
          anon_4._cte_datetime AS _cte_datetime,
          anon_4._cte_group AS _cte_group,
          anon_4._cte_event_property AS _cte_event_property,
          anon_4._cte_segment_identifier AS _cte_segment_identifier
   FROM anon_4
   WHERE anon_4._cte_datetime >= date_trunc('month', toDateTime('2023-01-01 00:00:00'))
     AND anon_4._cte_datetime <= toDateTime('2023-06-02 00:00:00')),
     anon_5 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property,
          1 AS _cte_segment_identifier
   FROM "default".tbl_1 AS t1
   WHERE t1.event_name = 'evt_2'),
     anon_2 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property,
          anon_5._cte_segment_identifier AS _cte_segment_identifier
   FROM anon_5
   WHERE anon_5._cte_datetime >= date_trunc('month', toDateTime('2023-01-01 00:00:00'))
     AND anon_5._cte_datetime <= toDateTime('2023-06-09 00:00:00')),
     anon_1 AS
  (SELECT CASE
              WHEN (count(CASE
                              WHEN (anon_2._cte_datetime <= anon_3._cte_datetime + interval '1' WEEK) THEN anon_2._cte_user_id
                          END) OVER (PARTITION BY anon_3._cte_user_id) = 0) THEN anon_3._cte_user_id
              ELSE NULL
          END AS _agg_value_1
   FROM anon_3 ASOF
   LEFT OUTER JOIN anon_2 ON anon_3._cte_user_id = anon_2._cte_user_id
   AND isNotNull(anon_3._cte_datetime)
   AND isNotNull(anon_2._cte_datetime)
   AND assumeNotNull(anon_2._cte_datetime) > assumeNotNull(anon_3._cte_datetime))
SELECT anon_1._agg_value_1 AS ___dim_id
FROM anon_1
WHERE anon_1._agg_value_1 IS NOT NULL
LIMIT 20001
SETTINGS join_use_nulls=1