WITH anon_3 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          coalesce(CAST(t1.user_country_code AS VARCHAR), '<n/a>') AS _cte_group,
          NULL AS _cte_event_property,
          0 AS _cte_segment_identifier
   FROM public.tbl_1 AS t1
   WHERE t1.event_name = 'evt_1'
     AND t1.user_country_code IN ('us',
                                  'gb')),
     anon_2 AS
  (SELECT anon_3._cte_user_id AS _cte_user_id,
          anon_3._cte_datetime AS _cte_datetime,
          anon_3._cte_group AS _cte_group,
          anon_3._cte_event_property AS _cte_event_property,
          anon_3._cte_segment_identifier AS _cte_segment_identifier
   FROM anon_3
   WHERE anon_3._cte_datetime >= date_trunc('MONTH', '2023-01-01 00:00:00')
     AND anon_3._cte_datetime <= '2023-06-02 00:00:00'),
     anon_5 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property,
          1 AS _cte_segment_identifier
   FROM public.tbl_1 AS t1
   WHERE t1.event_name = 'evt_2'),
     anon_4 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property,
          anon_5._cte_segment_identifier AS _cte_segment_identifier
   FROM anon_5
   WHERE anon_5._cte_datetime >= date_trunc('MONTH', '2023-01-01 00:00:00')
     AND anon_5._cte_datetime <= '2023-06-09 00:00:00'),
     anon_7 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property,
          2 AS _cte_segment_identifier
   FROM public.tbl_1 AS t1
   WHERE t1.event_name = 'evt_3'),
     anon_6 AS
  (SELECT anon_7._cte_user_id AS _cte_user_id,
          anon_7._cte_datetime AS _cte_datetime,
          anon_7._cte_group AS _cte_group,
          anon_7._cte_event_property AS _cte_event_property,
          anon_7._cte_segment_identifier AS _cte_segment_identifier
   FROM anon_7
   WHERE anon_7._cte_datetime >= date_trunc('MONTH', '2023-01-01 00:00:00')
     AND anon_7._cte_datetime <= '2023-06-09 00:00:00'),
     step_0 AS
  (SELECT anon_2._cte_user_id AS _cte_user_id,
          anon_2._cte_segment_identifier AS _cte_segment_identifier,
          CAST(anon_2._cte_group AS VARCHAR) AS _cte_group_0,
          anon_2._cte_datetime AS _cte_datetime_0,
          CAST(anon_2._cte_event_property AS VARCHAR) AS _cte_event_property
   FROM anon_2
   UNION ALL SELECT anon_4._cte_user_id AS _cte_user_id,
                    anon_4._cte_segment_identifier AS _cte_segment_identifier,
                    CAST(anon_4._cte_group AS VARCHAR) AS _cte_group_0,
                    anon_4._cte_datetime AS _cte_datetime_0,
                    CAST(anon_4._cte_event_property AS VARCHAR) AS _cte_event_property
   FROM anon_4
   UNION ALL SELECT anon_6._cte_user_id AS _cte_user_id,
                    anon_6._cte_segment_identifier AS _cte_segment_identifier,
                    CAST(anon_6._cte_group AS VARCHAR) AS _cte_group_0,
                    anon_6._cte_datetime AS _cte_datetime_0,
                    CAST(anon_6._cte_event_property AS VARCHAR) AS _cte_event_property
   FROM anon_6),
     step_1 AS
  (SELECT step_0._cte_user_id AS _cte_user_id,
          step_0._cte_segment_identifier AS _cte_segment_identifier,
          step_0._cte_group_0 AS _cte_group_0,
          step_0._cte_datetime_0 AS _cte_datetime_0,
          step_0._cte_event_property AS _cte_event_property,
          CASE
              WHEN (step_0._cte_segment_identifier = 0
                    AND TRUE) THEN min(CASE
                                           WHEN (step_0._cte_segment_identifier = 1) THEN step_0._cte_datetime_0
                                       END) OVER (PARTITION BY step_0._cte_user_id
                                                  ORDER BY coalesce(step_0._cte_datetime_0, step_0._cte_datetime_0),
                                                           CASE
                                                               WHEN (step_0._cte_segment_identifier != 1) THEN 1
                                                               ELSE 0
                                                           END ROWS BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING)
          END AS _cte_datetime_1,
          CAST('$$$' AS VARCHAR) AS _cte_group_1
   FROM step_0),
     step_2 AS
  (SELECT step_1._cte_user_id AS _cte_user_id,
          step_1._cte_segment_identifier AS _cte_segment_identifier,
          step_1._cte_group_0 AS _cte_group_0,
          step_1._cte_datetime_0 AS _cte_datetime_0,
          step_1._cte_event_property AS _cte_event_property,
          step_1._cte_datetime_1 AS _cte_datetime_1,
          step_1._cte_group_1 AS _cte_group_1,
          CASE
              WHEN (step_1._cte_segment_identifier = 0
                    AND step_1._cte_datetime_1 > step_1._cte_datetime_0
                    AND step_1._cte_datetime_1 <= step_1._cte_datetime_0 + interval '1 week') THEN min(CASE
                                                                                                           WHEN (step_1._cte_segment_identifier = 2) THEN step_1._cte_datetime_0
                                                                                                       END) OVER (PARTITION BY step_1._cte_user_id
                                                                                                                  ORDER BY coalesce(step_1._cte_datetime_1, step_1._cte_datetime_0),
                                                                                                                           CASE
                                                                                                                               WHEN (step_1._cte_segment_identifier != 2) THEN 1
                                                                                                                               ELSE 0
                                                                                                                           END ROWS BETWEEN 1 FOLLOWING AND UNBOUNDED FOLLOWING)
          END AS _cte_datetime_2,
          CAST('$$$' AS VARCHAR) AS _cte_group_2
   FROM step_1),
     filtered_conversions AS
  (SELECT step_2._cte_user_id AS _cte_user_id_0,
          step_2._cte_datetime_0 AS _cte_datetime_0,
          CASE
              WHEN (step_2._cte_datetime_1 > step_2._cte_datetime_0
                    AND step_2._cte_datetime_1 <= step_2._cte_datetime_0 + interval '1 week') THEN step_2._cte_user_id
          END AS _cte_user_id_1,
          CASE
              WHEN (step_2._cte_datetime_1 > step_2._cte_datetime_0
                    AND step_2._cte_datetime_1 <= step_2._cte_datetime_0 + interval '1 week') THEN step_2._cte_datetime_1
          END AS _cte_datetime_1,
          CASE
              WHEN (step_2._cte_datetime_2 > step_2._cte_datetime_1
                    AND step_2._cte_datetime_2 <= step_2._cte_datetime_0 + interval '1 week') THEN step_2._cte_user_id
          END AS _cte_user_id_2,
          CASE
              WHEN (step_2._cte_datetime_2 > step_2._cte_datetime_1
                    AND step_2._cte_datetime_2 <= step_2._cte_datetime_0 + interval '1 week') THEN step_2._cte_datetime_2
          END AS _cte_datetime_2,
          coalesce(step_2._cte_group_0, '$$$') || '~~~' || CASE
                                                               WHEN (step_2._cte_datetime_1 > step_2._cte_datetime_0
                                                                     AND step_2._cte_datetime_1 <= step_2._cte_datetime_0 + interval '1 week') THEN coalesce(step_2._cte_group_1, '@@@')
                                                               ELSE '$$$'
                                                           END || '~~~' || CASE
                                                                               WHEN (step_2._cte_datetime_2 > step_2._cte_datetime_1
                                                                                     AND step_2._cte_datetime_2 <= step_2._cte_datetime_0 + interval '1 week') THEN coalesce(step_2._cte_group_2, '@@@')
                                                                               ELSE '$$$'
                                                                           END AS _cte_group
   FROM step_2
   WHERE step_2._cte_segment_identifier = 0),
     anon_1 AS
  (SELECT DISTINCT filtered_conversions._cte_user_id_2 AS _agg_value_1
   FROM filtered_conversions)
SELECT anon_1._agg_value_1 AS ___dim_id
FROM anon_1
WHERE anon_1._agg_value_1 IS NOT NULL
LIMIT 20001