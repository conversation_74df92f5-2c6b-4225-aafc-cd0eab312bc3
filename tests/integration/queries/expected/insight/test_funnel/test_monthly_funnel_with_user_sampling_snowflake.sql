WITH anon_4 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM integration_test.tbl_1 AS t1 TABLESAMPLE ROW (100)
   WHERE t1.event_name = 'evt_1'),
     anon_2 AS
  (SELECT anon_4._cte_user_id AS _cte_user_id,
          anon_4._cte_datetime AS _cte_datetime,
          anon_4._cte_group AS _cte_group,
          anon_4._cte_event_property AS _cte_event_property
   FROM anon_4
   WHERE anon_4._cte_datetime >= TO_TIMESTAMP('2023-01-01 00:00:00')
     AND anon_4._cte_datetime <= TO_TIMESTAMP('2023-06-02 00:00:00')),
     anon_5 AS
  (SELECT t1.user_id AS _cte_user_id,
          t1.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM integration_test.tbl_1 AS t1 TABLESAMPLE ROW (100)
   WHERE t1.event_name = 'evt_1'),
     anon_3 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property
   FROM anon_5
   WHERE anon_5._cte_datetime >= TO_TIMESTAMP('2023-01-01 00:00:00')
     AND anon_5._cte_datetime <= TO_TIMESTAMP('2023-07-02 00:00:00')),
     anon_1 AS
  (SELECT NULL AS _cte_group,
          anon_2._cte_user_id AS _cte_user_id_0,
          anon_2._cte_datetime AS _cte_datetime_0,
          anon_3._cte_user_id AS _cte_user_id_1,
          anon_3._cte_datetime AS _cte_datetime_1
   FROM anon_2
   LEFT OUTER JOIN anon_3 ON anon_2._cte_user_id = anon_3._cte_user_id
   AND anon_3._cte_datetime > anon_2._cte_datetime
   AND anon_3._cte_datetime <= dateadd('month', 1, anon_2._cte_datetime))
SELECT NULL AS _datetime,
       NULL AS _group,
       count(DISTINCT anon_1._cte_user_id_0) AS _user_count_1,
       (count(DISTINCT anon_1._cte_user_id_0) * 100.0) / nullif(count(DISTINCT anon_1._cte_user_id_0), 0) AS _agg_value_1,
       count(DISTINCT anon_1._cte_user_id_1) AS _user_count_2,
       (count(DISTINCT anon_1._cte_user_id_1) * 100.0) / nullif(count(DISTINCT anon_1._cte_user_id_0), 0) AS _agg_value_2
FROM anon_1
GROUP BY 1,
         2