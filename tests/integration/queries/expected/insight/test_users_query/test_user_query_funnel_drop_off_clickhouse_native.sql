WITH anon_5 AS
  (SELECT t2.user_id AS _cte_user_id,
          t2.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM "default".user_query_dim_events AS t2
   WHERE t2.event_name = 'event_1'),
     anon_3 AS
  (SELECT anon_5._cte_user_id AS _cte_user_id,
          anon_5._cte_datetime AS _cte_datetime,
          anon_5._cte_group AS _cte_group,
          anon_5._cte_event_property AS _cte_event_property
   FROM anon_5
   WHERE anon_5._cte_datetime >= toDateTime('2023-01-01 00:00:00')
     AND anon_5._cte_datetime <= toDateTime('2023-06-02 00:00:00')),
     anon_6 AS
  (SELECT t2.user_id AS _cte_user_id,
          t2.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM "default".user_query_dim_events AS t2
   LEFT OUTER JOIN "default".user_query_dim_user_profiles AS t1 ON t1.user_id = t2.user_id
   WHERE t2.event_name = 'event_2'
     AND t1.locale IN ('en-us',
                       'en-gb')),
     anon_4 AS
  (SELECT anon_6._cte_user_id AS _cte_user_id,
          anon_6._cte_datetime AS _cte_datetime,
          anon_6._cte_group AS _cte_group,
          anon_6._cte_event_property AS _cte_event_property
   FROM anon_6
   WHERE anon_6._cte_datetime >= toDateTime('2023-01-01 00:00:00')
     AND anon_6._cte_datetime <= toDateTime('2023-08-02 00:00:00')),
     anon_7 AS
  (SELECT t2.user_id AS _cte_user_id,
          t2.event_time AS _cte_datetime,
          NULL AS _cte_group,
          NULL AS _cte_event_property
   FROM "default".user_query_dim_events AS t2
   LEFT OUTER JOIN "default".user_query_dim_user_profiles AS t1 ON t1.user_id = t2.user_id
   WHERE t2.event_name = 'event_3'
     AND t1.email IN ('<EMAIL>')),
     anon_2 AS
  (SELECT anon_7._cte_user_id AS _cte_user_id,
          anon_7._cte_datetime AS _cte_datetime,
          anon_7._cte_group AS _cte_group,
          anon_7._cte_event_property AS _cte_event_property
   FROM anon_7
   WHERE anon_7._cte_datetime >= toDateTime('2023-01-01 00:00:00')
     AND anon_7._cte_datetime <= toDateTime('2023-08-02 00:00:00')),
     anon_1 AS
  (SELECT CASE
              WHEN (count(CASE
                              WHEN (anon_2._cte_datetime <= anon_3._cte_datetime + interval '2' MONTH) THEN anon_2._cte_user_id
                          END) OVER (PARTITION BY anon_4._cte_user_id) = 0) THEN anon_4._cte_user_id
              ELSE NULL
          END AS _agg_value_1
   FROM anon_3 ASOF
   LEFT OUTER JOIN anon_4 ON anon_3._cte_user_id = anon_4._cte_user_id
   AND isNotNull(anon_3._cte_datetime)
   AND isNotNull(anon_4._cte_datetime)
   AND assumeNotNull(anon_4._cte_datetime) > assumeNotNull(anon_3._cte_datetime) ASOF
   LEFT OUTER JOIN anon_2 ON anon_4._cte_user_id = anon_2._cte_user_id
   AND isNotNull(anon_4._cte_datetime)
   AND isNotNull(anon_2._cte_datetime)
   AND assumeNotNull(anon_2._cte_datetime) > assumeNotNull(anon_4._cte_datetime))
SELECT anon_1._agg_value_1 AS ___dim_id,
       t1.locale AS dimension_user_query_dim_user_profiles_locale,
       t1.email AS dimension_user_query_dim_user_profiles_email
FROM anon_1
LEFT OUTER JOIN "default".user_query_dim_user_profiles AS t1 ON anon_1._agg_value_1 = t1.user_id
WHERE anon_1._agg_value_1 IS NOT NULL
LIMIT 20001
SETTINGS join_use_nulls=1