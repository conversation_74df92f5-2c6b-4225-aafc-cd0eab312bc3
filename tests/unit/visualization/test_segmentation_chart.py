import json
from dataclasses import replace
from datetime import datetime
from typing import Tuple

import pandas as pd
import pytest

import mitzu.model as M
import mitzu.visualization.common as VC
import mitzu.visualization.tables as TBL
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.adapters.generic_adapter import (
    EVENT_INDEX_COL,
    GenericDatasetAdapter,
)
from mitzu.constants import N_PER_A
from mitzu.visualization.charts import (
    get_preprocessed_dataframe,
    get_simple_chart,
)
from mitzu.visualization.preprocessor.common import (
    AGG_VALUE_DIFF_COL,
    ORIGINAL_GROUP_COL,
    OTHER_GROUPS,
    fix_empty_group_by_cols,
    should_handle_null_as_dropoff,
    subtitute_segment_labels_in_group_bys,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from tests.unit.visualization.charts_helper import (
    assert_df_colors_column,
    assert_df_columns,
    assert_df_label_column,
    assert_df_text_column,
    assert_df_x_axis_column,
    get_metric_config,
    pdf_column_to_dict,
)

cart_event_path = M.EventNamePath("simple", "cart")

purchase_event_path = M.EventNamePath("simple", "purchase")
category_code_event_path = M.EventFieldPath("simple", "cart", "category_code")

remove_from_cart_event = M.EventNamePath("simple", "remove_from_cart")
remove_from_cart_cateogry_code_prop = M.EventFieldPath(
    "simple", "remove_from_cart", "category_code"
)
price_event_prop = M.EventFieldPath("simple", "remove_from_cart", "price")


def get_chart_and_table_for_metric(
    webapp_metric: WM.WebappMetric,
    metric_converter: MetricConverter,
    adapter: GenericDatasetAdapter,
    project: M.Project,
) -> tuple[VC.SimpleChart, pd.DataFrame]:
    metric = metric_converter.convert_metric(webapp_metric)
    data = adapter.get_df(metric)
    empty_catalog = MC.MetricContext.empty()

    chart = get_simple_chart(webapp_metric, data, empty_catalog, project)
    table = TBL.prepare_dataframe_for_table(
        webapp_metric, data, empty_catalog, is_event_list_modal=False
    )
    return chart, table


def test_custom_json_segmentation_overall(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
    simple_csv_metric_context: MC.MetricContext,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    df = fix_empty_group_by_cols(df, should_handle_null_as_dropoff(webapp_metric))
    df = subtitute_segment_labels_in_group_bys(
        df,
        webapp_metric=webapp_metric,
        metric_context=simple_csv_metric_context,
    )
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "ev": "Cart",
        "tl": "Unique users",
        "tv": "108",
        "v": 1,
    }


def test_custom_json_segmentation_overall_with_title(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
                title="Sample Segment",
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "ev": "Sample Segment",
        "tl": "Unique users",
        "tv": "108",
        "v": 1,
    }
    assert preprocessed_df.suffix == ""
    assert preprocessed_df.all_groups == [N_PER_A]


def test_custom_json_segmentation_overall_multi_event(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            ),
            replace(
                WM.WebappSimpleSegment.create(purchase_event_path, None),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
            ),
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "ev": "A. Cart",
        "tl": "Unique users",
        "tv": "108",
        "v": 1,
    }
    assert pdf_column_to_dict(preprocessed_df.df.loc[1, VC.JSON_COL]) == {
        "si": 1,
        "ev": "B. Purchase",
        "tl": "Event count",
        "tv": "87",
        "v": 1,
    }


def test_custom_json_segmentation_overall_multi_event_with_title(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                title="First Segment",
            ),
            replace(
                WM.WebappSimpleSegment.create(purchase_event_path, None),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
                title="Second Segment",
            ),
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "ev": "A. First Segment",
        "tl": "Unique users",
        "tv": "108",
        "v": 1,
    }
    assert pdf_column_to_dict(preprocessed_df.df.loc[1, VC.JSON_COL]) == {
        "si": 1,
        "ev": "B. Second Segment",
        "tl": "Event count",
        "tv": "87",
        "v": 1,
    }


def test_custom_json_segmentation_overall_breakdown(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "bd": ["<n/a>"],
        "ev": "<n/a>",
        "tl": "Unique users",
        "tv": "107",
        "v": 1,
    }


def test_custom_json_segmentation_weekly(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.WEEK,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
                title="Sample Segment",
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "el": "2019-12-30",
        "ev": "Sample Segment",
        "tl": "Unique users",
        "tv": "108",
        "v": 1,
    }


def test_custom_json_segmentation_weekly_breakdown(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.WEEK,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "bd": ["<n/a>"],
        "el": "2019-12-30",
        "ev": "<n/a>",
        "tl": "Unique users",
        "tv": "107",
        "v": 1,
    }


def test_custom_json_segmentation_overall_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A"),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "ev": "Formula calculation",
        "tl": "Calculated formula",
        "tv": "108",
        "v": 1,
    }


def test_custom_json_segmentation_overall_breakdown_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A"),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "ev": "<n/a> - Formula calculation",
        "tl": "Calculated formula",
        "tv": "107",
        "v": 1,
    }


def test_custom_json_segmentation_weekly_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.WEEK,
            formula="A",
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
            )
        ],
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "el": "2019-12-30",
        "ev": "Formula calculation",
        "tl": "Calculated formula",
        "tv": "108",
        "v": 1,
    }


def test_custom_json_segmentation_weekly_breakdown_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.WEEK,
            formula="A",
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "el": "2019-12-30",
        "ev": "<n/a> - Formula calculation",
        "tl": "Calculated formula",
        "tv": "107",
        "v": 1,
    }


def test_custom_json_segmentation_hourly_breakdown_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.HOUR,
            formula="A",
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "el": "2020-01-01 00:00",
        "ev": "<n/a> - Formula calculation",
        "tl": "Calculated formula",
        "tv": "30",
        "v": 1,
    }


@pytest.mark.skip("works only on macs")
def test_custom_json_with_formula_having_missing_values(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_group=M.TimeGroup.MONTH,
            formula="B",
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            ),
            replace(
                WM.WebappSimpleSegment.create(remove_from_cart_event, None),
                group_by=[remove_from_cart_cateogry_code_prop],
                aggregation=aggregation,
            ),
        ],
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": -1,
        "el": "2020-01-01",
        "ev": "apparel.glove",
        "tl": "Calculated formula",
        "tv": "1",
        "v": 1,
    }

    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            )
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Group", "Calculation", "Label"])
    assert_df_label_column(table_df, ["Cart", "Cart", "Cart", "Cart"])

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == "Unique users"
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
        ],
    )
    assert_df_colors_column(
        df,
        [
            "<n/a>",
            "apparel.glove",
            "appliances.environment.vacuum",
            "stationery.cartrige",
        ],
    )
    assert_df_x_axis_column(df, ["", "", "", ""])
    assert_df_text_column(df, [107, 2, 2, 1])


def test_simple_segmentation_chart_with_filters(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            WM.WebappComplexSegment(
                left=WM.WebappComplexSegment(
                    left=WM.WebappSimpleSegment.create(
                        cart_event_path,
                        WM.WebappEventFilter(
                            category_code_event_path, M.Operator.ANY_OF, ["category-a"]
                        ),
                    ),
                    operator=M.BinaryOperator.OR,
                    right=replace(
                        WM.WebappSimpleSegment.create(cart_event_path, None),
                        group_by=[category_code_event_path],
                    ),
                    filter=None,
                    title=None,
                    aggregation=None,
                    group_by=[],
                    expanded_custom_event_key=None,
                ),
                operator=M.BinaryOperator.OR,
                right=WM.WebappSimpleSegment.create(purchase_event_path, None),
                aggregation=aggregation,
                filter=None,
                title=None,
                group_by=[],
                expanded_custom_event_key=None,
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Group", "Calculation", "Label"])
    assert_df_label_column(table_df, ["Cart, Purchase"])

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == "Unique users"
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
            ORIGINAL_GROUP_COL,
        ],
    )
    assert_df_colors_column(df, ["Cart, Purchase"])
    assert_df_x_axis_column(df, [""])
    assert_df_text_column(df, [110])


def test_simple_segmentation_chart_with_formula(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A"),
        segments=[
            WM.WebappComplexSegment(
                left=WM.WebappComplexSegment(
                    left=WM.WebappSimpleSegment.create(
                        cart_event_path,
                        WM.WebappEventFilter(
                            category_code_event_path, M.Operator.ANY_OF, ["category-a"]
                        ),
                    ),
                    operator=M.BinaryOperator.OR,
                    right=replace(
                        WM.WebappSimpleSegment.create(cart_event_path, None),
                        group_by=[category_code_event_path],
                    ),
                    title=None,
                    filter=None,
                    aggregation=None,
                    group_by=[],
                    expanded_custom_event_key=None,
                ),
                operator=M.BinaryOperator.OR,
                right=WM.WebappSimpleSegment.create(purchase_event_path, None),
                aggregation=aggregation,
                title=None,
                filter=None,
                group_by=[],
                expanded_custom_event_key=None,
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Group", "Calculation", "Label"])
    assert_df_label_column(table_df, ["Formula calculation"])

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == ""
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
            ORIGINAL_GROUP_COL,
        ],
    )
    assert_df_colors_column(df, ["Formula calculation"])
    assert_df_x_axis_column(df, [""])
    assert_df_text_column(df, [110])


def test_simple_segmentation_chart_with_multiple_events(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("simple", "purchase"), None
                ),
                aggregation=aggregation,
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Calculation", "Label", "A. Event - Category Code"])
    assert_df_label_column(
        table_df,
        [
            "A. Cart",
            "B. Purchase",
            "apparel.glove - A. Cart",
            "appliances.environment.vacuum - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == "Unique users"
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
            ORIGINAL_GROUP_COL,
        ],
    )
    assert_df_colors_column(
        df,
        [
            "<n/a> - A. Cart",
            "B. Purchase",
            "apparel.glove - A. Cart",
            "appliances.environment.vacuum - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )
    assert_df_x_axis_column(df, ["", "", "", "", ""])
    assert_df_text_column(df, [107, 11, 2, 2, 1])


@pytest.mark.skip("works only on macs - SQLite full outer join")
def test_simple_segmentation_chart_with_multiple_events_with_formula_and_group_by(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A"),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("simple", "purchase"), None
                ),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Calculation", "Label", "Group"])
    assert_df_label_column(
        table_df,
        [
            "Formula Calculation",
            "Formula Calculation",
            "Formula Calculation",
            "Formula Calculation",
            "Formula Calculation",
        ],
    )

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == ""
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            "_event_index",
            VC.TEXT_COL,
        ],
    )
    assert_df_colors_column(
        df,
        [
            "<n/a>",
            "apparel.glove",
            "appliances.environment.vacuum",
            "stationery.cartrige",
        ],
    )
    assert_df_x_axis_column(df, ["", "", "", ""])
    assert_df_text_column(df, [107.0, 2.0, 2.0, 1.0])


def test_simple_segmentation_chart_with_multiple_events_has_no_y_axis_label_when_segment_aggregations_are_different(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("simple", "purchase"), None
                ),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Calculation", "Label", "A. Event - Category Code"])
    assert_df_label_column(
        table_df,
        [
            "A. Cart",
            "B. Purchase",
            "apparel.glove - A. Cart",
            "appliances.environment.vacuum - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == ""
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
            ORIGINAL_GROUP_COL,
        ],
    )
    assert_df_colors_column(
        df,
        [
            "<n/a> - A. Cart",
            "B. Purchase",
            "apparel.glove - A. Cart",
            "appliances.environment.vacuum - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )
    assert_df_x_axis_column(df, ["", "", "", "", ""])
    assert_df_text_column(df, [107, 87, 2, 2, 1])


def test_simple_segmentation_chart_with_multiple_events_aggregated_by_event_property(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(
        M.AggType.BY_EVENT_PROPERTY,
        event_property_agg=WM.WebappEventPropertyAggregation(
            field_def=price_event_prop,
            agg_type=M.EventPropertyAggregationType.SUM,
        ),
    )
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                group_by=[category_code_event_path],
                aggregation=aggregation,
            ),
            replace(
                WM.WebappSimpleSegment.create(purchase_event_path, None),
                aggregation=aggregation,
            ),
        ],
    )

    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert_df_columns(table_df, ["Calculation", "Label", "A. Event - Category Code"])
    assert_df_label_column(
        table_df,
        [
            "A. Cart",
            "B. Purchase",
            "appliances.environment.vacuum - A. Cart",
            "apparel.glove - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )

    assert chart.x_axis_label == ""
    assert chart.y_axis_label == "Sum of Price"
    assert chart.chart_type == M.SimpleChartType.BAR

    df = chart.dataframe
    assert_df_columns(
        df,
        [
            VC.JSON_COL,
            VC.X_AXIS_COL,
            VC.COLOR_COL,
            VC.Y_AXIS_COL,
            AGG_VALUE_DIFF_COL,
            "_event_index",
            VC.TEXT_COL,
            ORIGINAL_GROUP_COL,
        ],
    )
    assert_df_colors_column(
        df,
        [
            "<n/a> - A. Cart",
            "B. Purchase",
            "appliances.environment.vacuum - A. Cart",
            "apparel.glove - A. Cart",
            "stationery.cartrige - A. Cart",
        ],
    )
    assert_df_x_axis_column(df, ["", "", "", "", ""])
    assert_df_text_column(df, [4754.53, 572.87, 31.1, 7.13, 2.14])


def test_custom_json_with_percentage_bar_chart(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            chart_type=M.SimpleChartType.PERCENTAGE_STACKED_BAR,
            time_group=M.TimeGroup.TOTAL,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
                group_by=[category_code_event_path],
                title="Sample Segment",
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "bd": ["<n/a>"],
        "ev": "<n/a>",
        "tl": "Unique users",
        "tv": "107",
        "v": 1,
        "np": 95.54,
    }
    assert pdf_column_to_dict(preprocessed_df.df.loc[1, VC.JSON_COL]) == {
        "si": 0,
        "bd": ["apparel.glove"],
        "ev": "apparel.glove",
        "tl": "Unique users",
        "tv": "2",
        "v": 1,
        "np": 1.79,
    }


def test_custom_json_segmentation_unique_field(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    user_id = M.EventFieldPath("simple", "cart", "user_id")
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
            time_group=M.TimeGroup.TOTAL,
            unique_field=user_id,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event_path, None),
                aggregation=aggregation,
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    chart, table_df = get_chart_and_table_for_metric(
        webapp_metric, metric_converter, adapter, project
    )

    assert pdf_column_to_dict(preprocessed_df.df.loc[0, VC.JSON_COL]) == {
        "si": 0,
        "ev": "Cart",
        "tv": "108",
        "v": 1,
        "tl": "Unique User Id(s)",
        # no need to push UFL to json
    }
    assert sorted(table_df.columns) == [
        "Calculation",
        "Group",
        "Label",
    ]


def test_subsegment_labels(
    simple_csv_project_adapter_converter: Tuple[GenericDatasetAdapter, MetricConverter],
    project: M.Project,
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            replace(
                WM.WebappComplexSegment.create(
                    WM.WebappSimpleSegment.create(cart_event_path, None),
                    M.BinaryOperator.OR,
                    WM.WebappSimpleSegment.create(purchase_event_path, None),
                ),
                group_by=[
                    M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
                    M.EventFieldPath("simple", "cart", "brand"),
                ],
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ],
        title=None,
        description=None,
    )

    metric = metric_converter.convert_metric(webapp_metric)
    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    assert list(preprocessed_df.df[VC.COLOR_COL]) == [
        "Cart (1), <n/a>",
        "Cart (1), runail",
        "Cart (1), irisk",
        "Cart (1), grattol",
        "Purchase (2), <n/a>",
        "Cart (1), ingarden",
        "Cart (1), lovely",
        "Cart (1), milv",
        "Cart (1), pnb",
        "Cart (1), uno",
    ]


def test_segmentation_chart_with_formula_and_comparison_options(
    simple_csv_metric_context: MC.MetricContext,
    project: M.Project,
):
    test_input_df = pd.DataFrame(
        {
            "_datetime": [
                "2021-12-01",
                "2021-12-02",
                "2021-12-03",
                "2021-12-04",
                "2021-12-05",
            ],
            "_group": ["<n/a>", "<n/a>", "<n/a>", "<n/a>", "<n/a>"],
            "_event_index": [0, 0, 0, 0, 0],
            "_agg_value": [78.0, 66.0, 51.0, 68.0, 50.0],
            "_is_comparison": [True, True, True, False, False],
            "_original_datetime": [
                "2021-11-30",
                "2021-12-01",
                "2021-12-02",
                "2021-12-03",
                "2021-12-04",
            ],
        }
    )
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A + B"),
        segments=[
            WM.WebappSimpleSegment.create(cart_event_path, None),
            WM.WebappSimpleSegment.create(M.EventNamePath("simple", "purchase"), None),
        ],
    )
    preprocessed_df = get_preprocessed_dataframe(
        test_input_df, webapp_metric, simple_csv_metric_context, project
    )
    assert preprocessed_df.df["_color"].tolist() == [
        "Formula calculation (previous)",
        "Formula calculation (previous)",
        "Formula calculation (previous)",
        "Formula calculation",
        "Formula calculation",
    ]


def test_segmentation_chart_custom_json_with_comparison_options(
    simple_csv_metric_context: MC.MetricContext,
    project: M.Project,
):
    test_input_df = pd.DataFrame(
        {
            "_datetime": [
                "2021-12-01",
                "2021-12-02",
                "2021-12-03",
                "2021-12-04",
                "2021-12-05",
            ],
            "_group": ["<n/a>", "<n/a>", "<n/a>", "<n/a>", "<n/a>"],
            "_event_index": [0, 0, 0, 0, 0],
            "_agg_value": [78.0, 66.0, 51.0, 68.0, 50.0],
            "_is_comparison": [True, True, True, False, False],
            "_original_datetime": [
                "2021-11-30",
                "2021-12-01",
                "2021-12-02",
                "2021-12-03",
                "2021-12-04",
            ],
        }
    )
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(formula="A + B"),
        segments=[
            WM.WebappSimpleSegment.create(cart_event_path, None),
            WM.WebappSimpleSegment.create(M.EventNamePath("simple", "purchase"), None),
        ],
    )
    preprocessed_df = get_preprocessed_dataframe(
        test_input_df, webapp_metric, simple_csv_metric_context, project
    )
    for json_value in preprocessed_df.df[VC.JSON_COL][:3]:
        json_as_object = json.loads(json_value)
        assert json_as_object["ic"] is True
    for json_value in preprocessed_df.df[VC.JSON_COL][3:]:
        json_as_object = json.loads(json_value)
        assert json_as_object["ic"] is False


def test_segmentation_chart_ordering(project: M.Project):
    test_input_df = pd.DataFrame(
        {
            "_datetime": pd.to_datetime(
                ["2022-11-28", "2022-11-28", "2022-11-28"]
                + ["2022-12-05", "2022-12-05", "2022-12-05"]
                + ["2022-12-12", "2022-12-12", "2022-12-12"]
            ),
            "_group": ["H1", "H2", "H3"] * 3,
            "_agg_value": [3, 7, 7, 4, 13, 10, 4, 4, 13],
            "_event_index": [0] * 9,
        }
    )
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(),
        segments=[
            WM.WebappSimpleSegment.create(
                M.EventNamePath("simple", "test_event"),
                None,
                group_by=[M.EventFieldPath("simple", "test", "test")],
            )
        ],
    )

    preprocessed_df = get_preprocessed_dataframe(
        test_input_df, webapp_metric, MC.MetricContext.empty(), project
    )

    expected_x_values = pd.to_datetime(
        ["2022-11-28", "2022-11-28", "2022-11-28"]
        + ["2022-12-05", "2022-12-05", "2022-12-05"]
        + ["2022-12-12", "2022-12-12", "2022-12-12"]
    )

    # values are sorted by `_datetime` column then based on the sum of values for each group.
    expected_color_values = ["H3", "H2", "H1"] * 3

    assert preprocessed_df.df[VC.X_AXIS_COL].tolist() == list(
        expected_x_values
    ), "Mismatch in `x` column values."

    assert (
        preprocessed_df.df[VC.COLOR_COL].tolist() == expected_color_values
    ), "Mismatch in `_color` column values."


def test_fill_segmentation_zeros_single_group(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    project = replace(
        project,
        insight_settings=M.InsightSettings(
            project.id, fill_missing_values_with_zero=True
        ),
    )

    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            time_group=M.TimeGroup.MONTH,
            start_dt=datetime(2023, 1, 1),
            end_dt=datetime(2023, 4, 1),
        ),
    )

    df = pd.DataFrame(
        {
            "_datetime": [datetime(2023, 1, 1), datetime(2023, 3, 1)],
            "_group": [None, None],
            "_event_index": [0, 0],
            "_agg_value": [10.0, 7.0],
        }
    )
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df
    assert preprocessed_df["x"].tolist() == [
        datetime(2023, 1, 1),
        datetime(2023, 2, 1),
        datetime(2023, 3, 1),
    ]

    assert preprocessed_df["y"].tolist() == [10.0, 0.0, 7.0]
    assert preprocessed_df["_text"].tolist() == [10.0, 0.0, 7.0]
    assert preprocessed_df["_og_group"].tolist() == [N_PER_A, N_PER_A, N_PER_A]


def test_fill_segmentation_zeros_single_group_with_comparison(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    project = replace(
        project,
        insight_settings=M.InsightSettings(
            project.id, fill_missing_values_with_zero=True
        ),
    )

    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            time_group=M.TimeGroup.MONTH,
            start_dt=datetime(2023, 1, 1),
            end_dt=datetime(2023, 4, 1),
            comparison_options=M.ComparisonOptions(
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                formula=M.ComparisonFormula.NO_FORMULA,
            ),
        ),
    )

    df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 3, 1),
                datetime(2022, 12, 1),
                datetime(2023, 2, 1),
            ],
            "_original_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 3, 1),
                datetime(2023, 1, 1),
                datetime(2023, 3, 1),
            ],
            "_group": 4 * [None],
            "_event_index": 4 * [0],
            "_agg_value": [10.0, 7.0, 2, 3],
            "_is_comparison": [False, False, True, True],
        }
    )
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df
    assert preprocessed_df["x"].tolist() == [
        datetime(2023, 1, 1),
        datetime(2023, 2, 1),
        datetime(2023, 3, 1),
        datetime(2023, 1, 1),
        datetime(2023, 2, 1),
        datetime(2023, 3, 1),
    ]

    assert preprocessed_df["y"].tolist() == [0, 3, 0, 10.0, 0.0, 7.0]
    assert preprocessed_df["_text"].tolist() == [0, 3, 0, 10.0, 0.0, 7.0]
    assert preprocessed_df["_og_group"].tolist() == 6 * [N_PER_A]


def test_fill_segmentation_zeros_multiple_groups(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    project = replace(
        project,
        insight_settings=M.InsightSettings(
            project.id, fill_missing_values_with_zero=True
        ),
    )

    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            time_group=M.TimeGroup.MONTH,
            start_dt=datetime(2023, 1, 1),
            end_dt=datetime(2023, 4, 1),
        ),
    )

    df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 3, 1),
                datetime(2023, 1, 1),
            ],
            "_group": ["A", "A", "B"],
            "_event_index": [0, 0, 0],
            "_agg_value": [10.0, 7.0, 5.0],
        }
    )

    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df

    assert preprocessed_df["x"].tolist() == [
        datetime(2023, 1, 1),
        datetime(2023, 1, 1),
        datetime(2023, 2, 1),
        datetime(2023, 2, 1),
        datetime(2023, 3, 1),
        datetime(2023, 3, 1),
    ]

    assert preprocessed_df["y"].tolist() == [10.0, 5.0, 0.0, 0.0, 7.0, 0.0]
    assert preprocessed_df["_text"].tolist() == [10.0, 5.0, 0.0, 0.0, 7.0, 0.0]
    assert preprocessed_df["_og_group"].tolist() == [
        "A",
        "B",
        "A",
        "B",
        "A",
        "B",
    ]


def test_show_other_groups(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    project = replace(
        project,
        insight_settings=M.InsightSettings(
            project.id, fill_missing_values_with_zero=False
        ),
    )
    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            max_group_count=5,
            show_other_groups=True,
        ),
    )

    groups = "ABCDEFGHIJKLM"
    values = [10.0, 9.0, 8.0, 7.0, 6.0] + [1.0] * 8

    df = pd.DataFrame(
        {
            "_datetime": [datetime(2023, 1, 1)] * 13,
            "_group": [g for g in groups],
            "_event_index": [0] * 13,
            "_agg_value": values,
        }
    )

    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df

    result_groups = set(preprocessed_df[ORIGINAL_GROUP_COL])

    assert result_groups == {
        "A",
        "B",
        "C",
        "D",
        "E",
        OTHER_GROUPS,
    }

    other_rows = preprocessed_df[preprocessed_df[ORIGINAL_GROUP_COL] == OTHER_GROUPS]
    assert len(other_rows) == 1

    expected_other_value = 8.0
    actual_other_value = other_rows["y"].iloc[0]
    assert actual_other_value == expected_other_value


def test_no_other_groups_multiple_event_indices(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    project = replace(
        project,
        insight_settings=M.InsightSettings(
            project.id, fill_missing_values_with_zero=False
        ),
    )
    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            max_group_count=5,
            show_other_groups=True,
        ),
        segments=[
            replace(
                webapp_metric_segmentation.segments[0],
                aggregation=WM.WebappAggregationConfig(
                    type=M.AggType.COUNT_UNIQUE_USERS
                ),
                group_by=[],
            ),
            replace(
                webapp_metric_segmentation.segments[0],
                aggregation=WM.WebappAggregationConfig(
                    type=M.AggType.COUNT_UNIQUE_USERS
                ),
                group_by=[],
            ),
        ],
    )

    groups = list("ABCDEFGHIJKLM")
    values = [10.0, 9.0, 8.0, 7.0, 6.0] + [1.0] * 8

    df = pd.DataFrame(
        {
            "_datetime": [datetime(2023, 1, 1)] * 26,
            "_group": groups + groups,
            "_event_index": [0] * 13 + [1] * 13,
            "_agg_value": values + values,
        }
    )

    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df

    segment_0 = preprocessed_df[preprocessed_df[EVENT_INDEX_COL] == 0]
    segment_1 = preprocessed_df[preprocessed_df[EVENT_INDEX_COL] == 1]

    result_groups_0 = set(segment_0[ORIGINAL_GROUP_COL])
    result_groups_1 = set(segment_1[ORIGINAL_GROUP_COL])

    assert result_groups_0 == {"A", "B", "C", "D", "E"}
    assert result_groups_1 == {"A", "B", "C", "D", "E"}


def test_show_other_groups_time_group_total(
    webapp_metric_segmentation: WM.WebappMetric, project: M.Project
):
    webapp_metric_segmentation = replace(
        webapp_metric_segmentation,
        config=replace(
            webapp_metric_segmentation.config,
            max_group_count=5,
            show_other_groups=True,
            time_group=M.TimeGroup.TOTAL,
        ),
    )

    groups = "ABCDEFGHIJKLM"
    values = [10.0, 9.0, 8.0, 7.0, 6.0] + [1.0] * 8

    df = pd.DataFrame(
        {
            "_datetime": [None] * 13,
            "_group": [g for g in groups],
            "_event_index": [0] * 13,
            "_agg_value": values,
        }
    )

    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric_segmentation, MC.MetricContext.empty(), project
    ).df

    result_groups = set(preprocessed_df[ORIGINAL_GROUP_COL])

    assert result_groups == {
        "A",
        "B",
        "C",
        "D",
        "E",
        OTHER_GROUPS,
    }

    other_rows = preprocessed_df[preprocessed_df[ORIGINAL_GROUP_COL] == OTHER_GROUPS]
    assert len(other_rows) == 1

    expected_other_value = 8.0
    actual_other_value = other_rows["y"].iloc[0]
    assert actual_other_value == expected_other_value
