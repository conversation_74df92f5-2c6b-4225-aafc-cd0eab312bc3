import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.visualization.preprocessor.conversion_preprocessor import (
    IS_DROPOFF_COL,
    STEP_DROP_PCT,
    ConversionPreprocessor,
)
from mitzu.visualization.transform_conv import STEP_INDEX


def test_compute_dropoff(project: M.Project):
    conversion_preprocessor = ConversionPreprocessor(
        WM.WebappMetric.create_empty_webapp_metric(M.MetricType.CONVERSION),
        MC.MetricContext.empty(),
        project,
    )
    df = pd.DataFrame(
        [
            {
                C.COLOR_COL: "a",
                C.X_AXIS_COL: 1,
                GA.AGG_VALUE_COL: 10,
                GA.USER_COUNT_COL: 5,
            },
            {
                C.COLOR_COL: "a",
                C.X_AXIS_COL: 2,
                GA.AGG_VALUE_COL: 8,
                GA.USER_COUNT_COL: 4,
            },
            {
                C.COLOR_COL: "b",
                C.X_AXIS_COL: 1,
                GA.AGG_VALUE_COL: 10,
                GA.USER_COUNT_COL: 5,
            },
            {
                C.COLOR_COL: "b",
                C.X_AXIS_COL: 2,
                GA.AGG_VALUE_COL: 7,
                GA.USER_COUNT_COL: 3,
            },
        ]
    )
    result_df = conversion_preprocessor.compute_dropoff(df)
    assert result_df.shape == (8, 5)
    assert result_df[IS_DROPOFF_COL].sum() == 4

    assert result_df[result_df[IS_DROPOFF_COL]][GA.USER_COUNT_COL].tolist() == [
        0,
        1,
        0,
        2,
    ]
    assert result_df[result_df[IS_DROPOFF_COL]][GA.AGG_VALUE_COL].tolist() == [
        0,
        2,
        0,
        3,
    ]


def test_calculate_precentage_from_previous_step(project: M.Project):
    conversion_preprocessor = ConversionPreprocessor(
        WM.WebappMetric.create_empty_webapp_metric(M.MetricType.CONVERSION),
        MC.MetricContext.empty(),
        project,
    )
    df = pd.DataFrame(
        [
            {
                C.X_AXIS_COL: "1. First Step",
                GA.USER_COUNT_COL: 10,
                GA.AGG_VALUE_COL: 100,
                STEP_INDEX: 0,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "2. Second Step",
                GA.USER_COUNT_COL: 8,
                GA.AGG_VALUE_COL: 80,
                STEP_INDEX: 1,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "3. Third Step",
                GA.USER_COUNT_COL: 4,
                GA.AGG_VALUE_COL: 40,
                STEP_INDEX: 2,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "4. Fourth Step",
                GA.USER_COUNT_COL: 2,
                GA.AGG_VALUE_COL: 50,
                STEP_INDEX: 3,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
        ]
    )
    result_df = conversion_preprocessor.calculate_precentage_from_previous_step(df)
    assert result_df[STEP_DROP_PCT].tolist() == [100, 80, 50, 50]


def test_calculate_precentage_from_previous_step_multiple_groups(project: M.Project):
    conversion_preprocessor = ConversionPreprocessor(
        WM.WebappMetric.create_empty_webapp_metric(M.MetricType.CONVERSION),
        MC.MetricContext.empty(),
        project,
    )
    df = pd.DataFrame(
        [
            {
                C.X_AXIS_COL: "1. First Step",
                GA.USER_COUNT_COL: 10,
                GA.AGG_VALUE_COL: 100,
                STEP_INDEX: 0,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "2. Second Step",
                GA.USER_COUNT_COL: 8,
                GA.AGG_VALUE_COL: 80,
                STEP_INDEX: 1,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "3. Third Step",
                GA.USER_COUNT_COL: 4,
                GA.AGG_VALUE_COL: 40,
                STEP_INDEX: 2,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "1. First Step",
                GA.USER_COUNT_COL: 10,
                GA.AGG_VALUE_COL: 100,
                STEP_INDEX: 0,
                GA.GROUP_COL: "b",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "2. Second Step",
                GA.USER_COUNT_COL: 6,
                GA.AGG_VALUE_COL: 60,
                STEP_INDEX: 1,
                GA.GROUP_COL: "b",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "3. Third Step",
                GA.USER_COUNT_COL: 3,
                GA.AGG_VALUE_COL: 50,
                STEP_INDEX: 2,
                GA.GROUP_COL: "b",
                IS_DROPOFF_COL: False,
            },
        ]
    )
    result_df = conversion_preprocessor.calculate_precentage_from_previous_step(df)
    assert result_df[STEP_DROP_PCT].tolist() == [100, 80, 50, 100, 60, 50]


def test_calculate_precentage_from_previous_step_with_dropoff(project: M.Project):
    conversion_preprocessor = ConversionPreprocessor(
        WM.WebappMetric.create_empty_webapp_metric(M.MetricType.CONVERSION),
        MC.MetricContext.empty(),
        project,
    )
    df = pd.DataFrame(
        [
            {
                C.X_AXIS_COL: "1. First Step",
                GA.USER_COUNT_COL: 10,
                GA.AGG_VALUE_COL: 100,
                STEP_INDEX: 0,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "1. First Step",
                GA.USER_COUNT_COL: 10,
                GA.AGG_VALUE_COL: 0,
                STEP_INDEX: 0,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: True,
            },
            {
                C.X_AXIS_COL: "2. Second Step",
                GA.USER_COUNT_COL: 8,
                GA.AGG_VALUE_COL: 80,
                STEP_INDEX: 1,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "2. Second Step",
                GA.USER_COUNT_COL: 8,
                GA.AGG_VALUE_COL: 20,
                STEP_INDEX: 1,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: True,
            },
            {
                C.X_AXIS_COL: "3. Third Step",
                GA.USER_COUNT_COL: 4,
                GA.AGG_VALUE_COL: 40,
                STEP_INDEX: 2,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: False,
            },
            {
                C.X_AXIS_COL: "3. Third Step",
                GA.USER_COUNT_COL: 4,
                GA.AGG_VALUE_COL: 50,
                STEP_INDEX: 2,
                GA.GROUP_COL: "a",
                IS_DROPOFF_COL: True,
            },
        ]
    )
    result_df = conversion_preprocessor.calculate_precentage_from_previous_step(df)
    assert result_df[STEP_DROP_PCT].tolist() == [100.0, 80.0, 50.0, 0.0, 20.0, 50.0]
