import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.visualization.preprocessor.conversion_preprocessor import (
    IS_DROPOFF_COL,
    ConversionPreprocessor,
)


def test_compute_dropoff(project: M.Project):
    conversion_preprocessor = ConversionPreprocessor(
        WM.WebappMetric.create_empty_webapp_metric(M.MetricType.CONVERSION),
        MC.MetricContext.empty(),
        project,
    )
    df = pd.DataFrame(
        [
            {
                C.COLOR_COL: "a",
                C.X_AXIS_COL: 1,
                GA.AGG_VALUE_COL: 10,
                GA.USER_COUNT_COL: 5,
            },
            {
                C.COLOR_COL: "a",
                C.X_AXIS_COL: 2,
                GA.AGG_VALUE_COL: 8,
                GA.USER_COUNT_COL: 4,
            },
            {
                C.COLOR_COL: "b",
                C.X_AXIS_COL: 1,
                GA.AGG_VALUE_COL: 10,
                GA.USER_COUNT_COL: 5,
            },
            {
                C.COLOR_COL: "b",
                C.X_AXIS_COL: 2,
                GA.AGG_VALUE_COL: 7,
                GA.USER_COUNT_COL: 3,
            },
        ]
    )
    result_df = conversion_preprocessor.compute_dropoff(df)
    assert result_df.shape == (8, 5)
    assert result_df[IS_DROPOFF_COL].sum() == 4

    assert result_df[result_df[IS_DROPOFF_COL]][GA.USER_COUNT_COL].tolist() == [
        0,
        1,
        0,
        2,
    ]
    assert result_df[result_df[IS_DROPOFF_COL]][GA.AGG_VALUE_COL].tolist() == [
        0,
        2,
        0,
        3,
    ]
