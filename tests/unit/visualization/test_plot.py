from dataclasses import replace
from datetime import datetime
from functools import partial
from typing import <PERSON>ple
from zoneinfo import ZoneInfo

import pandas as pd
import plotly.graph_objects as go
import pytest

import mitzu.helper as H
import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.metric_converter as SMC
from mitzu.adapters.generic_adapter import (
    GenericDatasetAdapter,
)
from mitzu.visualization.charts import (
    get_preprocessed_dataframe,
)
from mitzu.visualization.common import (
    COLOR_COL,
    CURRENT_CHART_VERSION,
    OTHER_GROUPS_COLOR,
    Y_AXIS_COL,
    Annotation,
    SimpleChart,
)
from mitzu.visualization.plot import (
    create_fat_number_chart,
    fix_bar_heights,
    generate_color_discrete_map,
    plot_chart,
    replace_incomplete_area_with_dotted_line,
)
from mitzu.visualization.preprocessor.common import (
    OTHER_GROUPS,
)
from mitzu.visualization.preprocessor.conversion_preprocessor import (
    IS_DROPOFF_COL,
)

cart_event = M.EventName<PERSON>ath("simple", "cart")
purchase_event = M.EventNamePath("simple", "purchase")


def get_metric_config(metric_type: M.MetricType, **kwargs) -> WM.WebappMetricConfig:
    default_kwargs = {
        "start_dt": H.parse_datetime_input("2020-01-01", None),
        "end_dt": H.parse_datetime_input("2021-01-01", None),
        "time_group": M.TimeGroup.TOTAL,
        "aggregation": M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
    }
    default_kwargs.update(**kwargs)
    if metric_type == M.MetricType.SEGMENTATION:
        return WM.WebappMetricConfig.from_model(M.SegmentationMetricConfig(**default_kwargs))  # type: ignore[arg-type]
    elif metric_type == M.MetricType.CONVERSION:
        return WM.WebappMetricConfig.from_model(M.ConversionMetricConfig(**default_kwargs))  # type: ignore[arg-type]
    else:
        raise ValueError(f"Unknown metric type: {metric_type}")


def test_annotations_trend(
    simple_csv_project: M.DiscoveredProject,
    simple_csv_project_adapter_converter: Tuple[
        GenericDatasetAdapter, SMC.MetricConverter
    ],
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    project = simple_csv_project.project
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=replace(
            get_metric_config(
                M.MetricType.SEGMENTATION,
                time_group=M.TimeGroup.HOUR,
                chart_type=M.SimpleChartType.LINE,
            ),
            start_dt=datetime(2020, 1, 1),
            end_dt=datetime(2020, 1, 2),
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event, None), aggregation=aggregation
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    chart = SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(webapp_metric),
        x_axis_label="",
        y_axis_label="",
        color_label="",
        chart_type=M.SimpleChartType.LINE,
        yaxis_ticksuffix="",
        dataframe=preprocessed_df.df,
        all_groups=len(preprocessed_df.all_groups),
        dataframe_version=CURRENT_CHART_VERSION,
    )

    fig, _ = plot_chart(
        chart,
        metric,
        MC.MetricContext.empty(),
        [
            Annotation(
                project_id=project.id,
                owner=WM.MembershipID("user"),
                created_at=datetime(2022, 1, 1),
                annotation_text="test",
                annotation_datetime=datetime(2020, 1, 1, 3, 0, 0),
                description="description",
            ),
            Annotation(
                project_id=project.id,
                owner=WM.MembershipID("user"),
                created_at=datetime(2022, 1, 1),
                annotation_text="ouside_window",
                annotation_datetime=datetime(2021, 1, 1, 3, 0, 0),
                description="description",
            ),
        ],
        webapp_metric,
        project.get_default_end_dt(),
    )

    assert fig is not None
    annotations = fig.to_dict()["layout"]["annotations"]

    assert len(annotations) == 1
    assert annotations[0]["text"] == "test"


def test_annotations_total(
    simple_csv_project: M.DiscoveredProject,
    simple_csv_project_adapter_converter: Tuple[
        GenericDatasetAdapter, SMC.MetricConverter
    ],
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    project = simple_csv_project.project
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            M.MetricType.SEGMENTATION,
            time_group=M.TimeGroup.TOTAL,
            chart_type=M.SimpleChartType.LINE,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event, None), aggregation=aggregation
            )
        ],
    )

    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    chart = SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(webapp_metric),
        x_axis_label="",
        y_axis_label="",
        color_label="",
        chart_type=M.SimpleChartType.LINE,
        yaxis_ticksuffix="",
        dataframe=preprocessed_df.df,
        all_groups=len(preprocessed_df.all_groups),
        dataframe_version=CURRENT_CHART_VERSION,
    )

    fig, _ = plot_chart(
        chart,
        metric,
        MC.MetricContext.empty(),
        [
            Annotation(
                project_id=project.id,
                owner=WM.MembershipID("user"),
                created_at=datetime(2022, 1, 1),
                annotation_text="test",
                annotation_datetime=datetime(2020, 1, 1, 3, 0, 0),
                description="description",
            ),
            Annotation(
                project_id=project.id,
                owner=WM.MembershipID("user"),
                created_at=datetime(2022, 1, 1),
                annotation_text="ouside_window",
                annotation_datetime=datetime(2021, 1, 1, 3, 0, 0),
                description="description",
            ),
        ],
        webapp_metric,
        project.get_default_end_dt(),
    )

    assert fig is not None
    assert "annotations" not in fig.to_dict()["layout"]


def test_fat_number_chart_two_values():
    pdf = pd.DataFrame(
        [
            {
                "_color": "a",
                "y": 1,
                "_text": "1",
            },
            {
                "_color": "a",
                "y": 2,
                "_text": "2",
            },
        ]
    )
    fig = create_fat_number_chart(pdf)
    assert fig is not None
    fig_dict = fig.to_dict()
    assert fig_dict["data"] == [
        {
            "mode": "text",
            "text": ["a: 1", "a: 2"],
            "x": [0, 0],
            "y": [20, 30],
            "type": "scatter",
            "textfont": {"size": 32},
        }
    ]
    assert fig_dict["layout"]["yaxis"]["range"] == [19, 31]
    assert fig_dict["layout"]["xaxis"]["range"] == [-1, 1]


def test_fat_number_chart_single_value():
    pdf = pd.DataFrame(
        [
            {
                "_color": "a",
                "y": 1,
                "_text": "1",
            },
        ]
    )
    fig = create_fat_number_chart(pdf)
    assert fig is not None
    fig_dict = fig.to_dict()
    assert fig_dict["data"] == [
        {
            "mode": "text",
            "text": [
                "1",
            ],
            "x": [0],
            "y": [20],
            "type": "scatter",
            "textfont": {"size": 64},
        }
    ]
    assert fig_dict["layout"]["yaxis"]["range"] == [19, 21]
    assert fig_dict["layout"]["xaxis"]["range"] == [-1, 1]


def test_fix_bar_heights():
    pdf = pd.DataFrame(
        [
            {Y_AXIS_COL: 0},
            {
                Y_AXIS_COL: 100,
            },
            {
                Y_AXIS_COL: 200,
            },
        ]
    )
    fix_bar_heights(pdf)
    res = pdf.to_dict(orient="records")
    assert res == [
        {
            Y_AXIS_COL: 2.0,
        },
        {
            Y_AXIS_COL: 100.0,
        },
        {
            Y_AXIS_COL: 200.0,
        },
    ]


def test_horizontal_bar_chart(
    simple_csv_project: M.DiscoveredProject,
    simple_csv_project_adapter_converter: Tuple[
        GenericDatasetAdapter, SMC.MetricConverter
    ],
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    project = simple_csv_project.project
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=get_metric_config(
            M.MetricType.SEGMENTATION,
            time_group=M.TimeGroup.TOTAL,
            chart_type=M.SimpleChartType.HORIZONTAL_BAR,
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(cart_event, None), aggregation=aggregation
            )
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )

    chart = SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(webapp_metric),
        x_axis_label="Country Code",
        y_axis_label="Count",
        color_label="Category",
        chart_type=M.SimpleChartType.HORIZONTAL_BAR,
        yaxis_ticksuffix="",
        dataframe=preprocessed_df.df,
        all_groups=len(preprocessed_df.all_groups),
        dataframe_version=CURRENT_CHART_VERSION,
    )

    fig, _ = plot_chart(
        chart,
        metric,
        MC.MetricContext.empty(),
        [],
        webapp_metric,
        project.get_default_end_dt(),
    )

    assert fig is not None
    fig_dict = fig.to_dict()

    assert fig_dict["data"][0]["type"] == "bar"
    assert fig_dict["data"][0]["orientation"] == "h"


def create_test_figure(x_values, y_values, custom_data):
    trace = go.Scatter(
        x=x_values,
        y=y_values,
        mode="lines",
        line=dict(color="blue"),
        customdata=custom_data,
        showlegend=True,
    )
    fig = go.Figure(data=[trace])
    return fig


def test_replace_incomplete_area_with_dotted_line(
    simple_segment: WM.WebappSimpleSegment,
):
    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2020, 1, 1),
                end_dt=datetime(2021, 1, 1),
                time_group=M.TimeGroup.MONTH,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                conv_attribution=M.ConversionAttribution.FIRST_EVENT,
                time_window=M.TimeWindow(1, M.TimeGroup.WEEK),
                conv_window_type=M.ConversionWindowType.ENTIRE_FUNNEL,
                chart_type=M.SimpleChartType.LINE,
            )
        ),
        segments=[simple_segment, simple_segment],
    )
    create_fig_with_fixed_values = partial(
        create_test_figure,
        x_values=[
            datetime(2023, 1, 1, tzinfo=ZoneInfo("UTC")),
            datetime(2023, 1, 4, tzinfo=ZoneInfo("UTC")),
            datetime(2023, 1, 6, tzinfo=ZoneInfo("UTC")),
        ],
        y_values=[10, 20, 30],
        custom_data=[{"customdata": 1}, {"customdata": 2}, {"customdata": 3}],
    )
    fig = create_fig_with_fixed_values()
    replace_incomplete_area_with_dotted_line(webapp_metric, datetime(2023, 1, 6), fig)
    assert (
        len(fig.data) == 2
    ), "Figure should split into two segments (solid and dotted)."
    assert fig.data[0].line.dash == "solid", "First segment should be solid."
    assert fig.data[1].line.dash == "dot", "Second segment should be dotted."
    assert fig.data[0].x[-1] == fig.data[1].x[0], "Segments should be connected."

    webapp_metric = replace(
        webapp_metric,
        config=replace(webapp_metric.config, chart_type=M.SimpleChartType.BAR),
    )
    new_fig = create_fig_with_fixed_values()
    replace_incomplete_area_with_dotted_line(webapp_metric, datetime(2023, 1, 6), fig)

    assert len(new_fig.data) == 1


def test_insufficient_segments(simple_segment):
    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2020, 1, 1),
                end_dt=datetime(2021, 1, 1),
                time_group=M.TimeGroup.MONTH,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                conv_attribution=M.ConversionAttribution.FIRST_EVENT,
                time_window=M.TimeWindow(1, M.TimeGroup.WEEK),
                conv_window_type=M.ConversionWindowType.ENTIRE_FUNNEL,
            )
        ),
        segments=[simple_segment],
    )
    fig = create_test_figure(
        x_values=["2023-01-01", "2023-01-04"],
        y_values=[10, 20],
        custom_data=[{"customdata": 1}, {"customdata": 2}],
    )
    replace_incomplete_area_with_dotted_line(
        webapp_metric, datetime(2023, 1, 4, tzinfo=ZoneInfo("America/Los_Angeles")), fig
    )

    assert len(fig.data) == 1, "Figure should remain unchanged with no segments."


def test_lines_should_be_connected(simple_segment: WM.WebappSimpleSegment):
    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2020, 1, 1),
                end_dt=datetime(2021, 1, 1),
                time_group=M.TimeGroup.MONTH,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                conv_attribution=M.ConversionAttribution.FIRST_EVENT,
                time_window=M.TimeWindow(1, M.TimeGroup.WEEK),
                conv_window_type=M.ConversionWindowType.ENTIRE_FUNNEL,
                chart_type=M.SimpleChartType.LINE,
            )
        ),
        segments=[
            simple_segment,
            simple_segment,
        ],
    )
    fig = create_test_figure(
        x_values=["2023-01-01", "2023-01-05", "2023-01-08"],
        y_values=[10, 20, 30],
        custom_data=[{"customdata": 1}, {"customdata": 2}, {"customdata": 3}],
    )
    replace_incomplete_area_with_dotted_line(
        webapp_metric, datetime(2023, 1, 7, tzinfo=ZoneInfo("America/Los_Angeles")), fig
    )

    assert len(fig.data) == 2, "Figure should split into two segments."
    assert fig.data[0].line.dash == "solid", "First segment should be solid."
    assert fig.data[1].line.dash == "dot", "Second segment should be dotted."
    assert fig.data[0].x[-1] == fig.data[1].x[0], "Segments should be connected."


def test_replace_incomplete_area_with_dotted_line_empty_figure(
    simple_segment: WM.WebappSimpleSegment,
):
    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2020, 1, 1),
                end_dt=datetime(2021, 1, 1),
                time_group=M.TimeGroup.MONTH,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                conv_attribution=M.ConversionAttribution.FIRST_EVENT,
                time_window=M.TimeWindow(1, M.TimeGroup.WEEK),
                conv_window_type=M.ConversionWindowType.ENTIRE_FUNNEL,
            )
        ),
        segments=[simple_segment, simple_segment],
    )
    fig = create_test_figure(
        x_values=[],
        y_values=[],
        custom_data=[],
    )
    replace_incomplete_area_with_dotted_line(
        webapp_metric, datetime(2023, 1, 6, tzinfo=ZoneInfo("UTC")), fig
    )
    assert len(fig.data) == 1


@pytest.mark.parametrize(
    "webapp_metric",
    [
        pytest.param(
            WM.WebappMetric(
                M.MetricType.CONVERSION,
                config=replace(
                    get_metric_config(
                        M.MetricType.CONVERSION,
                        time_group=M.TimeGroup.TOTAL,
                        chart_type=M.SimpleChartType.LINE,
                        aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
                    ),
                    start_dt=datetime(2020, 1, 1),
                    end_dt=datetime(2020, 1, 20),
                ),
                segments=[
                    replace(WM.WebappSimpleSegment.create(cart_event, None)),
                    replace(WM.WebappSimpleSegment.create(purchase_event, None)),
                ],
            ),
            id="conversion total gorup",
        ),
        pytest.param(
            WM.WebappMetric(
                M.MetricType.CONVERSION,
                config=replace(
                    get_metric_config(
                        M.MetricType.CONVERSION,
                        time_group=M.TimeGroup.DAY,
                        chart_type=M.SimpleChartType.LINE,
                        aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
                    ),
                    start_dt=datetime(2020, 1, 1),
                    end_dt=datetime(2020, 1, 20),
                ),
                segments=[
                    replace(WM.WebappSimpleSegment.create(cart_event, None)),
                    replace(WM.WebappSimpleSegment.create(purchase_event, None)),
                ],
            ),
            id="conversion daily trend",
        ),
        pytest.param(
            WM.WebappMetric(
                M.MetricType.CONVERSION,
                config=replace(
                    get_metric_config(
                        M.MetricType.CONVERSION,
                        time_group=M.TimeGroup.HOUR,
                        chart_type=M.SimpleChartType.LINE,
                        aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
                        time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                    ),
                    start_dt=datetime(2020, 1, 1),
                    end_dt=datetime(2020, 1, 20),
                ),
                segments=[
                    replace(
                        WM.WebappSimpleSegment.create(
                            cart_event,
                            None,
                            group_by=[
                                M.EventFieldPath("simple", "cart", "category_id")
                            ],
                        )
                    ),
                    replace(WM.WebappSimpleSegment.create(purchase_event, None)),
                ],
            ),
            id="conversion daily trend with group by",
        ),
    ],
)
def test_conversion_plot_smoke(
    webapp_metric: WM.WebappMetric,
    simple_csv_project: M.DiscoveredProject,
    simple_csv_project_adapter_converter: Tuple[
        GenericDatasetAdapter, SMC.MetricConverter
    ],
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    project = simple_csv_project.project
    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    assert not df.empty
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    chart = SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(webapp_metric),
        x_axis_label="",
        y_axis_label="",
        color_label="",
        chart_type=M.SimpleChartType.LINE,
        yaxis_ticksuffix="",
        dataframe=preprocessed_df.df,
        all_groups=len(preprocessed_df.all_groups),
        dataframe_version=CURRENT_CHART_VERSION,
    )

    # just smoke testing
    plot_chart(
        chart,
        metric,
        MC.MetricContext.empty(),
        [],
        webapp_metric,
        project.get_default_end_dt(),
    )


def test_conversion_plot_total_bar_chart_without_dropoff_column_smoke(
    simple_csv_project: M.DiscoveredProject,
    simple_csv_project_adapter_converter: Tuple[
        GenericDatasetAdapter, SMC.MetricConverter
    ],
):
    adapter, metric_converter = simple_csv_project_adapter_converter
    project = simple_csv_project.project
    purchase_event = M.EventNamePath("simple", "purchase")
    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=replace(
            get_metric_config(
                M.MetricType.CONVERSION,
                time_group=M.TimeGroup.TOTAL,
                chart_type=M.SimpleChartType.BAR,
                aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
            ),
            start_dt=datetime(2020, 1, 1),
            end_dt=datetime(2020, 1, 2),
        ),
        segments=[
            replace(WM.WebappSimpleSegment.create(cart_event, None)),
            replace(WM.WebappSimpleSegment.create(purchase_event, None)),
        ],
    )
    metric = metric_converter.convert_metric(webapp_metric)

    df = adapter.get_df(metric)
    preprocessed_df = get_preprocessed_dataframe(
        df, webapp_metric, MC.MetricContext.empty(), project
    )
    chart = SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(webapp_metric),
        x_axis_label="",
        y_axis_label="",
        color_label="",
        chart_type=M.SimpleChartType.BAR,
        yaxis_ticksuffix="",
        dataframe=preprocessed_df.df,
        all_groups=len(preprocessed_df.all_groups),
        dataframe_version=CURRENT_CHART_VERSION,
    )
    chart.dataframe.drop(columns=[IS_DROPOFF_COL], inplace=True)

    plot_chart(
        chart,
        metric,
        MC.MetricContext.empty(),
        [],
        webapp_metric,
        project.get_default_end_dt(),
    )


def test_generate_color_discrete_map_with_consistent_coloring():
    df = pd.DataFrame({COLOR_COL: ["Group A", "Group B", OTHER_GROUPS]})
    df_changed_order = pd.DataFrame({COLOR_COL: ["Group B", "Group A", OTHER_GROUPS]})

    color_map = generate_color_discrete_map(df, use_consistent_coloring=True)

    assert set(color_map.keys()) == {"Group A", "Group B", OTHER_GROUPS}
    assert color_map[OTHER_GROUPS] == OTHER_GROUPS_COLOR
    assert color_map == generate_color_discrete_map(
        df_changed_order, use_consistent_coloring=True
    )


def test_generate_color_discrete_map_with_non_consistent_coloring():
    df = pd.DataFrame({COLOR_COL: ["Group A", "Group B", OTHER_GROUPS]})

    color_map = generate_color_discrete_map(df, use_consistent_coloring=False)

    assert set(color_map.keys()) == {"Group A", "Group B", OTHER_GROUPS}
    assert color_map[OTHER_GROUPS] == OTHER_GROUPS_COLOR
