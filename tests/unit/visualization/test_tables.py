from dataclasses import replace
from datetime import datetime
from unittest.mock import MagicMock

import pandas as pd
import pytest

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.service.metric_converter as MC
from mitzu.constants import N_PER_A, NOT_SET
from mitzu.helper import DROP_OFF_PLACEHOLDER
from mitzu.visualization.charts import (
    get_preprocessed_dataframe,
)
from mitzu.visualization.common import (
    IS_COMPARISON_COL,
    SORTING_COL,
)
from mitzu.visualization.tables import (
    DATETIME_COL,
    DROP_OFF_LABEL,
    LABEL_COL,
    PROB_BEST_COL_LABEL,
    PROBABILITY_COL,
    RETENTION_COL,
    _get_pivot_index_params,
    _pivot_table_if_needed,
    generate_journey_dropoff_paths,
    prepare_dataframe_for_event_list_table,
    prepare_dataframe_for_table,
    reorder_table_columns,
)
from tests.helper import (
    assert_dataframe_equals,
    create_metric_context_from_discovered_project,
)


def test_normalize_table_dataframe(discovered_project: <PERSON><PERSON>DiscoveredProject):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [
                datetime(2023, 1, 1),
                datetime(2023, 1, 1),
                datetime(2023, 1, 1),
            ],
            "_group": ["group_1", "<n/a>", None],
            "_user_count_1": [2, 2, 8],
            "_agg_value_1": 3 * [100],
            "_user_count_2": [2, 2, 4],
            "_agg_value_2": [100, 100, 50],
            "_user_count_3": [2, 2, 0],
            "_agg_value_3": [100, 100, 0],
            "_user_count_4": [2, 2, 0],
            "_agg_value_4": [100, 100, 0],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                time_window=M.TimeWindow(3, M.TimeGroup.MONTH),
            )
        ),
        segments=[
            WM.WebappSimpleSegment.create(
                M.EventNamePath("checkouts", "checkout"),
                None,
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code")
                ],
            ),
        ],
    )

    # FIXME: these methods are mutating the state of the dataframe param
    get_preprocessed_dataframe(
        df, webapp_metric, metric_context, discovered_project.project
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "User Country Code": [NOT_SET, N_PER_A, "group_1"],
            "1. Unique users": [8, 2, 2],
            "1. Conversion rate": 3 * [100],
            "2. Unique users": [4, 2, 2],
            "2. Conversion rate": [50, 100, 100],
            "3. Unique users": [0, 2, 2],
            "3. Conversion rate": [0, 100, 100],
            "4. Unique users": [0, 2, 2],
            "4. Conversion rate": [0, 100, 100],
            "Label": [NOT_SET, N_PER_A, "group_1"],
        }
    )

    # There is always a slight variation in the probability column
    # We have separate test for that calculation
    result_df = result_df.drop(columns=[PROB_BEST_COL_LABEL])

    assert_dataframe_equals(result_df, expected_df)


def test_normalize_table_dataframe_with_multiple_segments(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [None, None],
            "_group": ["gb###en", "de###Germany"],
            "_agg_value": [10, 20],
            "_event_index": [0, 1],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
            )
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code"),
                    M.DimensionFieldPath("user_profiles", "user_locale"),
                ],
                aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("page_events", "page_visit"), None
                ),
                group_by=[
                    M.EventFieldPath("page_events", "page_visit", "user_country_code"),
                    M.DimensionFieldPath("group_profiles", "headquarter_country"),
                ],
                title="Other segment",
                aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
            ),
        ],
        title=None,
        description=None,
    )

    MC.MetricConverter(
        discovered_project.as_metric_definitions(), MagicMock(), MagicMock()
    ).convert_metric(webapp_metric)

    # FIXME: these methods are mutating the state of the dataframe param
    get_preprocessed_dataframe(
        df, webapp_metric, metric_context, discovered_project.project
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "Label": ["gb, en - A. Checkout", "de, Germany - B. Other segment"],
            "A. Event - User Country Code": ["gb", N_PER_A],
            "A. Event - User Locale": ["en", N_PER_A],
            "B. Other segment - User Country Code": [N_PER_A, "de"],
            "B. Other segment - Headquarter Country": [N_PER_A, "Germany"],
            "Conversion rate": [10, 20],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_normalize_table_dataframe_with_overlapping_colnames(
    discovered_project: M.DiscoveredProject,
):
    event = M.EventNamePath("checkouts", "checkout")
    field = M.EventFieldPath("checkouts", "checkout", "event_name")
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    metric_context = replace(
        metric_context,
        _property_catalog={
            **metric_context._property_catalog,
            field: replace(
                metric_context._property_catalog[field],
                display_name="Event",
            ),
        },
    )
    df = pd.DataFrame(
        {
            "_datetime": [datetime(2024, 1, 2)],
            "_group": ["checkout"],
            "_agg_value": [20],
            "_event_index": [0],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.MONTH,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
            )
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(event, None),
                group_by=[
                    field,
                ],
            ),
        ],
        title=None,
        description=None,
    )

    # FIXME: these methods are mutating the state of the dataframe param
    get_preprocessed_dataframe(
        df, webapp_metric, metric_context, discovered_project.project
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.concat(
        [
            pd.DataFrame(
                {
                    "Label": ["checkout"],
                }
            ),
            pd.DataFrame({"Event": ["checkout"], "2024-01-02": [20]}),
        ],
        axis=1,
    )

    pd.testing.assert_frame_equal(
        result_df,
        expected_df,
    )


def test_normalize_table_dataframe_with_multiple_segments_with_different_group_by_counts(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [datetime(2024, 1, 2), datetime(2024, 1, 2)],
            "_group": ["gb###en", None],
            "_agg_value": [10, 20],
            "_event_index": [0, 1],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.WEEK,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code"),
                    M.DimensionFieldPath("user_profiles", "user_locale"),
                ],
            ),
            WM.WebappSimpleSegment.create(
                M.EventNamePath("page_events", "page_visit"), None
            ),
        ],
    )

    # FIXME: these methods are mutating the state of the dataframe param
    get_preprocessed_dataframe(
        df, webapp_metric, metric_context, discovered_project.project
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "Label": ["gb, en - A. Checkout", "B. Page Visit"],
            "A. Event - User Country Code": ["gb", N_PER_A],
            "A. Event - User Locale": ["en", N_PER_A],
            "2024-01-02": [10, 20],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_normalize_table_dataframe_having_a_comma_in_the_groups(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [None, None],
            "_group": ["Berlin, DE, EU", "London"],
            "_agg_value": [10, 20],
            "_event_index": [0, 1],
        }
    )

    webapp_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
            )
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code")
                ],
                aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("page_events", "page_visit"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code")
                ],
                title="Other segment",
                aggregation=WM.WebappAggregationConfig(M.AggType.CONVERSION),
            ),
        ],
        title=None,
        description=None,
        global_filters=[],
    )

    metric_converter = MC.MetricConverter(
        discovered_project.as_metric_definitions(), MagicMock(), MagicMock()
    )
    metric_converter.convert_metric(webapp_metric)
    # FIXME: these methods are mutating the state of the dataframe param
    get_preprocessed_dataframe(
        df, webapp_metric, metric_context, discovered_project.project
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "Label": ["Berlin, DE, EU - A. Checkout", "London - B. Other segment"],
            "A. Event - User Country Code": ["Berlin, DE, EU", N_PER_A],
            "B. Other segment - User Country Code": [N_PER_A, "London"],
            "Conversion rate": [10, 20],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_normalize_table_dataframe_journey(discovered_project: M.DiscoveredProject):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [None],
            "_group": [[(1, "$$$"), (2, "CN")]],
            "_user_count_1": [2],
            "_agg_value_1": [100],
            "_user_count_2": [1],
            "_agg_value_2": [50],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.JOURNEY,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                time_window=M.TimeWindow(3, M.TimeGroup.MONTH),
            )
        ),
        segments=[
            WM.WebappSimpleSegment.create(
                M.EventNamePath("checkouts", "checkout"), None
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code")
                ],
            ),
        ],
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "1. Step": ["Checkout", "Checkout"],
            "2. Step": ["<drop-off>", "CN"],
            "Unique users": [1, 1],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_normalize_table_dataframe_journey_wiht_dropoff(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    df = pd.DataFrame(
        {
            "_datetime": [None],
            "_group": [[(1, "CN"), (2, "***")]],
            "_user_count_1": [2],
            "_agg_value_1": [100],
            "_user_count_2": [0],
            "_agg_value_2": [0],
        }
    )

    webapp_metric = WM.WebappMetric(
        M.MetricType.JOURNEY,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2023, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                time_window=M.TimeWindow(3, M.TimeGroup.MONTH),
            )
        ),
        segments=[
            WM.WebappSimpleSegment.create(
                M.EventNamePath("checkouts", "checkout"), None
            ),
            replace(
                WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "user_country_code")
                ],
            ),
        ],
    )
    result_df = prepare_dataframe_for_table(
        webapp_metric, df, metric_context, is_event_list_modal=False
    )

    expected_df = pd.DataFrame(
        {
            "1. Step": ["CN"],
            "2. Step": [DROP_OFF_LABEL],
            "Unique users": [2],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_pivot_table_retention_with_comparison(webapp_metric_retention_with_comparison):
    df = pd.DataFrame(
        [
            {
                DATETIME_COL: datetime(2023, 1, 1),
                RETENTION_COL: "1",
                "Calculation": 100,
                IS_COMPARISON_COL: False,
                SORTING_COL: 1,
            },
            {
                DATETIME_COL: datetime(2023, 1, 1),
                RETENTION_COL: "1",
                "Calculation": 200,
                IS_COMPARISON_COL: False,
                SORTING_COL: 1,
            },
            {
                DATETIME_COL: datetime(2023, 1, 2),
                RETENTION_COL: "1",
                "Calculation": 150,
                IS_COMPARISON_COL: True,
                SORTING_COL: 1,
            },
            {
                DATETIME_COL: datetime(2023, 1, 2),
                RETENTION_COL: "1",
                "Calculation": 250,
                IS_COMPARISON_COL: True,
                SORTING_COL: 1,
            },
        ]
    )
    index_params = _get_pivot_index_params(
        webapp_metric_retention_with_comparison, df, []
    )
    result_df = _pivot_table_if_needed(
        webapp_metric_retention_with_comparison,
        df,
        index_params=index_params,
        title_label="Calculation",
    )

    expected_df = pd.DataFrame(
        {
            RETENTION_COL: [
                "Week 1",
                "Week 1 (previous)",
            ],
            SORTING_COL: [1, 1],
            datetime(2023, 1, 1): [300.0, None],
            datetime(2023, 1, 2): [None, 400.0],
        }
    )
    expected_df.columns.name = "__datetime__"
    pd.testing.assert_frame_equal(
        result_df.reset_index(drop=True), expected_df.reset_index(drop=True)
    )


def test_pivot_table_segmentation_with_comparison(
    webapp_metric_segmentation_with_comparison,
):
    df = pd.DataFrame(
        [
            {
                "__datetime__": datetime(2021, 12, 1),
                "__label__": "A. Element Clicked On Page",
                "__group__": "<n/a>",
                "Calculation": 32.0,
                "_is_comparison": False,
            },
            {
                "__datetime__": datetime(2021, 12, 2),
                "__label__": "A. Element Clicked On Page",
                "__group__": "<n/a>",
                "Calculation": 40.0,
                "_is_comparison": False,
            },
            {
                "__datetime__": datetime(2021, 12, 2),
                "__label__": "B. Element Clicked On Page (previous)",
                "__group__": "<n/a>",
                "Calculation": 32.0,
                "_is_comparison": True,
            },
            {
                "__datetime__": datetime(2021, 12, 3),
                "__label__": "B. Element Clicked On Page (previous)",
                "__group__": "<n/a>",
                "Calculation": 40.0,
                "_is_comparison": True,
            },
        ]
    )

    expected_df = pd.DataFrame(
        {
            "__label__": [
                "A. Element Clicked On Page",
                "B. Element Clicked On Page (previous)",
            ],
            "__group__": "<n/a>",
            pd.to_datetime("2021-12-01"): [32.0, None],
            pd.to_datetime("2021-12-02"): [40.0, 32.0],
            pd.to_datetime("2021-12-03"): [None, 40.0],
        }
    )
    expected_df.columns.name = "__datetime__"

    result_df = _pivot_table_if_needed(
        webapp_metric_segmentation_with_comparison,
        df,
        index_params=["__label__", "__group__"],
        title_label="Calculation",
    )

    pd.testing.assert_frame_equal(
        result_df.reset_index(drop=True), expected_df.reset_index(drop=True)
    )


def test_prepare_dataframe_for_event_list_table_segmentation_simple_segment(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    event = M.EventNamePath("checkouts", "checkout")

    webapp_metric = WM.WebappMetric(
        M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2021, 1, 1, 0, 0),
                end_dt=datetime(2021, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ),
        segments=[
            replace(
                WM.WebappSimpleSegment.create(event, None),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                group_by=[
                    M.EventFieldPath("checkouts", "checkout", "event_time"),
                    M.EventFieldPath("checkouts", "checkout", "user_country_code"),
                    M.EventFieldPath("checkouts", "checkout", "user_locale"),
                ],
            ),
        ],
    )

    df = pd.DataFrame(
        {
            "_datetime": 3 * [None],
            "_group": [
                "2021-01-01 00:57:58###gb###br_BR",
                "2021-01-01 06:34:37###de###<n/a>",
                "2021-01-01 09:14:42###<n/a>###fr_FR",
            ],
            "_agg_value": [1, 1, 2],
            "_event_index": [0, 0, 0],
        }
    )

    result_df = prepare_dataframe_for_event_list_table(
        webapp_metric, df, metric_context
    )

    assert result_df.columns.tolist() == [
        "Event Time",
        "Event",
        "User Country Code",
        "User Locale",
    ]

    expected_df = pd.DataFrame(
        {
            "Event Time": [
                "2021-01-01 00:57:58",
                "2021-01-01 06:34:37",
                "2021-01-01 09:14:42",
                "2021-01-01 09:14:42",
            ],
            "Event": [
                "Checkout",
                "Checkout",
                "Checkout",
                "Checkout",
            ],
            "User Country Code": ["gb", "de", "<n/a>", "<n/a>"],
            "User Locale": ["br_BR", "<n/a>", "fr_FR", "fr_FR"],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_prepare_dataframe_for_event_list_table_segmentation_complex_segment(
    discovered_project: M.DiscoveredProject,
):
    metric_context = create_metric_context_from_discovered_project(discovered_project)

    webapp_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=datetime(2021, 1, 1, 0, 0),
                end_dt=datetime(2021, 6, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ),
        segments=[
            WM.WebappComplexSegment(
                left=WM.WebappSimpleSegment.create(
                    M.EventNamePath("checkouts", "checkout"), None
                ),
                operator=M.BinaryOperator.OR,
                right=WM.WebappSimpleSegment.create(
                    M.EventNamePath("page_events", "page_visit"), None
                ),
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                group_by=[
                    M.EVENT_TIME_FIELD_PATH,
                    M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
                    M.EventFieldPath("checkouts", "checkout", "user_country_code"),
                    M.EventFieldPath(
                        "page_events", "page_visit", "acquisition_campaign"
                    ),
                ],
                filter=None,
                title=None,
                expanded_custom_event_key=None,
            ),
        ],
        title=None,
        description=None,
    )

    df = pd.DataFrame(
        {
            "_datetime": 3 * [None],
            "_group": [
                "2021-01-01 00:57:58###<n/a>###_subsegment_index_0_0###gb###google",
                "2021-01-01 06:34:37###<n/a>###_subsegment_index_0_0###de###<n/a>",
                "<n/a>###2021-01-01 09:14:42###_subsegment_index_0_1###<n/a>###reddit",
            ],
            "_agg_value": [1, 1, 2],
            "_event_index": [0, 0, 0],
        }
    )

    result_df = prepare_dataframe_for_event_list_table(
        webapp_metric, df, metric_context
    )

    assert result_df.columns.tolist() == [
        "Event Time",
        "Event",
        "User Country Code",
        "Acquisition Campaign",
    ]

    expected_df = pd.DataFrame(
        {
            "Event Time": [
                "2021-01-01 00:57:58",
                "2021-01-01 06:34:37",
                "2021-01-01 09:14:42",
                "2021-01-01 09:14:42",
            ],
            "Event": [
                "Checkout (1)",
                "Checkout (1)",
                "Page Visit (2)",
                "Page Visit (2)",
            ],
            "User Country Code": ["gb", "de", "<n/a>", "<n/a>"],
            "Acquisition Campaign": ["google", "<n/a>", "reddit", "reddit"],
        }
    )

    assert_dataframe_equals(result_df, expected_df)


def test_generate_journey_dropoff_paths():
    data = {
        "_datetime": [None],
        "_group": [[(1, "step1"), (2, "step2"), (3, "step3")]],
        "_user_count_1": [40],
        "_user_count_2": [20],
        "_user_count_3": [10],
    }

    result_df = pd.DataFrame(data)
    result_df = generate_journey_dropoff_paths(result_df)

    expected_data = {
        "_datetime": [None, None, None],
        "_group": [
            [(1, "step1"), (2, "step2"), (3, f"{DROP_OFF_PLACEHOLDER}")],
            [(1, "step1"), (2, "step2"), (3, "step3")],
            [(1, "step1"), (2, f"{DROP_OFF_PLACEHOLDER}"), (3, "")],
        ],
        "_user_count_": [10, 10, 20],
        SORTING_COL: [10, 10, 20],
    }

    expected_df = pd.DataFrame(expected_data)

    result_df = result_df.sort_values(by=["_user_count_"]).reset_index(drop=True)
    expected_df = expected_df.sort_values(by=["_user_count_"]).reset_index(drop=True)

    assert_dataframe_equals(result_df, expected_df)


def test_generate_journey_dropoff_paths_with_group_by():
    data = {
        "_datetime": [None, None, None],
        "_group": [
            [(1, "group1"), (2, "***"), (3, "***")],
            [(1, "group2"), (2, "group2"), (3, "***")],
            [(1, "group3"), (2, "group3"), (3, "group3")],
        ],
        "_user_count_1": [40, 40, 40],
        "_user_count_2": [20, 20, 20],
        "_user_count_3": [10, 10, 10],
    }

    result_df = pd.DataFrame(data)
    result_df = generate_journey_dropoff_paths(result_df)

    expected_df = pd.DataFrame(
        {
            "_datetime": [None] * 9,
            "_group": [
                [(1, "group1"), (2, "***"), (3, "***")],
                [(1, "group1"), (2, "***"), (3, "***")],
                [(1, "group2"), (2, "group2"), (3, "***")],
                [(1, "group2"), (2, "group2"), (3, "***")],
                [(1, "group3"), (2, "group3"), (3, "***")],
                [(1, "group3"), (2, "group3"), (3, "group3")],
                [(1, "group1"), (2, "***"), (3, "")],
                [(1, "group2"), (2, "***"), (3, "")],
                [(1, "group3"), (2, "***"), (3, "")],
            ],
            "_user_count_": [10, 10, 10, 10, 10, 10, 20, 20, 20],
            SORTING_COL: [10, 10, 10, 10, 10, 10, 20, 20, 20],
        }
    )
    result_df = result_df.sort_values(by=["_user_count_"]).reset_index(drop=True)
    expected_df = expected_df.sort_values(by=["_user_count_"]).reset_index(drop=True)

    assert_dataframe_equals(result_df, expected_df)


@pytest.mark.parametrize(
    "columns,expected_order",
    [
        (
            [
                GA.AGG_VALUE_COL,
                "1. Unique users",
                GA.GROUP_COL,
                "2. Unique users",
                LABEL_COL,
                PROBABILITY_COL,
                "random_col",
            ],
            [
                LABEL_COL,
                GA.GROUP_COL,
                "1. Unique users",
                "2. Unique users",
                "random_col",
                GA.AGG_VALUE_COL,
                PROBABILITY_COL,
            ],
        ),
        (
            ["a", "b", "c", PROBABILITY_COL],
            ["a", "b", "c", PROBABILITY_COL],
        ),
        (
            [LABEL_COL, "X Group", "custom_metric", GA.AGG_VALUE_COL],
            [LABEL_COL, "X Group", "custom_metric", GA.AGG_VALUE_COL],
        ),
    ],
)
def test_reorder_table_columns(columns, expected_order):
    df = pd.DataFrame(columns=columns)
    result = reorder_table_columns(df, unique_field_name="Unique users")
    assert result.columns.tolist() == expected_order
