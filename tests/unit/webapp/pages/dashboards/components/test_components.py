from dataclasses import replace
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import dash_mantine_components as dmc
from flask import Flask

import mitzu.webapp.model as WM
from mitzu.model import MetricType, Project
from mitzu.visualization.common import SimpleChart
from mitzu.webapp.dependencies import Dependencies
from mitzu.webapp.pages.dashboards.components.components import (
    create_dashboard_metric_card_model,
    get_last_updated,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)


def test_refresh_dashboard_refresh_less_than_one_day(
    server: Flask,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        refresh_text = get_last_updated(dashboard_with_metric)
        assert "Refreshed less than a minute ago" in refresh_text.children
        assert refresh_text.color == "dark"


def test_refresh_dashboard_refresh_more_than_one_day(
    server: Flask,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        dash = replace(
            dashboard_with_metric, last_updated_at=(datetime.now() - timedelta(days=2))
        )
        refresh_text = get_last_updated(dash)
        assert isinstance(refresh_text, dmc.Text)
        assert "Refreshed 2 days ago" in refresh_text.children
        assert refresh_text.color == "red"


@patch("mitzu.webapp.pages.dashboards.components.components.PLT")
def test_create_dashboard_metric_card_model_use_consistent_coloring_overrides(PLT):
    sm = MagicMock()
    wm = WM.WebappMetric.create_empty_webapp_metric(MetricType.SEGMENTATION)
    wm = replace(wm, config=replace(wm.config, use_consistent_coloring=False))
    sm.metric.return_value = wm

    sm.project_id = "test"
    # This will be overridden by the use_consistent_coloring argument later

    create_dashboard_metric_card_model(
        dm=WM.DashboardMetricItem(
            saved_metric=sm,
            x=0,
            y=0,
            id="dm-0",
            width=1,
            height=1,
        ),
        loading=False,
        metric_converter=MagicMock(),
        metric_context=MagicMock(),
        is_user_read_only=False,
        annotations=[],
        is_shared=False,
        default_end_dt=datetime(2025, 3, 23),
        use_consistent_coloring=True,
        insight_result=MagicMock(),
        dashboard_has_filters=False,
    )

    # Making sure we are rendering the card with use_consistent_coloring=True
    assert PLT.plot_chart.call_args[0][4].config.use_consistent_coloring is True


def test_create_dashboard_metric_card_model_dashboard_has_filters(
    server: Flask,
    dashboard_with_metric: WM.Dashboard,
    project: Project,
    dependencies: Dependencies,
    insight_result: SimpleChart,
):
    metric_context = dependencies.get_catalog_service(
        project.id
    ).get_metric_context_for_events([])
    metric_updater = MetricUpdater(metric_context, project.insight_settings)
    project_storage = dependencies.get_project_storage(project.id)
    metric_defs = project_storage.get_definitions_for_dashboard(
        dashboard_with_metric, WM.DashboardFilter.empty()
    )
    metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)

    card = create_dashboard_metric_card_model(
        dm=dashboard_with_metric.dashboard_metrics[0],
        loading=False,
        metric_converter=metric_converter,
        metric_context=metric_context,
        is_user_read_only=False,
        annotations=[],
        is_shared=False,
        default_end_dt=datetime(2025, 3, 23),
        use_consistent_coloring=True,
        insight_result=insight_result,
        dashboard_has_filters=True,
    )

    assert (
        card.edit_href
        == f"/projects/{project.id}/explore?m={insight_result.metric_json}"
    )
    card = create_dashboard_metric_card_model(
        dm=dashboard_with_metric.dashboard_metrics[0],
        loading=False,
        metric_converter=metric_converter,
        metric_context=metric_context,
        is_user_read_only=False,
        annotations=[],
        is_shared=False,
        default_end_dt=datetime(2025, 3, 23),
        use_consistent_coloring=True,
        insight_result=insight_result,
        dashboard_has_filters=False,
    )
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    assert (
        card.edit_href
        == f"/projects/{project.id}/explore?sm={dashboard_with_metric.dashboard_metrics[0].saved_metric.id}"
    )
