from dataclasses import replace
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import dash_mantine_components as dmc
from flask import Flask

import mitzu.webapp.model as WM
from mitzu.model import MetricType
from mitzu.webapp.pages.dashboards.components.components import (
    create_dashboard_metric_card_model,
    get_last_updated,
)


def test_refresh_dashboard_refresh_less_than_one_day(
    server: Flask,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        refresh_text = get_last_updated(dashboard_with_metric)
        assert "Refreshed less than a minute ago" in refresh_text.children
        assert refresh_text.color == "dark"


def test_refresh_dashboard_refresh_more_than_one_day(
    server: Flask,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        dash = replace(
            dashboard_with_metric, last_updated_at=(datetime.now() - timedelta(days=2))
        )
        refresh_text = get_last_updated(dash)
        assert isinstance(refresh_text, dmc.Text)
        assert "Refreshed 2 days ago" in refresh_text.children
        assert refresh_text.color == "red"


@patch("mitzu.webapp.pages.dashboards.components.components.PLT")
def test_create_dashboard_metric_card_model_use_consistent_coloring_overrides(PLT):
    sm = MagicMock()
    wm = WM.WebappMetric.create_empty_webapp_metric(MetricType.SEGMENTATION)
    wm = replace(wm, config=replace(wm.config, use_consistent_coloring=False))
    sm.metric.return_value = wm

    sm.project_id = "test"
    # This will be overridden by the use_consistent_coloring argument later

    create_dashboard_metric_card_model(
        dm=WM.DashboardMetricItem(
            saved_metric=sm,
            x=0,
            y=0,
            id="dm-0",
            width=1,
            height=1,
        ),
        loading=False,
        metric_converter=MagicMock(),
        metric_context=MagicMock(),
        is_user_read_only=False,
        annotations=[],
        is_shared=False,
        default_end_dt=datetime(2025, 3, 23),
        use_consistent_coloring=True,
        insight_result=MagicMock(),
    )

    # Making sure we are rendering the card with use_consistent_coloring=True
    assert PLT.plot_chart.call_args[0][4].config.use_consistent_coloring is True
