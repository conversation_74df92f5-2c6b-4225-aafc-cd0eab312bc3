import base64
from copy import copy
from dataclasses import replace
from datetime import datetime, timedelta
from io import BytesIO
from typing import List, cast
from unittest.mock import MagicMock, patch

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import flask
import pandas as pd
import pytest
import sqlalchemy as SA
from dash import no_update
from flask import Flask
from freezegun import freeze_time

import mitzu.model as M
import mitzu.visualization.common as VC
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.dashboards.constants as DC
import mitzu.webapp.pages.dashboards.manage_dashboard as MD
import mitzu.webapp.pages.dashboards.manage_dashboards_component as MDC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.serialization as SE
import mitzu.webapp.storage.storage_model as SM
from mitzu.webapp.pages.common.share_modal import (
    handle_toggle_dashboard_sharing,
)
from mitzu.webapp.pages.dashboards.helper import (
    get_metric_context_for_dashboard,
    get_time_window_config,
)
from mitzu.webapp.pages.explore.time_window_chooser import (
    DEFAULT_VALUE,
)
from mitzu.webapp.service.dashboard_report_service import (
    DashboardReportChartCard,
    DashboardReportDividerCard,
    DashboardReportTextCard,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from tests.helper import (
    create_cohort,
    find_component_by_id,
    to_dict,
)

empty_dashboard_filter_string = SE.dashboard_filter_to_string(
    WM.DashboardFilter.empty()
)


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.MDC")
def test_layout(
    mdc_mock,
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    insight_result: VC.SimpleChart,
):
    mdc_mock.create_manage_dashboard_container.return_value = "dashboard container"
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    with server.test_request_context():
        project_storage = dependencies.get_project_storage(
            dashboard_with_metric.project_id
        )
        saved_metric = dashboard_with_metric.dashboard_metrics[0].saved_metric
        if saved_metric is None:
            raise ValueError("it's not a saved metric")
        ir = replace(insight_result, metric_json=saved_metric.metric_json)
        project_storage.set_insight_result(saved_metric.id, ir)
        res = MD.layout(dashboard_with_metric.project_id, dashboard_with_metric.id)
        assert res.children[2] == "dashboard container"

    mdc_mock.create_manage_dashboard_container.assert_called_once_with(
        dashboard_with_metric,
        dashboard_with_metric.project_id,
        WM.DashboardFilter.empty(),
        is_user_read_only=False,
        insight_results={saved_metric.id: ir},
    )


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.MDC")
def test_layout_with_dashboard_filter(
    mdc_mock,
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    insight_result: VC.SimpleChart,
    project: M.Project,
):
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    mdc_mock.create_manage_dashboard_container.return_value = "dashboard container"
    dfilter = WM.DashboardFilter(
        filters=[
            WM.WebappEventFilter(
                M.DimensionFieldPath("user_profiles", "user_country_code"),
                M.Operator.ANY_OF,
                ["gb"],
            )
        ],
        time_config=None,
    )
    project_storage = dependencies.get_project_storage(dashboard_with_metric.project_id)
    metric_context = get_metric_context_for_dashboard(
        dashboard_with_metric, dependencies.get_catalog_service(project.id)
    )
    project = project_storage.get_project()

    metric_updater = MetricUpdater(metric_context, project.insight_settings)
    saved_metric = dashboard_with_metric.dashboard_metrics[0].saved_metric
    if saved_metric is None:
        raise ValueError("it's not a saved metric")
    updated_metric = metric_updater.apply_dashboard_filters(
        saved_metric.metric(), dfilter
    )
    ir = replace(insight_result, metric_json=SE.to_compressed_string(updated_metric))
    project_storage.set_insight_result(saved_metric.id, ir)
    with server.test_request_context():
        res = MD.layout(
            dashboard_with_metric.project_id,
            dashboard_with_metric.id,
            dfilter=SE.dashboard_filter_to_string(dfilter),
        )
        assert res.children[2] == "dashboard container"

    mdc_mock.create_manage_dashboard_container.assert_called_once_with(
        dashboard_with_metric,
        dashboard_with_metric.project_id,
        dfilter,
        is_user_read_only=False,
        insight_results={saved_metric.id: ir},
    )


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.MDC")
def test_layout_with_dashboard_default_filter(
    mdc_mock,
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    insight_result: VC.SimpleChart,
    project: M.Project,
):
    mdc_mock.create_manage_dashboard_container.return_value = "dashboard container"
    dfilter = WM.DashboardFilter(
        filters=[
            WM.WebappEventFilter(
                M.DimensionFieldPath("user_profiles", "user_country_code"),
                M.Operator.ANY_OF,
                ["gb"],
            )
        ],
        time_config=None,
    )
    dashboard_with_metric = replace(dashboard_with_metric, default_filter=dfilter)
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    project_storage = dependencies.get_project_storage(dashboard_with_metric.project_id)
    project_storage.set_dashboard(dashboard_with_metric)
    metric_context = get_metric_context_for_dashboard(
        dashboard_with_metric, dependencies.get_catalog_service(project.id)
    )
    project = project_storage.get_project()

    metric_updater = MetricUpdater(metric_context, project.insight_settings)
    saved_metric = dashboard_with_metric.dashboard_metrics[0].saved_metric
    if saved_metric is None:
        raise ValueError("it's not a saved metric")
    updated_metric = metric_updater.apply_dashboard_filters(
        saved_metric.metric(), dfilter
    )
    ir = replace(insight_result, metric_json=SE.to_compressed_string(updated_metric))
    project_storage.set_insight_result(saved_metric.id, ir)
    with server.test_request_context():
        res = MD.layout(
            dashboard_with_metric.project_id,
            dashboard_with_metric.id,
        )
        assert res.children[2] == "dashboard container"

    mdc_mock.create_manage_dashboard_container.assert_called_once_with(
        dashboard_with_metric,
        dashboard_with_metric.project_id,
        dfilter,
        is_user_read_only=False,
        insight_results={saved_metric.id: ir},
    )


def test_dashboard_delete_confirmed(
    server: Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=dashboard.project_id,
            dashboard_id=dashboard.id,
        )
        res = MD.delete_dashboard_accepted(1, pathname)
        project_storage = dependencies.get_project_storage(dashboard.project_id)
        assert res is not None
        assert len(res) == 2
        assert res[0] == P.create_path(
            P.PROJECT_HOME_PATH, project_id=discovered_project.project.id
        )
        dashes = project_storage.list_dashboards_infos()
        assert len(dashes) == 0


def test_dashboard_name_changed(
    server: Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        res = MDC.dashboard_name_changed(
            "new_name",
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard.id,
                project_id=dashboard.project_id,
            ),
        )
        dashboard_storage = dependencies.get_project_storage(
            dashboard.project_id
        ).get_dashboard_storage(dashboard.id)

        assert res == "new_name"
        assert dashboard_storage.get_dashboard().name == "new_name"


def test_dashboard_desc_changed(
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        new_desc = "new_desc"
        res = MDC.dashboard_description_changed(
            new_desc,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard.id,
                project_id=dashboard.project_id,
            ),
        )
        dashboard_storage = dependencies.get_project_storage(
            dashboard.project_id
        ).get_dashboard_storage(dashboard.id)

        assert res == new_desc
        assert dashboard_storage.get_dashboard().description == new_desc

        res = MDC.dashboard_description_changed(
            "",
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard.id,
                project_id=dashboard.project_id,
            ),
        )

        assert res == ""
        assert dashboard_storage.get_dashboard().description is None


@patch("mitzu.webapp.pages.dashboards.components.components.PLT")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_delete_saved_metric(
    ctx,
    plt,
    server: Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        dashboard_metric_id = dashboard_with_metric.dashboard_metrics[0].id
        ctx.triggered_id = {
            "type": MDC.DELETE_SAVED_METRICS_TYPE,
            "index": dashboard_metric_id,
        }
        res = MDC.handle_chart_remove(
            delete_click=1,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                project_id=discovered_project.project.id,
                dashboard_id=dashboard_with_metric.id,
            ),
        )
        dashboard_storage = dependencies.get_project_storage(
            dashboard_with_metric.project_id
        ).get_dashboard_storage(dashboard_with_metric.id)

        assert res is not None
        assert res == "d-none"
        new_dash = dashboard_storage.get_dashboard()
        assert len(new_dash.dashboard_metrics) == 0


@patch("mitzu.webapp.pages.dashboards.components.components.PLT")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_resize_dashboard_metric(
    ctx,
    plt,
    server: Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    dashboard_with_metric: WM.Dashboard,
):
    dashboard_metric_id = dashboard_with_metric.dashboard_metrics[0].id

    with server.test_request_context():
        ctx.triggered_id = {
            "type": MDC.DELETE_SAVED_METRICS_TYPE,
            "index": dashboard_metric_id,
            "size": "12",
        }
        res = MDC.handle_chart_resize(
            resize_click=1,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                project_id=discovered_project.project.id,
                dashboard_id=dashboard_with_metric.id,
            ),
        )
        dashboard_storage = dependencies.get_project_storage(
            dashboard_with_metric.project_id
        ).get_dashboard_storage(dashboard_with_metric.id)
        assert res is not None
        assert res == 12

        ctx.triggered_id = {
            "type": MDC.DELETE_SAVED_METRICS_TYPE,
            "index": dashboard_metric_id,
            "size": "6",
        }
        res = MDC.handle_chart_resize(
            resize_click=1,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                project_id=discovered_project.project.id,
                dashboard_id=dashboard_with_metric.id,
            ),
        )
        assert res is not None
        assert res == 6
        new_dash = dashboard_storage.get_dashboard()
        assert len(new_dash.dashboard_metrics) == 1


@freeze_time("2023-08-01 00:00:00")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_refresh_dashboard(
    ctx,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    dependencies.tracking_service.track_explore_finished = MagicMock()  # type: ignore
    with server.test_request_context():
        project = discovered_project.project
        project_storage = dependencies.get_project_storage(project.id)
        ctx.triggered_id = DC.DASHBOARD_REFRESH_BUTTON
        res, last_upd, _, last_dfilter = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=1,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=DEFAULT_VALUE,
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard_with_metric.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        )
        dependencies.tracking_service.track_explore_finished.assert_called_once()

        last_upd = to_dict(last_upd)
        assert last_upd["children"] == [
            "Refreshed less than a minute ago",
        ]
        assert last_upd["color"] == "dark"
        assert last_dfilter == empty_dashboard_filter_string
        res = to_dict(res)
        assert len(res["children"]) == 3  # new metric plus the add insight card

        saved_metrics_info = project_storage.list_saved_metrics()[0]
        assert saved_metrics_info.last_updated_at == datetime(2023, 8, 1)
        assert saved_metrics_info.name == "test_saved_metric"
        assert saved_metrics_info.description == "this is a saved metric description"

        dashboard = project_storage.get_dashboard_storage(
            dashboard_with_metric.id
        ).get_dashboard()
        assert dashboard.last_updated_at == datetime(2023, 8, 1)


@freeze_time("2023-08-01 00:00:00")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_refresh_dashboard_tracking_called_for_all_metrics(
    ctx,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    dependencies.tracking_service.track_explore_finished = MagicMock()  # type: ignore
    with server.test_request_context():
        project = discovered_project.project
        project_storage = dependencies.get_project_storage(project.id)
        dashboard_with_metric.dashboard_metrics.append(
            copy(dashboard_with_metric.dashboard_metrics[0])
        )
        project_storage.set_dashboard(dashboard_with_metric)
        ctx.triggered_id = DC.DASHBOARD_REFRESH_BUTTON
        _, _, _, _ = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=1,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=DEFAULT_VALUE,
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard_with_metric.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        )
        dependencies.tracking_service.track_explore_finished.assert_called()
        dependencies.tracking_service.track_explore_finished.call_count == 2


@patch("mitzu.webapp.service.dashboard_service.create_adapter_from_project")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_refresh_dashboard_with_cohort_global_filter(
    ctx,
    adapter_mock,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    adapter_mock.return_value = adapter_mock
    adapter_mock.get_df.return_value = pd.DataFrame()
    with server.test_request_context():
        project = discovered_project.project
        project_storage = dependencies.get_project_storage(project.id)
        ctx.triggered_id = DC.DASHBOARD_REFRESH_BUTTON

        cohort = create_cohort(
            dependencies.user_service,
            event_name_path=M.EventNamePath("add_to_carts", "add_to_cart"),
        )
        project_storage.set_cohort(cohort)

        res, last_upd, error, last_dfilter = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=1,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=DEFAULT_VALUE,
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard_with_metric.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[str(M.CollectionFieldPath(M.USER_ENTITY))],
            operators=[M.Operator.IN_COHORT.value],
            values=[cohort.id],
        )
        assert error == []  # this means that the query was generated successfully
        assert SE.dashboard_filter_from_string(last_dfilter) == WM.DashboardFilter(
            filters=[
                WM.WebappEventFilter(
                    M.CollectionFieldPath(M.USER_ENTITY),
                    M.Operator.IN_COHORT,
                    WM.CohortReference(cohort.id),
                )
            ],
            time_config=None,
        )
        last_upd = to_dict(last_upd)
        assert last_upd["children"] == [
            "Refreshed less than a minute ago",
        ]
        assert last_upd["color"] == "dark"
        res = to_dict(res)
        assert len(res["children"]) == 3  # new metric plus the add insight card

        # last updated fields are not updated because of the filter
        saved_metrics_info = project_storage.list_saved_metrics()[0]
        assert dashboard_with_metric.dashboard_metrics[0].saved_metric is not None
        assert (
            saved_metrics_info.last_updated_at
            == dashboard_with_metric.dashboard_metrics[0].saved_metric.last_updated_at
        )
        assert saved_metrics_info.name == "test_saved_metric"
        assert saved_metrics_info.description == "this is a saved metric description"

        dashboard = project_storage.get_dashboard_storage(
            dashboard_with_metric.id
        ).get_dashboard()
        assert dashboard.last_updated_at == dashboard_with_metric.last_updated_at


@freeze_time("2023-08-01 12:00:00")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.refresh_dashboard")
@patch(
    "mitzu.webapp.pages.dashboards.manage_dashboards_component.create_responsive_grid_layout"
)
def test_refresh_dashboard_use_consistent_coloring(
    create_responsive_grid_layout,
    refresh_dashboard,
    ctx,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        project = discovered_project.project
        ctx.triggered_id = DC.DASHBOARD_USE_CONSISTENT_COLORING

        with dependencies.storage._new_db_session() as session:
            session.execute(
                SA.text("UPDATE dashboards SET last_updated_at = '2023-06-01 00:00:00'")
            )
            session.commit()

        res, last_upd, _, last_dfilter = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=1,
            use_consistent_coloring=True,
            custom_dates=[],
            lookback_days=DEFAULT_VALUE,
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard_with_metric.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        )
        last_upd = to_dict(last_upd)
        assert last_dfilter == empty_dashboard_filter_string

        assert refresh_dashboard.call_count == 0

        assert last_upd["children"] == [
            "Refreshed 61 days ago",
        ]
        assert create_responsive_grid_layout.call_count == 1
        assert (
            create_responsive_grid_layout.call_args[0][0].use_consistent_coloring
            is True
        )


@freeze_time("2023-08-01 00:00:00")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_refresh_dashboard_with_overwrite(
    ctx,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    insight_result: VC.SimpleChart,
):
    with server.test_request_context():
        project = discovered_project.project

        project_storage = dependencies.get_project_storage(project.id)
        project_storage.set_dashboard(dashboard_with_metric)
        for dm in dashboard_with_metric.dashboard_metrics:
            if not isinstance(dm, WM.DashboardMetricItem):
                continue
            ir = replace(
                insight_result,
                metric_json=dm.saved_metric.metric_json,
            )
            project_storage.set_insight_result(dm.saved_metric.id, ir)
        ctx.triggered_id = DC.DASHBOARD_REFRESH_BUTTON

        res, last_upd, error, last_dfilter = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=1,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=str(M.TimeWindow(1, M.TimeGroup.YEAR)),
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dashboard_with_metric.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        )
        assert error == []
        last_upd = to_dict(last_upd)
        assert SE.dashboard_filter_from_string(last_dfilter) == WM.DashboardFilter(
            filters=[],
            time_config=WM.TimeWindowChooserConfig(
                None,
                None,
                M.TimeGroup.WEEK,
                M.TimeWindow(1, M.TimeGroup.YEAR),
                None,
                None,
            ),
        )
        assert last_upd["children"] == [
            "Refreshed less than a minute ago",
        ]
        assert last_upd["color"] == "dark"
        res = to_dict(res)
        comp = cast(dict, find_component_by_id("datetime_text_id", res))
        assert (
            comp["children"]
            == "Last 1 year, Daily | this is a saved metric description"
        )

        dashboard = project_storage.get_dashboard_storage(
            dashboard_with_metric.id
        ).get_dashboard()
        assert dashboard.last_updated_at == dashboard_with_metric.last_updated_at


@freeze_time("2023-08-01 00:00:00")
@patch("mitzu.webapp.pages.dashboards.manage_dashboards_component.ctx")
def test_refresh_dashboard_page_open(
    ctx,
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        ctx.triggered_id = None
        project = discovered_project.project
        project_storage = dependencies.get_project_storage(project.id)

        dash = replace(
            dashboard_with_metric,
            last_updated_at=(datetime.now() - timedelta(days=253)),
        )
        project_storage.set_dashboard(dash)

        assert MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=None,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=str(M.TimeWindow(1, M.TimeGroup.YEAR)),
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dash.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        ) == (no_update, no_update, no_update, no_update)


def test_refresh_dashboard_page_raises_error_when_trial_expired(
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):
    with server.test_request_context():
        project = discovered_project.project

        with dependencies.storage._new_db_session() as session:
            sub = (
                session.query(SM.SubscriptionStorageRecord)
                .filter_by(org_id=dependencies.org_id)
                .one()
            )
            sub.expiration_date = datetime.now() - timedelta(days=3)
            session.commit()

        dash = replace(
            dashboard_with_metric,
            last_updated_at=(datetime.now() - timedelta(days=253)),
        )
        project_storage = dependencies.get_project_storage(project.id)
        project_storage.set_dashboard(dash)

        res, last_upd, error, last_dfilter = MDC.refresh_dashboards(
            set_progress=lambda _: None,
            refresh_button_clicks=None,
            use_consistent_coloring=False,
            custom_dates=[],
            lookback_days=str(M.TimeWindow(1, M.TimeGroup.YEAR)),
            calendar_date_range=None,
            pathname=P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                dashboard_id=dash.id,
                project_id=project.id,
            ),
            since_date=None,
            properties=[],
            operators=[],
            values=[],
        )
        assert res == no_update
        assert last_upd == no_update
        assert last_dfilter == no_update
        assert isinstance(error, dmc.Alert)
        assert error.title == "Your trial expired. Upgrade your package to continue."


def test_dashboard_reorder(
    server: Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    dashboard_with_metric: WM.Dashboard,
):
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    with server.test_request_context():
        saved_metric = dashboard_with_metric.dashboard_metrics[0].saved_metric
        sm2 = replace(saved_metric, id="test_sm2")
        project_storage = dependencies.get_project_storage(
            discovered_project.project.id
        )

        project_storage.set_saved_metric(saved_metric=sm2)
        dashboard_with_metric.dashboard_metrics.append(
            WM.DashboardMetricItem(
                id="test_sm2_dm",
                saved_metric=sm2,
                x=0,
                y=24,
                width=10,
                height=8,
            )
        )

        project_storage.set_dashboard(dashboard_with_metric)

        assert [d.y for d in dashboard_with_metric.dashboard_metrics] == [0, 24]
        chart_indices = [
            f'{{"index":"{dashboard_with_metric.dashboard_metrics[i].id}","type":"{DC.DASHBOARD_GRID_ITEM}"}}'
            for i in range(len(dashboard_with_metric.dashboard_metrics))
        ]

        MDC.manage_chart_reorder(
            chart_indices,
            P.create_path(
                P.DASHBOARDS_EDIT_PATH,
                project_id=discovered_project.project.id,
                dashboard_id=dashboard_with_metric.id,
            ),
        )

        dash = project_storage.get_dashboard_storage(
            dashboard_with_metric.id
        ).get_dashboard()
        assert [d.y for d in dash.dashboard_metrics] == [0, 8]


def test_get_metric_config_overwrite():
    mc = get_time_window_config(
        None, M.TimeWindow(1, M.TimeGroup.YEAR), M.TimeGroup.TOTAL, None, None
    )
    assert mc is not None
    assert mc.time_group == M.TimeGroup.TOTAL
    assert mc.lookback_days == M.TimeWindow(1, M.TimeGroup.YEAR)

    mc = get_time_window_config(
        None, M.TimeWindow(1, M.TimeGroup.YEAR), M.TimeGroup.DAY, None, None
    )
    assert mc is not None
    assert mc.time_group == M.TimeGroup.WEEK
    assert mc.lookback_days == M.TimeWindow(1, M.TimeGroup.YEAR)

    mc = get_time_window_config(
        None, M.TimeWindow(2, M.TimeGroup.YEAR), M.TimeGroup.DAY, None, None
    )
    assert mc is not None
    assert mc.time_group == M.TimeGroup.MONTH
    assert mc.lookback_days == M.TimeWindow(2, M.TimeGroup.YEAR)


@pytest.mark.parametrize(
    "custom_dates, lookback_time_window, time_group, calendar_date_range, expected_time_group, expected_lookback_days",
    [
        (
            (datetime(2024, 1, 1), datetime(2024, 1, 10)),
            None,
            M.TimeGroup.TOTAL,
            None,
            M.TimeGroup.TOTAL,
            M.DEF_LOOK_BACK_DAYS,
        ),
        (
            None,
            None,
            M.TimeGroup.HOUR,
            WM.CalendarDateRange.TODAY,
            M.TimeGroup.HOUR,
            None,
        ),
        (None, None, M.TimeGroup.TOTAL, None, None, None),
    ],
)
def test_get_time_window_config_edge_cases(
    custom_dates,
    lookback_time_window,
    time_group,
    calendar_date_range,
    expected_time_group,
    expected_lookback_days,
):
    result = get_time_window_config(
        custom_dates, lookback_time_window, time_group, calendar_date_range, None
    )

    if custom_dates or lookback_time_window or calendar_date_range:
        assert result is not None
        assert result.time_group == expected_time_group
        assert result.lookback_days == expected_lookback_days
    else:
        assert result is None


@pytest.mark.parametrize(
    "lookback_time_window, time_group, expected_time_group",
    [
        (M.TimeWindow(1, M.TimeGroup.YEAR), M.TimeGroup.DAY, M.TimeGroup.WEEK),
        (M.TimeWindow(1, M.TimeGroup.YEAR), M.TimeGroup.TOTAL, M.TimeGroup.TOTAL),
    ],
)
def test_get_time_window_config_with_lookback(
    lookback_time_window, time_group, expected_time_group
):
    result = get_time_window_config(None, lookback_time_window, time_group, None, None)
    assert result is not None
    assert result.time_group == expected_time_group
    assert result.lookback_days == lookback_time_window


def test_handle_toggle_dashboard_sharing(
    server: Flask,
    project: M.Project,
    dependencies: DEPS.Dependencies,
    dashboard: WM.Dashboard,
):
    project_storage = dependencies.get_project_storage(project.id)
    project_storage.set_dashboard(dashboard)
    with server.test_request_context():
        res2, res1 = handle_toggle_dashboard_sharing(
            True,
            P.create_path(
                P.DASHBOARDS_EDIT_PATH, project_id=project.id, dashboard_id=dashboard.id
            ),
        )
        assert res1 == "d-inline-block"
        assert res2 == "d-inline-block"

    assert (
        project_storage.get_dashboard_storage(dashboard.id).get_dashboard().shared
        is True
    )


def test_duplicate_dashboard_confirmed(
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=dashboard.project_id,
            dashboard_id=dashboard.id,
        )
        res = MD.duplicate_dashboard_confirmed(1, "dashboard_name", pathname, False)
        project_storage = dependencies.get_project_storage(dashboard.project_id)
        assert res is not None
        dasheboards = project_storage.list_dashboards_infos()
        assert len(dasheboards) == 2


def test_snapshot_dashboard_confirmed(
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=dashboard.project_id,
            dashboard_id=dashboard.id,
        )
        res = MD.snapshot_dashboard_confirmed(1, pathname, "text")
        project_storage = dependencies.get_project_storage(dashboard.project_id)
        assert res is not None
        dasheboards = project_storage.list_dashboards_infos()
        assert len(dasheboards) == 2
        assert dasheboards[1].name == "text"


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_display_textarea(
    ctx,
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
):

    with server.test_request_context():
        project_storage = dependencies.get_project_storage(
            dashboard_with_metric.project_id
        )
        dashboard_storage = project_storage.get_dashboard_storage(
            dashboard_with_metric.id
        )

        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=dashboard_with_metric.project_id,
            dashboard_id=dashboard_with_metric.id,
        )
        ctx.args_grouping = []
        ctx.triggered_id = DC.ADD_TEXT_TO_DASHBOARD_BUTTON

        res = MD.display_textarea(1, "", pathname)
        assert type(res[0]) == dbc.Col
        assert res[1] == no_update
        dashboard_with_metric = dashboard_storage.get_dashboard()
        assert len(dashboard_with_metric.dashboard_metrics) == 2


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_text_updated(
    dash_ctx,
    server: Flask,
    dependencies: DEPS.Dependencies,
    dashboard: WM.Dashboard,
):
    with server.test_request_context():
        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=dashboard.project_id,
            dashboard_id=dashboard.id,
        )
        dashboard_metric = WM.DashboardTextItem(
            text="",
            x=0,
            y=0,
            width=10,
            height=8,
            id="dm-text",
        )
        project_storage = dependencies.get_project_storage(dashboard.project_id)

        dashboard.dashboard_metrics.append(dashboard_metric)
        project_storage.set_dashboard(dashboard)
        dash_ctx.triggered_id = {
            "type": DC.DASHBOARD_TEXT_AREA,
            "index": dashboard_metric.id,
        }
        res = MD.text_updated("new value", pathname)
        stored_dashboard_metric = project_storage.get_dashboard_metric(
            dashboard_metric_id=dashboard_metric.id
        )
        assert isinstance(stored_dashboard_metric, WM.DashboardTextItem)
        assert stored_dashboard_metric.text == "new value"
        assert res == (no_update, no_update)


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_divider_updated_success(
    mock_ctx, server: Flask, dashboard: WM.Dashboard, dependencies: DEPS.Dependencies
):
    project_storage = dependencies.get_project_storage(dashboard.project_id)
    dashboard_storage = project_storage.get_dashboard_storage(dashboard.id)
    divider = WM.DashboardHorizontalDividerItem(
        x=0, y=0, width=10, height=8, id="divider-id", text="changeme"
    )
    dashboard_storage.append_dashboard_metric(divider)
    mock_ctx.triggered_id = {"index": divider.id}

    with server.test_request_context():
        value = "Updated divider text"
        pathname = f"/projects/{dashboard.project_id}/dashboards/{dashboard.id}"

        result_class_name, result_error = MD.divider_updated(value, pathname)

    assert result_class_name == no_update
    assert result_error == no_update

    updated_dashboard = dashboard_storage.get_dashboard()
    updated_divider = updated_dashboard.dashboard_metrics[-1]
    assert updated_divider == replace(divider, text=value)


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.render_dashboard_metric_card")
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.H.create_unique_id")
def test_display_divider_success(
    id_mock,
    mock_render_dashboard_metric_card,
    ctx,
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
    dashboard: WM.Dashboard,
    dependencies: DEPS.Dependencies,
):
    id_mock.return_value = "test-id"
    dashboard_metric = WM.DashboardHorizontalDividerItem(
        x=0,
        y=0,
        width=12,
        height=1,
        text="",
        id="test-id",
    )

    mock_dashboard = MagicMock()
    mock_dashboard.dashboard_metrics = [dashboard_metric]

    mock_render_dashboard_metric_card.return_value = "Rendered Divider"
    n_clicks = 1
    pathname = P.create_path(
        P.DASHBOARDS_EDIT_PATH,
        project_id=discovered_project.project.id,
        dashboard_id=dashboard.id,
    )

    with server.test_request_context():
        ctx.args_grouping = []
        ctx.triggered_id = DC.ADD_DIVIDER_TO_DASHBOARD_BUTTON
        result_divider, result_info = MD.display_divider(n_clicks, "", pathname)

    assert result_divider == "Rendered Divider"
    assert result_info == no_update

    added_metric = mock_dashboard.dashboard_metrics[-1]
    assert isinstance(added_metric, WM.DashboardHorizontalDividerItem)
    assert added_metric.width == 12
    assert added_metric.height == 1

    dashboard = (
        dependencies.get_project_storage(discovered_project.project.id)
        .get_dashboard_storage(dashboard.id)
        .get_dashboard()
    )

    assert isinstance(
        dashboard.dashboard_metrics[-1], WM.DashboardHorizontalDividerItem
    )
    assert dashboard.dashboard_metrics[-1].id == "test-id"


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_confirm_add_existing_insight(
    ctx,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    insight_result: VC.SimpleChart,
):
    with server.test_request_context():
        project_id = discovered_project.project.id
        project_storage = dependencies.get_project_storage(project_id)

        page_visit = M.EventNamePath("page_events", "page_visit")
        webapp_metric = WM.WebappMetric(
            M.MetricType.SEGMENTATION,
            WM.WebappMetricConfig.from_model(M.MetricConfig()),
            segments=[
                replace(
                    WM.WebappSimpleSegment.create(page_visit, None),
                    aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
                )
            ],
        )
        assert dependencies.user is not None

        saved_metrics = []
        for sm_id in ["sm1", "sm2"]:
            saved_metric = WM.SavedMetric(
                id=sm_id,
                name="Test Insight",
                description="desc",
                project_id=project_id,
                image_base64="",
                small_base64="",
                webapp_metric=webapp_metric,
                metric_json=SE.to_compressed_string(webapp_metric),
                owner=dependencies.user.membership_id,
            )
            project_storage.set_saved_metric(saved_metric)
            project_storage.set_insight_result(
                sm_id, replace(insight_result, metric_json=saved_metric.metric_json)
            )
            saved_metrics.append(saved_metric)
        assert dependencies.user is not None

        dashboard = WM.Dashboard(
            name="Test Dashboard",
            project_id=project_id,
            dashboard_metrics=[],
            owner=dependencies.user.membership_id,
            shared=False,
            show_annotations=False,
            auto_refresh_time_window=None,
        )
        project_storage.set_dashboard(dashboard)

        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=project_id,
            dashboard_id=dashboard.id,
        )

        card_component, error_component = MD.confirm_add_existing_insight(
            n_clicks=1,
            pathname=pathname,
            selected_insights=["sm1", "sm2"],
        )

        assert card_component is not None
        assert error_component is no_update
        dashboard = project_storage.get_dashboard_storage(dashboard.id).get_dashboard()
        dashboard_metrics = dashboard.dashboard_metrics
        assert len(dashboard_metrics) == 2

        assert (
            isinstance(dashboard_metrics[0], WM.DashboardMetricItem)
            and dashboard_metrics[0].saved_metric.id == "sm1"
        )
        assert (
            isinstance(dashboard_metrics[1], WM.DashboardMetricItem)
            and dashboard_metrics[1].saved_metric.id == "sm2"
        )


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_add_existing_insight_modal_populates_insights(
    ctx,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    saved_metric: WM.SavedMetric,
):
    with server.test_request_context():

        project_storage = dependencies.get_project_storage(
            discovered_project.project.id
        )

        project_storage.set_saved_metric(saved_metric)
        assert dependencies.user is not None
        dashboard = WM.Dashboard(
            name="Test Dashboard",
            project_id=discovered_project.project.id,
            dashboard_metrics=[],
            owner=dependencies.user.membership_id,
            shared=False,
            show_annotations=False,
            auto_refresh_time_window=None,
        )
        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=discovered_project.project.id,
            dashboard_id=dashboard.id,
        )
        project_storage.set_dashboard(dashboard)
        ctx.triggered_id = DC.ADD_EXISTING_INSIGHT_BUTTON

        is_open, comp, _ = MD.handle_add_existing_insight_modal(
            open_clicks=1,
            close_clicks=None,
            confirm_clicks=None,
            show_only_my_insights=True,
            pathname=pathname,
        )

        assert is_open is True
        children = to_dict(comp)
        chip_group = find_component_by_id(DC.ADD_ASSET_CHIP_GROUP, children)

        assert chip_group is not None
        assert chip_group["children"][0]["value"] == "test_sm"
        assert chip_group["children"][0]["children"] == "test_saved_metric"


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.ctx")
def test_existing_insights_are_filtered_out_of_modal(
    ctx,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    saved_metric: WM.SavedMetric,
):
    with server.test_request_context():
        project_id = discovered_project.project.id
        project_storage = dependencies.get_project_storage(project_id)

        project_storage.set_saved_metric(saved_metric)

        dashboard_metric = WM.DashboardMetricItem(
            saved_metric=saved_metric,
            x=0,
            y=0,
            width=10,
            height=8,
            id="dmi_1",
        )
        assert dependencies.user is not None
        dashboard = WM.Dashboard(
            name="Test Dashboard",
            project_id=project_id,
            dashboard_metrics=[dashboard_metric],
            owner=dependencies.user.membership_id,
            shared=False,
            show_annotations=False,
            auto_refresh_time_window=None,
        )
        project_storage.set_dashboard(dashboard)

        pathname = P.create_path(
            P.DASHBOARDS_EDIT_PATH,
            project_id=project_id,
            dashboard_id=dashboard.id,
        )

        ctx.triggered_id = DC.ADD_EXISTING_INSIGHT_BUTTON

        is_open, modal_body, _ = MD.handle_add_existing_insight_modal(
            open_clicks=1,
            close_clicks=None,
            confirm_clicks=None,
            show_only_my_insights=True,
            pathname=pathname,
        )

        assert is_open is True

        children = to_dict(modal_body)
        chip_group = find_component_by_id(DC.ADD_ASSET_CHIP_GROUP, children)

        assert chip_group is not None
        assert all(chip["value"] != saved_metric.id for chip in chip_group["children"])


def test_create_dashboard_report_cards(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    project: M.Project,
    insight_result: VC.SimpleChart,
):
    dashboard = replace(
        dashboard_with_metric,
        dashboard_metrics=[
            replace(
                dashboard_with_metric.dashboard_metrics[0],
                y=5,
            ),
            WM.DashboardTextItem(
                x=0,
                y=1,
                width=4,
                height=8,
                text="text-1",
                id="dm-1",
            ),
            WM.DashboardTextItem(
                x=0,
                y=2,
                width=4,
                height=8,
                text="text-2",
                id="dm-2",
            ),
            WM.DashboardHorizontalDividerItem(
                x=0,
                y=3,
                width=12,
                height=1,
                text="text-3",
                id="dm-3",
            ),
        ],
    )
    annotations: List[VC.Annotation] = []
    dfilter = WM.DashboardFilter.empty()
    project_storage = dependencies.get_project_storage(project.id)
    assert isinstance(dashboard.dashboard_metrics[0], WM.DashboardMetricItem)
    project_storage.set_insight_result(
        dashboard.dashboard_metrics[0].saved_metric.id, insight_result
    )
    cards = MD.create_dashboard_report_cards(
        dependencies,
        dependencies.get_project_storage(project.id),
        annotations,
        dfilter,
        dashboard,
    )
    assert len(cards) == 4
    assert isinstance(cards[0], DashboardReportTextCard) and "text-1" in cards[0].text
    assert isinstance(cards[1], DashboardReportTextCard) and "text-2" in cards[1].text
    assert (
        isinstance(cards[2], DashboardReportDividerCard)
        and cards[2].card_type == "divider"
    )
    assert (
        isinstance(cards[3], DashboardReportChartCard)
        and cards[3].name == "test_saved_metric"
    )


@patch("mitzu.webapp.pages.dashboards.manage_dashboard.generate_html_from_dashboard")
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.generate_pdf_report")
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.markdown_to_html")
def test_export_dashboard_pdf(
    mock_markdown_to_html,
    mock_generate_pdf_report,
    mock_generate_html_from_dashboard,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    project: M.Project,
    insight_result: VC.SimpleChart,
):
    project_storage = dependencies.get_project_storage(project.id)
    dashboard = replace(
        dashboard_with_metric,
        dashboard_metrics=[
            dashboard_with_metric.dashboard_metrics[0],
            WM.DashboardTextItem(
                x=1,
                y=0,
                width=4,
                height=8,
                text="text",
                id="dm-id",
            ),
        ],
    )
    mock_generate_pdf_report.return_value = BytesIO(b"pdf").read()
    mock_generate_html_from_dashboard.return_value = "html"
    mock_markdown_to_html.return_value = "html"
    project_storage.set_dashboard(dashboard)
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    project_storage.set_insight_result(
        dashboard_with_metric.dashboard_metrics[0].saved_metric.id, insight_result
    )
    pathname = P.create_path(
        P.DASHBOARDS_EDIT_PATH,
        project_id=project.id,
        dashboard_id=dashboard_with_metric.id,
    )
    with server.test_request_context():
        res, error = MD.export_dashboard_pdf(
            1,
            [],
            [],
            [],
            [],
            None,
            None,
            None,
            pathname,
        )
        assert res is not None
        assert error is no_update
        mock_generate_html_from_dashboard.assert_called_once()
        mock_generate_pdf_report.assert_called_with("html")
        mock_markdown_to_html.assert_called_once_with("text")
        assert res["content"] == base64.b64encode(b"pdf").decode()
        assert res["filename"] == "dashboard_report.pdf"


@patch(
    "mitzu.webapp.pages.dashboards.manage_dashboard.get_insight_result_for_dashboard"
)
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.generate_html_from_dashboard")
@patch("mitzu.webapp.pages.dashboards.manage_dashboard.generate_pdf_report")
def test_export_dashboard_pdf_insight_result_exception(
    mock_generate_pdf_report,
    mock_generate_html_from_dashboard,
    mock_get_insight_result_for_dashboard,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    project: M.Project,
):
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )
    mock_get_insight_result_for_dashboard.return_value = {
        dashboard_with_metric.dashboard_metrics[0].saved_metric.id: Exception()
    }
    mock_generate_pdf_report.return_value = BytesIO(b"pdf").read()
    mock_generate_html_from_dashboard.return_value = "html"
    assert isinstance(
        dashboard_with_metric.dashboard_metrics[0], WM.DashboardMetricItem
    )

    pathname = P.create_path(
        P.DASHBOARDS_EDIT_PATH,
        project_id=project.id,
        dashboard_id=dashboard_with_metric.id,
    )
    with server.test_request_context():
        res, error = MD.export_dashboard_pdf(
            1,
            [],
            [],
            [],
            [],
            None,
            None,
            None,
            pathname,
        )
        assert res is not None
        mock_generate_html_from_dashboard.assert_called_once()
        mock_generate_pdf_report.assert_called_with("html")
        assert res["content"] == base64.b64encode(b"pdf").decode()
        assert res["filename"] == "dashboard_report.pdf"
        assert error is no_update


def test_export_dashboard_pdf_failure(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    dashboard_with_metric: WM.Dashboard,
    project: M.Project,
):
    pathname = P.create_path(
        P.DASHBOARDS_EDIT_PATH,
        project_id=project.id,
        dashboard_id=dashboard_with_metric.id,
    )
    with server.test_request_context():
        res, error = MD.export_dashboard_pdf(
            1,
            [],
            [],
            [],
            [],
            None,
            None,
            None,
            pathname,
        )
        assert res is no_update
        assert error.title == "Something went wrong"
