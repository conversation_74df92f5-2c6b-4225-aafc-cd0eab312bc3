from datetime import datetime
from typing import Dict, List, Optional
from unittest.mock import ANY, patch

import pytest
from flask import Flask
from freezegun import freeze_time

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.dashboards.constants as DC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.input_generator as IG
from mitzu import configs
from mitzu.webapp.pages.dashboards.filter import (
    DASHBOARD_FILTER_OPERATOR_TYPE,
    DASHBOARD_FILTER_PROPERTY_TYPE,
    DASHBOARD_FILTER_VALUE_TYPE,
    handle_property_filter_change,
    handle_property_search,
    property_value_data_update,
    render_dashboard_filter_rows,
)
from mitzu.webapp.pages.explore.time_window_chooser import (
    CUSTOM_DATE_PICKER,
    LOOKBACK_WINDOW_DROPDOWN,
    SINCE_DATE_PICKER,
)
from tests.helper import (
    create_metric_context_from_discovered_project,
    safe_find_component_by_id,
    setup_discovered_project,
    to_dict,
)


@patch("mitzu.webapp.pages.dashboards.filter.ctx")
@patch("mitzu.webapp.pages.dashboards.filter.render_dashboard_filter_rows")
def test_handle_property_filter_change(
    render_mock, ctx_mock, server: Flask, project: M.Project, dashboard: WM.Dashboard
):
    href = P.create_path(
        P.DASHBOARDS_EDIT_PATH, project_id=project.id, dashboard_id=dashboard.id
    )
    render_mock.return_value = "test"
    with server.test_request_context():
        res = handle_property_filter_change([], [], [], [], None, None, None, href)
        assert res[0] == "test"
        assert res[2] == "dark"
        render_mock.assert_called_once_with(WM.DashboardFilter.empty(), ANY, set())

        ctx_mock.triggered_id = {"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": "0"}
        render_mock.reset_mock()
        res = handle_property_filter_change(
            [str(M.DimensionFieldPath("a", "b")), None],
            ["gt"],
            [["xx"]],
            [],
            None,
            None,
            None,
            href,
        )
        assert res[0] == "test"
        assert res[2] == "indigo"
        render_mock.assert_called_once_with(
            WM.DashboardFilter(
                [
                    WM.WebappEventFilter(
                        M.DimensionFieldPath("a", "b"), M.Operator.ANY_OF, []
                    )
                ],
                None,
            ),
            ANY,
            set(),
        )


event_field_path = M.EventFieldPath("table", "event", "field")
cohort_field_path = M.CollectionFieldPath(M.USER_ENTITY)


@pytest.mark.parametrize(
    "field_paths,operators,values,expected_filters",
    [
        pytest.param([None], [], [], [], id="no filter"),
        pytest.param(
            [str(event_field_path), None],
            ["any_of"],
            [[1, 2]],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, [1, 2])],
            id="any of",
        ),
        pytest.param(
            [str(event_field_path), None],
            [],
            [],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, [])],
            id="any of as default oeprator",
        ),
        pytest.param(
            [str(event_field_path), None],
            [],
            [],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, [])],
            id="any of as default oeprator",
        ),
        pytest.param(
            [str(cohort_field_path), None],
            [],
            [],
            [WM.WebappEventFilter(cohort_field_path, M.Operator.IN_COHORT, None)],
            id="in cohort as default oeprator",
        ),
        pytest.param(
            [str(cohort_field_path), None],
            ["in_cohort"],
            ["cohort-id"],
            [
                WM.WebappEventFilter(
                    cohort_field_path,
                    M.Operator.IN_COHORT,
                    WM.CohortReference("cohort-id"),
                )
            ],
            id="in cohort as default oeprator",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["is_not_null"],
            [],
            [WM.WebappEventFilter(event_field_path, M.Operator.IS_NOT_NULL, None)],
            id="unary oeprator",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["gt"],
            ["a"],
            [WM.WebappEventFilter(event_field_path, M.Operator.GT, "a")],
            id="binary oeprator",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["gt"],
            [],
            [WM.WebappEventFilter(event_field_path, M.Operator.GT, None)],
            id="binary oeprator no value",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["gt"],
            [["a"]],
            [WM.WebappEventFilter(event_field_path, M.Operator.GT, "a")],
            id="binary oeprator keeps the single item",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["gt"],
            [["a", "b"]],
            [WM.WebappEventFilter(event_field_path, M.Operator.GT, None)],
            id="binary oeprator drops all values if multiple values are given",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["any_of"],
            ["a"],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, ["a"])],
            id="ternary oeprator single value",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["any_of"],
            [],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, [])],
            id="ternary oeprator no value",
        ),
        pytest.param(
            [str(event_field_path), None],
            ["any_of"],
            [["a", "b"]],
            [WM.WebappEventFilter(event_field_path, M.Operator.ANY_OF, ["a", "b"])],
            id="ternary oeprator multiple values",
        ),
    ],
)
@patch("mitzu.webapp.pages.dashboards.filter.ctx")
@patch("mitzu.webapp.pages.dashboards.filter.render_dashboard_filter_rows")
def test_handle_property_filter_change_field_parsing(
    render_mock,
    ctx_mock,
    server: Flask,
    project: M.Project,
    dashboard: WM.Dashboard,
    field_paths: List[Optional[str]],
    operators: List[str],
    values: List[List[str]],
    expected_filters: List[WM.WebappEventFilter],
):
    href = P.create_path(
        P.DASHBOARDS_EDIT_PATH, project_id=project.id, dashboard_id=dashboard.id
    )
    render_mock.return_value = "test"
    with server.test_request_context():
        render_mock.reset_mock()
        res = handle_property_filter_change(
            field_paths, operators, values, [], None, None, None, href
        )
        assert res[0] == "test"
        assert (
            res[2] == "indigo"
            if len([x for x in field_paths if x is not None]) > 0
            else "dark"
        )
        assert res[3] == (
            f"?dfilter={SE.dashboard_filter_to_string(WM.DashboardFilter(expected_filters, None))}"
            if len(expected_filters) > 0
            else ""
        )
        render_mock.assert_called_once_with(
            WM.DashboardFilter(
                expected_filters,
                None,
            ),
            ANY,
            set(),
        )


@pytest.mark.parametrize(
    "custom_dates,lookback_days,calendar_date_range,since_date,triggered_id,expected_time_window",
    [
        pytest.param(
            ["2023-01-01", "2024-01-01"],
            "1 month",
            None,
            None,
            {"type": DC.TWC_DASHBOARD, "index": LOOKBACK_WINDOW_DROPDOWN},
            WM.TimeWindowChooserConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.TOTAL,
                lookback_days=M.TimeWindow(1, M.TimeGroup.MONTH),
                calendar_date_range=None,
                since_date=None,
            ),
            id="changing lookback window",
        ),
        pytest.param(
            ["2023-01-01", "2024-01-01"],
            "1 month",
            None,
            None,
            {"type": DC.TWC_DASHBOARD, "index": CUSTOM_DATE_PICKER},
            WM.TimeWindowChooserConfig(
                start_dt=datetime(2023, 1, 1, 0, 0),
                end_dt=datetime(2024, 1, 1, 0, 0),
                time_group=M.TimeGroup.TOTAL,
                lookback_days=M.DEF_LOOK_BACK_DAYS,
                calendar_date_range=None,
                since_date=None,
            ),
            id="date range picker",
        ),
        pytest.param(
            ["2023-01-01", "2024-01-01"],
            "1 month",
            None,
            "2023-01-01",
            {"type": DC.TWC_DASHBOARD, "index": SINCE_DATE_PICKER},
            WM.TimeWindowChooserConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.TOTAL,
                lookback_days=None,
                calendar_date_range=None,
                since_date=datetime(2023, 1, 1, 0, 0),
            ),
            id="since date picker",
        ),
    ],
)
@patch("mitzu.webapp.pages.dashboards.filter.ctx")
@patch("mitzu.webapp.pages.dashboards.filter.render_dashboard_filter_rows")
def test_handle_property_filter_change_time_window_change(
    render_mock,
    ctx_mock,
    server: Flask,
    project: M.Project,
    dashboard: WM.Dashboard,
    custom_dates: List[str],
    lookback_days: Optional[str],
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    triggered_id: Dict[str, str],
    expected_time_window: WM.TimeWindowChooserConfig,
):
    href = P.create_path(
        P.DASHBOARDS_EDIT_PATH, project_id=project.id, dashboard_id=dashboard.id
    )
    render_mock.return_value = "test"
    ctx_mock.triggered_id = triggered_id
    with server.test_request_context():
        render_mock.reset_mock()
        res = handle_property_filter_change(
            [],
            [],
            [],
            custom_dates,
            lookback_days,
            calendar_date_range,
            since_date,
            href,
        )
        assert res[0] == "test"
        render_mock.assert_called_once_with(
            WM.DashboardFilter(
                [],
                expected_time_window,
            ),
            ANY,
            set(),
        )
        assert res[3] == (
            f"?dfilter={SE.dashboard_filter_to_string(WM.DashboardFilter([], expected_time_window))}"
        )


def test_render_dashboard_filter_rows(
    discovered_project: M.DiscoveredProject, dependencies: DEPS.Dependencies
):
    setup_discovered_project(dependencies.storage, discovered_project)
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    project = discovered_project.project
    input_generator = IG.InputGenerator(
        project.id, metric_context, dependencies.get_project_storage(project.id)
    )
    res = render_dashboard_filter_rows(
        WM.DashboardFilter(
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("a", "b"), M.Operator.NONE_OF, ["a", "b"]
                )
            ],
            None,
        ),
        input_generator,
        set(),
    )

    res_dict = to_dict(res)

    assert (
        safe_find_component_by_id(
            {
                "type": DASHBOARD_FILTER_PROPERTY_TYPE,
                "index": "0",
            },
            res_dict,
        )["value"]
        == "dimension:a.b"
    )
    assert (
        safe_find_component_by_id(
            {
                "type": DASHBOARD_FILTER_OPERATOR_TYPE,
                "index": "0",
            },
            res_dict,
        )["value"]
        == "none_of"
    )
    assert safe_find_component_by_id(
        {
            "type": DASHBOARD_FILTER_VALUE_TYPE,
            "index": "0",
        },
        res_dict,
    )["value"] == ["a", "b"]
    assert (
        safe_find_component_by_id(
            {
                "type": DASHBOARD_FILTER_PROPERTY_TYPE,
                "index": "1",
            },
            res_dict,
        )["value"]
        is None
    )


@freeze_time("2023-08-01 00:00:00")
def test_adaptive_discovery_dimension_not_discovered(
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
):
    with server.test_request_context():
        field_path = M.DimensionFieldPath("user_profiles", "user_country_code")
        dim_def = discovered_project.get_dimension_field_def(field_path)

        # Clearing EFD
        dependencies.storage.get_project_discovery_storage(
            discovered_project.project.id
        ).set_dimension_field_definition(dim_def)

        href = configs.HOME_URL + P.create_path(
            P.PROJECTS_EXPLORE_PATH, project_id=discovered_project.project.id
        )

        opts, cls, placeholder = property_value_data_update(
            value=[IG.CUSTOM_LOOKUP_PROP_VALUE],
            field_path_str=str(dim_def.field_path),
            href=href,
        )

        assert placeholder == "br, cn, de, fr, gb, hu, us"
        assert cls == "w-100"
        assert opts == [
            {"label": "br", "value": "br"},
            {"label": "cn", "value": "cn"},
            {"label": "de", "value": "de"},
            {"label": "fr", "value": "fr"},
            {"label": "gb", "value": "gb"},
            {"label": "hu", "value": "hu"},
            {"label": "us", "value": "us"},
            {
                "label": "↻ re-index",
                "value": "_custom_lookup_option_",
            },
        ]

        loaded_dfd = (
            dependencies.get_project_storage(discovered_project.project.id)
            .get_discovered_project()
            .get_dimension_field_def(dim_def.field_path)
        )
        assert loaded_dfd.discovered_values == {
            "br",
            "cn",
            "de",
            "fr",
            "gb",
            "hu",
            "us",
        }
        assert loaded_dfd.last_discovered == datetime(2023, 8, 1)


def test_handle_property_search(
    server: Flask,
    discovered_project: M.DiscoveredProject,
    dashboard_with_metric: WM.Dashboard,
):
    href = P.create_path(
        P.DASHBOARDS_EDIT_PATH,
        project_id=discovered_project.project.id,
        dashboard_id=dashboard_with_metric.id,
    )
    search_value = None
    with server.test_request_context():
        result = handle_property_search(
            search_value, selected_str="page_events.page_visit.event_name", href=href
        )

    assert [opt["value"] for opt in result] == [
        "collection:session",
        "collection:user",
        "dimension:user_profiles.user_country_code",
        "dimension:user_profiles.user_id",
        "dimension:user_profiles.user_locale",
        "dimension:sessions.browser",
        "dimension:sessions.locale",
        "dimension:sessions.platform",
        "dimension:sessions.session_id",
        "page_events.page_visit.event_name",
    ]
