from mitzu.model import Di<PERSON><PERSON><PERSON><PERSON>Path, Operator
from mitzu.webapp.model import WebappEventFilter
from mitzu.webapp.pages.common.property_filter import (
    handle_custom_filter_values,
)
from mitzu.webapp.service.input_generator import (
    CUSTOM_LOOKUP_PROP_VALUE,
)


def test_handle_custom_filter_values() -> None:
    filter, indexing = handle_custom_filter_values(
        WebappEventFilter(
            left=DimensionFieldPath("user_profiles", "user_country_code"),
            operator=Operator.ANY_OF,
            right=["x", "y", "_custom_lookup_option_xxx", "_custom_prefix_abc"],
        )
    )

    assert indexing is True
    assert filter.right == ["x", "y", "abc"]

    filter, indexing = handle_custom_filter_values(
        WebappEventFilter(
            left=DimensionFieldPath("user_profiles", "user_country_code"),
            operator=Operator.EQ,
            right=f"{CUSTOM_LOOKUP_PROP_VALUE}xxx",
        )
    )

    assert indexing is True
    assert filter.right is None

    filter, indexing = handle_custom_filter_values(
        WebappEventFilter(
            left=DimensionFieldPath("user_profiles", "user_country_code"),
            operator=Operator.EQ,
            right="yyy",
        )
    )

    assert indexing is False
    assert filter.right == "yyy"

    filter, indexing = handle_custom_filter_values(
        WebappEventFilter(
            left=DimensionFieldPath("user_profiles", "user_country_code"),
            operator=Operator.EQ,
            right="_custom_prefix_abc",
        )
    )

    assert indexing is False
    assert filter.right == "abc"

    # does not crash when chaning from unary operator to anything else
    filter, indexing = handle_custom_filter_values(
        WebappEventFilter(
            left=DimensionFieldPath("user_profiles", "user_country_code"),
            operator=Operator.EQ,
            right=[None],
        )
    )

    assert indexing is False
    assert filter.right == []
