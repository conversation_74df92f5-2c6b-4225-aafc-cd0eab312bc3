import json
from copy import deepcopy
from dataclasses import replace
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, cast
from unittest.mock import MagicMock, patch
from urllib.parse import parse_qs, quote, unquote, urlparse

import flask
import pandas as pd
import pytest
from dash import no_update
from dash._utils import AttributeDict

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.helper as H
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.constants as CONST
import mitzu.webapp.pages.explore.explore_page as EXP
import mitzu.webapp.pages.explore.graph_handler as GH
import mitzu.webapp.pages.explore.metric_info_handler as MIH
import mitzu.webapp.pages.explore.simple_segment_handler as SSH
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.catalog_service as CS
import mitzu.webapp.storage.storage_model as SM
import samples.sample_project as SP
import tests.model as TM
from mitzu.helper import create_unique_id
from mitzu.visualization.charts import get_simple_chart
from mitzu.webapp.pages.explore.constants import (
    CLEAR_LABEL_RENAMING,
    COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_NAME,
    COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_TYPE,
    COMPLEX_SEGMENT_AGG_TYPE,
    DIM_PROFILE_ID,
)
from mitzu.webapp.pages.explore.metric_config_handler import (
    GLOBAL_FILTER_OPERATOR_TYPE,
    GLOBAL_FILTER_PROPERTY_TYPE,
    GLOBAL_FILTER_VALUE_TYPE,
    render_global_filters,
)
from mitzu.webapp.pages.explore.model import (
    OnInputsChangeResponse,
)
from mitzu.webapp.pages.explore.toolbar_handler import (
    download_clicked,
    filter_comparison_options,
    get_previous_comparison_options,
    match_comparison_options_with_time_group,
)
from mitzu.webapp.service.input_generator.generator import (
    InputGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    CUSTOM_LOOKUP_PROP_VALUE,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from tests.helper import (
    create_metric_context_from_discovered_project,
    find_component_by_id,
    get_project_config_storage,
    safe_find_component_by_id,
    setup_discovered_project,
    to_dict,
)
from tests.in_memory_cache import InMemoryCache

metric_context = MC.MetricContext(
    _event_catalog={
        em.event_name_path: em
        for em in [
            WM.EventMeta(
                event_id="event_1",
                source_table="checkouts",
                event_name="checkout",
                # aa prefix is needed to be the first event in the event dropdown
                display_name="AA User checkout",
                description="Checkout description",
            ),
            WM.EventMeta(
                event_id="event_2",
                source_table="email_subscriptions",
                event_name="subscribe",
                display_name="User subscribed",
            ),
        ]
    },
    _entity_meta={},
    _property_catalog={},
    _dimension_catalog={},
    _event_properties={},
    _table_catalog={},
    global_filters=[],
    revenue_settings=None,
    _custom_events={},
)

expected_event_dd_items = {
    "add_to_carts.add_to_cart": "Item added to cart",
    "blog_events.page_visit": "Page Visit",
    "checkouts.checkout": "Checkout",
    "page_events.element_clicked_on_page": "Element Clicked On Page",
    "email_opened_events.email_opened": "Email Opened",
    "email_sent_events.email_sent": "Email Sent",
    "page_events.page_visit": "Page Visit",
    "search_events.search": "Search",
    "email_subscriptions.subscribe": "User subscribed",
}

page_visit_event_name_path = M.EventNamePath(
    event_name="page_visit", source_table="page_events"
)


def get_all_inputs(project_id: str, inputs):
    default_all_inputs = {
        H.METRIC_SEGMENTS: {},
        H.MITZU_LOCATION: f"http://127.0.0.1:8082/projects/{project_id}/explore/",
        CONST.METRIC_TYPE_CHOICE: "segmentation",
        CONST.TIME_GROUP_DROPDOWN: M.TimeGroup.DAY.value,
        TWC.CUSTOM_DATE_PICKER: [None, None],
        TWC.LOOKBACK_WINDOW_DROPDOWN: "1 month",
        CONST.TIME_WINDOW_INTERVAL_STEPS: M.TimeGroup.DAY.value,
        CONST.TIME_WINDOW_INTERVAL: 1,
        CONST.AGGREGATION_TYPE: "user_count",
        CONST.CHART_TYPE_DD: M.SimpleChartType.LINE.name,
        CONST.SHOW_HIDE_ANNOTATIONS: False,
        CONST.CHART_CONSISTENT_COLORING: False,
        GLOBAL_FILTER_PROPERTY_TYPE: [],
        GLOBAL_FILTER_OPERATOR_TYPE: [],
        GLOBAL_FILTER_VALUE_TYPE: [],
    }

    default_all_inputs.update(**inputs)
    return default_all_inputs


def get_handle_input_changes_result(
    all_inputs: Dict[str, Any],
    discovered_project: M.DiscoveredProject,
    triggered_id: Union[None, str, dict] = None,
    cohorts: Optional[List[WM.CohortInfo]] = None,
) -> OnInputsChangeResponse:
    metric_context = create_metric_context_from_discovered_project(discovered_project)

    with (
        patch("mitzu.webapp.pages.explore.metric_config_handler.ctx") as ctx_mch,
        patch("mitzu.webapp.pages.explore.toolbar_handler.ctx") as ctx_dsh,
        patch("mitzu.webapp.pages.explore.metric_segments_handler.ctx") as ctx_msh,
        patch("mitzu.webapp.pages.explore.simple_segment_handler.ctx") as ctx_ss,
        patch("mitzu.webapp.pages.explore.complex_segment_handler.ctx") as ctx_csh,
        patch("mitzu.webapp.pages.explore.metric_advanced_configs.ctx") as mac_ctx,
        patch("mitzu.webapp.pages.explore.post_processing_handler.ctx") as pph_ctx,
        patch("mitzu.webapp.pages.explore.event_segment_handler.ctx") as esh_ctx,
        patch("mitzu.webapp.pages.explore.explore_page.ctx") as ep_ctx,
        patch(
            "mitzu.webapp.pages.explore.explore_page.DEPS"
        ) as deps,  # it's easier to mock than having a server here
    ):
        ctx_mch.triggered_id = triggered_id
        ctx_dsh.triggered_id = triggered_id
        ctx_msh.triggered_id = triggered_id
        ctx_ss.triggered_id = triggered_id
        ctx_csh.triggered_id = triggered_id
        mac_ctx.triggered_id = triggered_id
        pph_ctx.triggered_id = triggered_id
        ep_ctx.triggered_id = triggered_id
        esh_ctx.triggered_id = triggered_id
        dependencies = MagicMock()
        dependencies.home_url = "http://localhost"
        deps.Dependencies.get.return_value = dependencies
        catalog_service = MagicMock()
        dependencies.get_catalog_service.return_value = catalog_service
        catalog_service.get_metric_context.return_value = metric_context
        storage_mock = MagicMock()

        project_storage_mock = MagicMock()
        project_storage_mock.list_cohorts.return_value = (
            cohorts if cohorts is not None else []
        )
        dependencies.cache = InMemoryCache()
        dependencies.get_project_storage.return_value = project_storage_mock
        storage_mock.get_project_storage.return_value = project_storage_mock

        dependencies.storage = storage_mock

        return EXP.handle_input_changes(
            discovered_project.project.id,
            discovered_project.project.insight_settings,
            all_inputs,
            "edit",
            relevance_scores={},
        )


def get_create_metric_from_all_inputs_result(
    all_inputs: Dict[str, Any],
    discovered_project: M.DiscoveredProject,
    triggered_id: Union[None, str, dict] = None,
):
    with (
        patch("mitzu.webapp.pages.explore.metric_config_handler.ctx") as ctx_mch,
        patch("mitzu.webapp.pages.explore.toolbar_handler.ctx") as ctx_dsh,
        patch("mitzu.webapp.pages.explore.metric_segments_handler.ctx") as ctx_msh,
        patch("mitzu.webapp.pages.explore.metric_advanced_configs.ctx") as ctx_mac,
        patch("mitzu.webapp.pages.explore.simple_segment_handler.ctx") as ctx_ss,
        patch("mitzu.webapp.pages.explore.complex_segment_handler.ctx") as ctx_csh,
        patch("mitzu.webapp.pages.explore.post_processing_handler.ctx") as pph_ctx,
        patch("mitzu.webapp.pages.explore.event_segment_handler.ctx") as esh_ctx,
        patch("mitzu.webapp.pages.explore.explore_page.ctx") as exp_ctx,
    ):
        ctx_mch.triggered_id = triggered_id
        ctx_dsh.triggered_id = triggered_id
        ctx_msh.triggered_id = triggered_id
        ctx_ss.triggered_id = triggered_id
        ctx_csh.triggered_id = triggered_id
        ctx_mac.triggered_id = triggered_id
        pph_ctx.triggered_id = triggered_id
        esh_ctx.triggered_id = triggered_id
        exp_ctx.triggered_id = triggered_id

        metric_context = create_metric_context_from_discovered_project(
            discovered_project
        )
        catalog_service = MagicMock()
        catalog_service.get_metric_context.return_value = metric_context

        return EXP.create_metric_from_all_inputs(
            all_inputs,
            discovered_project.project.insight_settings,
            catalog_service,
            discovered_project.project.get_default_end_dt(),
            project=discovered_project.project,
            view_mode_store="edit",
        )


def assert_input_value(
    input_id: Union[str, Dict], page_dict: Dict[str, Any], expected_value: Any
):
    __tracebackhide__ = True
    input = find_component_by_id(input_id, page_dict)
    assert input is not None, f"Input not found with id {input_id}"
    assert input["value"] == expected_value


def get_field_name_or_dim_field_path(value: str) -> str:
    field_path = M.FieldPath.parse(value)
    # only the field name matters from an event field path
    if isinstance(field_path, M.EventFieldPath):
        return field_path.field_name
    return str(field_path)


def test_event_chosen_for_segmentation(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    )
                                }
                            }
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res = to_dict(res.metric_segments[0])

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, res
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, res
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res
        )

        assert second_event_dd is not None
        assert [
            (option["value"]) for option in second_event_dd["options"]
        ] == []  # options are filled on the client side

        assert first_property_dd is not None
        assert set([option["value"] for option in first_property_dd["options"]]) == set(
            [
                f"{SSH.REFRESH_CATALOG_PROPERTY}page_events.page_visit",
                str(TM.COHORT_FIELD_PATH),
                "dimension:group_profiles.group_id",
                "dimension:group_profiles.group_name",
                "dimension:group_profiles.headquarter_country",
                "dimension:sessions.browser",
                "dimension:sessions.locale",
                "dimension:sessions.platform",
                "dimension:sessions.session_id",
                "dimension:user_profiles.user_country_code",
                "dimension:user_profiles.user_id",
                "dimension:user_profiles.user_locale",
                "page_events.page_visit.acquisition_campaign",
                "page_events.page_visit.domain",
                "page_events.page_visit.event_name",
                "page_events.page_visit.event_time",
                "page_events.page_visit.item_id",
                "page_events.page_visit.session_id",
                "page_events.page_visit.title",
                "page_events.page_visit.user_country_code",
                "page_events.page_visit.user_id",
                "page_events.page_visit.user_locale",
            ]
        )
        assert first_property_operator_dd is not None
        assert "d-none" in first_property_operator_dd.get("className", "")


def test_clear_button_clicked(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        project_id = discovered_project.project.id
        all_inputs = get_all_inputs(
            project_id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    )
                                }
                            }
                        }
                    }
                },
            },
        )
        with (
            patch("mitzu.webapp.pages.explore.explore_page.ctx") as ctx,
            patch(
                "mitzu.webapp.pages.explore.explore_page.DEPS"
            ) as deps,  # it's easier to mock than having a server here
        ):
            ctx.triggered_id = MIH.METRIC_CLEAR_BUTTON
            dependencies = MagicMock()
            dependencies.home_url = "http://localhost"
            deps.Dependencies.get.return_value = dependencies
            catalog_service = MagicMock()
            dependencies.get_catalog_service.return_value = catalog_service
            catalog_service.get_metric_context.return_value = metric_context
            dependencies.cache = InMemoryCache()

            res = EXP.handle_input_changes(
                project_id,
                discovered_project.project.insight_settings,
                all_inputs,
                "edit",
                relevance_scores={},
            )
            assert (
                res.mitzu_location
                == "http://localhost:8082/projects/sample_project_id/explore"
            )
            assert (
                res.state_div
                == "http://localhost:8082/projects/sample_project_id/explore"
            )

            # 10 components for segments and 4 for the metric config in the sidebar
            assert (
                len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
                and len(res.metric_segments) == 10
            )
            assert (
                res.metric_config_container is not None
                and len(res.metric_config_container) == 7
            )

            # Button disabled
            assert res.show_metric_clear_button is True


def test_run_button_clicked(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        project_id = discovered_project.project.id
        all_inputs = get_all_inputs(
            project_id,
            {
                H.METRIC_SEGMENTS: {},
            },
        )
        with (
            patch("mitzu.webapp.pages.explore.explore_page.ctx") as ctx,
            patch("mitzu.webapp.pages.explore.toolbar_handler.ctx"),
            patch("mitzu.webapp.pages.explore.metric_config_handler.ctx"),
            patch("mitzu.webapp.pages.explore.metric_segments_handler.ctx"),
            patch("mitzu.webapp.pages.explore.post_processing_handler.ctx"),
            patch("mitzu.webapp.pages.explore.metric_advanced_configs.ctx"),
            patch(
                "mitzu.webapp.pages.explore.explore_page.DEPS"
            ) as deps,  # it's easier to mock than having a server here
        ):
            ctx.triggered_id = GH.GRAPH_RUN_BUTTON
            dependencies = MagicMock()
            dependencies.home_url = "http://localhost"
            deps.Dependencies.get.return_value = dependencies
            catalog_service = MagicMock()
            dependencies.get_catalog_service.return_value = catalog_service
            catalog_service.get_metric_context.return_value = metric_context
            dependencies.cache = InMemoryCache()
            inss = discovered_project.project.insight_settings
            inss = replace(inss, auto_refresh_enabled=False)

            res = EXP.handle_input_changes(
                project_id,
                inss,
                all_inputs,
                view_mode_store="edit",
                relevance_scores={},
            )
            assert res.mitzu_location is None
            assert (
                res.state_div
                == "http://localhost:8082/projects/sample_project_id/explore"
            )

            # 6 default components
            assert res.metric_segments == [
                None for _ in range(0, CONST.NUMBER_OF_SEGMENTS)
            ]

            # Button disabled
            assert res.show_metric_clear_button is None
            assert res.graph_top_container_styles == {"position": "relative"}
            assert res.graph_run_overlay_styles == {"display": "none"}


def test_metric_properly_encoded_in_the_url(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    )
                                }
                            }
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert res.clipboard is not None
        clipboard_url = urlparse(res.clipboard)
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH,
            clipboard_url.path,
            P.PROJECT_ID_PATH_PART,
        )
        assert project_id == discovered_project.project.id

        metric_string = parse_qs(clipboard_url.query)[P.PROJECTS_EXPLORE_METRIC_QUERY][
            0
        ]
        metric_converter = MetricConverter(
            discovered_project.as_metric_definitions(), MagicMock(), MagicMock()
        )
        metric = metric_converter.convert_metric(
            SE.from_compressed_string(metric_string)
        )
        assert metric is not None
        assert len(metric.segments) == 1
        segment = metric.segments[0]
        assert isinstance(segment, M.SimpleSegment)
        assert segment.event.event_name_path == page_visit_event_name_path

        assert res.clipboard == res.mitzu_location


def test_event_property_chosen_for_segmentation(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {
                                            CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.acquisition_campaign"
                                        }
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res = to_dict(res.metric_segments[0])

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, res
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, res
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res
        )
        first_property_value_input = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, res
        )

        assert second_event_dd is not None
        assert first_property_dd is not None
        assert first_property_operator_dd is not None
        assert first_property_value_input is not None
        assert first_property_operator_dd["data"] == [
            {"label": "is", "search": "is", "value": "any_of", "disabled": False},
            {
                "label": "is not",
                "search": "is not",
                "value": "none_of",
                "disabled": False,
            },
            {"label": ">", "search": ">", "value": "gt", "disabled": False},
            {"label": ">=", "search": ">=", "value": "gt_eq", "disabled": False},
            {"label": "<", "search": "<", "value": "lt", "disabled": False},
            {"label": "<=", "search": "<=", "value": "lt_eq", "disabled": False},
            {
                "label": "present",
                "search": "present",
                "value": "is_not_null",
                "disabled": False,
            },
            {
                "label": "missing",
                "search": "missing",
                "value": "is_null",
                "disabled": False,
            },
            {
                "label": "contains",
                "search": "contains",
                "value": "like",
                "disabled": False,
            },
            {
                "label": "not contains",
                "search": "not contains",
                "value": "not_like",
                "disabled": False,
            },
        ]
        assert first_property_operator_dd["value"] == "any_of"
        assert first_property_value_input["value"] == []


def test_event_property_changed_for_segmentation(
    discovered_project: M.DiscoveredProject,
):
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                                "children": {
                                    0: {
                                        CONST.PROPERTY_OPERATOR_DROPDOWN: ">",
                                        CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.acquisition_campaign",
                                        CONST.PROPERTY_VALUE_INPUT: [
                                            "organic",
                                            "aaa",
                                        ],
                                    },
                                    1: {
                                        CONST.PROPERTY_OPERATOR_DROPDOWN: "is",
                                        CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.domain",
                                        CONST.PROPERTY_VALUE_INPUT: [
                                            "xx",
                                            "xy",
                                        ],
                                    },
                                },
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    }
                }
            },
        },
    )

    res = get_handle_input_changes_result(
        all_inputs,
        discovered_project,
        triggered_id=AttributeDict(
            {"type": CONST.PROPERTY_NAME_DROPDOWN, "index": "0-0-0"}
        ),
    )
    assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
    res_dict = to_dict(res.metric_segments[0])

    assert_input_value(
        {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, res_dict, []
    )  # changed to default
    assert_input_value(
        {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res_dict, "any_of"
    )  # changed to default

    assert_input_value(
        {"index": "0-0-1", "type": CONST.PROPERTY_VALUE_INPUT}, res_dict, ["xx", "xy"]
    )  # didn't change
    assert_input_value(
        {"index": "0-0-1", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res_dict, "any_of"
    )


def test_event_property_chosen_for_group_by(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():

        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {
                                            CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.acquisition_campaign"
                                        }
                                    },
                                },
                                1: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        M.EventNamePath("blog_events", "page_visit")
                                    ),
                                    "children": {
                                        0: {
                                            CONST.PROPERTY_NAME_DROPDOWN: "blog_events.page_visit.post_title"
                                        }
                                    },
                                },
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: [
                                "page_events.page_visit.acquisition_campaign"
                            ],
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res_dict = to_dict(res.metric_segments[0])

        first_event_gp = find_component_by_id(
            {"index": "0", "type": CONST.COMPLEX_SEGMENT_GROUP_BY}, res_dict
        )

        assert first_event_gp is not None
        assert [
            {
                "label": o["label"]["children"][0]["children"][0],
                "value": get_field_name_or_dim_field_path(o["value"]),
            }
            for o in first_event_gp["options"]
        ] == [
            {
                "label": "Subsegment",
                "value": "___subsegment___",
            },
            {"label": "Acquisition Campaign", "value": "acquisition_campaign"},
            {"label": "Domain", "value": "domain"},
            {"label": "Event Name", "value": "event_name"},
            {"label": "Event Time", "value": "event_time"},
            {"label": "Item Id", "value": "item_id"},
            {"label": "Post Title", "value": "post_title"},
            {"label": "Session Id", "value": "session_id"},
            {"label": "Title", "value": "title"},
            {"label": "User Country Code", "value": "user_country_code"},
            {"label": "User Id", "value": "user_id"},
            {"label": "User Locale", "value": "user_locale"},
            {"label": "Browser", "value": "dimension:sessions.browser"},
            {"label": "Group Id", "value": "dimension:group_profiles.group_id"},
            {"label": "Group Name", "value": "dimension:group_profiles.group_name"},
            {
                "label": "Headquarter Country",
                "value": "dimension:group_profiles.headquarter_country",
            },
            {"label": "Locale", "value": "dimension:sessions.locale"},
            {"label": "Platform", "value": "dimension:sessions.platform"},
            {"label": "Session Id", "value": "dimension:sessions.session_id"},
            {
                "label": "User Country Code",
                "value": "dimension:user_profiles.user_country_code",
            },
            {"label": "User Id", "value": "dimension:user_profiles.user_id"},
            {"label": "User Locale", "value": "dimension:user_profiles.user_locale"},
        ]
        # only the field name matters
        assert [
            M.EventFieldPath.parse(value).field_name
            for value in first_event_gp["value"]
        ] == ["acquisition_campaign"]


def test_event_property_chosen_for_group_by_combined_events(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                1: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        M.EventNamePath(
                                            event_name="page_visit",
                                            source_table="page_events",
                                        )
                                    ),
                                },
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        M.EventNamePath(
                                            event_name="element_clicked_on_page",
                                            source_table="page_events",
                                        )
                                    ),
                                },
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: [
                                "page_events.page_visit.acquisition_campaign",
                            ],
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res_dict = to_dict(res.metric_segments[0])

        first_event_gp = find_component_by_id(
            {"index": "0", "type": CONST.COMPLEX_SEGMENT_GROUP_BY}, res_dict
        )

        assert first_event_gp is not None
        # only the field name matters
        assert [
            M.FieldPath.parse(value).field_name for value in first_event_gp["value"]
        ] == ["acquisition_campaign"]

        assert [
            {
                "label": o["label"]["children"][0]["children"][0],
                "value": get_field_name_or_dim_field_path(o["value"]),
            }
            for o in first_event_gp["options"]
        ] == [
            {
                "label": "Subsegment",
                "value": "___subsegment___",
            },
            {"label": "Acquisition Campaign", "value": "acquisition_campaign"},
            {"label": "Domain", "value": "domain"},
            {"label": "Event Name", "value": "event_name"},
            {"label": "Event Time", "value": "event_time"},
            {"label": "Item Id", "value": "item_id"},
            {"label": "Session Id", "value": "session_id"},
            {"label": "Title", "value": "title"},
            {"label": "User Country Code", "value": "user_country_code"},
            {"label": "User Id", "value": "user_id"},
            {"label": "User Locale", "value": "user_locale"},
            {"label": "Browser", "value": "dimension:sessions.browser"},
            {"label": "Group Id", "value": "dimension:group_profiles.group_id"},
            {"label": "Group Name", "value": "dimension:group_profiles.group_name"},
            {
                "label": "Headquarter Country",
                "value": "dimension:group_profiles.headquarter_country",
            },
            {"label": "Locale", "value": "dimension:sessions.locale"},
            {"label": "Platform", "value": "dimension:sessions.platform"},
            {"label": "Session Id", "value": "dimension:sessions.session_id"},
            {
                "label": "User Country Code",
                "value": "dimension:user_profiles.user_country_code",
            },
            {"label": "User Id", "value": "dimension:user_profiles.user_id"},
            {"label": "User Locale", "value": "dimension:user_profiles.user_locale"},
        ]


def test_event_property_operator_changed_with_values_already_chosen(
    discovered_project: M.DiscoveredProject,
):
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                                "children": {
                                    0: {
                                        CONST.PROPERTY_OPERATOR_DROPDOWN: ">",
                                        CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.acquisition_campaign",
                                        CONST.PROPERTY_VALUE_INPUT: [
                                            "organic",
                                            "aaa",
                                        ],
                                    },
                                    1: {CONST.PROPERTY_NAME_DROPDOWN: None},
                                },
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    }
                }
            },
        },
    )
    res = get_handle_input_changes_result(
        all_inputs,
        discovered_project,
        triggered_id=AttributeDict(
            {"type": CONST.PROPERTY_OPERATOR_DROPDOWN, "index": "0-0-0"}
        ),
    )
    assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
    res_dict = to_dict(res.metric_segments[0])

    assert_input_value(
        {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res_dict, "gt"
    )
    assert_input_value(
        {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, res_dict, ["organic"]
    )


def test_label_mapping_is_read(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = AttributeDict(
        {"type": CONST.PROPERTY_OPERATOR_DROPDOWN, "index": "0-0-0"}
    )
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            MIH.METRIC_LABEL_MAPPING: '{"value":"label"}',
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert metric.label_mapping == {
        "value": "label",
    }


@pytest.mark.parametrize(
    "triggered_id",
    [
        CLEAR_LABEL_RENAMING,
    ]
    + [
        AttributeDict({"type": type, "index": "1"})
        for type in [
            CONST.COMPLEX_SEGMENT_GROUP_BY,
            CONST.EVENT_NAME_DROPDOWN,
            CONST.COMPLEX_SEGMENT_MOVE_UP,
            CONST.COMPLEX_SEGMENT_MOVE_DOWN,
            CONST.COMPLEX_SEGMENT_REMOVE,
            CONST.METRIC_TYPE_CHOICE,
        ]
    ],
)
def test_label_mapping_is_cleared_on_certain_inputs_changes(
    discovered_project: M.DiscoveredProject, triggered_id: Union[str, AttributeDict]
):
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            MIH.METRIC_LABEL_MAPPING: '{"value":"label"}',
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    },
                    1: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    },
                }
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert metric.label_mapping == {}


def test_event_property_operator_changed_creates_correct_metric(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = AttributeDict(
        {"type": CONST.PROPERTY_OPERATOR_DROPDOWN, "index": "0-0-0"}
    )
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                                "children": {
                                    0: {
                                        CONST.PROPERTY_OPERATOR_DROPDOWN: ">",
                                        CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.acquisition_campaign",
                                        CONST.PROPERTY_VALUE_INPUT: ["a"],
                                    },
                                    1: {CONST.PROPERTY_NAME_DROPDOWN: None},
                                },
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    }
                }
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    ss = cast(WM.WebappSimpleSegment, metric.segments[0])
    assert ss.event.event_name == "page_visit"
    assert ss.event_filter is not None
    assert ss.event_filter.left.field_name == "acquisition_campaign"
    assert ss.event_filter.operator == M.Operator.GT
    assert ss.event_filter.right == "a"

    res = get_handle_input_changes_result(all_inputs, discovered_project, triggered_id)
    metric_segs = to_dict(res.metric_segments)

    # update contains only the content not the container
    assert find_component_by_id(H.get_segment_container_id(0), metric_segs) is None

    assert_input_value(
        {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, metric_segs, ["a"]
    )


def test_event_property_values_changed_custom_value(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = AttributeDict({"type": CONST.PROPERTY_VALUE_INPUT, "index": "0-0-0"})
    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                                "children": {
                                    0: {
                                        CONST.PROPERTY_OPERATOR_DROPDOWN: "is",
                                        CONST.PROPERTY_NAME_DROPDOWN: "page_events.page_visit.user_country_code",
                                        CONST.PROPERTY_VALUE_INPUT: [
                                            "gb",
                                            "cn",
                                            "_custom_lookup_option_",
                                            "_custom_lookup_option_de",
                                        ],
                                    },
                                    1: {CONST.PROPERTY_NAME_DROPDOWN: None},
                                },
                            },
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    }
                }
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    ss = cast(WM.WebappSimpleSegment, metric.segments[0])
    assert ss.event.event_name == "page_visit"
    assert ss.event_filter is not None
    assert ss.event_filter.left.field_name == "user_country_code"
    assert ss.event_filter.operator == M.Operator.ANY_OF
    assert ss.event_filter.right == ["gb", "cn"]

    res = get_handle_input_changes_result(all_inputs, discovered_project, triggered_id)
    segment = to_dict(res.metric_segments[0])
    values = find_component_by_id(
        {"type": CONST.PROPERTY_VALUE_INPUT, "index": "0-0-0"}, segment
    )
    assert values is not None
    assert values.get("value", []) == ["gb", "cn"]
    assert values.get("data", []) == [
        {"label": "cn", "search": "cn", "value": "cn", "disabled": False},
        {"label": "gb", "search": "gb", "value": "gb", "disabled": False},
        {
            "disabled": False,
            "label": "↻ re-index",
            "search": "↻ re-index",
            "value": "_custom_lookup_option_",
        },
    ]


def test_empty_page_with_project(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {"children": {0: {CONST.EVENT_NAME_DROPDOWN: None}}}
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        metric_segs = to_dict(res.metric_segments)
        metric_confs = to_dict(res.date_selector_container)

        # Metric Segments Part

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, metric_segs
        )
        first_event_dd = find_component_by_id(
            {"index": "0-0", "type": CONST.EVENT_NAME_DROPDOWN}, metric_segs
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, metric_segs
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, metric_segs
        )
        first_property_value_input = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, metric_segs
        )

        assert first_event_dd is not None
        assert first_property_dd is None
        assert second_event_dd is None
        assert first_property_operator_dd is None
        assert first_property_value_input is None

        # Metric Configuration Part

        lookback_dd = find_component_by_id(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN},
            metric_confs,
        )
        timegroup_dd = find_component_by_id(CONST.TIME_GROUP_DROPDOWN, metric_confs)

        assert lookback_dd is not None
        assert lookback_dd["value"] == "1 month"
        assert timegroup_dd is not None
        assert timegroup_dd["value"] == M.TimeGroup.DAY.value


def test_empty_page_config_change(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = TWC.LOOKBACK_WINDOW_DROPDOWN

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            1: {CONST.EVENT_NAME_DROPDOWN: None},
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                    }
                }
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert metric.config.lookback_days == M.TimeWindow(1, M.TimeGroup.MONTH)


def test_retention_configs_can_have_only_two_segments(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = CONST.METRIC_TYPE_CHOICE

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.METRIC_TYPE_CHOICE: "retention",
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                )
                            }
                        }
                    },
                    1: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                )
                            }
                        }
                    },
                    2: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                )
                            }
                        }
                    },
                }
            },
            CONST.FORMULA_INPUT: "A",
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert len(metric.segments) == 2
    assert metric.config.formula is None


def test_retention_switching_to_cohort_retention(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = CONST.TIME_GROUP_DROPDOWN

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.METRIC_TYPE_CHOICE: "retention",
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                )
                            }
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: [
                            "page_events.page_visit.acquisition_campaign"
                        ],
                    },
                    1: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                )
                            }
                        }
                    },
                }
            },
            CONST.TIME_GROUP_DROPDOWN: M.TimeGroup.WEEK.value,
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert len(metric.segments) == 2
    assert metric.segments[0].group_by == [
        M.EventFieldPath(
            source_table="page_events",
            field_name="acquisition_campaign",
            event_name="page_visit",
        )
    ]


def test_property_aggregation_updated_on_event_change_in_a_segment(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": CONST.EVENT_NAME_DROPDOWN}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.METRIC_TYPE_CHOICE: "segmentation",
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                            }
                        },
                        COMPLEX_SEGMENT_AGG_TYPE: "by_event_property",
                        COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_TYPE: "count_distinct",
                        COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_NAME: "checkouts.checkout.event_name",
                    },
                },
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert len(metric.segments) == 1
    agg_config = metric.segments[0].aggregation
    assert agg_config is not None
    assert agg_config.event_property_agg.field_def == M.EventFieldPath(
        "page_events", "page_visit", "event_name"
    )


def test_property_aggregation_updated_on_event_change_in_metric_config(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": CONST.EVENT_NAME_DROPDOWN}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.METRIC_TYPE_CHOICE: "conversion",
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                            }
                        },
                    },
                },
            },
            CONST.AGGREGATION_TYPE: "by_event_property",
            CONST.AGG_EVENT_PROPERTY_TYPE: "count_distinct",
            CONST.AGG_EVENT_PROPERTY_NAME: "checkouts.checkout.event_name",
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert len(metric.segments) == 1
    agg_config = metric.config.aggregation
    assert agg_config is not None
    assert agg_config.type == M.AggType.BY_EVENT_PROPERTY
    assert agg_config.event_property_agg.field_def == M.EventFieldPath(
        "page_events", "page_visit", "event_name"
    )


@pytest.mark.parametrize(
    "group_by_fields,expected_group_by_fields",
    [
        pytest.param([], [], id="handles segments without any group by fields"),
        pytest.param(
            [M.EventFieldPath("checkouts", "checkout", "event_name")],
            [M.EventFieldPath("page_events", "page_visit", "event_name")],
            id="group by fields are updated based on the field name",
        ),
        pytest.param(
            [M.DimensionFieldPath("user_profiles", "user_country_code")],
            [M.DimensionFieldPath("user_profiles", "user_country_code")],
            id="dimension field group bys are not changed",
        ),
        pytest.param(
            [
                M.EventFieldPath("checkouts", "checkout", "event_name"),
                M.EventFieldPath("checkouts", "checkout", "item"),
                M.DimensionFieldPath("user_profiles", "user_country_code"),
            ],
            [
                M.EventFieldPath("page_events", "page_visit", "event_name"),
                M.DimensionFieldPath("user_profiles", "user_country_code"),
            ],
            id="unkown fields are removed",
        ),
    ],
)
def test_segment_group_by_updated_on_event_change(
    group_by_fields: List[M.FieldPath],
    expected_group_by_fields: List[M.FieldPath],
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": CONST.EVENT_NAME_DROPDOWN}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.METRIC_TYPE_CHOICE: "conversion",
            H.METRIC_SEGMENTS: {
                "children": {
                    0: {
                        "children": {
                            0: {
                                CONST.EVENT_NAME_DROPDOWN: str(
                                    page_visit_event_name_path
                                ),
                            },
                        },
                        CONST.COMPLEX_SEGMENT_GROUP_BY: [
                            str(field) for field in group_by_fields
                        ],
                    },
                },
            },
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )
    assert len(metric.segments) == 1
    assert metric.segments[0].group_by == expected_group_by_fields


def test_custom_date_selected_new_start_date(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {CONST.PROPERTY_NAME_DROPDOWN: None}
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
                TWC.CUSTOM_DATE_PICKER: ["2021-12-01T00:00:00", "2022-01-01T00:00:00"],
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        container = to_dict(res.date_selector_container)

        # Metric Segments Part

        date_picker = find_component_by_id(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CUSTOM_DATE_PICKER}, container
        )

        assert date_picker is not None
        assert date_picker["style"] == {"width": "210px", "zIndex": 9999}
        assert "time-window-chooser-date-range-selected" in date_picker[
            "className"
        ].split(" ")
        assert date_picker["value"] == [
            datetime(2021, 12, 1, 0, 0),
            datetime(2022, 1, 1, 0, 0),
        ]


def test_custom_date_lookback_days_selected(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        triggered_id = AttributeDict(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}
        )
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {CONST.PROPERTY_NAME_DROPDOWN: None}
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(
            all_inputs, discovered_project, triggered_id
        )
        dates_container = to_dict(res.date_selector_container)

        # Metric Segments Part

        date_picker = find_component_by_id(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CUSTOM_DATE_PICKER},
            dates_container,
        )

        assert date_picker is not None
        assert date_picker["style"] == {"width": "150px", "zIndex": 9999}
        assert "time-window-chooser-date-range-selected" not in date_picker[
            "className"
        ].split(" ")
        assert date_picker["value"] == [None, None]

        assert_input_value(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN},
            dates_container,
            "1 month",
        )


@patch("mitzu.webapp.navbar.OM")
@patch("mitzu.webapp.pages.explore.metric_config_handler.ctx")
@patch("mitzu.webapp.pages.explore.event_segment_handler.ctx")
@patch("mitzu.webapp.pages.explore.simple_segment_handler.ctx")
def test_mitzu_link_redirected(
    ssh_ctx, esh_ctx, ctx, om_mock, server: flask.Flask, dependencies: DEPS.Dependencies
):
    with server.test_request_context():
        org_manager = MagicMock()
        cloud_storage = MagicMock()
        om_mock.OrgManager.get.return_value = org_manager
        org_manager._cloud_storage = cloud_storage

        cloud_storage.get_group_page_links.return_value = [
            SM.PageLinkStorageModel(
                use_case="sample_project_id",
                link="google.com",
                description="desc",
                group="support_hub",
            )
        ]

        discovered_project = dependencies.get_project_storage(
            SP.SAMPLE_PROJECT_ID
        ).get_discovered_project()

        catalog_service = CS.CatalogService(
            dependencies.storage.get_project_storage(discovered_project.project.id),
            InMemoryCache(),
        )
        catalog_service.register_new_events_if_not_exists(
            [
                event_def_ref.get_value_if_exists()
                for event_def_ref in discovered_project.event_definitions.values()
            ],
        )

        page_visit = M.EventNamePath("page_events", "page_visit")
        metric = WM.WebappMetric(
            M.MetricType.SEGMENTATION,
            WM.WebappMetricConfig.from_model(
                M.MetricConfig(
                    lookback_days=M.TimeWindow(2, M.TimeGroup.MONTH),
                    aggregation=M.AggregationConfig(M.AggType.COUNT_EVENTS),
                )
            ),
            segments=[
                WM.WebappSimpleSegment.create(page_visit, None),
            ],
            title="metric title",
            description="metric description",
        )
        query = SE.to_compressed_string(metric)

        query_params = {"m": unquote(query)}
        res = EXP.create_explore_page(
            query_params,
            discovered_project.project.id,
            storage=dependencies.storage,
            catalog_service=catalog_service,
            relevance_scores={},
        )

        explore_page = to_dict(res)

        assert_input_value(MIH.METRIC_NAME_INPUT, explore_page, metric.title)
        assert_input_value(
            MIH.METRIC_DESCRIPTION_INPUT, explore_page, metric.description
        )

        # Metric Segments Part
        assert_input_value(
            {"index": "0-0", "type": CONST.EVENT_NAME_DROPDOWN},
            explore_page,
            "page_events.page_visit",
        )

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, explore_page
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, explore_page
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, explore_page
        )

        assert second_event_dd is not None
        assert first_property_dd is not None
        assert set([option["value"] for option in first_property_dd["options"]]) == set(
            [
                f"{SSH.REFRESH_CATALOG_PROPERTY}page_events.page_visit",
                "collection:session",
                str(TM.COHORT_FIELD_PATH),
                "dimension:sessions.browser",
                "dimension:sessions.locale",
                "dimension:sessions.platform",
                "dimension:sessions.session_id",
                "dimension:user_profiles.user_country_code",
                "dimension:user_profiles.user_id",
                "dimension:user_profiles.user_locale",
                "page_events.page_visit.title",
                "page_events.page_visit.domain",
                "page_events.page_visit.acquisition_campaign",
                "page_events.page_visit.item_id",
                "page_events.page_visit.user_locale",
                "page_events.page_visit.user_country_code",
                "page_events.page_visit.event_time",
                "page_events.page_visit.user_id",
                "page_events.page_visit.event_name",
                "page_events.page_visit.session_id",
            ]
        )
        assert first_property_operator_dd is not None

        assert "d-none" in first_property_operator_dd.get("className", "")

        # Metric confs Part

        assert_input_value(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CUSTOM_DATE_PICKER},
            explore_page,
            [None, None],
        )
        assert_input_value(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN},
            explore_page,
            "2 months",
        )


@patch("mitzu.webapp.navbar.OM")
@patch("mitzu.webapp.pages.explore.metric_config_handler.ctx")
@patch("mitzu.webapp.pages.explore.event_segment_handler.ctx")
def test_unparsable_metric_link_is_kept(
    esh_ctx,
    ctx,
    om_mock,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
):
    query_params = {"m": "unparsable metric", "some extra": "some extra"}
    with server.test_request_context():
        org_manager = MagicMock()
        cloud_storage = MagicMock()
        om_mock.OrgManager.get.return_value = org_manager
        org_manager._cloud_storage = cloud_storage

        cloud_storage.get_group_page_links.return_value = [
            SM.PageLinkStorageModel(
                use_case="sample_project_id",
                link="google.com",
                description="desc",
                group="support_hub",
            )
        ]

        res = EXP.create_explore_page(
            query_params,
            discovered_project.project.id,
            storage=dependencies.storage,
            catalog_service=CS.CatalogService(
                dependencies.storage.get_project_storage(discovered_project.project.id),
                InMemoryCache(),
            ),
            relevance_scores={},
        )

        explore_page = to_dict(res)

        div = find_component_by_id(CONST.STATE_DIV, explore_page)
        assert div is not None, f"Element not found with id {CONST.STATE_DIV}"
        assert (
            div["children"]
            == "/projects/sample_project_id/explore?m=unparsable+metric&some+extra=some+extra"
        )


@patch("mitzu.webapp.navbar.OM")
@patch("mitzu.webapp.pages.explore.metric_config_handler.ctx")
@patch("mitzu.webapp.pages.explore.event_segment_handler.ctx")
@patch("mitzu.webapp.pages.explore.simple_segment_handler.ctx")
def test_saved_metric_opened(
    ssh_ctx,
    esh_ctx,
    ctx,
    om_mock,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    project: M.Project,
):
    with server.test_request_context():
        org_manager = MagicMock()
        cloud_storage = MagicMock()
        om_mock.OrgManager.get.return_value = org_manager
        org_manager._cloud_storage = cloud_storage

        cloud_storage.get_group_page_links.return_value = [
            SM.PageLinkStorageModel(
                use_case="sample_project_id",
                link="google.com",
                description="desc",
                group="support_hub",
            )
        ]

        storage = dependencies.storage
        project_storage = storage.get_project_storage(SP.SAMPLE_PROJECT_ID)
        discovered_project = project_storage.get_discovered_project()

        catalog_service = CS.CatalogService(
            project_storage,
            InMemoryCache(),
        )

        catalog_service.register_new_events_if_not_exists(
            [
                event_def_ref.get_value_if_exists()
                for event_def_ref in discovered_project.event_definitions.values()
            ],
        )

        page_visit = M.EventNamePath("page_events", "page_visit")
        title = "test metric"
        description = "description"
        webapp_metric = WM.WebappMetric(
            M.MetricType.SEGMENTATION,
            WM.WebappMetricConfig.from_model(
                M.MetricConfig(
                    lookback_days=M.TimeWindow(2, M.TimeGroup.MONTH),
                    aggregation=M.AggregationConfig(M.AggType.COUNT_EVENTS),
                )
            ),
            segments=[
                WM.WebappSimpleSegment.create(page_visit, None),
            ],
            title=title,
            description=description,
        )

        saved_metric = WM.SavedMetric(
            id=create_unique_id(),
            name="saved metric",
            description=description,
            image_base64="",
            small_base64="",
            project_id=discovered_project.project.id,
            owner=storage.get_org_owner().membership_id,
            metric_json=SE.to_compressed_string(webapp_metric),
        )
        project_storage.set_saved_metric(saved_metric)
        insight_result = get_simple_chart(
            webapp_metric, pd.DataFrame(), metric_context, project
        )
        project_storage.set_insight_result(saved_metric.id, insight_result)

        query_params = {"sm": unquote(saved_metric.id)}
        res = EXP.create_explore_page(
            query_params,
            discovered_project.project.id,
            storage=dependencies.storage,
            catalog_service=catalog_service,
            relevance_scores={},
        )

        explore_page = to_dict(res)

        assert_input_value(MIH.METRIC_NAME_INPUT, explore_page, webapp_metric.title)
        assert_input_value(
            MIH.METRIC_DESCRIPTION_INPUT, explore_page, webapp_metric.description
        )

        first_event_dd = find_component_by_id(
            {"index": "0-0", "type": CONST.EVENT_NAME_DROPDOWN}, explore_page
        )

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, explore_page
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, explore_page
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, explore_page
        )

        assert first_event_dd is not None
        assert first_event_dd["value"] == "page_events.page_visit"
        assert second_event_dd is not None
        assert first_property_dd is not None
        assert set([option["value"] for option in first_property_dd["options"]]) == set(
            [
                f"{SSH.REFRESH_CATALOG_PROPERTY}page_events.page_visit",
                "collection:session",
                str(TM.COHORT_FIELD_PATH),
                "dimension:sessions.browser",
                "dimension:sessions.locale",
                "dimension:sessions.platform",
                "dimension:sessions.session_id",
                "dimension:user_profiles.user_country_code",
                "dimension:user_profiles.user_id",
                "dimension:user_profiles.user_locale",
                "page_events.page_visit.title",
                "page_events.page_visit.domain",
                "page_events.page_visit.acquisition_campaign",
                "page_events.page_visit.item_id",
                "page_events.page_visit.user_locale",
                "page_events.page_visit.user_country_code",
                "page_events.page_visit.event_time",
                "page_events.page_visit.user_id",
                "page_events.page_visit.event_name",
                "page_events.page_visit.session_id",
            ]
        )
        assert first_property_operator_dd is not None
        assert "d-none" in first_property_operator_dd.get("className", "")

        # Metric confs Part

        assert_input_value(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CUSTOM_DATE_PICKER},
            explore_page,
            [None, None],
        )
        assert_input_value(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN},
            explore_page,
            "2 months",
        )

        # state div content set
        div = find_component_by_id(CONST.STATE_DIV, explore_page)
        assert div is not None, f"Element not found with id {CONST.STATE_DIV}"
        assert div[
            "children"
        ] == f"/projects/sample_project_id/explore?sm={saved_metric.id}&m=" + quote(
            SE.to_compressed_string(webapp_metric)
        )


def test_event_chosen_for_retention(
    server: flask.Flask, discovered_project: M.DiscoveredProject
):
    with server.test_request_context():
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    )
                                }
                            }
                        },
                        1: {
                            "children": {
                                0: {CONST.EVENT_NAME_DROPDOWN: "checkouts.checkout"}
                            }
                        },
                    }
                },
            },
        )

        res = get_handle_input_changes_result(all_inputs, discovered_project)
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res = to_dict(res.metric_segments[0])

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, res
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, res
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res
        )

        assert second_event_dd is not None
        assert first_property_dd is not None
        assert len(first_property_dd["options"]) == 22
        assert first_property_operator_dd is not None
        assert "d-none" in first_property_operator_dd.get("className", "")


@patch(
    "mitzu.webapp.pages.explore.explore_page.C",
)
def test_event_changed_with_aggregated_event_property(
    configs,
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        triggered_id = CONST.EVENT_NAME_DROPDOWN

        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {CONST.PROPERTY_NAME_DROPDOWN: None}
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(
            all_inputs,
            discovered_project,
            triggered_id,
        )
        metric_configs = to_dict(res.metric_config_container)
        assert res.mitzu_location is not None and res.mitzu_location.startswith(
            "http://localhost/projects/sample_project_id/explore?m="
        )

        assert_input_value(CONST.AGG_EVENT_PROPERTY_NAME, metric_configs, None)


def test_aggregated_property_type_changed(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        triggered_id = CONST.AGGREGATION_TYPE
        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                CONST.METRIC_TYPE_CHOICE: "conversion",
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {CONST.PROPERTY_NAME_DROPDOWN: None}
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
                CONST.AGGREGATION_TYPE: "by_event_property",
                CONST.AGG_EVENT_PROPERTY_TYPE: "count_distinct",
                CONST.AGG_EVENT_PROPERTY_NAME: None,
            },
        )

        res = get_handle_input_changes_result(
            all_inputs,
            discovered_project,
            triggered_id,
        )

        metric_configs = to_dict(res.metric_config_container)
        assert_input_value(
            CONST.AGG_EVENT_PROPERTY_TYPE, metric_configs, "count_distinct"
        )


def test_event_changed_event_name_to_none(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        triggered_id = CONST.EVENT_NAME_DROPDOWN

        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: None,
                                },
                            },
                        }
                    }
                },
                CONST.AGGREGATION_TYPE: "by_event_property",
                CONST.AGG_EVENT_PROPERTY_TYPE: "count_distinct",
                CONST.AGG_EVENT_PROPERTY_NAME: "checkouts.checkout.user_country_code",
            },
        )

        res = get_handle_input_changes_result(
            all_inputs, discovered_project, triggered_id
        )
        metric_configs = to_dict(res.metric_config_container)

        assert_input_value(CONST.AGG_EVENT_PROPERTY_NAME, metric_configs, None)
        assert_input_value(
            CONST.AGG_EVENT_PROPERTY_TYPE, metric_configs, "count_distinct"
        )


def test_cohort_property_chosen_for_segmentation(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
):
    with server.test_request_context():
        event = M.EventNamePath("page_events", "page_visit")
        agg_type = M.AggType.RETURN_UNIQUE_USERS
        metric = WM.WebappMetric(
            M.MetricType.SEGMENTATION,
            segments=[
                replace(
                    WM.WebappSimpleSegment.create(event, None),
                    aggregation=WM.WebappAggregationConfig(agg_type),
                ),
            ],
            config=WM.WebappMetricConfig.from_model(
                M.MetricConfig(
                    start_dt=datetime(2021, 12, 1),
                    end_dt=datetime(2021, 12, 31),
                    time_group=M.TimeGroup.TOTAL,
                    aggregation=M.AggregationConfig(agg_type),
                )
            ),
        )
        metric_json = json.dumps(SE.webapp_metric_to_dict(metric))
        user_service = dependencies.user_service
        cohort_to_save = WM.CohortInfo(
            id="1",
            name="Test Cohort",
            created_at=datetime.now(),
            last_updated_at=datetime.now(),
            owner=user_service.list_users()[0].membership_id,
            type=M.CohortType.DYNAMIC,
            metric_json=metric_json,
            cohort_size=2,
            entity=M.USER_ENTITY,
        )

        dependencies.get_project_storage(discovered_project.project.id).set_cohort(
            cohort_to_save
        )

        all_inputs = get_all_inputs(
            discovered_project.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    ),
                                    "children": {
                                        0: {
                                            CONST.PROPERTY_NAME_DROPDOWN: str(
                                                TM.COHORT_FIELD_PATH
                                            )
                                        }
                                    },
                                },
                                1: {CONST.EVENT_NAME_DROPDOWN: None},
                            },
                            CONST.COMPLEX_SEGMENT_GROUP_BY: None,
                        }
                    }
                },
            },
        )

        res = get_handle_input_changes_result(
            all_inputs, discovered_project, cohorts=[cohort_to_save]
        )
        assert len(res.metric_segments) == CONST.NUMBER_OF_SEGMENTS
        res = to_dict(res.metric_segments[0])

        second_event_dd = find_component_by_id(
            {"index": "0-1", "type": CONST.EVENT_NAME_DROPDOWN}, res
        )
        first_property_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_NAME_DROPDOWN}, res
        )
        first_property_operator_dd = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_OPERATOR_DROPDOWN}, res
        )
        first_property_value_input = find_component_by_id(
            {"index": "0-0-0", "type": CONST.PROPERTY_VALUE_INPUT}, res
        )

        assert second_event_dd is not None
        assert first_property_dd is not None
        assert first_property_operator_dd is not None
        assert first_property_value_input is not None
        assert first_property_operator_dd["data"] == [
            {
                "label": "is",
                "search": "is",
                "value": "in_cohort",
                "disabled": False,
            },
            {
                "label": "is not",
                "search": "is not",
                "value": "not_in_cohort",
                "disabled": False,
            },
        ]
        assert first_property_operator_dd["value"] == "in_cohort"
        assert len(first_property_value_input["data"]) == 1
        assert first_property_value_input["value"] == [None]


def test_download_button_clicked(server, discovered_project: M.DiscoveredProject):
    with server.test_request_context():
        event = M.EventNamePath("page_events", "page_visit")
        base_metric = WM.WebappMetric(
            M.MetricType.SEGMENTATION,
            segments=[
                replace(
                    WM.WebappSimpleSegment.create(event, None),
                    aggregation=WM.WebappAggregationConfig(
                        M.AggType.COUNT_UNIQUE_USERS
                    ),
                )
            ],
            config=WM.WebappMetricConfig.from_model(
                M.MetricConfig(
                    time_group=M.TimeGroup.MONTH,
                )
            ),
        )
        path = P.create_explore_path(discovered_project.project.id, base_metric)
        data, alert = download_clicked(1, path, "test_title", show_annotations=True)

        assert alert == no_update
        assert data["base64"] is True
        assert data["content"][0:10] == "iVBORw0KGg"  # the beginning of the PNG base64
        assert (
            len(data["content"]) > 10000
        )  # the length of the PNG base64 - around 26088


@pytest.mark.parametrize(
    "triggered,expected_redirect_url",
    [
        (
            {
                "prop_id": '{"index":"0-1","type":"event_name_dropdown"}.value',
                "value": "email_subscriptions.subscribe",
            },
            None,
        ),
        (
            {
                "prop_id": '{"index":"0-1-0","type":"property_name_dropdown"}.value',
                "value": "###refresh_catalog_propertyemail_subscriptions.subscribe",
            },
            "/projects/test_project/manage?tab=properties&event=email_subscriptions.subscribe&page=1",
        ),
        (
            {
                "prop_id": '{"index":"0","type":"complex_segment_combine_button"}.n_clicks',
                "value": 1,
            },
            None,
        ),
        (
            {
                "prop_id": '{"index":"0-0-2","type":"property_value_input"}.value',
                "value": [],
            },
            None,
        ),
        (
            {
                "prop_id": '{"index":"0-0-2","type":"property_value_input"}.value',
                "value": ["cn_CN"],
            },
            None,
        ),
    ],
)
@patch("mitzu.webapp.pages.explore.explore_page.ctx")
def test_handle_catalog_refresh_from_explore(
    ctx_mock, server: flask.Flask, triggered, expected_redirect_url
):
    ctx_mock.triggered = [triggered]
    project_id = "test_project"
    result = EXP.handle_catalog_refresh_from_explore(project_id)
    if expected_redirect_url is None:
        assert result is None
    else:
        assert result == OnInputsChangeResponse.default_instance(
            redirect_to_catalog_location=expected_redirect_url
        )


def test_event_chosen_for_segmentation_dimension_profile(
    server: flask.Flask,
    discovered_project: M.DiscoveredProject,
    dependencies: DEPS.Dependencies,
):
    insight_settings = replace(
        discovered_project.project.insight_settings, auto_refresh_enabled=True
    )
    get_project_config_storage(
        dependencies, discovered_project.project.id
    ).set_insight_settings(insight_settings)
    proj = deepcopy(discovered_project)
    proj.project.insight_settings = insight_settings
    with server.test_request_context():
        all_inputs = get_all_inputs(
            proj.project.id,
            {
                H.METRIC_SEGMENTS: {
                    "children": {
                        0: {
                            "children": {
                                0: {
                                    CONST.EVENT_NAME_DROPDOWN: str(
                                        page_visit_event_name_path
                                    )
                                }
                            }
                        }
                    }
                },
                DIM_PROFILE_ID: '[{"l": {"fp": "dimensionid:group"}, "op": "EQ", "r": "test"}]',
            },
        )

        res = get_handle_input_changes_result(all_inputs, proj)
        assert res.state_div is not None

        url_res = urlparse(res.state_div)
        query = parse_qs(url_res.query)["m"][0]
        metric = SE.from_compressed_string(query)

        assert metric.global_filters == [
            WM.WebappEventFilter(
                M.DimensionIDFieldPath(TM.GROUP_ENTITY),
                M.Operator.EQ,
                "test",
            )
        ]
        assert metric.segments[0].aggregation is not None
        assert metric.segments[0].aggregation.type == M.AggType.COUNT_EVENTS


def test_filter_comparison_options():
    option_none = {"value": None}
    assert filter_comparison_options(option_none, M.TimeGroup.DAY) is True
    assert filter_comparison_options(option_none, M.TimeGroup.WEEK) is True
    assert filter_comparison_options(option_none, M.TimeGroup.MONTH) is True

    option_day = {"value": M.TimeWindow(1, M.TimeGroup.DAY).serialize()}
    assert filter_comparison_options(option_day, M.TimeGroup.DAY) is True
    assert filter_comparison_options(option_day, M.TimeGroup.WEEK) is False
    assert filter_comparison_options(option_day, M.TimeGroup.MONTH) is False

    option_week = {"value": M.TimeWindow(1, M.TimeGroup.WEEK).serialize()}
    assert filter_comparison_options(option_week, M.TimeGroup.DAY) is True
    assert filter_comparison_options(option_week, M.TimeGroup.WEEK) is True
    assert filter_comparison_options(option_week, M.TimeGroup.MONTH) is False

    option_month = {"value": M.TimeWindow(1, M.TimeGroup.MONTH).serialize()}
    assert filter_comparison_options(option_month, M.TimeGroup.DAY) is True
    assert filter_comparison_options(option_month, M.TimeGroup.WEEK) is False
    assert filter_comparison_options(option_month, M.TimeGroup.MONTH) is True
    assert filter_comparison_options(option_month, M.TimeGroup.YEAR) is False

    option_year = {"value": M.TimeWindow(1, M.TimeGroup.YEAR).serialize()}
    assert filter_comparison_options(option_year, M.TimeGroup.DAY) is True
    assert filter_comparison_options(option_year, M.TimeGroup.WEEK) is True
    assert filter_comparison_options(option_year, M.TimeGroup.MONTH) is True
    assert filter_comparison_options(option_year, M.TimeGroup.YEAR) is True


def test_get_previous_comparison_options():
    options_day = get_previous_comparison_options(M.TimeGroup.DAY)
    assert len(options_day) == 6

    options_week = get_previous_comparison_options(M.TimeGroup.WEEK)
    expected_labels_week = [
        "No comparison",
        "Compare with previous week",
        "Compare with 4 weeks ago",
        "Compare with previous year",
    ]
    assert len(options_week) == 4
    assert all(opt["label"] in expected_labels_week for opt in options_week)

    options_month = get_previous_comparison_options(M.TimeGroup.MONTH)
    expected_labels_month = [
        "No comparison",
        "Compare with previous month",
        "Compare with previous year",
    ]
    assert len(options_month) == 3
    assert all(opt["label"] in expected_labels_month for opt in options_month)

    options_year = get_previous_comparison_options(M.TimeGroup.YEAR)
    expected_labels_year = [
        "No comparison",
        "Compare with previous year",
    ]
    assert len(options_year) == 2
    assert all(opt["label"] in expected_labels_year for opt in options_year)

    options_hour = get_previous_comparison_options(M.TimeGroup.HOUR)
    expected_labels_hour = [
        "No comparison",
        "Compare with previous day",
        "Compare with previous week",
    ]
    assert len(options_hour) == 3
    assert all(opt["label"] in expected_labels_hour for opt in options_hour)


def test_match_comparison_options_with_time_group_day():
    select_values: List[Dict[str, Optional[str]]] = [
        {"label": "No comparison", "value": None},
        {
            "label": "Compare with previous day",
            "value": M.TimeWindow(1, M.TimeGroup.DAY).serialize(),
        },
        {
            "label": "Compare with previous week",
            "value": M.TimeWindow(1, M.TimeGroup.WEEK).serialize(),
        },
    ]

    result = match_comparison_options_with_time_group(select_values, M.TimeGroup.DAY)

    assert result == M.TimeWindow(1, M.TimeGroup.DAY).serialize()


def test_match_comparison_options_with_time_group_week():
    select_values: List[Dict[str, Optional[str]]] = [
        {"label": "No comparison", "value": None},
        {
            "label": "Compare with previous day",
            "value": M.TimeWindow(1, M.TimeGroup.DAY).serialize(),
        },
        {
            "label": "Compare with previous week",
            "value": M.TimeWindow(1, M.TimeGroup.WEEK).serialize(),
        },
    ]

    result = match_comparison_options_with_time_group(select_values, M.TimeGroup.WEEK)

    assert result == M.TimeWindow(1, M.TimeGroup.WEEK).serialize()


def test_match_comparison_options_no_match_fallback_to_last():
    select_values: List[Dict[str, Optional[str]]] = [
        {"label": "No comparison", "value": None},
        {
            "label": "Compare with previous day",
            "value": M.TimeWindow(1, M.TimeGroup.DAY).serialize(),
        },
    ]

    result = match_comparison_options_with_time_group(select_values, M.TimeGroup.WEEK)

    assert result == M.TimeWindow(1, M.TimeGroup.DAY).serialize()


def test_match_comparison_options_empty_select_values():
    select_values: List[Dict[str, Optional[str]]] = []

    with pytest.raises(IndexError):
        match_comparison_options_with_time_group(select_values, M.TimeGroup.DAY)


def test_match_comparison_options_no_non_none_value():
    select_values: List[Dict[str, Optional[str]]] = [
        {"label": "No comparison", "value": None}
    ]

    result = match_comparison_options_with_time_group(select_values, M.TimeGroup.DAY)

    assert result is None


def test_render_global_filters(
    discovered_project: M.DiscoveredProject, dependencies: DEPS.Dependencies
):
    setup_discovered_project(dependencies.storage, discovered_project)
    metric_context = create_metric_context_from_discovered_project(discovered_project)
    project = discovered_project.project
    input_generator = InputGenerator(
        project.id, metric_context, dependencies.get_project_storage(project.id)
    )

    event_paths = set(metric_context.get_all_event_names())

    filters = [
        WM.WebappEventFilter(
            M.DimensionFieldPath("a", "b"),
            M.Operator.NONE_OF,
            "foo",
        ),
    ]

    res = render_global_filters(
        filters, input_generator, event_paths, show_filters_component=True
    )
    res_dict = to_dict(res)

    assert (
        safe_find_component_by_id(
            {
                "type": GLOBAL_FILTER_PROPERTY_TYPE,
                "index": "0",
            },
            res_dict,
        )["value"]
        == "dimension:a.b"
    )
    assert (
        safe_find_component_by_id(
            {
                "type": GLOBAL_FILTER_OPERATOR_TYPE,
                "index": "0",
            },
            res_dict,
        )["value"]
        == "none_of"
    )
    assert (
        safe_find_component_by_id(
            {
                "type": GLOBAL_FILTER_VALUE_TYPE,
                "index": "0",
            },
            res_dict,
        )["value"]
        == "foo"
    )

    assert (
        safe_find_component_by_id(
            {
                "type": GLOBAL_FILTER_PROPERTY_TYPE,
                "index": "1",
            },
            res_dict,
        )["value"]
        is None
    )


def test_create_metric_from_all_inputs_with_global_filter_inputs_property_changed(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": GLOBAL_FILTER_PROPERTY_TYPE, "index": "0"}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            GLOBAL_FILTER_PROPERTY_TYPE: ["dimension:user_profiles.user_id"],
            GLOBAL_FILTER_OPERATOR_TYPE: [M.Operator.EQ],
            GLOBAL_FILTER_VALUE_TYPE: [""],
            CONST.DIM_PROFILE_ID: '[{"l":{"fp":"dimension:user_profiles.user_id"},"op":"none_of","r":"should_not_be_used"}]',
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert metric.global_filters == [
        WM.WebappEventFilter(
            left=M.DimensionFieldPath("user_profiles", "user_id"),
            operator=M.Operator.ANY_OF,
            right=[],
        )
    ]


def test_creaet_metric_from_all_inputs_with_global_filter_inputs_value_changed(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": GLOBAL_FILTER_VALUE_TYPE, "index": "0"}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            GLOBAL_FILTER_PROPERTY_TYPE: ["dimension:user_profiles.user_id"],
            GLOBAL_FILTER_OPERATOR_TYPE: [M.Operator.EQ],
            GLOBAL_FILTER_VALUE_TYPE: ["user_123"],
            CONST.DIM_PROFILE_ID: '[{"l":{"fp":"dimension:user_profiles.user_id"},"op":"none_of","r":"should_not_be_used"}]',
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert metric.global_filters == [
        WM.WebappEventFilter(
            left=M.DimensionFieldPath("user_profiles", "user_id"),
            operator=M.Operator.EQ,
            right="user_123",
        )
    ]


def test_create_metric_from_all_inputs_with_global_filter_inputs_operator_changed(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": GLOBAL_FILTER_OPERATOR_TYPE, "index": "0"}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            GLOBAL_FILTER_PROPERTY_TYPE: ["dimension:user_profiles.user_id"],
            GLOBAL_FILTER_OPERATOR_TYPE: [M.Operator.NONE_OF],
            GLOBAL_FILTER_VALUE_TYPE: ["user_456"],
            CONST.DIM_PROFILE_ID: '[{"l":{"fp":"dimension:user_profiles.user_id"},"op":"eq","r":"should_not_be_used"}]',
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert metric.global_filters == [
        WM.WebappEventFilter(
            left=M.DimensionFieldPath("user_profiles", "user_id"),
            operator=M.Operator.NONE_OF,
            right=["user_456"],
        )
    ]


def test_create_metric_from_all_inputs_with_clear_global_filters_button_clicked(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": CONST.CLEAR_GLOBAL_FILTERS_BUTTON, "index": "0"}
    existing_filter_json = json.dumps(
        [{"l": {"fp": "dimensionid:group"}, "op": "eq", "r": "group_123"}]
    )

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.CLEAR_GLOBAL_FILTERS_BUTTON: 1,
            CONST.DIM_PROFILE_ID: existing_filter_json,
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert metric.global_filters == [
        WM.WebappEventFilter(
            left=M.DimensionIDFieldPath(entity=M.Entity("group")),
            operator=M.Operator.EQ,
            right="group_123",
        )
    ]


def test_create_metric_from_all_inputs_with_clear_global_filters_clears_filters(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": CONST.CLEAR_GLOBAL_FILTERS_BUTTON, "index": "0"}

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            CONST.CLEAR_GLOBAL_FILTERS_BUTTON: 1,
            CONST.DIM_PROFILE_ID: '[{"l":{"fp":"dimension:user_profiles.user_id"},"op":"eq","r":"should_not_be_used"}]',
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert metric.global_filters == []


def test_create_metric_from_all_inputs_with_existing_dim_id_global_filter(
    discovered_project: M.DiscoveredProject,
):
    triggered_id = {"type": GLOBAL_FILTER_VALUE_TYPE, "index": "0"}

    existing_filter_json = json.dumps(
        [{"l": {"fp": "dimensionid:group"}, "op": "eq", "r": "group_123"}]
    )

    all_inputs = get_all_inputs(
        discovered_project.project.id,
        {
            GLOBAL_FILTER_PROPERTY_TYPE: ["dimension:user_profiles.user_id"],
            GLOBAL_FILTER_OPERATOR_TYPE: [M.Operator.EQ],
            GLOBAL_FILTER_VALUE_TYPE: ["user_abc"],
            CONST.DIM_PROFILE_ID: existing_filter_json,
        },
    )

    metric, _ = get_create_metric_from_all_inputs_result(
        all_inputs, discovered_project, triggered_id
    )

    assert len(metric.global_filters) == 2
    assert metric.global_filters[0] == WM.WebappEventFilter(
        left=M.DimensionIDFieldPath(entity=M.Entity("group")),
        operator=M.Operator.EQ,
        right="group_123",
    )
    assert metric.global_filters[1] == WM.WebappEventFilter(
        left=M.DimensionFieldPath("user_profiles", "user_id"),
        operator=M.Operator.EQ,
        right="user_abc",
    )


@pytest.mark.parametrize(
    "properties, operators, values, triggered_type, triggered_index, expected",
    [
        (
            ["dimension:users.id"],
            ["any_of"],
            [["123"]],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("users", "id"), M.Operator.ANY_OF, ["123"]
                )
            ],
        ),
        (
            ["dimension:users.id"],
            [],
            [],
            GLOBAL_FILTER_PROPERTY_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("users", "id"), M.Operator.ANY_OF, []
                )
            ],
        ),
        (
            ["dimension:users.id"],
            ["is_null"],
            [[""]],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("users", "id"), M.Operator.IS_NULL, None
                )
            ],
        ),
        (
            ["dimension:events.name"],
            ["any_of"],
            [["_custom_prefix_click_button"]],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("events", "name"),
                    M.Operator.ANY_OF,
                    ["click_button"],
                )
            ],
        ),
        (
            ["dimension:events.name"],
            ["any_of"],
            [["_custom_prefix_submit_form", "hover_link"]],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("events", "name"),
                    M.Operator.ANY_OF,
                    ["submit_form", "hover_link"],
                )
            ],
        ),
        (
            ["dimension:users.id"],
            ["any_of"],
            [None],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("users", "id"), M.Operator.ANY_OF, []
                )
            ],
        ),
        (
            ["collection:cohorts"],
            ["in_cohort"],
            ["cohort_123"],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.CollectionFieldPath(entity=M.Entity("cohorts")),
                    M.Operator.IN_COHORT,
                    WM.CohortReference("cohort_123"),
                )
            ],
        ),
        (
            ["collection:cohorts"],
            ["not_in_cohort"],
            ["_custom_prefix_cohort_456"],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.CollectionFieldPath(M.Entity("cohorts")),
                    M.Operator.NOT_IN_COHORT,
                    WM.CohortReference("cohort_456"),
                )
            ],
        ),
        (
            ["dimension:events.name"],
            ["any_of"],
            [["valid_event", f"{CUSTOM_LOOKUP_PROP_VALUE}_should_be_skipped"]],
            GLOBAL_FILTER_VALUE_TYPE,
            0,
            [
                WM.WebappEventFilter(
                    M.DimensionFieldPath("events", "name"),
                    M.Operator.ANY_OF,
                    ["valid_event"],
                )
            ],
        ),
    ],
)
def test_get_global_filters_from_inputs(
    properties: List[Optional[str]],
    operators: List[str],
    values: List[Any],
    triggered_type: str,
    triggered_index: int,
    expected: List[WM.WebappEventFilter],
):
    result = EXP.get_global_filters_from_inputs(
        properties, operators, values, triggered_type, triggered_index
    )
    assert result == expected
