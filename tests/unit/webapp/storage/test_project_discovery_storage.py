import copy
from datetime import datetime
from typing import Optional

from freezegun import freeze_time

import mitzu.model as M
import mitzu.webapp.storage.cloud_storage as CS
import mitzu.webapp.storage.project_discovery_storage as PDS
import mitzu.webapp.storage.session_manager as SM
import mitzu.webapp.storage.storage as S
import mitzu.webapp.storage.storage_model as SSM
from tests.helper import setup_discovered_project


def get_discovery_storage(
    discovered_project: M.DiscoveredProject,
    session_manager: Optional[SM.SessionManager] = None,
) -> PDS.ProjectDiscoveryStorage:
    """
    Creates and returns a CloudStorage and an OrgScopedStorage which are working
    with the same in memory database.
    """

    org_id = "orga"
    if session_manager is None:
        session_manager = SM.SessionManager()

    SSM.Base.metadata.create_all(session_manager.engine)

    org_storage = S.OrgScopedStorage(
        org_id=org_id, session_manager=session_manager, read_only=False
    )
    cloud_storage = CS.CloudStorage(session_manager)
    cloud_storage.create_new_organisation(org_id, f"admin@{org_id}.test")

    setup_discovered_project(org_storage, discovered_project)

    return org_storage.get_project_discovery_storage(discovered_project.project.id)


def test_get_event_property_enums(discovered_project: <PERSON><PERSON>veredProject):
    discovery_storage = get_discovery_storage(discovered_project)
    assert discovery_storage.get_event_property_enums("missing") == set()

    random_event = next(
        iter(discovered_project.event_definitions.values())
    ).get_value_if_exists()
    event_property = copy.deepcopy(random_event.fields[0])
    assert (
        discovery_storage.get_event_property_enums(event_property.get_id())
        == event_property.discovered_values
    )

    new_vals = {"xx", "yy"}
    event_property = event_property.copy_with_new_custom_values(new_vals)
    discovery_storage.set_event_field_definition(event_property, random_event)

    assert discovery_storage.get_event_property_enums(
        event_property.get_id()
    ) == event_property.discovered_values.union(new_vals)


def test_get_dimension_property_enums(discovered_project: M.DiscoveredProject):
    discovery_storage = get_discovery_storage(discovered_project)
    project = discovery_storage.get_discovered_project(lazy_load=False)

    assert discovery_storage.get_dimension_property_enums("missing") == set()

    dimension_property = copy.deepcopy(
        next(iter(project.dimension_definitions.values()))
    )
    assert (
        discovery_storage.get_dimension_property_enums(dimension_property.get_id())
        == dimension_property.discovered_values
    )

    new_vals = {"xx", "yy"}
    dimension_property = dimension_property.copy_with_new_custom_values(new_vals)
    discovery_storage.set_dimension_field_definition(dimension_property)

    assert discovery_storage.get_dimension_property_enums(
        dimension_property.get_id()
    ) == dimension_property.discovered_values.union(new_vals)


@freeze_time("2023-01-02 00:00:00")
def test_mark_dimension_property_as_discovered(discovered_project: M.DiscoveredProject):
    discovery_storage = get_discovery_storage(discovered_project)
    discovered_project = discovery_storage.get_discovered_project(lazy_load=False)

    field_path = M.DimensionFieldPath("user_profiles", "user_country_code")
    dfd = discovered_project.dimension_definitions[field_path]

    discovery_storage.mark_dimension_property_as_discovered(dfd.get_id())

    dim_fields = discovery_storage.get_dimension_fields(discovered_project.project)
    assert dim_fields[field_path].last_discovered == datetime(2023, 1, 2)
