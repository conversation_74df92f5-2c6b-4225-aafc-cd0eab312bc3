from typing import Optional
from unittest.mock import MagicMock, patch

import pytest

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import tests.model as TM
from mitzu.webapp.service.input_generator import (
    DropdownOption,
    InputGenerator,
)
from tests.unit.webapp.service.input_generator.test_generator import (
    cohort_in_property_list,
    dimension_fields_in_properties,
    event_1_properties,
    group_collection_in_property_list,
    metric_context,
    project_id,
    search_for_more,
)


@pytest.mark.parametrize(
    "selected,placeholder",
    [
        (None, "Select an event"),
        (M.EventField<PERSON>ath("table", "event_1", "field_1"), "Whatever event"),
        (<PERSON><PERSON>("dim_user_table", "dim_field_1"), "Whatever dimension"),
        (TM.COHORT_FIELD_PATH, "Whatever cohort"),
    ],
)
@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    5,
)
def test_input_generator_create_property_dropdown_for_dashboard_filter_with_metric_context(
    selected: Optional[<PERSON>.<PERSON>], placeholder: str
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_global_filter(
        selected, placeholder, {event_name_path}
    )
    assert dd.options == [
        search_for_more(5, 7, None),
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[1],
        dimension_fields_in_properties[1],
        dimension_fields_in_properties[2],
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == placeholder
    assert dd.is_valid
    assert dd.is_visible


def test_input_generator_create_property_dropdown_for_dashboard_filter_list_only_the_common_fields():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_global_filter(
        None,
        "placeholder",
        {M.EventNamePath("table", "event_1"), M.EventNamePath("table", "event_2")},
    )
    assert dd.options == [
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[0],
        event_1_properties[1],
        dimension_fields_in_properties[0],
        dimension_fields_in_properties[1],
        dimension_fields_in_properties[2],
    ]

    assert dd.selected is None
    assert dd.placeholder == "placeholder"
    assert dd.is_valid
    assert dd.is_visible


def test_input_generator_create_property_dropdown_for_dashboard_filter_with_metric_context_and_error_on_missing_event():
    selected = M.EventFieldPath("missing", "event_1", "field_1")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("missing", "event_1")
    dd = input_generator.create_property_dropdown_for_global_filter(
        selected, "placeholder", {event_name_path}
    )
    assert dd.options == [
        cohort_in_property_list,
        dimension_fields_in_properties[0],
        dimension_fields_in_properties[1],
        DropdownOption(
            label="Field 1",
            search="missing.event_1.field_1",
            value="missing.event_1.field_1",
            is_valid=False,
        ),
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert not dd.is_valid
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    6,
)
def test_input_generator_create_property_dropdown_for_dashboard_filter_with_metric_context_and_error_on_missing_property():
    selected = M.EventFieldPath("table", "event_1", "missing")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_global_filter(
        placeholder="placeholder",
        selected=selected,
        event_name_paths={event_name_path},
    )
    assert dd.options == [
        search_for_more(6, 7, None),
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label="Missing",
            search="table.event_1.missing",
            value="table.event_1.missing",
            is_valid=False,
        ),
    ]

    assert dd.selected == str(selected)
    assert dd.is_valid is False
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    6,
)
def test_input_generator_create_property_dropdown_for_dashboard_filter_with_metric_context_and_error_on_missing_dim_property():
    selected = M.DimensionFieldPath("dim_1", "missing")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_global_filter(
        placeholder="placeholder",
        selected=selected,
        event_name_paths={event_name_path},
    )
    assert dd.options == [
        search_for_more(6, 7, None),
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label="Missing",
            search="dimension:dim_1.missing",
            value="dimension:dim_1.missing",
            is_valid=False,
        ),
    ]

    assert dd.selected == str(selected)
    assert dd.is_valid is False
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    6,
)
def test_get_property_options_for_dashboard_filter_always_includes_the_selected_value():
    event_1 = M.EventNamePath("table", "event_1")
    event_2 = M.EventNamePath("table", "event_2")
    metric_context = MC.MetricContext(
        _event_catalog={
            event_1: WM.EventMeta(
                "table", "event_1", "event_1_id", "Event Catalog", "description"
            ),
            event_2: WM.EventMeta("table", "event_1", "event_1_id", "Event 2", None),
        },
        _property_catalog={},
        _dimension_catalog={},
        _entity_meta={
            M.USER_ENTITY: WM.EntityMeta(M.USER_ENTITY, "User", "bi:person"),
        },
        _event_properties={
            event_1: [
                M.EventFieldPath("table", "event_1", f"evt_1_field_{i}")
                for i in range(0, 100)
            ]
            + [
                M.EventFieldPath("table", "event_1", f"common_field_{i}")
                for i in range(0, 100)
            ],
            event_2: [
                M.EventFieldPath("table", "event_2", f"evt_2_field_{i}")
                for i in range(0, 100)
            ]
            + [
                M.EventFieldPath("table", "event_2", f"common_field_{i}")
                for i in range(0, 100)
            ],
        },
        _table_catalog={
            "table": WM.EventTableMeta(entities={M.USER_ENTITY, TM.GROUP_ENTITY}),
            "table_without_groups": WM.EventTableMeta(entities={M.USER_ENTITY}),
        },
        global_filters=[],
        revenue_settings=None,
        _custom_events={},
    )
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_global_filter(
        placeholder="placeholder",
        event_name_paths={event_1, event_2},
        selected=M.EventFieldPath("table", "event_2", "common_field_9"),
    )
    assert [opt.value for opt in dd.options] == [
        "",
        "collection:user",
        "collection:group",
        "table.event_1.common_field_0",
        "table.event_1.common_field_1",
        "table.event_1.common_field_2",
        "table.event_1.common_field_9",
    ]
    assert all([opt.is_valid for opt in dd.options])

    assert dd.selected == str(M.EventFieldPath("table", "event_2", "common_field_9"))
    assert dd.is_valid is True
    assert dd.is_visible


def test_get_property_options_for_dashboard_filter_returns_no_result():
    event_1 = M.EventNamePath("table", "event_1")
    event_2 = M.EventNamePath("table", "event_2")
    metric_context = MC.MetricContext(
        _event_catalog={
            event_1: WM.EventMeta(
                "table", "event_1", "event_1_id", "Event Catalog", "description"
            ),
            event_2: WM.EventMeta("table", "event_1", "event_1_id", "Event 2", None),
        },
        _property_catalog={},
        _dimension_catalog={},
        _entity_meta={
            M.USER_ENTITY: WM.EntityMeta(M.USER_ENTITY, "User", "bi:person"),
        },
        _event_properties={
            event_1: [
                M.EventFieldPath("table", "event_1", f"evt_1_field_{i}")
                for i in range(0, 2)
            ]
            + [
                M.EventFieldPath("table", "event_1", f"common_field_{i}")
                for i in range(0, 2)
            ],
            event_2: [
                M.EventFieldPath("table", "event_2", f"evt_2_field_{i}")
                for i in range(0, 2)
            ]
            + [
                M.EventFieldPath("table", "event_2", f"common_field_{i}")
                for i in range(0, 2)
            ],
        },
        _table_catalog={
            "table": WM.EventTableMeta(entities={M.USER_ENTITY, TM.GROUP_ENTITY}),
            "table_without_groups": WM.EventTableMeta(entities={M.USER_ENTITY}),
        },
        global_filters=[],
        revenue_settings=None,
        _custom_events={},
    )
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    # has a hit with a single event
    options = input_generator.get_property_options_for_dashboard_filter(
        event_name_paths={event_2},
        include=None,
        search="evt 2 field 1",
    )
    assert [opt.value for opt in options] == ["table.event_2.evt_2_field_1"]
    assert all([opt.is_valid for opt in options])

    # no hit
    options = input_generator.get_property_options_for_dashboard_filter(
        event_name_paths={event_1, event_2},
        include=None,
        search="evt 2 field 1",
    )
    assert options == []
    assert all([opt.is_valid for opt in options])
