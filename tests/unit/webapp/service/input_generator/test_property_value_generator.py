import copy
from dataclasses import replace
from datetime import datetime
from typing import Any, List, Set
from unittest.mock import MagicMock

import pytest

import mitzu.model as M
import mitzu.webapp.model as WM
from mitzu.webapp.service.input_generator import (
    DropdownOption,
    InputGenerator,
)
from tests.unit.webapp.service.input_generator.test_generator import (
    metric_context,
    metric_context_with_revenue,
    project_id,
)


def test_create_property_value_input_without_event_filter():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_value_input(None, allow_reindex=True)
    assert dd.options == []
    assert dd.selected is None
    assert dd.placeholder == "..."
    assert dd.is_valid
    assert not dd.is_visible


def test_create_property_value_input_revenue_event_field():
    input_generator = InputGenerator(
        project_id, metric_context_with_revenue, MagicMock()
    )
    dd = input_generator.create_property_value_input(
        WM.WebappEventFilter(
            M.EventFieldPath("payments", "payments", "mrr"), M.Operator.GT, 100
        ),
        allow_reindex=True,
    )
    assert dd.options == [
        DropdownOption(
            label="100",
            value="100",
            search="100",
            is_valid=True,
            disabled=False,
            description=None,
            icon=None,
            field_type=None,
            is_property=True,
        ),
    ]
    assert dd.selected == 100
    assert dd.placeholder == "..."
    assert dd.is_valid
    assert dd.is_visible


test_cohort = WM.CohortInfo(
    id="test-cohort",
    name="Test Cohort",
    created_at=datetime.now(),
    last_updated_at=datetime.now(),
    owner=WM.MembershipID("membership-id"),
    type=M.CohortType.DYNAMIC,
    metric_json="{}",
    cohort_size=2,
    entity=M.USER_ENTITY,
)


@pytest.mark.parametrize(
    "filter,cohorts,expected_values,placeholder,reindex",
    [
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_2", "a_field_3"),
                M.Operator.ANY_OF,
                [True],
            ),
            [],
            [DropdownOption.create("True")],
            "True",
            True,
            id="bool filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_2", "a_field_2"),
                M.Operator.ANY_OF,
                [1],
            ),
            [],
            [DropdownOption.create("1")],
            "1",
            True,
            id="number filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_2", "field_1"),
                M.Operator.ANY_OF,
                ["1"],
            ),
            [],
            [DropdownOption.create("1")],
            "1",
            True,
            id="string filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.CollectionFieldPath(M.USER_ENTITY),
                M.Operator.IN_COHORT,
                test_cohort,
            ),
            [test_cohort],
            [
                DropdownOption(
                    label="Test Cohort", search="Test Cohort", value="test-cohort"
                )
            ],
            "Test Cohort...",
            False,
            id="cohort filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_2", "missing"),
                M.Operator.GT_EQ,
                123,
            ),
            [],
            [DropdownOption.create("123")],
            "...",
            False,
            id="missing field keeps the value",
        ),
    ],
)
def test_create_property_value_input(
    filter: WM.WebappEventFilter,
    cohorts: List[WM.CohortInfo],
    expected_values: List[DropdownOption],
    placeholder: str,
    reindex: bool,
):
    project_storage_mock = MagicMock()
    project_storage_mock.list_cohorts.return_value = cohorts

    input_generator = InputGenerator(
        project_id,
        metric_context_with_revenue,
        project_storage_mock,
    )
    dd = input_generator.create_property_value_input(filter, allow_reindex=reindex)
    if reindex:
        expected_values.append(
            DropdownOption(
                label="↻ re-index", value="_custom_lookup_option_", search="↻ re-index"
            )
        )
    assert dd.options == expected_values
    if len(cohorts) == 0:
        assert dd.selected == (
            [str(val) for val in filter.right]
            if isinstance(filter.right, list)
            else filter.right
        )
    else:
        assert dd.selected == [filter.right.id] if filter.right is not None else None

    assert dd.placeholder == placeholder
    assert dd.is_valid
    assert dd.is_visible


@pytest.mark.parametrize(
    "filter,stored_values,expected_values",
    [
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_1", "field_1"),
                M.Operator.ANY_OF,
                123,
            ),
            {"a", "b", "c"},
            [DropdownOption.create(val) for val in ["123", "a", "b", "c"]],
            id="loads event property enums from the storage",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_1", "field_1"),
                M.Operator.ANY_OF,
                123,
            ),
            set(),
            [DropdownOption.create("123")],
            id="loads event property enums from the storage when there are not stored values",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
                M.Operator.ANY_OF,
                123,
            ),
            {"a", "b", "c"},
            [DropdownOption.create(val) for val in ["123", "a", "b", "c"]],
            id="loads dimension property enums from the storage",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
                M.Operator.ANY_OF,
                123,
            ),
            set(),
            [DropdownOption.create("123")],
            id="loads dimension property enums from the storage when there are not stored values",
        ),
    ],
)
@pytest.mark.parametrize("allow_reindex", [True, False])
def test_create_property_value_input_getting_values_from_the_storage(
    filter: WM.WebappEventFilter,
    stored_values: Set[Any],
    expected_values: List[DropdownOption],
    allow_reindex: bool,
):
    storage_mock = MagicMock()
    storage_mock.get_project_storage.return_value = storage_mock
    if isinstance(filter.left, M.EventFieldPath):
        storage_mock.get_event_property_enums.return_value = stored_values
    elif isinstance(filter.left, M.DimensionFieldPath):
        storage_mock.get_dimension_property_enums.return_value = stored_values
    else:
        raise ValueError(f"Unknown field path: {filter.left}")

    mc = copy.deepcopy(metric_context)
    if filter.left in mc._property_catalog.keys() and isinstance(
        filter.left, M.EventFieldPath
    ):
        mc._property_catalog[filter.left] = replace(
            mc._property_catalog[filter.left],
            last_discovered=datetime.now(),
        )
    elif filter.left in mc._dimension_catalog.keys() and isinstance(
        filter.left, M.DimensionFieldPath
    ):
        mc._dimension_catalog[filter.left] = replace(
            mc._dimension_catalog[filter.left],
            last_discovered=datetime.now(),
        )

    input_generator = InputGenerator(
        project_id,
        mc,
        storage_mock,
    )
    dd = input_generator.create_property_value_input(
        filter, allow_reindex=allow_reindex
    )
    if allow_reindex:
        # do not update the params directly
        final_expected_values = expected_values + [
            DropdownOption(
                label="↻ re-index", value="_custom_lookup_option_", search="↻ re-index"
            )
        ]
    else:
        final_expected_values = expected_values
    assert dd.options == final_expected_values
    assert dd.selected == (
        [str(val) for val in filter.right]
        if isinstance(filter.right, list)
        else filter.right
    )
    assert dd.placeholder == ", ".join(sorted([str(filter.right), *stored_values]))

    assert dd.is_valid
    assert dd.is_visible
