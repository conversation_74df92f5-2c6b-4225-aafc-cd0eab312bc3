import copy
from dataclasses import replace
from typing import List, Optional
from unittest.mock import MagicMock

import pytest

import mitzu.model as M
import mitzu.webapp.model as WM
from mitzu.webapp.pages.explore.constants import (
    OPERATOR_MAPPING,
)
from mitzu.webapp.service.input_generator.generator import (
    InputGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    DropdownOption,
)
from tests.unit.webapp.service.input_generator.test_generator import (
    metric_context_with_revenue,
)


@pytest.mark.parametrize(
    "filter,property_type,expected_operators",
    [
        pytest.param(
            None,
            None,
            [],
            id="dropdown is hidden when filter is not set",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventField<PERSON>ath("table", "event_1", "field_1"),
                M.Operator.IN_COHORT,
                123,
            ),
            M.DataType.STRING,
            [M.Operator.IN_COHORT, M.Operator.NOT_IN_COHORT],
            id="cohort filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.Event<PERSON>ield<PERSON>("table", "event_1", "field_1"),
                M.Operator.ANY_OF,
                [True],
            ),
            M.DataType.BOOL,
            [
                M.Operator.ANY_OF,
                M.Operator.NONE_OF,
                M.Operator.IS_NOT_NULL,
                M.Operator.IS_NULL,
            ],
            id="bool filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_1", "field_1"),
                M.Operator.ANY_OF,
                [1],
            ),
            M.DataType.NUMBER,
            [
                M.Operator.ANY_OF,
                M.Operator.NONE_OF,
                M.Operator.GT,
                M.Operator.GT_EQ,
                M.Operator.LT,
                M.Operator.LT_EQ,
                M.Operator.IS_NOT_NULL,
                M.Operator.IS_NULL,
            ],
            id="number filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_1", "field_1"),
                M.Operator.ANY_OF,
                ["1"],
            ),
            M.DataType.STRING,
            [
                M.Operator.ANY_OF,
                M.Operator.NONE_OF,
                M.Operator.GT,
                M.Operator.GT_EQ,
                M.Operator.LT,
                M.Operator.LT_EQ,
                M.Operator.IS_NOT_NULL,
                M.Operator.IS_NULL,
                M.Operator.LIKE,
                M.Operator.NOT_LIKE,
            ],
            id="string filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_1", "field_1"),
                M.Operator.ARRAY_CONTAINS,
                ["1"],
            ),
            M.DataType.ARRAY,
            [
                M.Operator.ARRAY_CONTAINS,
                M.Operator.ARRAY_NOT_CONTAINS,
                M.Operator.ARRAY_LIKE,
                M.Operator.ARRAY_NOT_LIKE,
                M.Operator.IS_NOT_NULL,
                M.Operator.IS_NULL,
            ],
            id="array filter",
        ),
        pytest.param(
            WM.WebappEventFilter(
                M.EventFieldPath("table", "event_2", "missing"),
                M.Operator.GT_EQ,
                123,
            ),
            M.DataType.STRING,
            [M.Operator.GT_EQ],
            id="missing field keeps the operator",
        ),
    ],
)
def test_create_property_operator_dropdown(
    filter: Optional[WM.WebappEventFilter],
    property_type: Optional[M.DataType],
    expected_operators: List[M.Operator],
):
    mc = copy.deepcopy(metric_context_with_revenue)
    if (
        filter
        and filter.left in mc._property_catalog.keys()
        and isinstance(filter.left, M.EventFieldPath)
    ):
        assert property_type is not None, "invalid test data"
        mc._property_catalog[filter.left] = replace(
            mc._property_catalog[filter.left], property_type=property_type
        )

    input_generator = InputGenerator("project_id", mc, MagicMock())
    dd = input_generator.create_property_operator_dropdown(
        filter=filter,
    )

    if filter is None:
        assert dd.is_valid
        assert not dd.is_visible
    else:
        assert dd.is_valid
        assert dd.is_visible
        assert dd.options == [
            DropdownOption(
                label=OPERATOR_MAPPING[op],
                value=op.value,
                search=OPERATOR_MAPPING[op],
            )
            for op in expected_operators
        ]
        assert dd.selected == filter.operator.value
