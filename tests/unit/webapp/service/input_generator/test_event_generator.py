from dataclasses import replace
from typing import Optional
from unittest.mock import MagicMock

import pytest

import mitzu.model as M
import mitzu.webapp.model as WM
import tests.model as TM
from mitzu.webapp.service.input_generator import (
    DropdownOption,
    DropdownOptionType,
    InputGenerator,
)
from tests.unit.webapp.service.input_generator.test_generator import (
    custom_event_path,
    event_dropdown_items,
    metric_context,
    project_id,
)


def test_get_event_dropdown_options():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    res = input_generator.get_event_dropdown_options(relevance_scores={})
    assert (
        res
        == [
            DropdownOption(
                label="Custom",
                value="__custom_event__.custom-event",
                search="Custom Event 1 or Event 2",
                is_valid=True,
                disabled=False,
                description="Event 1 or Event 2",
                icon=None,
                field_type=DropdownOptionType.EVENT,
                is_property=False,
                class_name=None,
                tooltip_info=None,
            )
        ]
        + event_dropdown_items
    )


def test_get_event_dropdown_options_with_scores(relevance_scores={}):
    mock_metric_context = replace(
        metric_context,
        _event_catalog={
            M.EventNamePath("table", "event_1"): WM.EventMeta(
                "table", "event_1", "event_1_id", "event 1", None
            ),
            M.EventNamePath("table", "event_2"): WM.EventMeta(
                "table", "event_2", "event_2_id", "event 2", None
            ),
            M.EventNamePath("table", "event_3"): WM.EventMeta(
                "table", "event_3", "event_3_id", "event 3", None
            ),
            M.EventNamePath("table", "event_4"): WM.EventMeta(
                "table", "event_4", "event_4_id", "event 4", None
            ),
            M.EventNamePath("table", "event_5"): WM.EventMeta(
                "table", "event_5", "event_5_id", "event 5", None
            ),
        },
    )
    event_relevance_scores_for_user = {
        "table.event_1": WM.UserEventData(count=5, project_id="proj_1", score=0.5),
        "table.event_2": WM.UserEventData(count=9, project_id="proj_1", score=0.9),
        "table.event_3": WM.UserEventData(count=3, project_id="proj_1", score=0.3),
        "table.event_4": WM.UserEventData(count=7, project_id="proj_1", score=0.7),
        "table.event_5": WM.UserEventData(count=1, project_id="proj_1", score=0.1),
    }

    input_generator = InputGenerator(project_id, mock_metric_context, MagicMock())

    options = input_generator.get_event_dropdown_options(
        relevance_scores=event_relevance_scores_for_user
    )

    expected_options = [
        DropdownOption(
            label="event 2",
            value="table.event_2",
            search="event 2",
            is_property=False,
            field_type=DropdownOptionType.EVENT,
            class_name="highlighted-dropdown-option highlighted-opacity-1",
            tooltip_info="Used 9 times recently",
        ),
        DropdownOption(
            label="event 4",
            value="table.event_4",
            search="event 4",
            is_property=False,
            field_type=DropdownOptionType.EVENT,
            class_name="highlighted-dropdown-option highlighted-opacity-1",
            tooltip_info="Used 7 times recently",
        ),
        DropdownOption(
            label="event 1",
            value="table.event_1",
            search="event 1",
            is_property=False,
            field_type=DropdownOptionType.EVENT,
            class_name="highlighted-dropdown-option highlighted-opacity-2",
            tooltip_info="Used 5 times recently",
        ),
        DropdownOption(
            label="event 3",
            value="table.event_3",
            search="event 3",
            is_property=False,
            field_type=DropdownOptionType.EVENT,
            class_name="highlighted-dropdown-option highlighted-opacity-2",
            tooltip_info="Used 3 times recently",
        ),
        DropdownOption(
            label="event 5",
            value="table.event_5",
            search="event 5",
            is_property=False,
            field_type=DropdownOptionType.EVENT,
            class_name="highlighted-dropdown-option highlighted-opacity-2",
            tooltip_info="Used once recently",
        ),
    ]

    assert (
        options == expected_options
    ), "Expected options to be sorted by relevance scores and criteria"


@pytest.mark.parametrize(
    "selected,placeholder,is_visible",
    [
        (None, "Select an event", True),
        (M.EventNamePath("table", "event_2"), "Whatever", False),
    ],
)
def test_input_generator_creates_event_dropdown_with_metric_context(
    selected: Optional[M.EventNamePath], placeholder: str, is_visible: bool
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected, placeholder, is_visible, M.USER_ENTITY, relevance_scores={}
    )

    assert dd.options == ([event_dropdown_items[0]] if selected else [])
    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == placeholder
    assert dd.is_visible == is_visible
    assert dd.is_valid is True


def test_input_generator_creates_event_dropdown_with_metric_context_and_error():
    selected = M.EventNamePath("missing", "missing")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected, "placeholder", True, M.USER_ENTITY, relevance_scores={}
    )

    assert dd.options == [
        DropdownOption(
            label="Missing",
            search="missing.missing",
            value="missing.missing",
            is_property=False,
        ),
    ]
    assert dd.selected == str(selected)
    assert not dd.is_valid


def test_input_generator_creates_event_dropdown_with_group_focus_error():
    selected = M.EventNamePath("table_without_groups", "no_groups")
    group_focus_metric_context = replace(
        metric_context,
        global_filters=[
            WM.WebappEventFilter(
                left=M.DimensionIDFieldPath(TM.GROUP_ENTITY),
                operator=M.Operator.EQ,
                right="xxx",
            )
        ],
    )
    input_generator = InputGenerator(
        project_id, group_focus_metric_context, MagicMock()
    )

    dd = input_generator.create_event_dropdown(
        selected, "placeholder", True, M.USER_ENTITY, relevance_scores={}
    )

    assert dd.options == [event_dropdown_items[2]]
    assert dd.selected == str(selected)
    assert not dd.is_valid


def test_input_generator_creates_event_dropdown_with_unique_field_group_error():
    selected = M.EventNamePath("table_without_groups", "no_groups")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected, "placeholder", True, TM.GROUP_ENTITY, relevance_scores={}
    )

    assert dd.options == [event_dropdown_items[2]]
    assert dd.selected == str(selected)
    assert not dd.is_valid


def test_input_generator_creates_event_dropdown_with_unique_field_missing_field_error():
    selected = M.EventNamePath("table", "event_2")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected,
        "placeholder",
        True,
        M.EventFieldPath("missing", "missing", "missing"),
        relevance_scores={},
    )

    assert dd.options == [event_dropdown_items[0]]
    assert dd.selected == str(selected)
    assert not dd.is_valid


def test_input_generator_creates_event_dropdown_with_unique_field():
    selected = M.EventNamePath("table", "event_2")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected,
        "placeholder",
        True,
        M.EventFieldPath("table", "event_1", "field_1"),
        relevance_scores={},
    )

    assert dd.options == [event_dropdown_items[0]]
    assert dd.selected == str(selected)
    assert dd.is_valid


@pytest.mark.parametrize(
    "relative_field_def,expected_to_be_valid",
    zip([M.USER_ENTITY, TM.GROUP_ENTITY], [True, False]),
)
def test_input_generator_creates_event_dropdown_with_unique_field_relative_field_defs(
    relative_field_def: M.Entity, expected_to_be_valid: bool
):
    selected = M.EventNamePath("table_without_groups", "no_groups")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        selected, "placeholder", True, relative_field_def, relevance_scores={}
    )

    assert dd.options == [event_dropdown_items[2]]
    assert dd.selected == str(selected)
    assert dd.is_valid == expected_to_be_valid


@pytest.mark.parametrize(
    "relative_field_def,expected_to_be_valid",
    zip([M.USER_ENTITY, TM.GROUP_ENTITY], [True, True]),
)
def test_input_generator_creates_event_dropdown_with_unique_field_relative_field_defs_with_custom_event(
    relative_field_def: M.Entity, expected_to_be_valid: bool
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        custom_event_path, "placeholder", True, relative_field_def, relevance_scores={}
    )

    assert [opt.value for opt in dd.options] == [str(custom_event_path)]
    assert dd.selected == str(custom_event_path)
    assert dd.is_valid == expected_to_be_valid


@pytest.mark.parametrize(
    "field_path,expected_to_be_valid",
    [pytest.param(M.EventFieldPath("table", "event_1", "field_1"), True)],
)
def test_input_generator_creates_event_dropdown_with_unique_field_def_and_custom_event(
    field_path: M.EventFieldPath, expected_to_be_valid: bool
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())

    dd = input_generator.create_event_dropdown(
        custom_event_path, "placeholder", True, field_path, relevance_scores={}
    )

    assert [opt.value for opt in dd.options] == [str(custom_event_path)]
    assert dd.selected == str(custom_event_path)
    assert dd.is_valid == expected_to_be_valid
