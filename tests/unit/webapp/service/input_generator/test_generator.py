from dataclasses import replace
from datetime import datetime
from typing import Dict, List, Optional, Union
from unittest.mock import MagicMock, patch

import pytest

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import tests.model as TM
from mitzu.webapp.service.input_generator import (
    DropdownOption,
    DropdownOptionIcon,
    DropdownOptionIconType,
    DropdownOptionType,
    InputGenerator,
)

edt = M.EventDataTable.create(
    "table_1",
    id="edt_table",
    schema="public",
    event_time_field=M.Field("event_time", M.DataType.DATETIME),
    user_id_field=M.Field("user_id", M.DataType.STRING),
    foreign_keys={},
)

edt_without_groups = M.EventDataTable.create(
    "table_without_groups",
    id="edt_table_without_groups",
    schema="public",
    event_time_field=M.Field("event_time", M.DataType.DATETIME),
    user_id_field=M.Field("user_id", M.DataType.STRING),
    foreign_keys={},
)

ddt_user = M.DimensionDataTable.create(
    "dim_user_table",
    M<PERSON>Field("user_id", M.DataType.STRING),
    schema="public",
    id="dim_user_table",
    entity=M.USER_ENTITY,
    entity_id="project_id-user",
)

ddt_group = M.DimensionDataTable.create(
    "dim_group_table",
    M.Field("team_id", M.DataType.STRING),
    schema="public",
    id="dim_group_table",
    entity=TM.GROUP_ENTITY,
    entity_id="project_id-user",
)

dimension_fields = [
    ("dim_user_table", "dim_field_1"),
    ("dim_group_table", "dim_field_2"),
    ("dim_user_table", "dim_field_3"),
]

project_id = "project_id"

discovered_project = M.DiscoveredProject(
    event_definitions={
        M.EventNamePath(
            "table_without_groups", "no_groups"
        ): M.Reference.create_from_value(
            M.EventDef(
                "no_groups",
                fields=[
                    M.EventFieldDef(
                        M.Field("field_1", M.DataType.STRING),
                        discovered_values=set(),
                        custom_values=set(),
                        last_discovered=None,
                        event_name_path=M.EventNamePath(
                            "table", "table_without_groups"
                        ),
                        _id="table_without_groups_field_1",
                    ),
                ],
                event_data_table=edt_without_groups,
                last_discovered=None,
                _id="table_without_groups_id",
            )
        ),
        **{
            M.EventNamePath("table", event_name): M.Reference.create_from_value(
                M.EventDef(
                    event_name,
                    fields=[
                        M.EventFieldDef(
                            M.Field("field_1", M.DataType.STRING),
                            discovered_values=set(),
                            custom_values=set(),
                            last_discovered=None,
                            event_name_path=M.EventNamePath("table", event_name),
                            _id=f"{event_name}_field_1",
                        ),
                        M.EventFieldDef(
                            M.Field(
                                "a_field_2", M.DataType.NUMBER
                            ),  # prefixed with a_ to check order of the options
                            discovered_values=set(),
                            custom_values=set(),
                            last_discovered=None,
                            event_name_path=M.EventNamePath("table", event_name),
                            _id=f"{event_name}_field_2",
                        ),
                    ]
                    + (
                        [
                            M.EventFieldDef(
                                M.Field(
                                    "a_field_3", M.DataType.BOOL
                                ),  # prefixed with a_ to check order of the options
                                discovered_values=set(),
                                custom_values=set(),
                                last_discovered=None,
                                event_name_path=M.EventNamePath("table", event_name),
                                _id=f"{event_name}_field_3",
                            ),
                        ]
                        if event_name == "event_2"
                        else []
                    ),
                    event_data_table=edt,
                    last_discovered=None,
                    _id=f"{event_name}_id",
                )
            )
            for event_name in ["event_1", "event_2"]
        },
    },
    dimension_definitions={
        M.DimensionFieldPath(table_name, field_name): M.DimensionFieldDef(
            M.Field(field_name, M.DataType.STRING),
            discovered_values=set(),
            custom_values=set(),
            last_discovered=None,
            dimension_data_table=ddt_group
            if table_name == "dim_group_table"
            else ddt_user,
            _id=f"{field_name}_id",
        )
        for table_name, field_name in dimension_fields
    },
    project=MagicMock(),
)
custom_event_path = M.CustomEventNamePath("custom-event")
custom_event = WM.CustomEvent(
    id="custom-event",
    display_name="Custom",
    description="Event 1 or Event 2",
    segment=WM.WebappComplexSegment.create(
        WM.WebappSimpleSegment.create(M.EventNamePath("table", "event_1"), None),
        M.BinaryOperator.OR,
        WM.WebappSimpleSegment.create(M.EventNamePath("table", "event_2"), None),
    ),
    owner=WM.MembershipID("owner-id"),
    created_at=datetime.now(),
    last_updated_at=datetime.now(),
)

metric_context = MC.MetricContext(
    _event_catalog={
        M.EventNamePath("table", "event_1"): WM.EventMeta(
            "table", "event_1", "event_1_id", "Event Catalog", "description"
        ),
        M.EventNamePath("table", "event_2"): WM.EventMeta(
            "table", "event_1", "event_1_id", "Event 2", None
        ),
        M.EventNamePath("table_without_groups", "no_groups"): WM.EventMeta(
            "table_without_groups",
            "no_groups",
            "table_without_groups_id",
            "No Groups",
            None,
        ),
        custom_event_path: WM.EventMeta(
            source_table=M.CUSTOM_EVENTS_TABLE,
            event_name=custom_event.id,
            event_id=custom_event.id,
            display_name=custom_event.display_name,
            description=custom_event.description,
            is_hidden=False,
        ),
    },
    _property_catalog={
        M.EventFieldPath("table", "event_1", "field_1"): MC.EventPropertyMeta(
            event_property_id="event_1_field_1",
            display_name="Field Catalog",
            description="description",
            property_type=M.DataType.STRING,
            last_discovered=None,
        )
    },
    _dimension_catalog={
        M.DimensionFieldPath(table_name, field_name): MC.DimensionPropertyMeta(
            dimension_property_id=f"{field_name}_id",
            display_name="Dimension catalog"
            if field_name == "dim_field_1"
            else MC.get_property_name_label(field_name),
            description="description" if field_name == "dim_field_1" else None,
            property_type=M.DataType.STRING,
            last_discovered=None,
            created_with_default_values=field_name != "dim_field_1",
            entity=M.USER_ENTITY
            if table_name != "dim_group_table"
            else TM.GROUP_ENTITY,
        )
        for table_name, field_name in dimension_fields
    },
    _entity_meta={
        M.USER_ENTITY: WM.EntityMeta(M.USER_ENTITY, "User", "bi:person"),
        TM.GROUP_ENTITY: WM.EntityMeta(TM.GROUP_ENTITY, "Group", "bi:people"),
    },
    _event_properties={
        M.EventNamePath("table", event_name): [
            M.EventFieldPath("table", event_name, "field_1"),
            M.EventFieldPath("table", event_name, "a_field_2"),
        ]
        + (
            [
                M.EventFieldPath("table", event_name, "a_field_3"),
            ]
            if event_name == "event_2"
            else []
        )
        for event_name in ["event_1", "event_2"]
    },
    _table_catalog={
        "table": WM.EventTableMeta(entities={M.USER_ENTITY, TM.GROUP_ENTITY}),
        "table_without_groups": WM.EventTableMeta(entities={M.USER_ENTITY}),
    },
    global_filters=[],
    revenue_settings=None,
    _custom_events={custom_event.id: custom_event},
)

revenue_event = M.EventNamePath("table", "revenue")
metric_context_with_revenue = replace(
    metric_context,
    _event_catalog={
        revenue_event: WM.EventMeta(
            "table", "revenue", "rev_id", "Revenue", "description"
        ),
        **metric_context._event_catalog,
    },
    _property_catalog={
        M.EventFieldPath("table", "revenue", "amount"): MC.EventPropertyMeta(
            event_property_id="revenue_amount",
            display_name="Amount Field",
            description=None,
            property_type=M.DataType.NUMBER,
            last_discovered=None,
        ),
        M.EventFieldPath("table", "revenue", "period_start"): MC.EventPropertyMeta(
            event_property_id="revenue_period_start",
            display_name="Period Start Field",
            description=None,
            property_type=M.DataType.DATETIME,
            last_discovered=None,
        ),
        M.EventFieldPath("table", "revenue", "period_end"): MC.EventPropertyMeta(
            event_property_id="revenue_period_end",
            display_name="Period End Field",
            description=None,
            property_type=M.DataType.DATETIME,
            last_discovered=None,
        ),
        M.EventFieldPath("table", "revenue", "user_id"): MC.EventPropertyMeta(
            event_property_id="revenue_user_id",
            display_name="User Id Field",
            description=None,
            property_type=M.DataType.STRING,
            last_discovered=None,
        ),
        **metric_context._property_catalog,
    },
    _event_properties={
        revenue_event: [
            M.EventFieldPath("table", "revenue", field)
            for field in ["amount", "period_start", "period_end", "user_id"]
        ],
        **metric_context._event_properties,
    },
    revenue_settings=M.RevenueSettings(
        project_id=project_id,
        revenue_event=revenue_event,
        amount_field=M.EventFieldPath("table_1", "event_1", "amount"),
        period_start_field=M.EventFieldPath("table_1", "event_1", "period_start"),
        period_end_field=M.EventFieldPath("table_1", "event_1", "period_end"),
        currency="$",
        multiplier=1,
    ),
)


revenue_fields = [
    DropdownOption(
        label="Amount Field",
        value="table.revenue.amount",
        search="Amount Field",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
        field_type=DropdownOptionType.EVENT_PROPERTY,
        is_property=True,
    ),
    DropdownOption(
        label="Period End Field",
        value="table.revenue.period_end",
        search="Period End Field",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
        field_type=DropdownOptionType.EVENT_PROPERTY,
        is_property=True,
    ),
    DropdownOption(
        label="Period Start Field",
        value="table.revenue.period_start",
        search="Period Start Field",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
        field_type=DropdownOptionType.EVENT_PROPERTY,
        is_property=True,
    ),
    DropdownOption(
        label="User Id Field",
        value="table.revenue.user_id",
        search="User Id Field",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
        field_type=DropdownOptionType.EVENT_PROPERTY,
        is_property=True,
    ),
]


event_dropdown_items: List[DropdownOption] = [
    DropdownOption(
        label="Event 2",
        value="table.event_2",
        search="Event 2",
        is_property=False,
        field_type=DropdownOptionType.EVENT,
        class_name=None,
    ),
    DropdownOption(
        label="Event Catalog",
        value="table.event_1",
        search="Event Catalog description",
        description="description",
        is_property=False,
        field_type=DropdownOptionType.EVENT,
        class_name=None,
    ),
    DropdownOption(
        label="No Groups",
        value="table_without_groups.no_groups",
        search="No Groups",
        description=None,
        is_property=False,
        field_type=DropdownOptionType.EVENT,
        class_name=None,
    ),
]

cohort_in_property_list = DropdownOption(
    label="Cohort",
    search="Cohort",
    value="collection:user",
    icon=DropdownOptionIcon(DropdownOptionIconType.COHORT),
    class_name=None,
)
group_collection_in_property_list = DropdownOption(
    label="Group collection",
    search="Group collection",
    value="collection:group",
    icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:people"),
    class_name=None,
)

analyze_by_default_fields = [
    DropdownOption(
        label="User",
        value="user",
        search="user User",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:person"),
        field_type=None,
        is_property=True,
        class_name=None,
    ),
    DropdownOption(
        label="Group",
        value="group",
        search="group Group",
        is_valid=True,
        disabled=False,
        description=None,
        icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:people"),
        field_type=None,
        is_property=True,
        class_name=None,
    ),
]

analyze_by_start_typing = [
    DropdownOption(
        label="Start typing for more...",
        value="",
        search="",
        is_valid=True,
        disabled=True,
        description=None,
        icon=None,
        field_type=None,
        is_property=True,
        class_name=None,
    ),
]


dimension_fields_in_properties = [
    DropdownOption(
        label="Dim Field 3",
        search="Dim Field 3",
        value="dimension:dim_user_table.dim_field_3",
        field_type=DropdownOptionType.DIMENSION_PROPERTY,
        icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:person"),
    ),
    DropdownOption(
        label="Dimension catalog",
        search="Dimension catalog description",
        value="dimension:dim_user_table.dim_field_1",
        description="description",
        field_type=DropdownOptionType.DIMENSION_PROPERTY,
        icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:person"),
    ),
    DropdownOption(
        label="Dim Field 2",
        search="Dim Field 2",
        value="dimension:dim_group_table.dim_field_2",
        field_type=DropdownOptionType.DIMENSION_PROPERTY,
        icon=DropdownOptionIcon(DropdownOptionIconType.ENTITY, "bi:people"),
    ),
]

event_1_properties = [
    DropdownOption(
        label="A Field 2",
        value="table.event_1.a_field_2",
        search="A Field 2",
        field_type=DropdownOptionType.EVENT_PROPERTY,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
    ),
    DropdownOption(
        label="Field Catalog",
        value="table.event_1.field_1",
        search="Field Catalog description",
        description="description",
        field_type=DropdownOptionType.EVENT_PROPERTY,
        icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
    ),
]


event_2_field_3 = DropdownOption(
    label="A Field 3",
    value="table.event_2.a_field_3",
    search="A Field 3",
    field_type=DropdownOptionType.EVENT_PROPERTY,
    icon=DropdownOptionIcon(DropdownOptionIconType.EVENT_PROPERTY),
)

no_groups_field_1 = DropdownOption(
    label="Field 1",
    value="table_no_groups.no_groups.field_1",
    search="table_no_groups.no_groups.field_1",
    field_type=None,
    icon=None,
)


def search_for_more(
    shown: int, total: int, search_term: Optional[str]
) -> DropdownOption:
    return DropdownOption(
        disabled=True,
        label=f"Showing {shown} out of {total} properties. Type for more",
        value=search_term or "",
        search=search_term or "",
    )


@pytest.mark.parametrize(
    "selected,placeholder",
    [
        (None, "Select an event"),
        (M.EventFieldPath("table", "event_1", "field_1"), "Whatever"),
    ],
)
@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    5,
)
def test_input_generator_create_property_dropdown_for_segment_filter_for_user_profiles(
    selected: Optional[M.FieldPath], placeholder: str
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_segment_filter(
        event_name_path, placeholder, selected, False, False
    )
    assert dd.options == event_1_properties

    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == placeholder
    assert dd.is_valid
    assert dd.is_visible


@pytest.mark.parametrize(
    "selected,placeholder",
    [
        (None, "Select an event"),
        (M.EventFieldPath("table", "event_1", "field_1"), "Whatever"),
        (M.DimensionFieldPath("dim_user_table", "dim_field_1"), "Whatever"),
        (TM.COHORT_FIELD_PATH, "Whatever"),
    ],
)
@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    5,
)
def test_input_generator_create_property_dropdown_for_segment_filter_with_metric_context(
    selected: Optional[M.FieldPath], placeholder: str
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_segment_filter(
        event_name_path, placeholder, selected, True, True
    )
    assert dd.options == [
        search_for_more(5, 7, None),
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[1],
        dimension_fields_in_properties[1],
        dimension_fields_in_properties[2],
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == placeholder
    assert dd.is_valid
    assert dd.is_visible


def test_input_generator_create_property_dropdown_for_segment_filter_with_metric_context_and_error_on_missing_event():
    selected = M.EventFieldPath("missing", "event_1", "field_1")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("missing", "event_1")
    dd = input_generator.create_property_dropdown_for_segment_filter(
        event_name_path, "placeholder", selected, True, True
    )
    assert dd.options == [
        DropdownOption(
            label="Field 1",
            search="missing.event_1.field_1",
            value="missing.event_1.field_1",
        ),
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert not dd.is_valid
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    6,
)
def test_input_generator_create_property_dropdown_for_segment_filter_with_metric_context_and_error_on_missing_property():
    selected = M.EventFieldPath("table", "event_1", "missing")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    dd = input_generator.create_property_dropdown_for_segment_filter(
        event_name_path=event_name_path,
        placeholder="placeholder",
        selected=selected,
        with_cohort=True,
        with_dimensions=True,
    )
    assert dd.options == [
        search_for_more(6, 7, None),
        group_collection_in_property_list,
        cohort_in_property_list,
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label="Missing",
            search="table.event_1.missing",
            value="table.event_1.missing",
            is_valid=False,
        ),
    ]

    assert dd.selected == str(selected)
    assert dd.is_valid
    assert dd.is_visible


@pytest.mark.parametrize(
    "selected,placeholder",
    [
        (None, "Select an event"),
        (M.DimensionFieldPath("dim_user_table", "dim_field_1"), "Whatever"),
    ],
)
@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    1,
)
def test_input_generator_create_property_dropdown_for_dimension_search_filter_with_metric_context(
    selected: Optional[M.FieldPath], placeholder: str
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_dimension_search_filter(
        M.USER_ENTITY,
        placeholder,
        selected,
        None,
    )
    assert dd.options == [
        search_for_more(1, 2, None),
        dimension_fields_in_properties[1],
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == placeholder
    assert dd.is_valid
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    1,
)
def test_input_generator_create_property_dropdown_for_dimension_search_filter_with_search_filter():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    selected = M.DimensionFieldPath("dim_user_table", "dim_field_1")
    dd = input_generator.create_property_dropdown_for_dimension_search_filter(
        M.USER_ENTITY,
        "placeholder",
        selected,
        "field",
    )
    assert dd.options == [
        search_for_more(1, 2, "field"),
        dimension_fields_in_properties[1],
    ]

    assert dd.selected == (str(selected) if selected else None)
    assert dd.placeholder == "placeholder"
    assert dd.is_valid
    assert dd.is_visible


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    1,
)
def test_input_generator_create_property_dropdown_for_dimension_search_filter_with_metric_context_and_error_on_missing_property():
    selected = M.DimensionFieldPath("dim", "missing")
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_dimension_search_filter(
        entity=M.USER_ENTITY,
        placeholder="placeholder",
        selected=selected,
        search=None,
    )
    assert dd.options == [
        search_for_more(1, 2, None),
        dimension_fields_in_properties[1],
        DropdownOption(
            label="Missing",
            search=str(selected),
            value=str(selected),
            is_valid=False,
        ),
    ]

    assert dd.selected == str(selected)
    assert dd.is_valid
    assert dd.is_visible


@pytest.mark.parametrize(
    "search,include,expected_result",
    [
        pytest.param(
            None,
            [],
            (
                [
                    search_for_more(5, 7, None),
                    group_collection_in_property_list,
                    cohort_in_property_list,
                    event_1_properties[1],
                    dimension_fields_in_properties[1],
                    dimension_fields_in_properties[2],
                ]
            ),
            id="without search and selected item",
        ),
        pytest.param("no result", [], [], id="search without selected item"),
        pytest.param(
            "cohort", [], [cohort_in_property_list], id="search keeps the cohort item"
        ),
        pytest.param(
            "field 2",
            [],
            [event_1_properties[0], dimension_fields_in_properties[2]],
            id="search without selected item and two hits",
        ),
        pytest.param(
            "dim field 2",
            [],
            [dimension_fields_in_properties[2]],
            id="search without selected item and a single hit",
        ),
        pytest.param(
            "fIElD 2",
            [],
            [event_1_properties[0], dimension_fields_in_properties[2]],
            id="case insensitive search without selected item and two hits",
        ),
        pytest.param(
            "descr",
            [],
            [event_1_properties[1], dimension_fields_in_properties[1]],
            id="search in description",
        ),
        pytest.param(
            "field 2",
            [M.EventFieldPath("table", "event_1", "field_1")],
            event_1_properties + [dimension_fields_in_properties[2]],
            id="search with a selected item and a hit",
        ),
        pytest.param(
            "no result",
            [TM.COHORT_FIELD_PATH],
            [cohort_in_property_list],
            id="no search result but cohort is selected",
        ),
    ],
)
@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    5,
)
def test_get_property_options_for_event_with_search(
    search: Optional[str], include: List[M.FieldPath], expected_result: List[Dict]
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    assert (
        input_generator.get_property_options_for_event(
            event_name_path, include, search, True, True
        )
        == expected_result
    )


def test_get_property_options_for_event_resolves_custom_events():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    assert (
        input_generator.get_property_options_for_event(
            custom_event_path,
            include=[],
            search=None,
            with_cohort=True,
            with_dimensions=True,
        )
        == [
            group_collection_in_property_list,
            cohort_in_property_list,
        ]
        + event_1_properties[0:-1]
        + [event_2_field_3, event_1_properties[-1]]
        + dimension_fields_in_properties
    )


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    2,
)
@pytest.mark.parametrize(
    "selected,search,expected_list_item",
    [
        pytest.param(
            M.EventFieldPath("table", "event_1", "field_1"),
            "field",
            [
                search_for_more(2, 4, "field"),
                event_1_properties[1],
                dimension_fields_in_properties[0],
            ],
            id="event property selected with a lot of results",
        ),
        pytest.param(
            M.DimensionFieldPath("dim_group_table", "dim_field_2"),
            "field",
            [
                search_for_more(2, 4, "field"),
                event_1_properties[1],
                dimension_fields_in_properties[0],
            ],
            id="dimension property selected with a lot of results",
        ),
    ],
)
def test_get_event_property_dropdown_options_limit_keeps_the_included_item(
    selected: M.FieldPath,
    search: str,
    expected_list_item: List[Dict],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    assert (
        input_generator.get_property_options_for_event(
            event_name_path, [selected], search, True, True
        )
        == expected_list_item
    )


def test_get_event_property_dropdown_options_revenue_table():
    input_generator = InputGenerator(
        project_id, metric_context_with_revenue, MagicMock()
    )
    assert (
        input_generator.get_property_options_for_event(
            revenue_event, [], None, True, True
        )
        == [
            group_collection_in_property_list,
            cohort_in_property_list,
        ]
        + revenue_fields
        + dimension_fields_in_properties[0:3]
    )


@pytest.mark.parametrize(
    "selected",
    [
        pytest.param([], id="no selected value"),
        pytest.param(
            [M.EventFieldPath("table", "event_1", "field_1")],
            id="common field selected",
        ),
        pytest.param(
            [M.DimensionFieldPath("dim_user_table", "dim_field_1")],
            id="dimension field selected",
        ),
        pytest.param(
            [M.EventFieldPath("table", "event_2", "a_field_3")],
            id="event specific field selected",
        ),
        pytest.param(
            [
                M.EventFieldPath("table", "event_1", "field_1"),
                M.EventFieldPath("table", "event_2", "a_field_3"),
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
            ],
            id="multiple fields selected",
        ),
    ],
)
def test_create_property_dropdown_for_segment_break_down_renders_properly_with_catalog(
    selected: List[M.FieldPath],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        },
        selected=selected,
        with_subsegment_breakdown=False,
    )
    assert (
        dd.options
        == [
            event_1_properties[0],
            event_2_field_3,
            event_1_properties[1],
        ]
        + dimension_fields_in_properties
    )

    assert dd.selected == [str(field_path) for field_path in selected]
    assert dd.placeholder == "+ Break downs"
    assert dd.is_valid is True


def test_create_property_dropdown_for_segment_break_down_updates_selected_values_on_event_changes():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_2"),
        },
        selected=[
            M.EventFieldPath(
                "table", "event_1", "field_1"
            ),  # this could be set when event_1 was in the segment
            M.EventFieldPath("table", "event_2", "a_field_3"),
        ],
        with_subsegment_breakdown=False,
    )
    assert dd.selected == ["table.event_2.field_1", "table.event_2.a_field_3"]
    assert dd.is_valid is True


def test_create_property_dropdown_for_segment_break_down_is_invalid_on_selected_group_id_without_group_table():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table_without_groups", "no_groups"),
        },
        selected=[
            M.EventFieldPath(
                "table_without_groups", "no_groups", "field_1"
            ),  # this could be set when event_1 was in the segment
            M.DimensionFieldPath("dim_group_table", "dim_field_2"),
        ],
        with_subsegment_breakdown=False,
    )
    assert dd.selected == [
        "table_without_groups.no_groups.field_1",
        "dimension:dim_group_table.dim_field_2",
    ]
    assert dd.options == dimension_fields_in_properties + [
        DropdownOption(
            label="Field 1",
            value="table_without_groups.no_groups.field_1",
            search="table_without_groups.no_groups.field_1",
            is_valid=False,
            disabled=False,
            description=None,
            icon=None,
            field_type=None,
            is_property=True,
        ),
    ]
    assert dd.is_valid is False


@pytest.mark.parametrize(
    "selected",
    [
        pytest.param(
            [M.EventFieldPath("table", "event_1", "missing")],
            id="invalid event field path",
        ),
        pytest.param(
            [M.EventFieldPath("table", "event_1", "missing2")],
            id="multiple invalid event field path",
        ),
        pytest.param(
            [M.DimensionFieldPath("missing", "missing")],
            id="invalid dimension field path",
        ),
        pytest.param(
            [
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
                M.DimensionFieldPath("missing", "missing"),
            ],
            id="valid and invalid fields mixed',",
        ),
    ],
)
def test_create_property_dropdown_for_segment_break_down_renders_properly_with_catalog_and_error(
    selected: List[M.FieldPath],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        },
        selected=selected,
        with_subsegment_breakdown=True,
    )

    invalid_fields: List[M.FieldPath] = []
    for field in selected:
        if (
            isinstance(field, M.EventFieldPath)
            and field not in discovered_project.event_definitions.keys()
        ):
            invalid_fields.append(field)  # type: ignore[arg-type]
        elif (
            isinstance(field, M.DimensionFieldPath)
            and field not in discovered_project.dimension_definitions.keys()
        ):
            invalid_fields.append(field)  # type: ignore[arg-type]

    assert dd.options == [
        DropdownOption(
            label="Subsegment",
            value="___subsegment___",
            search="subsegment",
            is_valid=True,
            disabled=False,
            description=None,
            icon=DropdownOptionIcon(DropdownOptionIconType.SUBSEGMENT_BREAK_DOWN),
            field_type=None,
            is_property=True,
        ),
        event_1_properties[0],
        event_2_field_3,
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label=field_path.field_name.capitalize(),
            search=str(field_path),
            value=str(field_path),
            is_valid=False,
        )
        for field_path in invalid_fields
        if field_path not in discovered_project.event_definitions.keys()
    ]

    assert dd.selected == [str(field_path) for field_path in selected]
    assert dd.placeholder == "+ Break downs"
    assert dd.is_valid is False


@pytest.mark.parametrize(
    "selected",
    [
        pytest.param([], id="no selected value"),
        pytest.param(
            [M.EventFieldPath("table", "event_1", "field_1")],
            id="common field selected",
        ),
        pytest.param(
            [M.DimensionFieldPath("dim_user_table", "dim_field_1")],
            id="dimension field selected",
        ),
        pytest.param(
            [M.EventFieldPath("table", "event_2", "a_field_3")],
            id="event specific field selected",
        ),
        pytest.param(
            [
                M.EventFieldPath("table", "event_1", "field_1"),
                M.EventFieldPath("table", "event_2", "a_field_3"),
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
            ],
            id="multiple fields selected",
        ),
    ],
)
def test_get_property_options_for_segment_break_down_renders_properly_with_catalog(
    selected: List[M.FieldPath],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options, values = input_generator.get_property_options_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        },
        include=selected,
        search=None,
        with_subsegment_breakdown=False,
    )
    assert (
        options
        == [
            event_1_properties[0],
            event_2_field_3,
            event_1_properties[1],
        ]
        + dimension_fields_in_properties
    )

    assert values == [str(field_path) for field_path in selected]


def test_get_property_options_for_segment_break_down_updates_selected_values_on_event_changes():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    _, values = input_generator.get_property_options_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_2"),
        },
        include=[
            M.EventFieldPath(
                "table", "event_1", "field_1"
            ),  # this could be set when event_1 was in the segment
            M.EventFieldPath("table", "event_2", "a_field_3"),
        ],
        search=None,
        with_subsegment_breakdown=False,
    )
    assert values == ["table.event_2.field_1", "table.event_2.a_field_3"]


@pytest.mark.parametrize(
    "selected",
    [
        pytest.param(
            [M.EventFieldPath("table", "event_1", "missing")],
            id="invalid event field path",
        ),
        pytest.param(
            [M.EventFieldPath("table", "event_1", "missing2")],
            id="multiple invalid event field path",
        ),
        pytest.param(
            [M.DimensionFieldPath("missing", "missing")],
            id="invalid dimension field path",
        ),
        pytest.param(
            [
                M.DimensionFieldPath("dim_user_table", "dim_field_1"),
                M.DimensionFieldPath("missing", "missing"),
            ],
            id="valid and invalid fields mixed',",
        ),
    ],
)
def test_get_property_options_for_segment_break_down_renders_properly_with_catalog_and_error(
    selected: List[M.FieldPath],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options, values = input_generator.get_property_options_for_segment_break_down(
        event_name_paths={
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        },
        include=selected,
        search=None,
        with_subsegment_breakdown=False,
    )

    invalid_fields: List[M.FieldPath] = []
    for field in selected:
        if (
            isinstance(field, M.EventFieldPath)
            and field not in discovered_project.event_definitions.keys()
        ):
            invalid_fields.append(field)  # type: ignore[arg-type]
        elif (
            isinstance(field, M.DimensionFieldPath)
            and field not in discovered_project.dimension_definitions.keys()
        ):
            invalid_fields.append(field)  # type: ignore[arg-type]

    assert options == [
        event_1_properties[0],
        event_2_field_3,
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label=field_path.field_name.capitalize(),
            search=str(field_path),
            value=str(field_path),
            is_valid=False,
        )
        for field_path in invalid_fields
        if field_path not in discovered_project.event_definitions.keys()
    ]

    assert values == [str(field_path) for field_path in selected]


def test_get_property_options_for_segment_break_down_renders_properly_with_custom_event():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options, values = input_generator.get_property_options_for_segment_break_down(
        event_name_paths={custom_event_path},
        include=[],
        search=None,
        with_subsegment_breakdown=False,
    )

    assert (
        options
        == [event_1_properties[0], event_2_field_3, event_1_properties[1]]
        + dimension_fields_in_properties
    )

    assert values == []


@patch(
    "mitzu.webapp.service.input_generator.base_generator.NUMBER_OF_PROPERTIES_TO_LIST",
    2,
)
@pytest.mark.parametrize(
    "selected,search,expected_list_item",
    [
        pytest.param(
            M.EventFieldPath("table", "event_1", "field_1"),
            "field",
            [
                search_for_more(2, 4, "field"),
                event_1_properties[1],
                dimension_fields_in_properties[0],
            ],
            id="event property selected with a lot of results",
        ),
        pytest.param(
            M.DimensionFieldPath("dim_group_table", "dim_field_2"),
            "field",
            [
                search_for_more(2, 4, "field"),
                event_1_properties[1],
                dimension_fields_in_properties[0],
            ],
            id="dimension property selected with a lot of results",
        ),
    ],
)
def test_get_property_options_for_segment_break_down_limit_keeps_the_included_item(
    selected: M.FieldPath,
    search: str,
    expected_list_item: List[DropdownOption],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    event_name_path = M.EventNamePath("table", "event_1")
    assert (
        input_generator.get_property_options_for_segment_break_down(
            {event_name_path},
            [selected],
            search,
            with_subsegment_breakdown=False,
        )[0]
        == expected_list_item
    )


@pytest.mark.parametrize(
    "include,search,expected",
    [
        pytest.param(
            [],
            None,
            [
                event_1_properties[0],
                event_2_field_3,
                event_1_properties[1],
            ]
            + dimension_fields_in_properties,
            id="no search no value",
        ),
        pytest.param(
            [M.EventFieldPath("table", "event_1", "field_1")],
            "field_2",
            [event_1_properties[1]],
            id="included fields are not filtered out",
        ),
        pytest.param(
            [],
            "field 2",
            [
                event_1_properties[0],
                dimension_fields_in_properties[2],
            ],
            id="properties are filtered",
        ),
    ],
)
def test_get_property_options_for_list_events_modal_renders_properly_with_catalog(
    include: List[M.FieldPath],
    search: Optional[str],
    expected: List[DropdownOption],
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options = input_generator.get_property_options_for_list_events_modal(
        event_name_paths=[
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        ],
        include=include,
        search=search,
    )
    assert options == expected


@pytest.mark.parametrize(
    "event_name_paths,selected,expected_options,visible",
    [
        pytest.param(
            [M.EventNamePath("table", "event_1")],
            [M.EventFieldPath("table", "event_1", "field_1")],
            event_1_properties,
            True,
            id="renders all event fields",
        ),
        pytest.param(
            [M.EventNamePath("table", "event_1")],
            [M.EventFieldPath("table", "event_1", "field_1")],
            [],
            False,
            id="renders empty dropdown when not visible",
        ),
        pytest.param(
            [
                M.EventNamePath("table", "event_1"),
                M.EventNamePath("table", "event_2"),
            ],
            [M.EventFieldPath("table", "event_1", "field_1")],
            event_1_properties,
            True,
            id="renders all common event fields",
        ),
    ],
)
def test_create_property_dropdown_for_custom_holding_constants_renders_properly_with_catalog(
    event_name_paths: List[M.EventNamePath],
    selected: List[M.EventFieldPath],
    expected_options: List,
    visible: bool,
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_custom_holding_constants(
        event_name_paths=event_name_paths,
        selected_field_paths=selected,
        visible=visible,
    )
    assert dd.options == expected_options
    assert dd.placeholder == "Select property"

    if visible:
        assert dd.is_valid
        assert dd.is_visible
        assert dd.selected == [str(field_path) for field_path in selected]
    else:
        assert dd.is_valid
        assert not dd.is_visible
        assert dd.selected == []


def test_create_property_dropdown_for_custom_holding_constants_updates_selected_values_on_event_changes():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_custom_holding_constants(
        event_name_paths=[
            M.EventNamePath("table", "event_2"),
        ],
        selected_field_paths=[
            M.EventFieldPath(
                "table", "event_1", "field_1"
            ),  # this could be set when event_1 was in the segment
        ],
        visible=True,
    )
    assert dd.selected == ["table.event_2.field_1"]


def test_create_property_dropdown_for_custom_holding_constants_works_with_missing_event():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_custom_holding_constants(
        event_name_paths=[
            M.EventNamePath("missing", "event_2"),
        ],
        selected_field_paths=[
            M.EventFieldPath(
                "table", "missing", "field_1"
            ),  # this could be set when event_1 was in the segment
        ],
        visible=True,
    )
    assert dd.options == [
        DropdownOption(
            label="Field 1",
            search="table.missing.field_1",
            value="table.missing.field_1",
        ),
    ]
    assert dd.selected == ["table.missing.field_1"]
    assert not dd.is_valid
    assert dd.is_visible


def test_create_property_dropdown_for_custom_holding_constants_resolves_custom_events():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_custom_holding_constants(
        event_name_paths=[
            custom_event_path,
        ],
        selected_field_paths=[],
        visible=True,
    )
    assert dd.options == event_1_properties


@pytest.mark.parametrize(
    "event_name_paths,selected,expected_options,visible",
    [
        pytest.param(
            [M.EventNamePath("table", "event_1")],
            M.EventFieldPath("table", "event_1", "field_1"),
            event_1_properties,
            True,
            id="renders all event fields",
        ),
        pytest.param(
            [],
            None,
            [],
            True,
            id="renders empty dropdown when no event selected",
        ),
        pytest.param(
            [
                M.EventNamePath("table", "event_1"),
                M.EventNamePath("table", "event_2"),
            ],
            M.EventFieldPath("table", "event_1", "field_1"),
            event_1_properties,
            True,
            id="renders all common event fields",
        ),
        pytest.param(
            [M.EventNamePath("table", "event_1")],
            M.USER_ENTITY,
            event_1_properties,
            True,
            id="relative field def is selected",
        ),
    ],
)
def test_create_property_dropdown_for_unique_field_selector(
    event_name_paths: List[M.EventNamePath],
    selected: Union[None, M.Entity, M.EventFieldPath],
    expected_options: List,
    visible: bool,
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_unique_field_selector(
        event_name_paths=event_name_paths,
        selected_field_path=selected,
    )
    assert (
        dd.options
        == analyze_by_default_fields + expected_options + analyze_by_start_typing
    )
    assert dd.placeholder == "Select property"

    if visible:
        assert dd.is_valid
        assert dd.is_visible
        if selected is None:
            assert dd.selected is None
        elif isinstance(selected, M.EventFieldPath):
            assert dd.selected == str(selected)
        elif isinstance(selected, M.Entity):
            assert dd.selected == selected.name
        else:
            raise ValueError(f"Unknown selected field type {selected}")
    else:
        assert dd.is_valid
        assert not dd.is_visible
        assert dd.selected is None


def test_create_property_dropdown_for_unique_field_selector_updates_selected_values_on_event_changes():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_unique_field_selector(
        event_name_paths=[
            M.EventNamePath("table", "event_2"),
        ],
        selected_field_path=M.EventFieldPath(
            "table", "event_1", "field_1"
        ),  # this could be set when event_1 was in the segment,
    )
    assert dd.selected == "table.event_2.field_1"


def test_create_property_dropdown_for_unique_field_selector_works_with_missing_event():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_unique_field_selector(
        event_name_paths=[
            M.EventNamePath("missing", "event_2"),
        ],
        selected_field_path=M.EventFieldPath(
            "table", "missing", "field_1"
        ),  # this could be set when event_1 was in the segment,
    )
    assert (
        dd.options
        == analyze_by_default_fields
        + [
            DropdownOption(
                label="Field 1",
                search="table.missing.field_1",
                value="table.missing.field_1",
                is_valid=False,
            ),
        ]
        + analyze_by_start_typing
    )
    assert dd.selected == "table.missing.field_1"
    assert not dd.is_valid
    assert dd.is_visible


def test_get_property_options_for_unique_field_selector_without_search():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options = input_generator.get_property_options_for_unique_field_selector(
        event_name_paths=[
            M.EventNamePath("table", "event_1"),
        ],
        selected_field_path=M.USER_ENTITY,
        search=None,
    )

    assert (
        options
        == analyze_by_default_fields + event_1_properties + analyze_by_start_typing
    )


def test_get_property_options_for_unique_field_selector_searching_for_relative_field_def():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    options = input_generator.get_property_options_for_unique_field_selector(
        event_name_paths=[
            M.EventNamePath("table", "event_1"),
        ],
        selected_field_path=M.USER_ENTITY,
        search="user",
    )

    assert options == analyze_by_default_fields + [search_for_more(1, 1, "user")]


@pytest.mark.parametrize(
    "selected,visible",
    [
        pytest.param(None, True, id="no selected value"),
        pytest.param(
            M.EventFieldPath("table", "event_1", "field_1"),
            False,
            id="fields not listed for invisible input",
        ),
        pytest.param(
            M.EventFieldPath("table", "event_1", "field_1"),
            True,
            id="common field selected",
        ),
        pytest.param(
            M.DimensionFieldPath("dim_user_table", "dim_field_1"),
            True,
            id="dimension field selected",
        ),
        pytest.param(
            M.EventFieldPath("table", "event_2", "a_field_3"),
            True,
            id="event specific field selected",
        ),
    ],
)
def test_create_property_dropdown_for_property_aggregation_renders_properly_with_catalog(
    selected: Optional[M.FieldPath],
    visible: bool,
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[
            M.EventNamePath("table", "event_1"),
            M.EventNamePath("table", "event_2"),
        ],
        selected_field_path=selected,
        visible=visible,
    )
    if visible:
        assert (
            dd.options
            == [
                event_1_properties[0],
                event_2_field_3,
                event_1_properties[1],
            ]
            + dimension_fields_in_properties
        )
        assert dd.selected == (str(selected) if selected else None)
        assert dd.is_visible
    else:
        assert dd.options == []
        assert dd.selected is None
        assert not dd.is_visible

    assert dd.placeholder == "Select a property"
    assert dd.is_valid


def test_create_property_dropdown_for_property_aggregation_updates_selected_values_on_event_changes():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[
            M.EventNamePath("table", "event_2"),
        ],
        selected_field_path=M.EventFieldPath(
            "table", "event_1", "field_1"
        ),  # this could be set when event_1 was in the segment
        visible=True,
    )
    assert dd.selected == "table.event_2.field_1"


def test_create_property_dropdown_for_property_aggregation_is_invalid_on_selected_group_id_without_group_table():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[
            M.EventNamePath("table_no_groups", "no_groups"),
        ],
        selected_field_path=M.DimensionFieldPath("dim_group_table", "dim_field_2"),
        visible=True,
    )
    assert dd.selected == "dimension:dim_group_table.dim_field_2"
    assert dd.options == dimension_fields_in_properties
    assert dd.is_valid is False


def test_create_property_dropdown_for_property_aggregation_updates_selected_values_on_missing_event():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    selected = M.EventFieldPath("table", "missing", "field_1")
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[
            M.EventNamePath("table", "missing"),
        ],
        selected_field_path=selected,
        visible=True,
    )
    assert dd.selected == "table.missing.field_1"
    assert dd.options == dimension_fields_in_properties + [
        DropdownOption(
            label="Field 1",
            search="table.missing.field_1",
            value="table.missing.field_1",
        ),
    ]


@pytest.mark.parametrize(
    "selected",
    [
        pytest.param(
            M.EventFieldPath("table", "event_1", "missing"),
            id="missing event field path",
        ),
        pytest.param(
            M.DimensionFieldPath("user_profiles", "missing"),
            id="missing dimension field path",
        ),
    ],
)
def test_create_property_dropdown_for_property_aggregation_updates_selected_values_on_missing_property(
    selected: M.FieldPath,
):
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[
            M.EventNamePath("table", "event_1"),
        ],
        selected_field_path=selected,
        visible=True,
    )
    assert dd.selected == str(selected)
    assert dd.options == [
        event_1_properties[0],
        event_1_properties[1],
    ] + dimension_fields_in_properties + [
        DropdownOption(
            label="Missing",
            search=str(selected),
            value=str(selected),
        ),
    ]


def test_create_property_dropdown_for_property_aggregation_renders_properly_with_custom_event():
    input_generator = InputGenerator(project_id, metric_context, MagicMock())
    dd = input_generator.create_property_dropdown_for_property_aggregation(
        event_name_paths=[custom_event_path], selected_field_path=None, visible=True
    )

    assert (
        dd.options
        == [event_1_properties[0], event_2_field_3]
        + event_1_properties[1:]
        + dimension_fields_in_properties
    )
