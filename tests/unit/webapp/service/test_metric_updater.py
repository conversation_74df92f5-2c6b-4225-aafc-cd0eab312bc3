from dataclasses import replace
from datetime import datetime, timedelta
from typing import Any, Iterable, List
from unittest.mock import MagicMock

import pytest
from dateutil.tz import tzutc

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.constants import EMPTY_VALUE, N_PER_A
from mitzu.webapp.service.metric_updater import (
    CannotApplyDashboardFilter,
    CustomEventFiltersResultNoEvents,
    ExploreJourneyDirection,
    MetricUpdater,
)
from tests.model import COHORT_FIELD_PATH

event_path = M.EventNamePath("test", "event_1")
event_field_a_path = M.EventFieldPath("test", "event_1", "field_a")
event_field_b_path = M.EventFieldPath("test", "event_1", "field_b")
event_field_c_path = M.EventFieldPath("test", "event_1", "field_c")
event_common_field_path = M.EventFieldPath("test", "event_1", "common_field")

event_2_path = M.EventName<PERSON>ath("test", "event_2")
event_2_field_a_path = M.Event<PERSON>ield<PERSON>("test", "event_2", "field_a")
event_2_field_d_path = M.EventFieldPath("test", "event_2", "field_d")
event_2_common_field_path = M.EventFieldPath("test", "event_2", "common_field")

event_3_path = M.EventNamePath("test", "event_3")

dimension_field_a_path = M.DimensionFieldPath("test_dim", "field_a")

revenue_event_path = M.EventNamePath("test", "revenue")
amount_field_path = M.EventFieldPath("test", "revenue", "amount")
period_start_field_path = M.EventFieldPath("test", "revenue", "period_start")
period_end_field_path = M.EventFieldPath("test", "revenue", "period_end")

metric_context = MC.MetricContext(
    _event_catalog={},
    _property_catalog={},
    _dimension_catalog={},
    _event_properties={
        event_path: [
            event_field_a_path,
            event_field_b_path,
            event_field_c_path,
            event_common_field_path,
        ],
        event_2_path: [
            event_2_field_a_path,
            event_2_common_field_path,
            event_2_field_d_path,
        ],
        event_3_path: [],
    },
    _table_catalog={},
    _entity_meta={},
    global_filters=[],
    revenue_settings=M.RevenueSettings(
        project_id="project-id",
        revenue_event=revenue_event_path,
        amount_field=amount_field_path,
        period_start_field=period_start_field_path,
        period_end_field=period_end_field_path,
        currency="$",
        multiplier=1,
    ),
    _custom_events={},
)

drill_down_date = datetime(2024, 4, 1)
insight_settings = M.InsightSettings(
    project_id="project-id",
    end_date_config=M.WebappEndDateConfig.CUSTOM_DATE,
    custom_end_date=datetime(2024, 5, 1),
)


def flat_list(nested_list: Iterable[List[Any]]) -> List[Any]:
    result = []
    for sublist in nested_list:
        for item in sublist:
            result.append(item)
    return result


event_segment_without_filter: WM.WebappSegment = WM.WebappSimpleSegment.create(
    event_path, None
)
event_2_segment_without_filter: WM.WebappSegment = WM.WebappSimpleSegment.create(
    event_2_path, None
)

segment_with_field_b_filter: WM.WebappSegment = WM.WebappSimpleSegment.create(
    event_path,
    WM.WebappEventFilter(event_field_b_path, M.Operator.GT_EQ, 2),
)

segment_with_field_c_filter: WM.WebappSegment = WM.WebappSimpleSegment.create(
    event_path,
    WM.WebappEventFilter(event_field_c_path, M.Operator.LT, 2),
)

complex_segment_with_filters: WM.WebappSegment = WM.WebappComplexSegment.create(
    segment_with_field_b_filter,
    M.BinaryOperator.AND,
    segment_with_field_c_filter,
)


def add_default_agg(segment: WM.WebappSegment) -> WM.WebappSegment:
    if segment.aggregation is not None:
        return segment
    return replace(
        segment, aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
    )


@pytest.mark.parametrize(
    "orig_segment,filter,expected_segment",
    flat_list(
        [
            pytest.param(
                WM.WebappSimpleSegment.create(event_path, None),
                filter,
                WM.WebappSimpleSegment.create(
                    event_path,
                    filter,
                ),
                id=f"adds an {type} filter to an unfiltered simple segment",
            ),
            pytest.param(
                WM.WebappSimpleSegment.create(
                    event_path,
                    WM.WebappEventFilter(filter.left, M.Operator.GT, 12),
                ),
                filter,
                WM.WebappSimpleSegment.create(
                    event_path,
                    filter,
                ),
                id=f"updates the {type} filter of a simple segment if the field is the same",
            ),
            pytest.param(
                segment_with_field_b_filter,
                filter,
                WM.WebappComplexSegment.create(
                    segment_with_field_b_filter,
                    M.BinaryOperator.AND,
                    WM.WebappSimpleSegment.create(
                        event_path,
                        filter,
                    ),
                ),
                id=f"creates a complex segment with an {type} filter from an already filtered simple segment",
            ),
            pytest.param(
                complex_segment_with_filters,
                filter,
                WM.WebappComplexSegment.create(
                    complex_segment_with_filters,
                    M.BinaryOperator.AND,
                    WM.WebappSimpleSegment.create(
                        event_path,
                        filter,
                    ),
                ),
                id=f"extends a complex segment with an {type} filter if all segments belong to the same event",
            ),
        ]
        for type, filter in [
            (
                "event",
                WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, [1, 2]),
            ),
            (
                "dimension",
                WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1, 2]),
            ),
        ]
    )
    + [
        pytest.param(
            WM.WebappComplexSegment.create(
                event_2_segment_without_filter,
                M.BinaryOperator.OR,
                WM.WebappSimpleSegment.create(event_path, None),
            ),
            WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, [1, 2]),
            WM.WebappComplexSegment.create(
                event_2_segment_without_filter,
                M.BinaryOperator.OR,
                WM.WebappSimpleSegment.create(
                    event_path,
                    WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, [1, 2]),
                ),
            ),
            id="extends the proper part of a complex segment with an event filter",
        ),
        pytest.param(
            WM.WebappComplexSegment.create(
                event_2_segment_without_filter,
                M.BinaryOperator.OR,
                WM.WebappSimpleSegment.create(event_path, None),
            ),
            WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1, 2]),
            WM.WebappComplexSegment.create(
                WM.WebappSimpleSegment.create(
                    event_2_path,
                    WM.WebappEventFilter(
                        dimension_field_a_path, M.Operator.ANY_OF, [1, 2]
                    ),
                ),
                M.BinaryOperator.OR,
                WM.WebappSimpleSegment.create(
                    event_path,
                    WM.WebappEventFilter(
                        dimension_field_a_path, M.Operator.ANY_OF, [1, 2]
                    ),
                ),
            ),
            id="extends all part part of a complex segment with a dimension filter",
        ),
        pytest.param(
            WM.WebappSimpleSegment.create(event_2_path, None),
            WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, [1, 2]),
            WM.WebappSimpleSegment.create(event_2_path, None),
            id="does not apply filter with foreign event field path",
        ),
        pytest.param(
            event_segment_without_filter,
            WM.WebappEventFilter(event_2_common_field_path, M.Operator.ANY_OF, [1, 2]),
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(
                    event_common_field_path, M.Operator.ANY_OF, [1, 2]
                ),
            ),
            id="finds the proper field path based on the field name",
        ),
    ],
)
def test_add_or_update_segment_filter(
    orig_segment: WM.WebappSegment,
    filter: WM.WebappEventFilter,
    expected_segment: WM.WebappSegment,
):
    updater = MetricUpdater(metric_context, insight_settings)
    assert (
        updater.add_or_update_segment_filter(
            orig_segment, filter.left, filter.operator, filter.right
        )
        == expected_segment
    )


@pytest.mark.parametrize(
    "property_type,value,expected_value",
    [
        pytest.param(
            M.DataType.STRING,
            "abc",
            "abc",
            id="add_segment_filter fixes string data type",
        ),
        pytest.param(
            M.DataType.NUMBER,
            "123",
            123,
            id="add_segment_filter fixes number data type",
        ),
        pytest.param(
            M.DataType.BOOL,
            "false",
            False,
            id="add_segment_filter fixes boolean data type",
        ),
        pytest.param(
            M.DataType.DATETIME,
            "2024-01-02T12:23:45Z",
            datetime(2024, 1, 2, 12, 23, 45, tzinfo=tzutc()),
            id="add_segment_filter fixes datetime data type",
        ),
    ],
)
def test_add_or_update_segment_filter_fixes_the_value_type(
    property_type: M.DataType, value: str, expected_value: Any
):
    mc = replace(
        metric_context,
        _property_catalog={
            event_field_a_path: MC.EventPropertyMeta(
                "display name",
                property_type=property_type,
                last_discovered=None,
                description=None,
                event_property_id="id",
            )
        },
    )
    updater = MetricUpdater(mc, insight_settings)
    updated_segment = updater.add_or_update_segment_filter(
        event_segment_without_filter, event_field_a_path, M.Operator.ANY_OF, value
    )

    assert isinstance(updated_segment, WM.WebappSimpleSegment)
    assert updated_segment.event_filter is not None
    assert updated_segment.event_filter.right == [expected_value]


common_cases_for_segmentation_metrics = (
    [
        pytest.param(
            [
                event_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=0),
            [
                event_segment_without_filter,
            ],
            id="signle segment no breakdown",
        ),
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=1),
            [
                event_2_segment_without_filter,
            ],
            id="proper segment is selected",
        ),
    ]
    + flat_list(
        [
            [
                pytest.param(
                    [
                        replace(event_segment_without_filter, group_by=[field_path]),
                    ],
                    WM.TooltipCustomJson(segment_index=0, break_down=["val_1"]),
                    [
                        replace(
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    field_path, M.Operator.ANY_OF, ["val_1"]
                                ),
                            ),
                            group_by=[field_path],
                        )
                    ],
                    id=f"{type} filter is added to the unfiltered simple segment",
                ),
                pytest.param(
                    [
                        replace(event_segment_without_filter, group_by=[field_path]),
                    ],
                    WM.TooltipCustomJson(segment_index=0, break_down=["val_1"]),
                    [
                        replace(
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    field_path, M.Operator.ANY_OF, ["val_1"]
                                ),
                            ),
                            group_by=[field_path],
                        )
                    ],
                    id=f"{type} filter is added to the unfiltered simple segment",
                ),
                pytest.param(
                    [
                        replace(event_segment_without_filter, group_by=[field_path]),
                    ],
                    WM.TooltipCustomJson(segment_index=0, break_down=[N_PER_A]),
                    [
                        replace(
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    field_path, M.Operator.IS_NULL, None
                                ),
                            ),
                            group_by=[field_path],
                        )
                    ],
                    id=f"{type} filter is added with is_null operator when value is n/a",
                ),
                pytest.param(
                    [
                        replace(event_segment_without_filter, group_by=[field_path]),
                    ],
                    WM.TooltipCustomJson(segment_index=0, break_down=[EMPTY_VALUE]),
                    [
                        replace(
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    field_path, M.Operator.ANY_OF, [""]
                                ),
                            ),
                            group_by=[field_path],
                        )
                    ],
                    id=f"{type} filter is added with is_null operator when value is empty",
                ),
            ]
            for type, field_path in [
                ("event", event_field_a_path),
                ("dimension", dimension_field_a_path),
            ]
        ]
    )
    + [
        pytest.param(
            [
                replace(
                    WM.WebappComplexSegment.create(
                        event_segment_without_filter,
                        M.BinaryOperator.OR,
                        event_2_segment_without_filter,
                    ),
                    group_by=[event_common_field_path],
                )
            ],
            WM.TooltipCustomJson(segment_index=0, break_down=["abc"]),
            [
                replace(
                    WM.WebappComplexSegment.create(
                        WM.WebappSimpleSegment.create(
                            event_path,
                            WM.WebappEventFilter(
                                event_common_field_path, M.Operator.ANY_OF, ["abc"]
                            ),
                        ),
                        M.BinaryOperator.OR,
                        WM.WebappSimpleSegment.create(
                            event_2_path,
                            WM.WebappEventFilter(
                                event_2_common_field_path, M.Operator.ANY_OF, ["abc"]
                            ),
                        ),
                    ),
                    group_by=[event_common_field_path],
                ),
            ],
            id="event filters set properly with a common event field",
        ),
        pytest.param(
            [
                replace(
                    WM.WebappComplexSegment.create(
                        WM.WebappComplexSegment.create(
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    event_field_a_path, M.Operator.ANY_OF, [1, 2]
                                ),
                            ),
                            M.BinaryOperator.AND,
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    event_field_b_path, M.Operator.ANY_OF, [1, 2]
                                ),
                            ),
                        ),
                        M.BinaryOperator.OR,
                        event_2_segment_without_filter,
                    ),
                    group_by=[event_field_c_path, event_2_field_d_path],
                ),
            ],
            WM.TooltipCustomJson(segment_index=0, break_down=["a", "b"]),
            [
                replace(
                    WM.WebappComplexSegment.create(
                        WM.WebappComplexSegment.create(
                            WM.WebappComplexSegment.create(
                                WM.WebappSimpleSegment.create(
                                    event_path,
                                    WM.WebappEventFilter(
                                        event_field_a_path, M.Operator.ANY_OF, [1, 2]
                                    ),
                                ),
                                M.BinaryOperator.AND,
                                WM.WebappSimpleSegment.create(
                                    event_path,
                                    WM.WebappEventFilter(
                                        event_field_b_path, M.Operator.ANY_OF, [1, 2]
                                    ),
                                ),
                            ),
                            M.BinaryOperator.AND,
                            WM.WebappSimpleSegment.create(
                                event_path,
                                WM.WebappEventFilter(
                                    event_field_c_path, M.Operator.ANY_OF, ["a"]
                                ),
                            ),
                        ),
                        M.BinaryOperator.OR,
                        WM.WebappSimpleSegment.create(
                            event_2_path,
                            WM.WebappEventFilter(
                                event_2_field_d_path, M.Operator.ANY_OF, ["b"]
                            ),
                        ),
                    ),
                    group_by=[event_field_c_path, event_2_field_d_path],
                ),
            ],
            id="nested complex segments",
        ),
    ]
)


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    common_cases_for_segmentation_metrics,
)
def test_drill_down_segmentation_metric_with_monthly_group(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.MONTH,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=29),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.BAR,
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


def test_drill_down_segmentation_metric_with_formula():
    metric_config = WM.WebappMetricConfig.from_model(
        M.SegmentationMetricConfig(
            time_group=M.TimeGroup.MONTH,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            formula="a+b",
        )
    )
    segments = [
        replace(event_segment_without_filter, group_by=[event_common_field_path]),
        replace(event_2_segment_without_filter, group_by=[event_2_common_field_path]),
    ]

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(segment_index=0, break_down=["abc"])

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.SegmentationMetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=29),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.BAR,
                formula="a+b",
            )
        ),
        segments=segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


def test_drill_down_segmentation_with_subsegment_break_down():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.MONTH,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS)
    segments: List[WM.WebappSegment] = [
        WM.WebappComplexSegment(
            left=WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, [1]),
            ),
            operator=M.BinaryOperator.OR,
            right=WM.WebappSimpleSegment.create(
                event_2_path,
                WM.WebappEventFilter(event_2_field_d_path, M.Operator.ANY_OF, [1]),
            ),
            group_by=[
                event_field_b_path,
                event_common_field_path,
                M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
            ],
            aggregation=aggregation,
            filter=M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,
            title="title",
            expanded_custom_event_key=None,
        )
    ]

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(
        segment_index=0, break_down=["a", "b", "_subsegment_index_2_1"]
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=29),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.BAR,
            )
        ),
        segments=[
            WM.WebappComplexSegment(
                left=replace(
                    WM.WebappSimpleSegment.create(
                        event_2_path,
                        WM.WebappEventFilter(
                            event_2_field_d_path, M.Operator.ANY_OF, [1]
                        ),
                    ),
                    filter=M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,  # TODO: it's unused and harmless
                ),
                operator=M.BinaryOperator.AND,
                right=WM.WebappSimpleSegment.create(
                    event_2_path,
                    WM.WebappEventFilter(
                        event_2_common_field_path, M.Operator.ANY_OF, ["b"]
                    ),
                ),
                aggregation=aggregation,
                filter=M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,
                group_by=[event_2_common_field_path],
                title="title",
                expanded_custom_event_key=None,
            )
        ],
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "index,expected_event",
    [
        (0, event_path),
        (1, event_2_path),
        (2, event_3_path),
    ],
)
def test_drill_down_segmentation_with_subsegment_break_down_several_events(
    index: int,
    expected_event: M.EventNamePath,
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.MONTH,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    aggregation = WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS)
    segments: List[WM.WebappSegment] = [
        WM.WebappComplexSegment(
            left=WM.WebappComplexSegment.create(
                WM.WebappSimpleSegment.create(event_path, None),
                M.BinaryOperator.OR,
                WM.WebappSimpleSegment.create(event_2_path, None),
            ),
            operator=M.BinaryOperator.OR,
            right=WM.WebappSimpleSegment.create(event_3_path, None),
            group_by=[
                M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
            ],
            aggregation=aggregation,
            filter=M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,
            title="title",
            expanded_custom_event_key=None,
        )
    ]
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(
        segment_index=0, break_down=[f"_subsegment_index_0_{index}"]
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    assert output_metric.segments[0].get_event_name_paths() == {expected_event}


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    common_cases_for_segmentation_metrics,
)
def test_see_trends_segmentation_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.see_trends(input_metric, tooltip_json)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.DAY,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.LINE,
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


def test_see_trends_segmentation_metric_with_formula():
    metric_config = WM.WebappMetricConfig.from_model(
        M.SegmentationMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            formula="a+b",
        )
    )
    segments = [
        replace(event_segment_without_filter, group_by=[event_common_field_path]),
        replace(event_2_segment_without_filter, group_by=[event_2_common_field_path]),
    ]

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(segment_index=0, break_down=["abc"])

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.see_trends(input_metric, tooltip_json)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.SegmentationMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.DAY,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.LINE,
                formula="a+b",
            )
        ),
        segments=segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                replace(
                    event_segment_without_filter, group_by=[event_common_field_path]
                ),
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=0, break_down=["val_1"]),
            [
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_common_field_path, M.Operator.ANY_OF, ["val_1"]
                        ),
                    ),
                    group_by=[event_common_field_path],
                ),
                event_2_segment_without_filter,
            ],
            id="updates only the first segment",
        ),
        pytest.param(
            [
                event_2_segment_without_filter,
                replace(
                    event_segment_without_filter, group_by=[event_common_field_path]
                ),
                event_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=3, break_down=["val_1"]),
            [
                event_2_segment_without_filter,
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_common_field_path, M.Operator.ANY_OF, ["val_1"]
                        ),
                    ),
                    group_by=[event_common_field_path],
                ),
                event_segment_without_filter,
            ],
            id="updates the second segment",
        ),
    ],
)
def test_drill_down_conversion_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.WEEK,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=6),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                chart_type=M.SimpleChartType.BAR,
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=2),
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
                event_segment_without_filter,
            ],
            id="keeps up to the selected segment if timegroup is total",
        ),
        pytest.param(
            [
                event_segment_without_filter,
                replace(
                    event_2_segment_without_filter, group_by=[event_2_common_field_path]
                ),
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=2, break_down=["val_1"]),
            [
                event_segment_without_filter,
                replace(
                    WM.WebappSimpleSegment.create(
                        event_2_path,
                        WM.WebappEventFilter(
                            event_2_common_field_path, M.Operator.ANY_OF, ["val_1"]
                        ),
                    ),
                    group_by=[event_2_common_field_path],
                ),
                event_segment_without_filter,
            ],
            id="applies breakdown filters",
        ),
    ],
)
def test_see_trends_conversion_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.see_trends(input_metric, tooltip_json)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.DAY,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=M.SimpleChartType.LINE,
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=2, retention_index=1),
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="keeps up to the selected segment if timegroup is total",
        ),
    ],
)
def test_drill_down_retention_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.RetentionMetricConfig(
            time_group=M.TimeGroup.WEEK,
            aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.drill_down(
        input_metric,
        tooltip_json,
        drill_down_date,
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=WM.WebappMetricConfig.from_model(
            M.RetentionMetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=6),
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=M.SimpleChartType.BAR,
                retention_indices=[1],
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=1, retention_index=1),
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="keeps up to the selected segment if timegroup is total",
        ),
    ],
)
def test_see_trends_retention_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.RetentionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )
    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.see_trends(input_metric, tooltip_json)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=WM.WebappMetricConfig.from_model(
            M.RetentionMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.DAY,
                aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=M.SimpleChartType.LINE,
                retention_indices=[1],
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    common_cases_for_segmentation_metrics,
)
def test_create_cohort_segmentation_metric_with_monthly_group(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.MONTH,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[add_default_agg(segment) for segment in initial_segments],
        title=None,
        description=None,
    )

    output_metric = MetricUpdater(
        metric_context, insight_settings
    ).create_cohort_metric(input_metric, tooltip_json, drill_down_date)

    expected_segments = [
        segment.clear_top_level_configs().aggregate(
            WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
        )
        for segment in expected_segments
    ]

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=drill_down_date,
                end_dt=drill_down_date + timedelta(days=29),
                time_group=M.TimeGroup.TOTAL,
                chart_type=M.SimpleChartType.BAR,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


def test_create_cohort_segmentation_metric_with_formula():
    metric_config = WM.WebappMetricConfig.from_model(
        M.SegmentationMetricConfig(
            time_group=M.TimeGroup.MONTH,
            formula="a+b",
        )
    )
    segments = [
        replace(event_segment_without_filter, group_by=[event_common_field_path]),
        replace(event_2_segment_without_filter, group_by=[event_2_common_field_path]),
    ]

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(segment_index=0, break_down=["abc"])
    with pytest.raises(ValueError):
        MetricUpdater(metric_context, insight_settings).create_cohort_metric(
            input_metric, tooltip_json, drill_down_date
        )


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    common_cases_for_segmentation_metrics,
)
def test_create_cohort_segmentation_metric_with_total_group(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[add_default_agg(segment) for segment in initial_segments],
        title=None,
        description=None,
    )

    output_metric = MetricUpdater(
        metric_context, insight_settings
    ).create_cohort_metric(input_metric, tooltip_json, None)

    expected_segments = [
        segment.clear_top_level_configs().aggregate(
            WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS)
        )
        for segment in expected_segments
    ]

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.TOTAL,
                chart_type=None,
                aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=2),
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
                event_segment_without_filter,
            ],
            id="keeps up to the selected segment if timegroup is total",
        ),
    ],
)
def test_create_cohort_conversion_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    start_dt = datetime(2024, 1, 2)
    end_dt = datetime(2024, 2, 1)
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            start_dt=start_dt,
            end_dt=end_dt,
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.CONVERSION),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    output_metric = MetricUpdater(
        metric_context, insight_settings
    ).create_cohort_metric(input_metric, tooltip_json, None)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=start_dt,
                end_dt=end_dt,
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=None,
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,tooltip_json,expected_segments",
    [
        pytest.param(
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=1, retention_index=1),
            [
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="keeps up to the selected segment if timegroup is total",
        ),
    ],
)
def test_create_cohort_retention_metric(
    initial_segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.RetentionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    output_metric = MetricUpdater(
        metric_context, insight_settings
    ).create_cohort_metric(input_metric, tooltip_json, None)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=WM.WebappMetricConfig.from_model(
            M.RetentionMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=None,
                retention_indices=[1],
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


def test_create_cohort_retention_metric_complex_subsegment_break_down():
    metric_config = WM.WebappMetricConfig.from_model(
        M.RetentionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
            time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
        )
    )
    event_1 = M.EventNamePath("page_events", "page_visit")
    event_2 = M.EventNamePath("checkouts", "checkout")
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=[
            replace(
                WM.WebappComplexSegment.create(
                    WM.WebappSimpleSegment.create(event_1, None),
                    M.BinaryOperator.OR,
                    WM.WebappSimpleSegment.create(event_2, None),
                ),
                group_by=[
                    M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH,
                    M.EventFieldPath("page_events", "page_visit", "campaign"),
                ],
            ),
            WM.WebappSimpleSegment.create(event_2, None),
        ],
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(
        segment_index=-1,
        break_down=["_subsegment_index_0_1", "n/a"],
        event_label="Checkout (2), n/a",
        event_value="Week 1",
        title_label="Retention rate",
        title_value="35.29 %",
        subtitle=None,
        version=1,
        normalized_percentage=None,
        unique_field_label="Unique users",
        retention_index=1,
    )

    output_metric = MetricUpdater(
        metric_context, insight_settings
    ).create_cohort_metric(input_metric, tooltip_json, None)

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=WM.WebappMetricConfig.from_model(
            M.RetentionMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.TOTAL,
                aggregation=M.AggregationConfig(M.AggType.RETENTION_RATE),
                time_window=M.TimeWindow(1, M.TimeGroup.MONTH),
                chart_type=None,
                retention_indices=[1],
            )
        ),
        segments=[
            WM.WebappSimpleSegment.create(event_2, None),
            WM.WebappSimpleSegment.create(event_2, None),
        ],
        title=None,
        description=None,
    )
    assert output_metric == compare_metric


def test_create_transfer_metric_with_cohort_filter():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.MONTH,
        )
    )

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(
                    COHORT_FIELD_PATH,
                    M.Operator.IN_COHORT,
                    WM.CohortReference("cohort-id"),
                ),
            )
        ],
        title=None,
        description=None,
    )

    tooltip_json = WM.TooltipCustomJson(segment_index=0)
    assert (
        MetricUpdater(metric_context, insight_settings).create_cohort_metric(
            input_metric, tooltip_json, drill_down_date
        )
        is not None
    )


def test_update_aggregation_type_conversion():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            aggregation=M.AggregationConfig(
                type=M.AggType.CONVERSION,
                conv_param=0.5,
            ),
        )
    )

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(
                    COHORT_FIELD_PATH,
                    M.Operator.IN_COHORT,
                    WM.CohortReference("cohort-id"),
                ),
            )
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_users(
        input_metric, False
    )

    assert new_metric.config.aggregation.type in M.LIST_USER_AGGS
    assert new_metric.config.aggregation.conv_param is None


def test_update_aggregation_type_conversion_drop_off():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            aggregation=M.AggregationConfig(
                type=M.AggType.CONVERSION,
                conv_param=0.5,
            ),
        )
    )

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(
                    COHORT_FIELD_PATH,
                    M.Operator.IN_COHORT,
                    WM.CohortReference("cohort-id"),
                ),
            )
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_users(
        input_metric, is_drop_off=True
    )

    assert new_metric.config.aggregation.type == M.AggType.RETURN_DROP_OFF_USERS
    assert new_metric.config.aggregation.conv_param is None


def test_update_aggregation_type_segmentation():
    agg_config = WM.WebappAggregationConfig(
        type=M.AggType.BY_EVENT_PROPERTY,
        event_property_agg=WM.WebappEventPropertyAggregation(
            field_def=event_field_a_path,
            agg_type=M.EventPropertyAggregationType.COUNT_DISTINCT,
        ),
    )
    metric_config = WM.WebappMetricConfig.from_model(M.MetricConfig())

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None).aggregate(agg_config),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_users(
        input_metric, False
    )

    assert new_metric.config.aggregation.type in M.LIST_USER_AGGS
    assert new_metric.config.aggregation.conv_param is None

    segment = new_metric.segments[0]
    assert segment.aggregation is not None
    assert segment.aggregation.type in M.LIST_USER_AGGS
    assert segment.aggregation.event_property_agg is None


def test_update_aggregation_type_segmentation_for_revenue():
    agg_config = WM.WebappAggregationConfig(M.AggType.TOTAL_MRR)
    metric_config = WM.WebappMetricConfig.from_model(M.MetricConfig())

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(revenue_event_path, None).aggregate(
                agg_config
            ),
            WM.WebappSimpleSegment.create(revenue_event_path, None),
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_users(
        input_metric, False
    )

    assert new_metric.config.aggregation.type == M.AggType.RETURN_SUBSCRIBERS
    assert new_metric.config.aggregation.conv_param is None

    segment = new_metric.segments[0]
    assert segment.aggregation is not None
    assert segment.aggregation.type == M.AggType.RETURN_SUBSCRIBERS
    assert segment.aggregation.event_property_agg is None


def test_filter_on():
    metric_config = WM.WebappMetricConfig.from_model(M.MetricConfig())

    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            replace(
                WM.WebappSimpleSegment.create(event_path, None),
                group_by=[event_field_b_path, event_field_c_path],
            ),
        ],
        title=None,
        description=None,
    )

    result = MetricUpdater(metric_context, insight_settings).filter_on(
        metric, 1, ["xx", "yy"]
    )

    assert result.segments[0] == metric.segments[0]

    segment = result.segments[1]
    assert isinstance(segment, WM.WebappComplexSegment)
    assert segment.left == WM.WebappSimpleSegment.create(
        event_path, WM.WebappEventFilter(event_field_b_path, M.Operator.ANY_OF, ["xx"])
    )
    assert segment.right == WM.WebappSimpleSegment.create(
        event_path, WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, ["yy"])
    )


def test_apply_dashboard_filters_updates_time_config():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    time_config = WM.TimeWindowChooserConfig(
        datetime.now(),
        datetime.now() + timedelta(days=2),
        M.TimeGroup.QUARTER,
        lookback_days=M.TimeWindow(12, M.TimeGroup.WEEK),
        calendar_date_range=None,
        since_date=None,
    )
    result_config = (
        MetricUpdater(metric_context, insight_settings)
        .apply_dashboard_filters(
            metric,
            WM.DashboardFilter([], time_config),
        )
        .config
    )

    assert result_config.time_window_chooser_config.start_dt == time_config.start_dt
    assert result_config.time_window_chooser_config.end_dt == time_config.end_dt
    assert (
        result_config.time_window_chooser_config.lookback_days
        == time_config.lookback_days
    )
    assert result_config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL


def test_apply_dashboard_filters_global_filters():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    filter_row = WM.WebappEventFilter(event_field_a_path, M.Operator.EQ, "user_id")

    result_metric = MetricUpdater(
        metric_context, insight_settings
    ).apply_dashboard_filters(
        metric,
        WM.DashboardFilter([filter_row], None),
    )

    assert result_metric.global_filters == [filter_row]


def test_apply_dashboard_filters_merges_global_filters():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )
    global_filter_row = WM.WebappEventFilter(
        M.DimensionFieldPath("profiles", "user_id"), M.Operator.EQ, "user_id"
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
        global_filters=[global_filter_row],
    )

    filter_row = WM.WebappEventFilter(
        M.DimensionFieldPath("profiles", "ucc"), M.Operator.ANY_OF, ["gb", "de"]
    )

    result_metric = MetricUpdater(
        metric_context, insight_settings
    ).apply_dashboard_filters(
        metric,
        WM.DashboardFilter([filter_row], None),
    )

    assert result_metric.global_filters == [global_filter_row, filter_row]


def test_apply_dashboard_filters_global_filters_raises_error_when_filter_cannot_be_applied():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    filter_row = WM.WebappEventFilter(
        M.EventFieldPath("table", "event", "missing"), M.Operator.EQ, "user_id"
    )

    with pytest.raises(CannotApplyDashboardFilter):
        MetricUpdater(metric_context, insight_settings).apply_dashboard_filters(
            metric,
            WM.DashboardFilter([filter_row], None),
        )


def test_apply_dashboard_filters_updates_time_config_and_global_filters():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    time_config = WM.TimeWindowChooserConfig(
        datetime.now(),
        datetime.now() + timedelta(days=2),
        M.TimeGroup.QUARTER,
        lookback_days=M.TimeWindow(12, M.TimeGroup.WEEK),
        calendar_date_range=None,
        since_date=None,
    )
    filter_row = WM.WebappEventFilter(
        M.DimensionFieldPath("profiles", "ucc"), M.Operator.ANY_OF, ["gb", "de"]
    )
    result_metric = MetricUpdater(
        metric_context, insight_settings
    ).apply_dashboard_filters(
        metric,
        WM.DashboardFilter([filter_row], time_config),
    )

    result_config = result_metric.config

    assert result_metric.global_filters == [filter_row]

    assert result_config.time_window_chooser_config.start_dt == time_config.start_dt
    assert result_config.time_window_chooser_config.end_dt == time_config.end_dt
    assert (
        result_config.time_window_chooser_config.lookback_days
        == time_config.lookback_days
    )
    assert result_config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL


def test_apply_dashboard_filters_with_retention():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.HOUR)
    )
    metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_path, None),
            WM.WebappSimpleSegment.create(event_path, None),
        ],
        title=None,
        description=None,
    )

    time_config = WM.TimeWindowChooserConfig(
        datetime.now(),
        datetime.now() + timedelta(days=2),
        M.TimeGroup.QUARTER,
        lookback_days=M.TimeWindow(12, M.TimeGroup.WEEK),
        calendar_date_range=None,
        since_date=None,
    )
    result_config = (
        MetricUpdater(metric_context, insight_settings)
        .apply_dashboard_filters(metric, WM.DashboardFilter([], time_config))
        .config
    )

    assert result_config.time_window_chooser_config.start_dt == time_config.start_dt
    assert result_config.time_window_chooser_config.end_dt == time_config.end_dt
    assert (
        result_config.time_window_chooser_config.lookback_days
        == time_config.lookback_days
    )
    assert result_config.time_window_chooser_config.time_group == M.TimeGroup.HOUR


@pytest.mark.parametrize(
    "value,datatype",
    [
        (True, M.DataType.BOOL),
        (False, M.DataType.BOOL),
        (10, M.DataType.NUMBER),
        (10.21, M.DataType.NUMBER),
        ("string", M.DataType.STRING),
    ],
)
def test_datetype_parsing_from_string(value: Any, datatype: M.DataType):
    assert MetricUpdater._parse_string_values(datatype, str(value)) == value


@pytest.mark.parametrize(
    "metric_type, segment_agg_type, expected_agg_type",
    [
        (
            M.MetricType.SEGMENTATION,
            M.AggType.RETURN_UNIQUE_USERS,
            M.AggType.COUNT_UNIQUE_USERS,
        ),
        (
            M.MetricType.SEGMENTATION,
            M.AggType.RETURN_SUBSCRIBERS,
            M.AggType.COUNT_SUBSCRIBERS,
        ),
        (
            M.MetricType.SEGMENTATION,
            M.AggType.COUNT_CONVERTED_USERS,
            M.AggType.COUNT_UNIQUE_USERS,
        ),
        (
            M.MetricType.SEGMENTATION,
            M.AggType.TOTAL_MRR,
            M.AggType.COUNT_UNIQUE_USERS,
        ),
        (
            M.MetricType.CONVERSION,
            M.AggType.CONVERSION,
            M.AggType.CONVERSION,
        ),
        (
            M.MetricType.JOURNEY,
            M.AggType.CONVERSION,
            M.AggType.CONVERSION,
        ),
        (
            M.MetricType.RETENTION,
            M.AggType.COUNT_EVENTS,
            M.AggType.RETENTION_RATE,
        ),
    ],
)
def test_fix_cohort_backwards_compatible_agg_types_for_view_insight(
    metric_type: M.MetricType,
    segment_agg_type: M.AggType,
    expected_agg_type: M.AggType,
):
    segments = (
        [
            replace(
                event_segment_without_filter,
                aggregation=WM.WebappAggregationConfig(segment_agg_type),
            )
        ]
        if metric_type == M.MetricType.SEGMENTATION
        else [event_segment_without_filter]
    )

    cohort_metric = WM.WebappMetric(
        metric_type=metric_type,
        config=WM.WebappMetricConfig.from_model(
            M.MetricConfig(aggregation=M.AggregationConfig(type=segment_agg_type))
        ),
        segments=segments,
        title="Test Cohort",
        description="Description",
    )

    result_metric = (
        MetricUpdater.fix_cohort_backwards_compatible_agg_types_for_view_insight(
            cohort_metric
        )
    )

    if metric_type == M.MetricType.SEGMENTATION:
        for segment in result_metric.segments:
            assert segment.aggregation is not None
            assert segment.aggregation.type == expected_agg_type
    else:
        assert result_metric.config.aggregation.type == expected_agg_type


def test_return_events_add_event_time():
    agg_config = WM.WebappAggregationConfig(M.AggType.COUNT_CONVERTED_USERS)
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.WEEK)
    )

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_2_path, None),  # this should be removed
            replace(
                WM.WebappSimpleSegment.create(event_path, None),
                aggregation=agg_config,
                group_by=[event_field_a_path],
            ),
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_events(
        input_metric,
        [],
        segment_index=1,
        selected_date_str="2024-01-02",
        break_down_values=["group_by_a"],
    )

    assert new_metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL

    assert new_metric.config.start_dt == datetime(2024, 1, 2, 0, 0)
    assert new_metric.config.end_dt == datetime(2024, 1, 8, 0, 0)
    assert len(new_metric.segments) == 1
    segment = new_metric.segments[0]
    assert segment.aggregation is not None
    assert segment.aggregation.type == M.AggType.COUNT_EVENTS
    assert segment.group_by == [M.EVENT_TIME_FIELD_PATH]
    assert segment.filter is None
    assert isinstance(segment, WM.WebappSimpleSegment)
    assert segment.event_filter == WM.WebappEventFilter(
        event_field_a_path, M.Operator.ANY_OF, ["group_by_a"]
    )


def test_return_events_add_event_time_total_time_group():
    agg_config = WM.WebappAggregationConfig(M.AggType.COUNT_CONVERTED_USERS)
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(time_group=M.TimeGroup.TOTAL)
    )

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.SEGMENTATION,
        config=metric_config,
        segments=[
            WM.WebappSimpleSegment.create(event_2_path, None),  # this should be removed
            replace(
                WM.WebappSimpleSegment.create(event_path, None),
                aggregation=agg_config,
                group_by=[event_field_a_path],
            ),
        ],
        title=None,
        description=None,
    )

    new_metric = MetricUpdater(metric_context, insight_settings).return_events(
        input_metric,
        [],
        segment_index=1,
        selected_date_str="2024-01-02",
        break_down_values=["event_a"],
    )

    assert new_metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL

    assert (
        new_metric.config.time_window_chooser_config.start_dt
        == metric_config.time_window_chooser_config.start_dt
    )
    assert (
        new_metric.config.time_window_chooser_config.end_dt
        == metric_config.time_window_chooser_config.end_dt
    )
    assert len(new_metric.segments) == 1
    segment = new_metric.segments[0]
    assert segment.aggregation is not None
    assert segment.aggregation.type == M.AggType.COUNT_EVENTS
    assert segment.group_by == [M.EVENT_TIME_FIELD_PATH]
    assert segment.filter is None
    assert isinstance(segment, WM.WebappSimpleSegment)
    assert segment.event_filter == WM.WebappEventFilter(
        event_field_a_path, M.Operator.ANY_OF, ["event_a"]
    )


@pytest.mark.parametrize(
    "metric_type, segments, tooltip_json, expected_segments",
    [
        pytest.param(
            M.MetricType.SEGMENTATION,
            [
                event_segment_without_filter,
                replace(
                    event_segment_without_filter,
                    group_by=[event_field_a_path],
                ),
            ],
            WM.TooltipCustomJson(segment_index=1, break_down=["val_a"]),
            [
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_field_a_path, M.Operator.ANY_OF, ["val_a"]
                        ),
                    ),
                    group_by=[event_field_a_path],
                ),
            ],
            id="segmentation metric with group_by filter",
        ),
        pytest.param(
            M.MetricType.CONVERSION,
            [
                replace(
                    event_segment_without_filter,
                    group_by=[event_field_a_path],
                ),
                event_2_segment_without_filter,
            ],
            WM.TooltipCustomJson(segment_index=0, break_down=["val_b"]),
            [
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_field_a_path, M.Operator.ANY_OF, ["val_b"]
                        ),
                    ),
                    group_by=[event_field_a_path],
                ),
                event_2_segment_without_filter,
            ],
            id="conversion metric",
        ),
        pytest.param(
            M.MetricType.RETENTION,
            [
                event_segment_without_filter,
                replace(
                    event_segment_without_filter,
                    group_by=[event_field_a_path],
                ),
            ],
            WM.TooltipCustomJson(segment_index=1, break_down=["val_c"]),
            [
                event_segment_without_filter,
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_field_a_path, M.Operator.ANY_OF, ["val_c"]
                        ),
                    ),
                    group_by=[event_field_a_path],
                ),
            ],
            id="retention metric filters all segments",
        ),
    ],
)
def test_create_filtered_metric(
    metric_type: M.MetricType,
    segments: List[WM.WebappSegment],
    tooltip_json: WM.TooltipCustomJson,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=metric_type,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)
    output_metric = metric_updater.create_filtered_metric(input_metric, tooltip_json)

    assert output_metric.segments == expected_segments


def test_filter_segments():
    metric_config = WM.WebappMetricConfig.from_model(
        M.MetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        )
    )

    segments = [
        replace(
            event_segment_without_filter,
            group_by=[event_field_a_path],
        ),
        event_2_segment_without_filter,
    ]
    tooltip_json = WM.TooltipCustomJson(segment_index=1, break_down=["val_b"])

    expected_segments = [
        replace(
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, ["val_b"]),
            ),
            group_by=[event_field_a_path],
        ),
        event_2_segment_without_filter,
    ]

    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.RETENTION,
        config=metric_config,
        segments=segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)
    output_segments = metric_updater._filter_segments(
        input_metric, tooltip_json, cutoff_at_current_funnel_step=False
    )

    assert output_segments == expected_segments


custom_event_id = "custom-event-id"


def get_storage_mock_with_custom_event(
    custom_event_segment: WM.WebappSegment,
) -> MagicMock:
    storage_mock = MagicMock()
    storage_mock.get_custom_event.return_value = WM.CustomEvent(
        id="custom-event-id",
        display_name="custom",
        description=None,
        segment=custom_event_segment,
        owner=WM.MembershipID("owner"),
        created_at=datetime.now(),
        last_updated_at=datetime.now(),
    )
    return storage_mock


def get_metric_context_with_custom_event(
    custom_event: WM.CustomEvent,
) -> MC.MetricContext:
    return replace(metric_context, _custom_events={custom_event.id: custom_event})


def get_custom_event_with_segment(segment: WM.WebappSegment) -> WM.CustomEvent:
    return WM.CustomEvent(
        "custom-event-id",
        "custom event",
        description=None,
        segment=segment,
        owner=WM.MembershipID("owner"),
        created_at=datetime.now(),
        last_updated_at=datetime.now(),
    )


@pytest.mark.parametrize(
    "custom_event_segment",
    [
        event_segment_without_filter,
        segment_with_field_b_filter,
    ],
)
def test_resolves_custom_events_to_webapp_segments(
    custom_event_segment: WM.WebappSegment,
):
    custom_event = get_custom_event_with_segment(custom_event_segment)
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), None
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        custom_event_segment, expanded_custom_event_key=custom_event_key
    )


@pytest.mark.parametrize(
    "custom_event_segment",
    [
        event_segment_without_filter,
        segment_with_field_b_filter,
    ],
)
def test_resolves_custom_events_to_webapp_segments_keeping_the_root_segment_properties(
    custom_event_segment: WM.WebappSegment,
):
    custom_event = get_custom_event_with_segment(custom_event_segment)
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    custom_segment = replace(
        WM.WebappSimpleSegment.create(M.CustomEventNamePath(custom_event_id), None),
        group_by=[event_field_b_path],
        filter=M.SegmentFilter.FIRST_EVENT_EVER,
        title="awesome segment",
        aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        custom_event_segment,
        group_by=[event_field_b_path],
        filter=M.SegmentFilter.FIRST_EVENT_EVER,
        title="awesome segment",
        aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_UNIQUE_USERS),
        expanded_custom_event_key=custom_event_key,
    )


def test_event_filter_added_to_simple_custom_event_segment():
    custom_event = get_custom_event_with_segment(event_2_segment_without_filter)
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(event_2_field_a_path, M.Operator.ANY_OF, [1])
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappSimpleSegment.create(event_2_path, filter),
        expanded_custom_event_key=custom_event_key,
    )


def test_event_filter_added_to_simple_custom_event_segment_with_filters():
    custom_event = get_custom_event_with_segment(segment_with_field_b_filter)
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, [1])
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                segment_with_field_b_filter, expanded_custom_event_key=custom_event_key
            ),
            M.BinaryOperator.AND,
            replace(
                WM.WebappSimpleSegment.create(event_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_event_filter_added_to_complex_custom_event_segment_different_event_without_filters():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                WM.WebappSimpleSegment.create(event_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
            M.BinaryOperator.OR,
            replace(
                WM.WebappSimpleSegment.create(event_2_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_event_filter_added_to_complex_custom_event_segment_different_event_with_filters():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, [1]),
            ),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(
                event_2_path,
                WM.WebappEventFilter(event_2_field_a_path, M.Operator.ANY_OF, [1]),
            ),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_path,
                            WM.WebappEventFilter(
                                event_field_a_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key=custom_event_key,
                    ),
                    M.BinaryOperator.AND,
                    replace(
                        WM.WebappSimpleSegment.create(event_path, filter),
                        expanded_custom_event_key=custom_event_key,
                    ),
                ),
                expanded_custom_event_key=custom_event_key,
            ),
            M.BinaryOperator.OR,
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_2_path,
                            WM.WebappEventFilter(
                                event_2_field_a_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key=custom_event_key,
                    ),
                    M.BinaryOperator.AND,
                    replace(
                        WM.WebappSimpleSegment.create(event_2_path, filter),
                        expanded_custom_event_key=custom_event_key,
                    ),
                ),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_event_filter_added_to_complex_custom_event_segment_same_event_with_filters():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(event_field_a_path, M.Operator.ANY_OF, [1]),
            ),
            M.BinaryOperator.AND,
            WM.WebappSimpleSegment.create(
                event_path,
                WM.WebappEventFilter(event_field_b_path, M.Operator.ANY_OF, [1]),
            ),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )
    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_path,
                            WM.WebappEventFilter(
                                event_field_a_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key=custom_event_key,
                    ),
                    M.BinaryOperator.AND,
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_path,
                            WM.WebappEventFilter(
                                event_field_b_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key=custom_event_key,
                    ),
                ),
                expanded_custom_event_key=custom_event_key,
            ),
            M.BinaryOperator.AND,
            replace(
                WM.WebappSimpleSegment.create(event_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_complex_segment_event_filter_added_to_complex_custom_event_segment_different_event_without_filters():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])
    filter_2 = WM.WebappEventFilter(event_common_field_path, M.Operator.ANY_OF, [2])

    custom_segment = WM.WebappComplexSegment.create(
        WM.WebappSimpleSegment.create(M.CustomEventNamePath(custom_event_id), filter),
        M.BinaryOperator.AND,
        WM.WebappSimpleSegment.create(M.CustomEventNamePath(custom_event_id), filter_2),
    )

    custom_event_key = "a"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(event_path, filter),
                        expanded_custom_event_key=custom_event_key,
                    ),
                    M.BinaryOperator.AND,
                    replace(
                        WM.WebappSimpleSegment.create(event_path, filter_2),
                        expanded_custom_event_key=custom_event_key,
                    ),
                ),
                expanded_custom_event_key=custom_event_key,
            ),
            M.BinaryOperator.OR,
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(event_2_path, filter),
                        expanded_custom_event_key=custom_event_key,
                    ),
                    M.BinaryOperator.AND,
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_2_path,
                            WM.WebappEventFilter(
                                event_2_common_field_path, M.Operator.ANY_OF, [2]
                            ),
                        ),
                        expanded_custom_event_key=custom_event_key,
                    ),
                ),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_collection_filter_added_to_custom_events():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    collection_path = M.CollectionFieldPath(M.USER_ENTITY)
    filter = WM.WebappEventFilter(collection_path, M.Operator.ANY_OF, ["collection-id"])

    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )

    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappComplexSegment.create(
            replace(
                WM.WebappSimpleSegment.create(event_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
            M.BinaryOperator.OR,
            replace(
                WM.WebappSimpleSegment.create(event_2_path, filter),
                expanded_custom_event_key=custom_event_key,
            ),
        ),
        expanded_custom_event_key=custom_event_key,
    )


def test_custom_event_in_a_complex_segment_resolved_properly():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])

    custom_segment = WM.WebappComplexSegment.create(
        WM.WebappSimpleSegment.create(M.CustomEventNamePath(custom_event_id), filter),
        M.BinaryOperator.OR,
        WM.WebappSimpleSegment.create(event_path, None),
    )

    custom_event_key = "ls"
    assert metric_updater.resolve_custom_events(
        custom_segment, ""
    ) == WM.WebappComplexSegment.create(
        replace(
            WM.WebappComplexSegment.create(
                replace(
                    WM.WebappSimpleSegment.create(event_path, filter),
                    expanded_custom_event_key=custom_event_key,
                ),
                M.BinaryOperator.OR,
                replace(
                    WM.WebappSimpleSegment.create(event_2_path, filter),
                    expanded_custom_event_key=custom_event_key,
                ),
            ),
            expanded_custom_event_key=custom_event_key,
        ),
        M.BinaryOperator.OR,
        WM.WebappSimpleSegment.create(event_path, None),
    )


event_segment_journey_first_segment = replace(
    event_segment_without_filter,
    filter=M.SegmentFilter.FIRST_EVENT_EVER,
)


@pytest.mark.parametrize(
    "filters,expected_segment",
    [
        pytest.param(
            [
                WM.WebappEventFilter(event_2_common_field_path, M.Operator.ANY_OF, [1]),
            ],
            replace(
                WM.WebappComplexSegment.create(
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_path,
                            WM.WebappEventFilter(
                                event_common_field_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key="s",
                    ),
                    M.BinaryOperator.OR,
                    replace(
                        WM.WebappSimpleSegment.create(
                            event_2_path,
                            WM.WebappEventFilter(
                                event_2_common_field_path, M.Operator.ANY_OF, [1]
                            ),
                        ),
                        expanded_custom_event_key="s",
                    ),
                ),
                expanded_custom_event_key="s",
            ),
            id="keeps segments when there are a field with similar name",
        ),
        pytest.param(
            [
                WM.WebappEventFilter(event_2_field_d_path, M.Operator.ANY_OF, [1]),
            ],
            replace(
                WM.WebappSimpleSegment.create(
                    event_2_path,
                    WM.WebappEventFilter(event_2_field_d_path, M.Operator.ANY_OF, [1]),
                ),
                expanded_custom_event_key="s",
            ),
            id="removes left segment when its not matching on the filter",
        ),
        pytest.param(
            [
                WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, [1]),
            ],
            replace(
                WM.WebappSimpleSegment.create(
                    event_path,
                    WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, [1]),
                ),
                expanded_custom_event_key="s",
            ),
            id="removes right segment when its not matching on the filter",
        ),
    ],
)
def test_custom_event_is_sanitized_to_match_all_filters(
    filters: List[WM.WebappEventFilter], expected_segment: WM.WebappSegment
):
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(dimension_field_a_path, M.Operator.ANY_OF, [1])

    custom_segment: WM.WebappSegment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filters[0]
    )
    for filter in filters[1:]:
        custom_segment = WM.WebappComplexSegment.create(
            custom_segment,
            M.BinaryOperator.AND,
            WM.WebappSimpleSegment.create(
                M.CustomEventNamePath(custom_event_id), filter
            ),
        )

    assert metric_updater.resolve_custom_events(custom_segment, "") == expected_segment


def test_custom_event_is_sanitized_to_match_all_filters_and_operator():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappComplexSegment.create(
                WM.WebappSimpleSegment.create(
                    event_2_path,
                    WM.WebappEventFilter(event_2_field_a_path, M.Operator.ANY_OF, [1]),
                ),
                M.BinaryOperator.AND,
                WM.WebappSimpleSegment.create(
                    event_2_path,
                    WM.WebappEventFilter(event_2_field_d_path, M.Operator.ANY_OF, [2]),
                ),
            ),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(event_field_c_path, M.Operator.ANY_OF, ["x"])

    custom_segment: WM.WebappSegment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )

    custom_event_key = "s"
    assert metric_updater.resolve_custom_events(custom_segment, "") == replace(
        WM.WebappSimpleSegment.create(event_path, filter),
        expanded_custom_event_key=custom_event_key,
    )


def test_resolve_custom_event_raises_error_when_all_events_are_filtered_out():
    custom_event = get_custom_event_with_segment(
        WM.WebappComplexSegment.create(
            WM.WebappSimpleSegment.create(event_path, None),
            M.BinaryOperator.OR,
            WM.WebappSimpleSegment.create(event_2_path, None),
        )
    )
    metric_updater = MetricUpdater(
        get_metric_context_with_custom_event(custom_event), insight_settings
    )

    filter = WM.WebappEventFilter(
        M.EventFieldPath("table", "event_3", "field_x"), M.Operator.ANY_OF, [1]
    )
    custom_segment = WM.WebappSimpleSegment.create(
        M.CustomEventNamePath(custom_event_id), filter
    )

    with pytest.raises(CustomEventFiltersResultNoEvents):
        metric_updater.resolve_custom_events(custom_segment, "")


@pytest.mark.parametrize(
    "initial_segments,segment_index,break_down_values,direction,expected_segments",
    [
        pytest.param(
            [event_segment_journey_first_segment, event_2_segment_without_filter],
            0,
            [],
            ExploreJourneyDirection.FURTHER,
            [
                event_segment_journey_first_segment,
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="explore further duplicates segment without filters",
        ),
        pytest.param(
            [event_segment_journey_first_segment, event_2_segment_without_filter],
            1,
            [],
            ExploreJourneyDirection.PREVIOUS,
            [
                event_segment_journey_first_segment,
                event_2_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="explore previous duplicates segment without filters",
        ),
        pytest.param(
            [
                event_segment_journey_first_segment,
                event_2_segment_without_filter,
            ],
            0,
            [],
            ExploreJourneyDirection.PREVIOUS,
            [
                event_segment_journey_first_segment,
                event_segment_without_filter,
                event_2_segment_without_filter,
            ],
            id="explore previous of the first segment fixes segment filters",
        ),
        pytest.param(
            [replace(event_segment_without_filter, group_by=[event_field_b_path])],
            0,
            ["2"],
            ExploreJourneyDirection.FURTHER,
            [
                replace(
                    WM.WebappSimpleSegment.create(
                        event_path,
                        WM.WebappEventFilter(
                            event_field_b_path, M.Operator.ANY_OF, ["2"]
                        ),
                    ),
                    group_by=[event_field_b_path],
                ),
                replace(event_segment_without_filter, group_by=[event_field_b_path]),
            ],
            id="explore further applies filter before duplicating",
        ),
    ],
)
def test_explore_journey_step(
    initial_segments: List[WM.WebappSegment],
    segment_index: int,
    break_down_values: List[str],
    direction: ExploreJourneyDirection,
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.CONVERSION),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.JOURNEY,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.explore_journey_step(
        input_metric, segment_index, break_down_values, direction
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.JOURNEY,
        config=metric_config,
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


journey_to_conversion_cases = [
    pytest.param(
        [event_segment_without_filter, event_2_segment_without_filter],
        1,
        [],
        [event_segment_without_filter, event_2_segment_without_filter],
        id="see trends shows a conversion metric",
    ),
    pytest.param(
        [
            event_segment_without_filter,
            event_2_segment_without_filter,
            event_segment_without_filter,
            event_segment_without_filter,
        ],
        2,
        [],
        [
            event_segment_without_filter,
            event_2_segment_without_filter,
            event_segment_without_filter,
        ],
        id="see trends shows drops additional steps",
    ),
    pytest.param(
        [
            replace(
                event_2_segment_without_filter,
                group_by=[event_field_b_path],
            ),
            replace(
                WM.WebappSimpleSegment.create(event_path, None),
                group_by=[event_field_b_path],
            ),
            event_segment_without_filter,
            event_segment_without_filter,
        ],
        1,
        ["xx"],
        [
            event_2_segment_without_filter,
            replace(
                WM.WebappSimpleSegment.create(
                    event_path,
                    WM.WebappEventFilter(event_field_b_path, M.Operator.ANY_OF, ["xx"]),
                ),
                group_by=[],
            ),
        ],
        id="see trends shows adds group by filter",
    ),
]


@pytest.mark.parametrize(
    "initial_segments,segment_index,break_down_values,expected_segments",
    journey_to_conversion_cases,
)
def test_journey_show_trends(
    initial_segments: List[WM.WebappSegment],
    segment_index: int,
    break_down_values: List[str],
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.CONVERSION),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.JOURNEY,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.journey_show_trends(
        input_metric, segment_index, break_down_values
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=WM.WebappMetricConfig.from_model(
            M.ConversionMetricConfig(
                start_dt=None,
                end_dt=None,
                time_group=M.TimeGroup.DAY,
                aggregation=M.AggregationConfig(M.AggType.CONVERSION),
                chart_type=M.SimpleChartType.LINE,
            )
        ),
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


@pytest.mark.parametrize(
    "initial_segments,segment_index,break_down_values,expected_segments",
    journey_to_conversion_cases,
)
def test_journey_to_funnel(
    initial_segments: List[WM.WebappSegment],
    segment_index: int,
    break_down_values: List[str],
    expected_segments: List[WM.WebappSegment],
):
    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.CONVERSION),
        )
    )
    input_metric = WM.WebappMetric(
        metric_type=M.MetricType.JOURNEY,
        config=metric_config,
        segments=initial_segments,
        title=None,
        description=None,
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    output_metric = metric_updater.journey_to_funnel(
        input_metric, segment_index, break_down_values
    )

    compare_metric = WM.WebappMetric(
        metric_type=M.MetricType.CONVERSION,
        config=replace(metric_config, chart_type=M.SimpleChartType.BAR),
        segments=expected_segments,
        title=None,
        description=None,
    )

    assert output_metric == compare_metric


def test_get_metric_time_window():
    insight_settings = M.InsightSettings(
        project_id="test_project",
        end_date_config=M.WebappEndDateConfig.CUSTOM_DATE,
        custom_end_date=datetime(2024, 5, 1),
    )

    metric_updater = MetricUpdater(metric_context, insight_settings)

    metric_config = WM.WebappMetricConfig.from_model(
        M.ConversionMetricConfig(
            time_group=M.TimeGroup.TOTAL,
            aggregation=M.AggregationConfig(M.AggType.CONVERSION),
        )
    )
    metric_config = replace(
        metric_config,
        since_date=datetime(2024, 1, 1),
        start_dt=None,
        end_dt=None,
        lookback_days=None,
    )
    start_dt, end_dt = metric_updater._get_metric_time_window(config=metric_config)
    assert start_dt == datetime(2024, 1, 1)
    assert end_dt == datetime(2024, 5, 1)
