import base64
from datetime import datetime

from lxml import etree

from mitzu.webapp.service.svg_updater import (
    SVG_IMAGE_HEADER,
    update_svg_for_miro,
)

LOGO_SVG = """
<svg width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="36" height="37" rx="4" fill="white"/>
</svg>"""

EXPECTED_SVG = (
    """
<svg width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="100%" height="100%" fill="white"/><style>
.small {
    font: lighter 11px sans-serif;
    fill: black;
}
.title {
    font: bold 14px sans-serif;
    fill: black;
}
</style><text x="4" y="33" class="small">Chart generated at: 2024-01-02 03:04:05 UTC</text>"""
    """<text x="4" y="18" class="title">title | description</text>"""
    """<rect width="36" height="37" rx="4" fill="white"/>
</svg>
"""
)


def test_update_svg_for_miro():
    now = datetime(2024, 1, 2, 3, 4, 5)
    res = update_svg_for_miro(
        SVG_IMAGE_HEADER + base64.b64encode(LOGO_SVG.encode("utf-8")).decode("utf-8"),
        now,
        "title",
        "description",
    )
    assert base64.b64decode(res.lstrip(SVG_IMAGE_HEADER)).decode(
        "utf-8"
    ) == etree.tostring(etree.XML(EXPECTED_SVG)).decode("utf-8")


def test_update_svg_for_miro_junk():
    now = datetime(2024, 1, 2, 3, 4, 5)
    junk = "not svg content"
    assert update_svg_for_miro(junk, now, "title", None) == junk
    assert (
        update_svg_for_miro(SVG_IMAGE_HEADER + junk, now, "title", None)
        == SVG_IMAGE_HEADER + junk
    )
