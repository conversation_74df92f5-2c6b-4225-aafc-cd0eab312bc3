import json
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from datetime import datetime
from unittest.mock import ANY, MagicMock, patch

import flask
from freezegun import freeze_time

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.task_scheduler.model as TSM
from mitzu.model import Project
from mitzu.webapp.endpoints.miro import (
    add_miro_team_to_project,
    authorize_miro_app,
    authorize_miro_app_finish,
    embed_insight_in_miro,
    miro_create_new_insight,
    miro_duplicate_insight,
    miro_login,
    miro_refresh_insight,
    miro_task_status,
    serve_insight_details_for_miro,
    start_session_from_miro,
)
from mitzu.webapp.integrations.miro import MiroIntegration
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)
from mitzu.webapp.storage.storage_model import (
    MiroIntegrationStorageRecord,
)
from mitzu.webapp.storage.task_storage import TaskStorage
from tests.helper import (
    get_storages_with_org,
    setup_project,
)
from tests.in_memory_cache import InMemory<PERSON>ache


@patch("mitzu.webapp.endpoints.miro.get_error_image", lambda error: error)
def test_serve_insight_details_for_miro_metric_insight_id_not_set(server: flask.Flask):
    with server.test_request_context() as ctx:
        resp = serve_insight_details_for_miro(ctx.request)
        assert resp.status_code == 200
        assert resp.json == {
            "error": "Insight id is not set",
            "insight_id": None,
            "insight_image": "Insight id is not set",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }


@patch("mitzu.webapp.endpoints.miro.get_error_image", lambda error: error)
def test_serve_insight_details_for_miro_metric_not_found(server: flask.Flask):
    with server.test_request_context("?sm=1234") as ctx:
        resp = serve_insight_details_for_miro(ctx.request)
        assert resp.status_code == 200
        assert resp.json == {
            "error": "Insight not found",
            "insight_id": None,
            "insight_image": "Insight not found",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }


@patch("mitzu.webapp.endpoints.miro.get_error_image", lambda error: error)
def test_serve_insight_details_for_miro_metric_is_found(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    saved_metric: WM.SavedMetric,
):
    project_storage = dependencies.get_project_storage(saved_metric.project_id)
    with server.test_request_context(f"?sm={saved_metric.id}") as ctx:
        resp = serve_insight_details_for_miro(ctx.request)
        assert resp.status_code == 200
        assert resp.json == {
            "error": "Insight not found",
            "insight_id": None,
            "insight_image": "Insight not found",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }

        project_storage.set_saved_metric(saved_metric)

        resp = serve_insight_details_for_miro(ctx.request)
        assert resp.status_code == 200
        assert resp.json == {
            "error": None,
            "insight_id": "test_sm",
            "insight_image": saved_metric.image_base64,
            "insight_title": saved_metric.name,
            "org_id": "test",
            "project_id": saved_metric.project_id,
        }


def test_add_miro_team_to_project_redirects_to_home_on_error(server: flask.Flask):
    # request is not set properly
    with server.test_request_context() as ctx:
        resp = add_miro_team_to_project(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == P.HOME_PATH


@patch("mitzu.webapp.endpoints.miro.MiroService")
def test_add_miro_team_to_project_redirects_to_webapp_settings(
    miro_mock,
    server: flask.Flask,
    dependencies_admin: DEPS.Dependencies,
    project: Project,
):
    setup_project(dependencies_admin.storage, project)
    auth_code = "auth-code"
    miro_mock.return_value = miro_mock
    with server.test_request_context(f"?code={auth_code}&state={project.id}") as ctx:
        resp = add_miro_team_to_project(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == P.create_workspace_settings_path(
            project.id, P.WorkspaceSettingsTab.MIRO
        )

        miro_mock.add_team_to_project_by_auth_code.assert_called_once_with(auth_code)


def test_embed_insight_in_miro(
    server: flask.Flask,
    saved_metric: WM.SavedMetric,
    dependencies: DEPS.Dependencies,
):
    with server.test_request_context() as ctx:
        resp = embed_insight_in_miro(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == "/shared-insights/notfound?embed=true"

    # missing access token
    with server.test_request_context("?insight_id=insight-id") as ctx:
        resp = embed_insight_in_miro(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == "/shared-insights/notfound?embed=true"

    # project_id not found
    access_token = "access-token"
    with server.test_request_context(
        f"?insight_id=insight-id&access_token={access_token}"
    ) as ctx:
        resp = embed_insight_in_miro(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == "/shared-insights/notfound?embed=true"

    # saved metric exists
    dependencies.get_project_storage(saved_metric.project_id).set_saved_metric(
        saved_metric
    )
    with server.test_request_context(
        f"?insight_id={saved_metric.id}&access_token={access_token}"
    ) as ctx:
        resp = embed_insight_in_miro(ctx.request)
        assert resp.status_code == 307
        assert (
            resp.headers.get("Location")
            == "/projects/sample_project_id/explore?sm=test_sm&miro=true&access_token=access-token"
        )


def test_embed_insight_in_miro_logged_out(
    server_logged_out: flask.Flask,
    saved_metric: WM.SavedMetric,
):
    access_token = "access-token"
    with server_logged_out.test_request_context(
        f"?insight_id={saved_metric.id}&access_token={access_token}"
    ) as ctx:
        resp = embed_insight_in_miro(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == "/shared-insights/notfound?embed=true"


@patch("mitzu.configs.MIRO_CLIENT_ID", "client-id")
def test_authorize_miro_app():
    resp = authorize_miro_app()
    assert resp.status_code == 200
    assert resp.get_data().decode("utf-8") == (
        "https://miro.com/oauth/authorize?response_type=code&client_id=client-id&"
        "redirect_uri=http://localhost:8082/api/miro/authorize-app-finish"
    )


def test_authorize_miro_app_finish_no_auth_code(server: flask.Flask):
    with server.test_request_context() as ctx:
        resp = authorize_miro_app_finish(ctx.request)
        assert "Failed to authenticate the use" in str(resp.get_data())


@patch("mitzu.webapp.endpoints.miro.MiroService")
def test_authorize_miro_app_finish_success(miro_mock, server: flask.Flask):
    auth_code = "code"
    cache_key = "cache-key"
    tokens = {"access_token": "token", "team_id": "team ID"}
    miro_mock.exchange_auth_code_to_token.return_value = tokens
    miro_mock.get_token_info.return_value = "tokeninfo"
    miro_mock.store_miro_token_details.return_value = cache_key
    with server.test_request_context(f"/?code={auth_code}") as ctx:
        resp = authorize_miro_app_finish(ctx.request)
        assert "Auth successful!" in str(resp.get_data())
        assert (
            resp.headers.get("Set-Cookie")
            == "mitzu-token-key=cache-key; Secure; Path=/; SameSite=None"
        )

        miro_mock.exchange_auth_code_to_token.assert_called_once_with(
            auth_code, "http://localhost:8082/api/miro/authorize-app-finish"
        )
        miro_mock.get_token_info.assert_called_once_with("token")
        miro_mock.store_miro_token_details.assert_called_once_with(ANY, "tokeninfo")


def test_start_session_from_miro(server: flask.Flask, project: Project):
    cache = InMemoryCache()
    cloud_storage, org_storage = get_storages_with_org("org1")
    setup_project(org_storage, project)
    owner = org_storage.get_org_owner()
    cloud_authorizer = MagicMock()
    miro_org_id = "miro-org"
    team_id = "team-id"
    email = owner.email
    token_cache_key = "cachekey"
    token = {"team": {"id": team_id}}

    with server.test_request_context() as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        assert "Email is not set" in str(resp.get_data())

    with server.test_request_context(f"/?email={email}") as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        assert "Token cache key is not set" in str(resp.get_data())

    with server.test_request_context(
        f"/?email={email}&token_cache_key={token_cache_key}"
    ) as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        # when the app is opened for the second time we won't get the oauth code again
        assert resp.status_code == 204
        assert resp.get_data() == b""

    cache.put(token_cache_key, token, 100)
    with server.test_request_context(
        f"/?email={email}&token_cache_key={token_cache_key}"
    ) as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        assert "Team is not assigned" in str(resp.get_data())

    with cloud_storage._new_db_session() as session:
        session.add(
            MiroIntegrationStorageRecord.from_model_instance(
                MiroIntegration(
                    project.id, miro_org_id, "miro org", team_id, "miro team"
                )
            )
        )
        session.commit()

    with server.test_request_context(
        f"/?email=a@b.c&token_cache_key={token_cache_key}"
    ) as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        assert "User not found" in str(resp.get_data())

    cloud_authorizer._generate_new_token_with_claims.return_value = "jwt-token"
    with server.test_request_context(
        f"/?email={email}&token_cache_key={token_cache_key}"
    ) as ctx:
        resp = start_session_from_miro(
            ctx.request, cache, cloud_storage, cloud_authorizer
        )
        assert resp.json == {
            "access_token": "jwt-token",
            "email": "<EMAIL>",
            "org_name": "org1",
        }


@patch("mitzu.webapp.endpoints.miro.get_post_message_html_response")
def test_miro_login(
    post_message_mock, server: flask.Flask, dependencies: DEPS.Dependencies
):
    post_message_mock.return_value = "response"
    cloud_authorizer = MagicMock()
    cloud_authorizer.get_auth_token.return_value = None
    with server.test_request_context() as ctx:
        assert miro_login(ctx.request, cloud_authorizer) == "response"
        post_message_mock.assert_called_once_with(
            {"error": "First login at app.mitzu.io"}
        )

    cloud_authorizer.get_auth_token.return_value = "token"
    post_message_mock.reset_mock()
    with server.test_request_context() as ctx:
        assert miro_login(ctx.request, cloud_authorizer) == "response"
        post_message_mock.assert_called_once_with(
            {
                "access_token": "token",
                "email": "a@b.c",
                "org_name": "test",
                "projects": [
                    {"id": "sample_project_id", "name": "Sample ecommerce project"}
                ],
            }
        )


@patch("mitzu.webapp.endpoints.miro.get_error_image")
def test_miro_create_new_insight(
    mock_error_image,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    project: Project,
):
    mock_error_image.return_value = "error image"
    with server.test_request_context() as ctx:
        assert miro_create_new_insight(ctx.request).json == {
            "error": "400 Bad Request: project id or metric type is not set",
            "insight_id": None,
            "insight_image": "error image",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }
        mock_error_image.assert_called_once_with(
            "400 Bad Request: project id or metric type is not set"
        )

    mock_error_image.reset_mock()
    with server.test_request_context(f"/?project_id={project.id}") as ctx:
        assert miro_create_new_insight(ctx.request).json == {
            "error": "400 Bad Request: project id or metric type is not set",
            "insight_id": None,
            "insight_image": "error image",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }
        mock_error_image.assert_called_once_with(
            "400 Bad Request: project id or metric type is not set"
        )

    mock_error_image.reset_mock()
    project_storage = dependencies.get_project_storage(project.id)
    with server.test_request_context(
        f"/?project_id={project.id}&metric_type=segmentation"
    ) as ctx:
        resp = miro_create_new_insight(ctx.request).json
        saved_metric = project_storage.list_saved_metrics()[0]
        assert resp == {
            "error": None,
            "insight_id": saved_metric.id,
            "insight_image": "error image",
            "insight_title": "New Miro insight",
            "org_id": dependencies.org_id,
            "project_id": project.id,
        }
        mock_error_image.assert_called_with("Empty insight")
        mock_error_image.assert_called_with("Empty insight")


@patch("mitzu.webapp.endpoints.miro.get_error_image")
def test_miro_duplicate_insight(
    mock_error_image,
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    project: Project,
    saved_metric: WM.SavedMetric,
):
    mock_error_image.return_value = "error image"
    with server.test_request_context() as ctx:
        assert miro_duplicate_insight(ctx.request).json == {
            "error": "400 Bad Request: insight id is not set",
            "insight_id": None,
            "insight_image": "error image",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }
        mock_error_image.assert_called_once_with(
            "400 Bad Request: insight id is not set"
        )

    mock_error_image.reset_mock()
    with server.test_request_context(f"/?insight_id={saved_metric.id}") as ctx:
        assert miro_duplicate_insight(ctx.request).json == {
            "error": "project not found for insight",
            "insight_id": None,
            "insight_image": "error image",
            "insight_title": None,
            "org_id": None,
            "project_id": None,
        }
        mock_error_image.assert_called_once_with("project not found for insight")

    mock_error_image.reset_mock()
    project_storage = dependencies.get_project_storage(project.id)
    project_storage.set_saved_metric(saved_metric)
    with server.test_request_context(f"/?insight_id={saved_metric.id}") as ctx:
        resp = miro_duplicate_insight(ctx.request).json
        assert isinstance(resp, dict)
        assert resp == {
            "error": None,
            "insight_id": resp["insight_id"],  # random string
            "insight_image": "",
            "insight_title": "test_saved_metric (copy)",
            "org_id": "test",
            "project_id": project.id,
        }
        new_saved_metric = [
            sm
            for sm in project_storage.list_saved_metrics()
            if sm.id != saved_metric.id
        ][0]
        assert resp == {
            "error": None,
            "insight_id": new_saved_metric.id,
            "insight_image": "",
            "insight_title": "test_saved_metric (copy)",
            "org_id": dependencies.org_id,
            "project_id": project.id,
        }


@freeze_time("2024-12-23T11:22:33")
@patch("mitzu.webapp.endpoints.miro.create_unique_id", lambda: "task-id")
def test_miro_refresh_insight(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    project: Project,
    saved_metric: WM.SavedMetric,
):
    worker_pool = ThreadPoolExecutor(max_workers=1)
    with server.test_request_context(
        "/",
        data="{}",
        content_type="application/json",
    ) as ctx:
        assert miro_refresh_insight(ctx.request, worker_pool).json == {
            "error": "400 Bad Request: insight id is not set",
            "state": None,
            "task_id": None,
        }

    with server.test_request_context(
        "/",
        data=json.dumps({"insight_id": saved_metric.id}),
        content_type="application/json",
    ) as ctx:
        assert miro_refresh_insight(ctx.request, worker_pool).json == {
            "error": "project not found for insight",
            "state": None,
            "task_id": None,
        }

    project_storage = dependencies.get_project_storage(project.id)
    project_storage.set_saved_metric(saved_metric)
    with server.test_request_context(
        "/",
        data=json.dumps({"insight_id": saved_metric.id}),
        content_type="application/json",
    ) as ctx:
        resp = miro_refresh_insight(ctx.request, worker_pool).json
        assert resp == {
            "error": None,
            "state": "queued",
            "task_id": "task-id",
        }

    task_storage = TaskStorage("task-id", dependencies.storage._session_manager)
    user = dependencies.user
    assert user is not None
    assert task_storage.get_task() == TSM.RefreshInsightTask(
        task_id="task-id",
        source=TSM.TaskSource(
            org_id="test",
            project_id="sample_project_id",
            user_id=user.id,
            user_email="a@b.c",
            user_role=WM.Role.MEMBER,
            org_name="test",
            url="/",
            tracking_context=TrackingContext(
                user_id=user.id,
                user_email="a@b.c",
                org_id="test",
                org_name="test",
                eventn_id=None,
                user_agent=None,
                ga_id=None,
            ),
        ),
        state=TSM.TaskState.QUEUED,
        created_at=datetime(2024, 12, 23, 11, 22, 33),
        error=None,
        insight_id="test_sm",
    )


def test_miro_task_status(
    server: flask.Flask,
    dependencies: DEPS.Dependencies,
    project: Project,
):
    task_id = "task-id"
    insight_id = "insight-id"
    assert dependencies.user is not None

    with server.test_request_context(
        "/",
        data="{}",
        content_type="application/json",
    ) as ctx:
        assert miro_task_status(ctx.request).json == {
            "error": "400 Bad Request: task id is not set",
            "state": None,
            "task_id": None,
        }

    with server.test_request_context(
        f"/?task_id={task_id}",
    ) as ctx:
        assert miro_task_status(ctx.request).json == {
            "error": "Task not found",
            "state": None,
            "task_id": None,
        }

    refresh_insight_task = TSM.RefreshInsightTask(
        task_id=task_id,
        source=TSM.TaskSource(
            org_id=dependencies.org_id or "",
            project_id=project.id,
            user_id=dependencies.user.id,
            user_email=dependencies.user.email,
            user_role=dependencies.user.role,
            org_name=dependencies.org_name or "",
            url="/path",
            tracking_context=dependencies.get_tracking_context(),
        ),
        insight_id=insight_id,
        state=TSM.TaskState.QUEUED,
        created_at=datetime.now(),
        error=None,
    )
    dependencies.storage.submit_new_task(refresh_insight_task)

    with server.test_request_context(
        f"/?task_id={task_id}",
    ) as ctx:
        assert miro_task_status(ctx.request).json == {
            "error": None,
            "state": "queued",
            "task_id": task_id,
        }

    task_storage = TaskStorage(task_id, dependencies.storage._session_manager)
    task_storage.mark_task_finished("error")

    with server.test_request_context(
        f"/?task_id={task_id}",
    ) as ctx:
        assert miro_task_status(ctx.request).json == {
            "error": "error",
            "state": "finished",
            "task_id": task_id,
        }
