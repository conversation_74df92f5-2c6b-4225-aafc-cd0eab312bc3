from datetime import datetime, timedelta
from unittest.mock import ANY, MagicMock, patch

import flask

import mitzu.webapp.dependencies as DEPS
from mitzu.webapp.auth.model import (
    LAST_WORKSPACE_ID_COOKIE_NAME,
)
from mitzu.webapp.endpoints.stripe import (
    handle_stripe_event,
    redirect_back_to_billing,
)
from mitzu.webapp.service.stripe_service import (
    Customer,
    Subscription,
)


@patch("mitzu.webapp.endpoints.stripe.StripeService")
@patch("mitzu.webapp.endpoints.stripe.DEPS")
def test_handle_stripe_event(
    deps_mock, ss_mock, server: flask.Flask, dependencies: DEPS.Dependencies
):
    customer_id = "customer_id"
    payload = b"xxx"
    subscription_id = "subscription_id"
    expires_at = datetime.now() + timedelta(days=4)
    orig_expires_at = (
        dependencies.get_subscription_service().get_org_sub_expiration_date()
    )

    deps_mock.Dependencies.get.return_value = dependencies
    event_mock = MagicMock()
    ss_mock.return_value = ss_mock

    # parsing error
    ss_mock.get_event_from_request.return_value = None
    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_not_called()
        deps_mock.Dependencies.get.assert_not_called()

    # customer is missing from the event
    ss_mock.get_event_from_request.return_value = event_mock
    event_mock.data.object = {
        "customer": None,
    }

    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_not_called()
        deps_mock.Dependencies.get.assert_not_called()

    # customer does not exist
    ss_mock.get_event_from_request.return_value = event_mock
    ss_mock.get_customer.return_value = None
    event_mock.data.object = {
        "customer": customer_id,
    }

    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_called_once_with(customer_id)
        deps_mock.Dependencies.get.assert_not_called()

    # customer does not have any subscriptions
    ss_mock.reset_mock()
    ss_mock.get_event_from_request.return_value = event_mock
    ss_mock.get_customer.return_value = Customer(
        id=customer_id,
        email="a@b.c",
        subscription=None,
        is_allowed_to_subscribe=True,
    )
    event_mock.data.object = {
        "customer": customer_id,
    }

    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_called_once_with(customer_id)
        deps_mock.Dependencies.get.assert_not_called()

    # org not assigned to the customer
    ss_mock.reset_mock()
    ss_mock.get_event_from_request.return_value = event_mock
    ss_mock.get_customer.return_value = Customer(
        id=customer_id,
        email="a@b.c",
        subscription=Subscription(
            id=subscription_id,
            status="active",
            seats=10,
            cancel_at=expires_at,
            expires_at=expires_at,
        ),
        is_allowed_to_subscribe=True,
    )
    event_mock.data.object = {
        "customer": customer_id,
    }

    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_called_once_with(customer_id)
        deps_mock.Dependencies.get.assert_called_once()

    assert (
        dependencies.get_subscription_service().get_org_sub_expiration_date()
        == orig_expires_at
    )

    # org is updated properly
    stripe_storage = dependencies.get_subscription_storage(customer_id)
    assert dependencies.org_id is not None  # linter
    stripe_storage.set_customer_id(dependencies.org_id)
    deps_mock.reset_mock()
    ss_mock.reset_mock()
    ss_mock.get_event_from_request.return_value = event_mock
    ss_mock.get_customer.return_value = Customer(
        id=customer_id,
        email="a@b.c",
        subscription=Subscription(
            id=subscription_id,
            status="active",
            seats=10,
            cancel_at=expires_at,
            expires_at=expires_at,
        ),
        is_allowed_to_subscribe=True,
    )
    event_mock.data.object = {
        "customer": customer_id,
    }

    with server.test_request_context(data=payload):
        resp = handle_stripe_event(flask.request)
        assert resp.status_code == 200

        ss_mock.get_event_from_request.assert_called_with(payload, ANY)
        ss_mock.get_customer.assert_called_once_with(customer_id)
        deps_mock.Dependencies.get.assert_called_once()

    assert (
        dependencies.get_subscription_service().get_org_sub_expiration_date()
        == expires_at + timedelta(days=7)
    )
    assert dependencies.get_subscription_service().is_allowed_to_run_queries() is True


def test_redirect_back_to_billing(server: flask.Flask):
    with server.test_request_context() as ctx:
        resp = redirect_back_to_billing(ctx.request)
        assert resp.status_code == 307
        assert resp.headers.get("Location") == "http://localhost:8082"

    with server.test_request_context(
        headers={"Cookie": f"{LAST_WORKSPACE_ID_COOKIE_NAME}=project-id"}
    ) as ctx:
        resp = redirect_back_to_billing(ctx.request)
        assert resp.status_code == 307
        assert (
            resp.headers.get("Location")
            == "http://localhost:8082/projects/project-id/organisation?tab=subscription"
        )
