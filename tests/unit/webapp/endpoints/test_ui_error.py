from unittest.mock import patch

import flask
import pytest

from mitzu import __version__
from mitzu.webapp.endpoints.ui_error import handle_ui_error


@patch("mitzu.webapp.endpoints.ui_error.CloudAuthorizer.get")
def test_handle_ui_error_unauthorized(ca_mock, server: flask.Flask):
    ca_mock.return_value = ca_mock
    ca_mock.get_auth_token.return_value = None

    with server.test_request_context(
        json={"error": "ui-error", "message": "something went wrong"},
    ) as ctx:
        assert handle_ui_error(ctx.request) == {"backend_version": "unauthorized"}


@patch("mitzu.webapp.endpoints.ui_error.CloudAuthorizer.get")
def test_handle_ui_error_backend_error(ca_mock, server: flask.Flask):
    ca_mock.return_value = ca_mock
    ca_mock.get_auth_token.return_value = "TOKEN"
    ca_mock.parse_auth_token.return_value = "USER"

    with server.test_request_context(
        json={"error": "ui-error", "message": "something went wrong"},
    ) as ctx:
        assert handle_ui_error(ctx.request) == {"backend_version": __version__}


@pytest.mark.parametrize(
    "is_shared,expected_version",
    [
        (True, __version__),
        (False, "unreachable_dashboard"),
    ],
)
@patch("mitzu.webapp.endpoints.ui_error.OM.OrgManager.get")
@patch("mitzu.webapp.endpoints.ui_error.CloudAuthorizer.get")
def test_handle_ui_error_private_dashboard(
    ca_mock, om_mock, server: flask.Flask, is_shared: bool, expected_version: str
):
    ca_mock.return_value = ca_mock
    ca_mock.get_auth_token.return_value = None

    om_mock.return_value = om_mock
    om_mock.is_dashboard_shared.return_value = is_shared

    with server.test_request_context(
        json={
            "error": "ui-error",
            "message": "something went wrong",
            "pathname": "/shared-dashboards/dashboard-id",
        },
    ) as ctx:
        assert handle_ui_error(ctx.request) == {"backend_version": expected_version}
        assert om_mock.is_dashboard_shared.has_been_called_once_with("dashboard-id")


@pytest.mark.parametrize(
    "is_shared,expected_version",
    [
        (True, __version__),
        (False, "unreachable_insight"),
    ],
)
@patch("mitzu.webapp.endpoints.ui_error.OM.OrgManager.get")
@patch("mitzu.webapp.endpoints.ui_error.CloudAuthorizer.get")
def test_handle_ui_error_shared_insight(
    ca_mock, om_mock, server: flask.Flask, is_shared: bool, expected_version: str
):
    ca_mock.return_value = ca_mock
    ca_mock.get_auth_token.return_value = None

    om_mock.return_value = om_mock
    om_mock.is_insight_shared.return_value = is_shared

    with server.test_request_context(
        json={
            "error": "ui-error",
            "message": "something went wrong",
            "pathname": "/shared-insights/insight-id",
        },
    ) as ctx:
        assert handle_ui_error(ctx.request) == {"backend_version": expected_version}
        assert om_mock.is_insight_shared.has_been_called_once_with("insight-id")
