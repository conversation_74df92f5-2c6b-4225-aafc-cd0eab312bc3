from datetime import datetime
from unittest.mock import MagicMock, patch

import flask
import pytest
import werkzeug

import mitzu.webapp.model as WM
from mitzu.webapp.endpoints.auth import (
    handle_enterprise_login,
    reload_auth_config,
)

app = flask.Flask(__name__)


@patch("mitzu.webapp.endpoints.auth.DEPS")
def test_auth_config_reload_redirects_when_not_logged_in(deps_mock):
    deps = MagicMock()
    deps.user = None
    deps_mock.Dependencies.get.return_value = deps
    with app.test_request_context():
        resp = reload_auth_config()
        assert resp.status_code == 307


@patch("mitzu.webapp.endpoints.auth.DEPS")
def test_auth_config_reload_redirects_when_user_is_not_an_employee(deps_mock):
    deps = MagicMock()
    deps.user = WM.User(
        email="<EMAIL>",
        org_id="org_id",
        invited_at=datetime.now(),
    )
    deps_mock.Dependencies.get.return_value = deps
    with app.test_request_context():
        resp = reload_auth_config()
        assert resp.status_code == 307


@patch("mitzu.webapp.endpoints.auth.DEPS")
@patch("mitzu.webapp.endpoints.auth.AuthConfigManager")
def test_auth_config_reload_returns_success(acm_mock, deps_mock):
    deps = MagicMock()
    deps.user = WM.User(
        email="<EMAIL>",
        org_id="org_id",
        invited_at=datetime.now(),
    )
    deps_mock.Dependencies.get.return_value = deps

    with app.test_request_context():
        resp = reload_auth_config()
        assert resp.status_code == 200
        acm_mock.clear_cached_config.assert_called_once()


def test_handle_enterprise_login_with_idp():
    request = MagicMock()
    email = "<EMAIL>"
    request.json = {"email": email}

    org_manager = MagicMock()
    auth_config = MagicMock()
    auth_config.identity_provider_id = "idp-id"
    org_manager.get_oauth_config_for_email_domain.return_value = auth_config

    resp = handle_enterprise_login(request, org_manager)

    assert resp == {
        "login_url": "/auth/redirect-to-login/idp-id",
    }

    org_manager.get_oauth_config_for_email_domain.assert_called_once_with("domain.com")


def test_handle_enterprise_login_without_idp():
    request = MagicMock()
    email = "<EMAIL>"
    request.json = {"email": email}

    org_manager = MagicMock()
    auth_config = MagicMock()
    auth_config.identity_provider_id = None
    org_manager.get_oauth_config_for_email_domain.return_value = auth_config

    resp = handle_enterprise_login(request, org_manager)

    assert resp == {
        "login_url": "http://localhost:8082/auth/redirect-to-login",
    }

    org_manager.get_oauth_config_for_email_domain.assert_called_once_with("domain.com")


def test_handle_enterprise_login_handles_missing_email():
    request = MagicMock()
    request.json = {}
    org_manager = MagicMock()

    with pytest.raises(werkzeug.exceptions.BadRequest):
        handle_enterprise_login(request, org_manager)


def test_handle_enterprise_mailformed_email():
    request = MagicMock()
    request.json = {"email": "emailatsomething.com"}
    org_manager = MagicMock()

    with pytest.raises(werkzeug.exceptions.BadRequest):
        handle_enterprise_login(request, org_manager)


def test_handle_enterprise_login_wit_idp_but_it_does_not_exists():
    request = MagicMock()
    email = "<EMAIL>"
    request.json = {"email": email}

    org_manager = MagicMock()
    org_manager.get_oauth_config_for_email_domain.return_value = None

    with pytest.raises(werkzeug.exceptions.BadRequest):
        handle_enterprise_login(request, org_manager)
