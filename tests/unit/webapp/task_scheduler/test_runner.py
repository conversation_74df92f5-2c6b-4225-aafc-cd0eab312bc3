from unittest.mock import MagicMock

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
from mitzu.project_discovery import DiscoveryR<PERSON>ult
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)
from mitzu.webapp.task_scheduler.model import (
    EDTIndexingTask,
    TaskSource,
)
from mitzu.webapp.task_scheduler.runner import TaskRunner
from tests.helper import setup_project
from tests.in_memory_cache import In<PERSON><PERSON>ory<PERSON>ache


def test_test_runner(dependencies: DEPS.Dependencies, project: M.Project):
    cache = InMemoryCache()
    runner = TaskRunner(dependencies.storage._session_manager, cache)
    setup_project(dependencies.storage, project)

    edt_id = project.event_data_tables[0].id
    task = EDTIndexingTask(
        "task_id",
        source=TaskSource(
            "test",
            project.id,
            "user-id",
            "<EMAIL>",
            WM.Role.MEMBER,
            "org_name",
            "url",
            TrackingContext.empty(),
        ),
        event_data_table_ids=[edt_id],
        remove_orphan_fields=True,
        remove_orphan_events=False,
    )
    set_progress = MagicMock()

    result = runner.run_edt_indexing_task(task, set_progress)
    assert result == DiscoveryResult(
        discovered_tables=1,
        skipped_tables=0,
        event_definitions=result.event_definitions,
        dimension_definitions={},
        errors={},
    )

    assert set(result.event_definitions.keys()) == {
        M.EventNamePath("blog_events", "page_visit")
    }
