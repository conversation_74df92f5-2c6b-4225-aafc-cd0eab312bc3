from datetime import datetime
from unittest.mock import MagicMock

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
from mitzu.project_discovery import DiscoveryResult
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)
from mitzu.webapp.task_scheduler.model import (
    EDTIndexingTask,
    TaskSource,
    TaskState,
)
from mitzu.webapp.task_scheduler.runner import TaskRunner
from tests.helper import setup_project
from tests.in_memory_cache import InMemoryCache


def test_test_runner(dependencies: DEPS.Dependencies, project: M.Project):
    cache = InMemoryCache()
    runner = TaskRunner(dependencies.storage._session_manager, cache)
    setup_project(dependencies.storage, project)

    edt_id = project.event_data_tables[0].id
    task = EDTIndexingTask(
        "task_id",
        source=TaskSource(
            "test",
            project.id,
            "user-id",
            "<EMAIL>",
            WM.Role.MEMBER,
            "org_name",
            "url",
            TrackingContext.empty(),
        ),
        event_data_table_id=edt_id,
        event_data_table_hash="hash",
        remove_orphan_fields=True,
        remove_orphan_events=False,
        state=TaskState.QUEUED,
        created_at=datetime.now(),
        error=None,
    )
    set_progress = MagicMock()
    dependencies.storage.submit_new_task(task)

    result = runner.run_task(task.task_id, set_progress)
    assert result == DiscoveryResult(
        discovered_tables=1,
        skipped_tables=0,
        event_definitions=result.event_definitions,
        dimension_definitions={},
        errors={},
    )

    assert set(result.event_definitions.keys()) == {
        M.EventNamePath("blog_events", "page_visit")
    }
