from datetime import datetime
from unittest.mock import MagicMock

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
from mitzu.project_discovery import DiscoveryResult
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)
from mitzu.webapp.task_scheduler.model import (
    EDTIndexingTask,
    RefreshInsightTask,
    TaskSource,
    TaskState,
)
from mitzu.webapp.task_scheduler.runner import TaskRunner
from tests.helper import (
    setup_discovered_project,
    setup_project,
)
from tests.in_memory_cache import InMemoryCache


def test_test_runner_indexing_task(dependencies: DEPS.Dependencies, project: M.Project):
    cache = InMemoryCache()
    runner = TaskRunner(dependencies.storage._session_manager, cache)
    setup_project(dependencies.storage, project)

    edt_id = project.event_data_tables[0].id
    task = EDTIndexingTask(
        "task_id",
        source=TaskSource(
            "test",
            project.id,
            "user-id",
            "<EMAIL>",
            WM.Role.MEMBER,
            "org_name",
            "url",
            TrackingContext.empty(),
        ),
        event_data_table_id=edt_id,
        event_data_table_hash="hash",
        remove_orphan_fields=True,
        remove_orphan_events=False,
        state=TaskState.QUEUED,
        created_at=datetime.now(),
        error=None,
    )
    set_progress = MagicMock()
    dependencies.storage.submit_new_task(task)

    result = runner.run_task(task.task_id, set_progress)
    assert result == DiscoveryResult(
        discovered_tables=1,
        skipped_tables=0,
        event_definitions=result.event_definitions,
        dimension_definitions={},
        errors={},
    )

    assert set(result.event_definitions.keys()) == {
        M.EventNamePath("blog_events", "page_visit")
    }


def test_test_runner_refresh_insight_task(
    dependencies: DEPS.Dependencies,
    discovered_project: M.DiscoveredProject,
    saved_metric: WM.SavedMetric,
):
    assert dependencies.user is not None
    cache = InMemoryCache()
    runner = TaskRunner(dependencies.storage._session_manager, cache)
    project = discovered_project.project
    setup_discovered_project(dependencies.storage, discovered_project)
    project_storage = dependencies.get_project_storage(project.id)
    project_storage.set_saved_metric(saved_metric)

    task = RefreshInsightTask(
        task_id="task-id",
        source=TaskSource(
            org_id=dependencies.org_id or "",
            project_id=project.id,
            user_id=dependencies.user.id,
            user_email=dependencies.user.email,
            user_role=dependencies.user.role,
            org_name=dependencies.org_name or "",
            url="/path",
            tracking_context=dependencies.get_tracking_context(),
        ),
        insight_id=saved_metric.id,
        state=TaskState.QUEUED,
        created_at=datetime.now(),
        error=None,
    )
    dependencies.storage.submit_new_task(task)
    runner.run_task(task.task_id, lambda *args: None)

    updated_saved_metric = project_storage.get_saved_metric(saved_metric.id)
    assert updated_saved_metric.image_base64 != saved_metric.image_base64
    assert updated_saved_metric.small_base64 != saved_metric.small_base64
    assert updated_saved_metric.last_updated_at == saved_metric.last_updated_at
    insight_result = project_storage.get_insight_result(
        saved_metric.id, saved_metric.metric_json
    )
    assert insight_result is not None
    assert insight_result.last_updated_at != saved_metric.last_updated_at
