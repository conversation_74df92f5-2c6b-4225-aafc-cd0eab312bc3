import os
from datetime import datetime

import pytest_alembic
import sqlalchemy as <PERSON>


def test_get_errors_from_tasks(
    alembic_engine,
    alembic_runner: pytest_alembic.MigrationContext,
):
    alembic_runner.migrate_up_before("ce850353399e")

    org_id = "org_id"
    project_id = "project_id"
    now = datetime.now()
    owner_id = "owner-id"
    user_id = "user-id"
    now = datetime.now()
    alembic_runner.insert_into(
        "organisations", {"org_id": org_id, "name": "testorg", "created_at": now}
    )
    alembic_runner.insert_into(
        "projects",
        {
            "project_id": project_id,
            "org_id": org_id,
            "is_sample": False,
            "is_debug_enabled": False,
        },
    )

    alembic_runner.insert_into(
        "users",
        [
            {
                "user_id": user_id,
                "email": "a@b.c",
                "identity_provider_id": None,
                "is_super_admin": False,
                "log_performance": False,
                "can_create_second_org": False,
            }
        ],
    )
    alembic_runner.insert_into(
        "user_org_memberships",
        [
            {
                "org_id": org_id,
                "user_id": user_id,
                "role": "ADMIN",
                "membership_id": owner_id,
                "is_org_owner": True,
                "invited_at": now,
            }
        ],
    )

    edt_skeleton = {
        "project_id": project_id,
        "table_name": "table-1",
        "event_time_field": "{}",
        "event_time_field_type": "timestamp",
        "user_id_field": "{}",
        "schema": "public",
        "event_name_field": None,
        "date_partition_field": None,
        "date_partition_field_type": None,
        "ignored_fields": "[]",
        "foreign_keys": "{}",
        "disabled": False,
    }

    now = datetime.now()
    is_sqlite = os.getenv("DB_ENGINE") == "sqlite"
    alembic_runner.insert_into(
        "event_data_tables",
        [
            {
                **edt_skeleton,
                "event_data_table_id": "edt-not-indexed",
                "last_indexed": None,
                "last_indexed_hash": None,
                "last_indexing_error": None,
            },
            {
                **edt_skeleton,
                "event_data_table_id": "edt-indexed",
                "last_indexed": now,
                "last_indexed_hash": "hash",
                "last_indexing_error": None,
            },
            {
                **edt_skeleton,
                "event_data_table_id": "edt-indexed-with-errors",
                "last_indexed": now,
                "last_indexed_hash": "hash2",
                "last_indexing_error": "error",
            },
        ],
    )

    alembic_runner.migrate_up_one()
    with alembic_engine.connect() as conn:
        result = conn.execute(
            SA.text(
                """
                SELECT edt.event_data_table_id,
                    task_id, org_id, tasks.project_id, user_id, user_email,
                    user_role, org_name, url, tracking_context,
                    task_type, task_details, state, created_at, error, resource_id
                FROM event_data_tables edt
                LEFT JOIN tasks ON edt.last_index_task_id = tasks.task_id
                ORDER BY edt.event_data_table_id
                """
            ),
        ).fetchall()
        assert result == [
            (
                "edt-indexed",
                "migration-edt-indexed",
                "org_id",
                "project_id",
                "user-id",
                "a@b.c",
                "ADMIN",
                "testorg",
                "migration",
                "{}",
                "edt_indexing",
                '{"event_data_table_id": "edt-indexed", "event_data_table_hash": "hash"}',
                "finished",
                str(now) if is_sqlite else now,
                None,
                "edt-indexed",
            ),
            (
                "edt-indexed-with-errors",
                "migration-edt-indexed-with-errors",
                "org_id",
                "project_id",
                "user-id",
                "a@b.c",
                "ADMIN",
                "testorg",
                "migration",
                "{}",
                "edt_indexing",
                '{"event_data_table_id": "edt-indexed-with-errors", "event_data_table_hash": "hash2"}',
                "finished",
                str(now) if is_sqlite else now,
                "error",
                "edt-indexed-with-errors",
            ),
            (
                "edt-not-indexed",
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
            ),
        ]

    alembic_runner.migrate_down_one()
