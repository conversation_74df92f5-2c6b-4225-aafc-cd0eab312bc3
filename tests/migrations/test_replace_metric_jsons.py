from datetime import datetime

import pytest_alembic
import sqlalchemy as SA

import mitzu.model as M
import mitzu.webapp.serialization as SE

chart_types = [
    M.SimpleChartType.BAR,
    M.SimpleChartType.STACKED_BAR,
    M.SimpleChartType.LINE,
    M.SimpleChartType.STACKED_AREA,
    M.SimpleChartType.HEATMAP,
    M.SimpleChartType.GEO_COUNTRY,
    M.SimpleChartType.PERCENTAGE_STACKED_BAR,
    M.SimpleChartType.PERCENTAGE_STACKED_AREA,
    M.SimpleChartType.SANKEY,
    M.SimpleChartType.NUMBER,
    M.SimpleChartType.PIE,
    M.SimpleChartType.HORIZONTAL_BAR,
]


def test_replace_metric_jsons(
    alembic_engine,
    alembic_runner: pytest_alembic.MigrationContext,
):
    alembic_runner.migrate_up_before("5d77265e024d")

    org_id = "org_id"
    project_id = "project_id"
    now = datetime.now()
    owner_id = "owner-id"
    user_id = "user-id"
    now = datetime.now()
    alembic_runner.insert_into(
        "organisations", {"org_id": org_id, "name": "testorg", "created_at": now}
    )
    alembic_runner.insert_into(
        "projects",
        {
            "project_id": project_id,
            "org_id": org_id,
            "is_sample": False,
            "is_debug_enabled": False,
        },
    )
    alembic_runner.insert_into(
        "users",
        [
            {
                "user_id": user_id,
                "email": "a@b.c",
                "identity_provider_id": None,
                "is_super_admin": False,
                "log_performance": False,
                "can_create_second_org": False,
            }
        ],
    )
    alembic_runner.insert_into(
        "user_org_memberships",
        [
            {
                "org_id": org_id,
                "user_id": user_id,
                "role": "ADMIN",
                "membership_id": owner_id,
                "is_org_owner": True,
                "invited_at": now,
            }
        ],
    )

    original_saved_metric = "eNqVkEsOwyAMRK-CvM6i2fYqVRURYhEUPhVQoghx92JHldruuoIZP9sDFVyGqwAVfMGYTPAwCEioU3dvFbAfFRIzsw16woI-J4LQk_mQGqdiksnQ2iC-O7j4V4daUW3h-cm_rU7fB0rKuJ0XKo7CBZ9XorMmY5EHCadVV-OFGngwmUry1RqPJCPSI6Hni8eZkqH9nLsjbqSl5n3y95taay_iB16u"  # noqa: E501
    expected_saved_metric = "eNqFT80KwyAMfpWR8w7rda8yRrGaWqmaUW1LKX33JcqgDMYu6vebuIMmuF92ULZemS_m4oJTchThuDKsrHcRQSA5w7hXPqHAVcTmsiKOIvvOVCJQzIMwwWpmmhs_J0wiItdvLZ8xiyGRPRVmAWDUJtLcC5gTToKWUNLG5bJY-N5WutDKiMcOWH6EUTwvZbFdXHJ1Xsl1nmzdIcHBdf8ThfuZ0APqkeaT_8OI-3m8AZpQbPo="  # noqa: E501
    # fmt: off
    alembic_runner.insert_into(
        "saved_metrics",
        [
            {
                "saved_metric_id": "sm-1",
                "project_id": project_id,
                "name": "saved metric",
                "description": None,
                "image_base64": "data:image/svg+xml;base64,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",  # noqa: E501
                "small_image_base64": "data:image/svg+xml;base64,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",  # noqa: E501
                "created_at": now,
                "last_updated_at": now,
                "owner": owner_id,
                "metric_json": original_saved_metric,
            },
        ],
    )

    alembic_runner.insert_into(
        "insight_results",
        [
            {
                "saved_metric_id": "sm-1",
                "metric_json": original_saved_metric,
                "x_axis_label": "x",
                "y_axis_label": "y",
                "color_label": "color",
                "yaxis_ticksuffix": "a",
                "hover_mode": "hover",
                "chart_type": "chart",
                "dataframe": "dataframe",
                "dataframe_version": 2,
                "all_groups": 3,
                "last_updated_at": now,
            },
        ],
    )
    # fmt: on

    alembic_runner.migrate_up_one()

    with alembic_engine.connect() as conn:
        result = conn.execute(
            SA.text(
                """
                SELECT saved_metric_id, metric_json
                FROM saved_metrics
                """
            ),
        ).fetchall()
        assert result == [
            ("sm-1", expected_saved_metric),
        ]
        assert SE.from_compressed_string(result[0][1]) == SE.from_compressed_string(
            original_saved_metric
        )

    with alembic_engine.connect() as conn:
        result = conn.execute(
            SA.text(
                """
                SELECT saved_metric_id, metric_json
                FROM insight_results
                """
            ),
        ).fetchall()
        assert result == [
            ("sm-1", expected_saved_metric),
        ]
        assert SE.from_compressed_string(result[0][1]) == SE.from_compressed_string(
            original_saved_metric
        )

    alembic_runner.migrate_down_one()
