POETRY := $(shell command -v poetry 2> /dev/null)
NPM := $(shell command -v npm 2> /dev/null)

-include .env
export $(shell sed 's/=.*//' .env)

clean:
	$(POETRY) run pyclean mitzu release tests 
	rm -rf dist
	rm -rf htmlcov
	rm -rf .pytest_cache
	rm -rf .mypy_cache
	rm -rf .ipynb_checkpoints
	rm -f coverage.xml
	rm -rf .hypothesis
	rm -rf storage

init: init_js generate_docs_enum
	$(POETRY) install --all-extras
	$(POETRY) self add "poetry-dynamic-versioning[plugin]"
	$(POETRY) run vendoring sync
	git restore mitzu/vendor/.gitignore  # we don't want to commit the vendor sources
	cp scripts/pre-commit .git/hooks/pre-commit
	$(POETRY) run python3 scripts/generate_stubs.py

init_js:
	npm install

init_docs:
	cd docs && npm install

serve_docs:
	cd docs && npm run start

build_docs:
	cd docs && npm run build

serve_docs_prod: build_docs
	cd docs && npm run serve

deploy_docs:
	cd docs && npm run deploy

generate_docs_enum:
	$(POETRY) run python scripts/generate_docs_enum.py
	
format: ## formats all python code
	$(POETRY) run autoflake \
		-r -i \
		--remove-all-unused-imports \
		--remove-unused-variables \
		--expand-star-imports \
		--exclude mitzu/vendor \
		mitzu tests/ release/ samples/ scripts/ infra/ utils/
	$(POETRY) run black --exclude mitzu/vendor mitzu tests release samples scripts infra utils
	$(POETRY) run isort mitzu tests release samples scripts infra utils
	$(NPM) run prettier:write

lint: ## lints and checks formatting all python code
	$(POETRY) run isort --check-only mitzu tests release samples scripts infra
	$(POETRY) run black --exclude .dbs --exclude mitzu/vendor --check mitzu tests release samples scripts infra
	$(POETRY) run flake8 --exclude mitzu/vendor mitzu tests release samples scripts infra
	$(POETRY) run mypy mitzu tests release samples scripts infra
	$(NPM) run prettier:check

ingest_sample_data_postgres:
	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample ecommerce --target postgres --overwrite

	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample saas --target postgres --overwrite

ingest_discovery_perf_data_postgres:
	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample discovery_performance --target postgres --overwrite

ingest_sample_data_clickhouse:
	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample ecommerce --target postgres --overwrite --flatten

	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample saas --target postgres --overwrite --flatten

	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample ecommerce --target clickhouse --overwrite

	$(POETRY) run python3 scripts/ingest_sample_data.py \
		--sample saas --target clickhouse --overwrite

generate_stubs:
	$(POETRY) run python3 scripts/generate_stubs.py

ingest_sample_data: ingest_sample_data_postgres ingest_sample_data_clickhouse ingest_discovery_perf_data_postgres
	@echo 'done'

reset_local_db:
	PGPASSWORD=test psql -h localhost -U test postgres -c "drop database if exists mitzu_local";
	PGPASSWORD=test psql -h localhost -U test postgres -c "create database mitzu_local";
	PGPASSWORD=test psql -h localhost -U test postgres -c "drop database if exists tracking";
	PGPASSWORD=test psql -h localhost -U test postgres -c "create database tracking";
	PGPASSWORD=test psql -h localhost -U test postgres -c "drop database if exists test";
	PGPASSWORD=test psql -h localhost -U test postgres -c "create database test";

test_units:
	$(POETRY) run pytest -svv -n $(or $(PYTEST_WORKERS),auto) --dist loadgroup tests/unit/

test_migrations_sqlite:
	DB_ENGINE=sqlite $(POETRY) run pytest -svv --test-alembic tests/migrations

test_migrations_postgres:
	DB_ENGINE=postgres $(POETRY) run pytest -svv --test-alembic tests/migrations

test_migrations: test_migrations_sqlite test_migrations_postgres
	@echo 'done'

test_browser:
	$(POETRY) run pytest -svv tests/browser

test_js:
	npm test

test_js_watch:
	npm run test-watch

test_js_miro:
	cd integrations/miro && npm test

lint_miro:
	cd integrations/miro && npm run prettier:check && npm run lint 

format_miro:
	cd integrations/miro && npm run prettier:write

test_hypothesis: ## runs all property based tests with a bigger example size
	HYPOTHESIS_MAX_EXAMPLES=100 $(POETRY) run pytest -sv $(shell grep -r "tests.unit.webapp.generators" tests/ 2>/dev/null | cut -d ":" -f 1)

test_integrations: wait_for_trino
	docker-compose -f docker/docker-compose.yml up -d --no-recreate
	$(POETRY) run pytest -sv --dist loadgroup tests/integration/ -n auto

test_project_creation_and_discovery:
	$(POETRY) run python3 scripts/create_example_project.py --project-dir . --overwrite-records --adapter postgresql

test_metrics:
	$(POETRY) run pytest -n auto --junit-xml metrics_junit.xml tests/metrics || true
	$(POETRY) run python scripts/format_saved_metrics_result.py --result-path metrics_junit.xml > metrics_result.html
	@echo "Test report generated: $(shell pwd)/metrics_result.html"

docker_test_down:
	rm -rf tests/.dbs/
	docker-compose -f docker/docker-compose.yml down

wait_for_trino:
	$(POETRY) run python3 scripts/wait_for_trino.py

docker_test_up:	
	docker-compose -f docker/docker-compose.yml up -d
	$(POETRY) run python3 scripts/wait_for_trino.py

docker_restart: docker_test_down docker_test_up
	@echo "Restarted docker containers"

new_migration: ## applies all migrations to an empty database and generates a new one based on the differences
	rm -rf alembic.db
	ALEMBIC_ENV=autogenerate $(POETRY) run alembic -c release/app/alembic.ini upgrade head
	ALEMBIC_ENV=autogenerate $(POETRY) run alembic -c release/app/alembic.ini revision --autogenerate -m "Autogenerated migration"
	$(POETRY) run autoflake -r -i --remove-all-unused-imports --remove-unused-variables --expand-star-imports release/app/migrations
	$(POETRY) run black release/app/migrations
	
test_coverage: wait_for_trino
	$(POETRY) run pytest \
		-n auto \
		--cov-config=.coveragerc \
		--cov=mitzu \
		--cov-report=html \
		--cov-report=xml \
		--ignore tests/migrations \
		--ignore tests/browser \
		--ignore tests/metrics \
		--dist loadgroup \
		tests/

test_matrix: wait_for_trino
	$(POETRY) run pytest tests/integration -m matrix -n auto --dist loadgroup

check: lint test_coverage test_migrations
	@echo 'done'

quick_check: lint
	$(POETRY) run python3 scripts/quick_check.py

dynamic_versioning:
	$(POETRY) dynamic-versioning

prepare_release_requirements: dynamic_versioning
	rm mitzu/vendor/.gitignore || true
	rm mitzu/webapp/.gitignore || true
	$(POETRY) build
	cp -r ./dist/ ./release/dist/
	poetry export \
		-E webapp \
		-E trinodwh \
		-E postgres \
		-E databricks \
		-E athena \
		-E snowflake \
		-E redshift \
		-E bigquery \
		--without-hashes \
		--format=requirements.txt > release/requirements.txt

prepare_metric_test_requirements: dynamic_versioning
	poetry export \
		--all-extras \
		--with dev \
		--without-hashes \
		--format=requirements.txt > tests/metrics/requirements.txt
	mkdir -p tests/metrics/tests
	cp tests/*.py tests/metrics/tests
	cp -r mitzu tests/metrics

# This make command is used for testing the SSO
serve_cognito_sso: migrate clear_redis
	cd release/app/ && \
	LOG_LEVEL=INFO \
	AUTH_BACKEND="cognito" \
	COGNITO_CLIENT_ID="1bqlja23lfmniv7bm703aid9o0" \
	COGNITO_CLIENT_SECRET="${COGNITO_CLIENT_SECRET}" \
	COGNITO_DOMAIN="signin.mitzu.io" \
	COGNITO_REGION="eu-west-1" \
	COGNITO_POOL_ID="eu-west-1_QkZu6BnVD" \
	HOME_URL="http://localhost:8082" \
	SECRET_ENCRYPTION_KEY="test" \
	$(POETRY) run gunicorn -b 0.0.0.0:8082 app:server --reload

serve_google_sso: migrate clear_redis
	cd release/app/ && \
	LOG_LEVEL=INFO \
	AUTH_BACKEND="google" \
	GOOGLE_CLIENT_ID="669095060108-42hhm4rgo8cjseumiu47saq2g8690ehh.apps.googleusercontent.com" \
	GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET}" \
	GOOGLE_PROJECT_ID="mitzu-test" \
	HOME_URL="http://localhost:8082" \
	SECRET_ENCRYPTION_KEY="test" \
	$(POETRY) run gunicorn -b 0.0.0.0:8082 app:server --reload

serve: migrate clear_redis
	cd release/app/ && \
	LOG_LEVEL=INFO \
	AUTH_BACKEND="cognito" \
	COGNITO_CLIENT_ID="1bqlja23lfmniv7bm703aid9o0" \
	COGNITO_CLIENT_SECRET="${COGNITO_CLIENT_SECRET}" \
	COGNITO_DOMAIN="signin.mitzu.io" \
	COGNITO_REGION="eu-west-1" \
	COGNITO_POOL_ID="eu-west-1_QkZu6BnVD" \
	HOME_URL="http://localhost:8082" \
	SECRET_ENCRYPTION_KEY="test" \
	TRACK_TO_POSTGRES="true" \
	$(POETRY) run gunicorn -b 0.0.0.0:8082 app:server --reload --workers=2

serve_miro:
	cd integrations/miro && npm run start

run: migrate clear_redis
	LOG_LEVEL=INFO \
	AUTH_BACKEND="cognito" \
	COGNITO_CLIENT_ID="1bqlja23lfmniv7bm703aid9o0" \
	COGNITO_CLIENT_SECRET="${COGNITO_CLIENT_SECRET}" \
	COGNITO_DOMAIN="signin.mitzu.io" \
	COGNITO_REGION="eu-west-1" \
	COGNITO_POOL_ID="eu-west-1_QkZu6BnVD" \
	HOME_URL="http://localhost:8050" \
	SECRET_ENCRYPTION_KEY="test" \
	TRACK_TO_POSTGRES="true" \
	$(POETRY) run python mitzu/webapp/webapp.py

migrate:
	$(POETRY) run python release/app/migrate.py

clear_redis:
	redis-cli flushall

create_clickhouse_dbs:
	curl -X POST "http://localhost:8123/" \
		-H "Authorization: Basic dGVzdDp0ZXN0" \
		-d "CREATE DATABASE sample_saas ENGINE = PostgreSQL('postgres:5432', 'sample_saas', 'test', 'test')"
	curl -X POST "http://localhost:8123/" \
		-H "Authorization: Basic dGVzdDp0ZXN0" \
		-d "CREATE DATABASE sample_ecommerce ENGINE = PostgreSQL('postgres:5432', 'sample_ecommerce', 'test', 'test')"
	curl -X POST "http://localhost:8123/" \
		-H "Authorization: Basic dGVzdDp0ZXN0" \
		-d "CREATE DATABASE sample_discovery_performance ENGINE = PostgreSQL('postgres:5432', 'sample_discovery_performance', 'test', 'test')"

setup_local_org: create_clickhouse_dbs
	LOCAL_ORG_ADMIN_EMAIL="${LOCAL_ORG_ADMIN_EMAIL}" \
	SECRET_ENCRYPTION_KEY="test" \
	$(POETRY) run python scripts/setup_local_org.py
	$(POETRY) run python scripts/fetch_relevance_data.py --database-type postgres

build: check
	$(POETRY) build

docker_build_latest: 	
	$(POETRY) build
	cp -r ./dist/ ./release/dist/
	poetry export \
		-E webapp \
		-E trinodwh \
		-E postgres \
		-E databricks \
		-E athena \
		-E snowflake \
		-E redshift \
		-E bigquery \
		--without-hashes \
		--format=requirements.txt > release/requirements.txt	
	docker build ./release \
	--platform "linux/amd64" \
	--build-arg MITZU_VERSION=$(shell poetry version -s) \
	--build-arg TRACKING_HOST=${TRACKING_HOST} \
	--build-arg TRACKING_API_KEY=${TRACKING_API_KEY} \
	--build-arg RUDDER_TRACKING_HOST=${RUDDER_TRACKING_HOST} \
	--build-arg RUDDER_TRACKING_API_KEY=${RUDDER_TRACKING_API_KEY} \
	-f ./release/Dockerfile \
	-t mitzuio/mitzu:$(shell poetry version -s) \
	-t mitzuio/mitzu:latest

docker_build_amd64_snapshot: 
	$(POETRY) build
	cp -r ./dist/ ./release/dist/
	poetry export \
		-E webapp \
		-E trinodwh \
		-E postgres \
		-E databricks \
		-E athena \
		-E snowflake \
		-E redshift \
		-E bigquery \
		--without-hashes \
		--format=requirements.txt > release/requirements.txt	
	docker build ./release \
	--platform "linux/amd64" \
	--build-arg MITZU_VERSION=$(shell poetry version -s) \
	--build-arg TRACKING_HOST=${TRACKING_HOST} \
	--build-arg TRACKING_API_KEY=${TRACKING_API_KEY}  \
	--build-arg RUDDER_TRACKING_HOST=${RUDDER_TRACKING_HOST} \
	--build-arg RUDDER_TRACKING_API_KEY=${RUDDER_TRACKING_API_KEY} \
	-f ./release/Dockerfile \
	-t mitzuio/mitzu:snapshot

docker_run_amd64_snapshot:	
	docker run  \
	-e KALEIDO_CONFIGS=""  \
	-e AUTH_ROOT_PASSWORD="testuser" \
	-p 8082:8080 mitzuio/mitzu:snapshot
