import datetime
from typing import List, Optional, Tuple

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.adapters.model import fix_col_index
from mitzu.constants import N_PER_A
from mitzu.helper import (
    DROP_OFF_PLACEHOLDER,
    NO_GROUP,
    STRING_SEPARATOR,
    get_segment_letter,
)
from mitzu.logger import LOGGER
from mitzu.visualization.common import (
    IS_COMPARISON_COL,
    ORIGINAL_DATETIME,
    SORTING_COL,
    extend_df_with_sorting_column,
    retention_period_label,
)
from mitzu.visualization.comparison import (
    get_finalized_label,
)
from mitzu.visualization.experimentation.bayesian_calculator import (
    calculate_bayesian_probabilities,
)
from mitzu.visualization.labels import get_segment_label
from mitzu.visualization.preprocessor.common import (
    ORIGINAL_GROUP_COL,
    fix_empty_group_by_cols,
    get_readable_group_by_values,
    should_handle_null_as_dropoff,
    subtitute_segment_labels_in_group_bys,
)
from mitzu.webapp.metric_context import MetricContext

CALCULATION_COL = "Calculation"
EVENT_COL_LABEL = "Event"
PROB_BEST_COL_LABEL = "Probability to be best"
DATETIME_COL = "__datetime__"
GROUP_COL = "__group__"
LABEL_COL = "__label__"
RETENTION_COL = "__retention_period__"
MODAL_EVENT_TIME_COL = "__modal_event_time_col__"
PROBABILITY_COL = "_prob_best"
DROP_OFF_LABEL = "<drop-off>"


def _rename_intermediary_column_names(
    name: str,
    is_event_list_modal: bool,
) -> str:
    if name == DATETIME_COL:
        return "Datetime"
    if name == GROUP_COL:
        return "Group"
    if name == RETENTION_COL:
        return "Retention period"
    if name == LABEL_COL:
        return "Label" if not is_event_list_modal else "Event"
    if name == MODAL_EVENT_TIME_COL:
        return "Event Time"

    return name  # it's not an intermediary column


def _normalize_column_name(
    name: str,
    unique_field_name: str,
    group_by_col_names: List[Tuple[M.FieldPath, str]],
    metric: WM.WebappMetric,
) -> str:
    if name == GA.AGG_VALUE_COL:
        return CALCULATION_COL
    if name == GA.DATETIME_COL:
        return DATETIME_COL
    if name == GA.GROUP_COL:
        return group_by_col_names[0][1] if len(group_by_col_names) > 0 else GROUP_COL
    if name == GA.RETENTION_INDEX:
        return RETENTION_COL
    if name == LABEL_COL:
        return LABEL_COL
    if name == MODAL_EVENT_TIME_COL:
        return MODAL_EVENT_TIME_COL

    if name.startswith(GA.AGG_VALUE_COL):
        step = name.replace(GA.AGG_VALUE_COL + "_", "")
        return f"{step}. {CALCULATION_COL}"
    if name.startswith(GA.USER_COUNT_COL):
        step = name.replace(GA.USER_COUNT_COL + "_", "")
        return (
            f"{step}. Unique {unique_field_name}"
            if step.strip() != ""
            else f"Unique {unique_field_name}"
        )
    if name.startswith(GA.GROUP_COL + "_"):
        index = int(name.replace(GA.GROUP_COL + "_", ""))
        if metric.metric_type == M.MetricType.JOURNEY:
            return f"{index+1}. Step"
        else:
            if index >= len(group_by_col_names):
                LOGGER.bind(
                    group_by_col_names=group_by_col_names,
                    index=index,
                    group_col_names_size=len(group_by_col_names),
                ).error("Group index is out of bounds")
                return f"{index+1}. Group"

            return group_by_col_names[index][1]
    if name == IS_COMPARISON_COL:
        return name
    if name == ORIGINAL_DATETIME:
        return name
    if name == SORTING_COL:
        return SORTING_COL
    if name == PROBABILITY_COL:
        return PROB_BEST_COL_LABEL

    raise ValueError(f"{name} is not supported for table visualization.")


def _format_timestamp(dt: datetime.datetime, metric: WM.WebappMetric) -> Optional[str]:
    time_format = "%Y-%m-%d"
    if metric and metric.config.time_group == M.TimeGroup.HOUR:
        time_format = "%Y-%m-%d %H:%M"

    if not pd.isnull(dt):
        return dt.strftime(time_format)
    return None


def _pivot_table_if_needed(
    metric: WM.WebappMetric,
    result_df: pd.DataFrame,
    group_by_col_names: List[str],
) -> pd.DataFrame:
    segment_count = len(metric.segments)
    if metric.config.time_group == M.TimeGroup.TOTAL:
        if metric.metric_type == M.MetricType.RETENTION:
            result_df[RETENTION_COL] = result_df[RETENTION_COL].apply(
                lambda val: retention_period_label(val, metric)
            )
            return result_df.pivot_table(
                aggfunc="sum",
                values=CALCULATION_COL,
                index=group_by_col_names,
                columns=RETENTION_COL,
            ).reset_index()
        return result_df.drop(DATETIME_COL, axis=1)
    else:
        if metric.metric_type == M.MetricType.SEGMENTATION:
            return result_df.pivot_table(
                aggfunc="sum",
                values=CALCULATION_COL,
                index=group_by_col_names,
                columns=DATETIME_COL,
            ).reset_index()
        if metric.metric_type == M.MetricType.CONVERSION:
            if IS_COMPARISON_COL in result_df.columns:
                calculation_df = result_df[~result_df["_is_comparison"]].copy()
                calculation_previous_df = result_df[result_df[IS_COMPARISON_COL]].copy()

                calculation_pivot = calculation_df.pivot_table(
                    aggfunc="sum",
                    values=f"{segment_count}. {CALCULATION_COL}",
                    index=group_by_col_names,
                    columns=DATETIME_COL,
                ).reset_index()
                calculation_previous_pivot = calculation_previous_df.pivot_table(
                    aggfunc="sum",
                    values=f"{segment_count}. {CALCULATION_COL}",
                    index=group_by_col_names,
                    columns=DATETIME_COL,
                ).reset_index()

                combined_df = pd.concat([calculation_pivot, calculation_previous_pivot])

                return combined_df

            else:
                return result_df.pivot_table(
                    aggfunc="sum",
                    values=f"{segment_count}. {CALCULATION_COL}",
                    index=group_by_col_names,
                    columns=DATETIME_COL,
                ).reset_index()

        if metric.metric_type == M.MetricType.RETENTION:
            result_df[RETENTION_COL] = result_df[RETENTION_COL].apply(
                lambda val: retention_period_label(val, metric)
            )
            index_params: List[str] = [RETENTION_COL]
            if metric.config.chart_type != M.SimpleChartType.HEATMAP:
                index_params.extend(group_by_col_names)

            if C.SORTING_COL not in index_params:
                index_params.append(C.SORTING_COL)

            if IS_COMPARISON_COL in result_df.columns:
                retention_df = result_df[~result_df[IS_COMPARISON_COL]].copy()
                retention_previous_df = result_df[result_df[IS_COMPARISON_COL]].copy()

                retention_pivot = retention_df.pivot_table(
                    aggfunc="sum",
                    values=CALCULATION_COL,
                    index=index_params,
                    columns=DATETIME_COL,
                ).reset_index()

                retention_previous_pivot = retention_previous_df.pivot_table(
                    aggfunc="sum",
                    values=CALCULATION_COL,
                    index=index_params,
                    columns=DATETIME_COL,
                ).reset_index()

                retention_previous_pivot[RETENTION_COL] = retention_previous_pivot[
                    RETENTION_COL
                ].apply(lambda x: f"{x} (previous)")

                combined_df = pd.concat([retention_pivot, retention_previous_pivot])
                return combined_df

            else:
                result_df = result_df.pivot_table(
                    aggfunc="sum",
                    values=CALCULATION_COL,
                    index=index_params,
                    columns=DATETIME_COL,
                ).reset_index()

                return result_df
    return result_df


def _get_segment_title(
    row: pd.Series, webapp_metric: WM.WebappMetric, metric_context: MC.MetricContext
) -> str:
    return (
        f"{get_segment_letter(row[GA.EVENT_INDEX_COL])}. "
        if len(webapp_metric.segments) > 1
        else ""
    ) + (
        webapp_metric.segments[row[GA.EVENT_INDEX_COL]].title
        if webapp_metric.segments[row[GA.EVENT_INDEX_COL]].title is not None
        else get_segment_label(
            webapp_metric.segments[row[GA.EVENT_INDEX_COL]], metric_context
        )
    )


def _split_group(
    row: pd.Series,
    metric: WM.WebappMetric,
    number_of_group_bys: int,
    metric_context: MC.MetricContext,
) -> List[str]:
    group_col = row[GA.GROUP_COL]
    if group_col is None or group_col == N_PER_A:
        return number_of_group_bys * [N_PER_A]

    groups = []
    for group in group_col.split(STRING_SEPARATOR):
        if group.startswith(GA.SUBSEGMENT_INDEX):
            segment_index, event_index = [
                int(val)
                for val in group.replace(f"{GA.SUBSEGMENT_INDEX}_", "").split("_")
            ]
            segment = metric.segments[segment_index]

            event = segment.get_events_in_order()[event_index]
            name = f"{metric_context.get_event_meta_or_default_values(event).display_name} ({event_index+1})"
            groups.append(name)
        else:
            groups.append(group)

    if GA.EVENT_INDEX_COL not in row:
        return groups

    result = []
    for index, segment in enumerate(metric.segments):
        if index == row[GA.EVENT_INDEX_COL]:
            result.extend(groups)
        else:
            result.extend(len(segment.group_by) * [N_PER_A])

    return result


def split_journey_group_col(
    result_df: pd.DataFrame,
    webapp_metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
) -> pd.DataFrame:
    def fix_group_val(val: Tuple[int, str]) -> str:
        step = val[0]
        segment_title = webapp_metric.segments[step - 1].title
        event_names = [
            metric_context.get_event_meta_or_default_values(
                event_name_path
            ).display_name
            for event_name_path in webapp_metric.segments[
                step - 1
            ].get_events_in_order()
        ]
        if val[1] == NO_GROUP:
            return segment_title or ", ".join(event_names)
        elif val[1] == DROP_OFF_PLACEHOLDER:
            return DROP_OFF_LABEL
        else:
            return val[1] + (" - " + segment_title if segment_title is not None else "")

    result_df[GA.GROUP_COL] = result_df[GA.GROUP_COL].apply(
        lambda val: [fix_group_val(v) for v in val]
    )

    group_by_df = (
        result_df[GA.GROUP_COL].apply(pd.Series).add_prefix(f"{GA.GROUP_COL}_")
    )
    for index, col_name in enumerate(group_by_df.columns):
        result_df.insert(2 + index, col_name, group_by_df[col_name])
    result_df = result_df.drop(columns=[GA.GROUP_COL])
    return result_df


def generate_journey_dropoff_paths(df: pd.DataFrame) -> pd.DataFrame:
    new_rows = []
    user_count_column = f"{GA.USER_COUNT_COL}_"
    for _, row in df.iterrows():
        path = row[GA.GROUP_COL]
        user_counts = [
            row[col] for col in df.columns if col.startswith(user_count_column)
        ]

        for i in range(len(user_counts) - 1, 0, -1):
            dropoff_count = user_counts[i - 1] - user_counts[i]
            if dropoff_count > 0:
                new_path = path[:i] + [(i + 1, DROP_OFF_PLACEHOLDER)]
                new_path += [(i + 2, "")] * (len(path) - len(new_path))

                new_row = row.drop(
                    labels=[
                        col for col in df.columns if col.startswith(user_count_column)
                    ]
                ).copy()
                new_row[GA.GROUP_COL] = new_path
                new_row[user_count_column] = dropoff_count
                new_row[SORTING_COL] = dropoff_count
                if dropoff_count > 0:
                    new_rows.append(new_row)

        final_row = row.drop(
            labels=[col for col in df.columns if col.startswith(user_count_column)]
        ).copy()
        final_row[user_count_column] = user_counts[-1]
        final_row[SORTING_COL] = user_counts[-1]

        if final_row[user_count_column] > 0:
            new_rows.append(final_row)
    new_pd = pd.DataFrame(new_rows)
    return new_pd


def drop_agg_value_columns(df: pd.DataFrame) -> pd.DataFrame:
    return df.drop(columns=[col for col in df.columns if col.startswith("_agg_value")])


def prepare_dataframe_for_table(
    metric: WM.WebappMetric,
    result_df: pd.DataFrame,
    metric_context: MetricContext,
    is_event_list_modal: bool,
) -> pd.DataFrame:
    if (
        metric.metric_type == M.MetricType.CONVERSION
        and metric.config.time_group == M.TimeGroup.TOTAL
        and metric.config.conv_orientation != M.ConversionOrientation.REVERSE
        and len(metric.segments) > 1
        and ORIGINAL_GROUP_COL in result_df.columns
        and len(result_df[ORIGINAL_GROUP_COL].unique()) > 1
    ):
        add_bayesian_probability_column(len(metric.segments), result_df)
    if ORIGINAL_GROUP_COL in result_df.columns:
        result_df = result_df.drop([ORIGINAL_GROUP_COL], axis=1, errors="ignore")

    result_df = extend_df_with_sorting_column(metric, result_df)

    unique_field_name = metric_context.get_unique_field_display_name(
        metric.config.unique_field
    )

    group_by_cols = []
    segment_count_with_group_by = len(
        [segment for segment in metric.segments if len(segment.group_by) > 0]
    )
    for index, segment in enumerate(metric.segments):
        if GA.EVENT_INDEX_COL in result_df.columns and len(metric.segments) > 1:
            prefix = f"{get_segment_letter(index)}. "
        else:
            prefix = ""

        if segment_count_with_group_by > 1 or (
            metric.metric_type == M.MetricType.SEGMENTATION and len(metric.segments) > 1
        ):
            if segment.title is not None:
                prefix += f"{segment.title} - "
            else:
                prefix += "Event - "

        group_by_cols.extend(
            [
                (
                    field_path,
                    MODAL_EVENT_TIME_COL
                    if is_event_list_modal and field_index == 0
                    else (prefix + metric_context.get_display_name(field_path)),
                )
                for field_index, field_path in enumerate(segment.group_by)
            ]
        )

    size = result_df.shape[0]
    if size == 0:
        result_df = result_df.drop(
            columns=[C.IS_COMPARISON_COL, GA.EVENT_INDEX_COL],
            errors="ignore",  # some columns may not exist
        )
        # FIXME: empty df should have proper group by cols
        result_df.columns = pd.Index(
            [
                _normalize_column_name(col, unique_field_name, group_by_cols, metric)
                for col in result_df.columns
            ]
        )

        return result_df

    if metric.metric_type == M.MetricType.JOURNEY:
        result_df = generate_journey_dropoff_paths(result_df)
        result_df = drop_agg_value_columns(result_df)
        result_df = split_journey_group_col(result_df, metric, metric_context)
    else:
        result_df[LABEL_COL] = result_df.apply(
            lambda row: get_label_column(row, metric, metric_context),
            axis=1,
        )
        if len(group_by_cols) > 0:
            result_df[GA.GROUP_COL] = result_df.apply(
                lambda row: _split_group(
                    row,
                    metric,
                    number_of_group_bys=len(group_by_cols),
                    metric_context=metric_context,
                ),
                axis=1,
            )
            group_by_df = (
                result_df[GA.GROUP_COL].apply(pd.Series).add_prefix(f"{GA.GROUP_COL}_")
            )
            for index, col_name in enumerate(group_by_df.columns):
                result_df.insert(2 + index, col_name, group_by_df[col_name])
            result_df = result_df.drop(columns=[GA.GROUP_COL])

    if GA.EVENT_INDEX_COL in result_df.columns:
        result_df = result_df.drop(GA.EVENT_INDEX_COL, axis=1)

        if is_event_list_modal:
            result_df = result_df.sort_values(
                by=[result_df.columns[2]],
                ascending=False,
            )

    if GA.DATETIME_COL in result_df.columns:
        result_df[GA.DATETIME_COL] = result_df[GA.DATETIME_COL].apply(
            lambda val: _format_timestamp(val, metric)
        )
    result_df.columns = pd.Index(
        [
            _normalize_column_name(col, unique_field_name, group_by_cols, metric)
            for col in result_df.columns
        ]
    )

    result_df = _pivot_table_if_needed(
        metric,
        result_df,
        ([LABEL_COL] if LABEL_COL in result_df.columns else [])
        + [col[1] for col in group_by_cols]
        + [C.SORTING_COL],
    )

    if is_event_list_modal:
        result_df.sort_values(by=[MODAL_EVENT_TIME_COL], inplace=True, ascending=False)
    else:
        result_df.sort_values(by=[C.SORTING_COL], inplace=True, ascending=False)

    result_df = result_df.drop(columns=[C.SORTING_COL])

    if C.IS_COMPARISON_COL in result_df.columns:
        result_df = result_df.drop(columns=[C.IS_COMPARISON_COL])

    result_df.columns = pd.Index([str(c) for c in result_df.columns])
    result_df = round(result_df, 2)

    # label col must be the first
    if LABEL_COL in result_df.columns:
        result_df = result_df[
            [LABEL_COL] + [col for col in result_df.columns if col != LABEL_COL]
        ]

    result_df.columns = pd.Index(
        [
            _rename_intermediary_column_names(col, is_event_list_modal)
            for col in result_df.columns
        ],
    )
    return result_df


def add_bayesian_probability_column(
    funnel_length: int, result_df: pd.DataFrame
) -> None:
    probs = calculate_bayesian_probabilities(
        result_df,
        variant_col=ORIGINAL_GROUP_COL,
        original_users=fix_col_index(1, GA.USER_COUNT_COL),
        converted_users=fix_col_index(funnel_length, GA.USER_COUNT_COL),
    )
    result_df[PROBABILITY_COL] = (
        result_df[ORIGINAL_GROUP_COL]
        .map(probs)
        .apply(lambda x: f"{x * 100:.1f}%".rjust(6, " "))
    )


def get_label_column(
    row: pd.Series, metric: WM.WebappMetric, metric_context: MC.MetricContext
) -> str:
    group_bys = ", ".join(
        get_readable_group_by_values(row[GA.GROUP_COL], metric, metric_context)
    )
    if metric.metric_type == M.MetricType.SEGMENTATION:
        segment_title = _get_segment_title(row, metric, metric_context)
        if metric.config.formula is not None:
            if group_bys == N_PER_A:
                label = "Formula calculation"
            else:
                label = group_bys + " - Formula calculation"
        else:
            if group_bys == N_PER_A:
                label = segment_title
            else:
                label = group_bys
                if len(metric.segments) > 1:
                    label += " - " + segment_title

    elif (
        metric.metric_type == M.MetricType.RETENTION
        and metric.config.chart_type != M.SimpleChartType.HEATMAP
        and metric.config.time_window_chooser_config.time_group != M.TimeGroup.TOTAL
    ):
        label = retention_period_label(row[GA.RETENTION_INDEX], metric)
        if group_bys != N_PER_A:
            label += " - " + group_bys
    else:
        label = group_bys

    return get_finalized_label(
        label,
        row[C.IS_COMPARISON_COL] if C.IS_COMPARISON_COL in row else False,
        label_mapping=metric.label_mapping,
    )


def prepare_dataframe_for_event_list_table(
    webapp_metric: WM.WebappMetric,
    result_df: pd.DataFrame,
    metric_context: MetricContext,
) -> pd.DataFrame:
    def fix_event_time_group_bys(col: str) -> str:
        group_by_vals = col.split(STRING_SEPARATOR)
        all_events = metric_context.resolve_custom_events_in_list(
            webapp_metric.segments[0].get_event_name_paths()
        )
        event_time_col_count = len(all_events)

        vals = []
        for val in group_by_vals[0:event_time_col_count]:
            if val != N_PER_A:
                vals.append(val)
                break

        vals.extend(group_by_vals[event_time_col_count:])

        return STRING_SEPARATOR.join(vals)

    result_df[GA.GROUP_COL] = result_df[GA.GROUP_COL].apply(fix_event_time_group_bys)

    result_df = fix_empty_group_by_cols(
        result_df, should_handle_null_as_dropoff(webapp_metric)
    )
    result_df = subtitute_segment_labels_in_group_bys(
        result_df,
        webapp_metric=webapp_metric,
        metric_context=metric_context,
    )
    result_df.sort_values(by=[GA.GROUP_COL], ascending=False, inplace=True)

    # events with the same group by values (eg. same timestamp) is present only once
    # with an N>1 agg value
    for row in result_df[result_df[GA.AGG_VALUE_COL] > 1].iloc:  # type: ignore[attr-defined]
        new_df = pd.DataFrame(int(row[GA.AGG_VALUE_COL] - 1) * [row])
        result_df = pd.concat([result_df, new_df])

    result_df = prepare_dataframe_for_table(
        webapp_metric, result_df, metric_context, is_event_list_modal=True
    ).drop(columns=[CALCULATION_COL])

    if isinstance(webapp_metric.segments[0], WM.WebappComplexSegment):
        result_df = result_df.drop(columns=[EVENT_COL_LABEL], errors="ignore").rename(
            columns={"Subsegment": EVENT_COL_LABEL}
        )
        result_df[EVENT_COL_LABEL] = result_df[EVENT_COL_LABEL].apply(
            lambda col: webapp_metric.label_mapping.get(col, col)
        )
    elif isinstance(webapp_metric.segments[0], WM.WebappSimpleSegment):
        # reorder columns with indices since there can be duplicated column names
        display_name = metric_context.get_event_meta_or_default_values(
            webapp_metric.segments[0].event
        ).display_name
        display_name = webapp_metric.label_mapping.get(display_name, display_name)
        result_df[EVENT_COL_LABEL] = display_name
        column_indices = [1, 0] + list(range(2, len(result_df.columns)))
        result_df = result_df.iloc[:, column_indices]
    else:
        raise ValueError(f"Unknown webapp segemtn type: {webapp_metric.segments[0]}")

    return result_df
