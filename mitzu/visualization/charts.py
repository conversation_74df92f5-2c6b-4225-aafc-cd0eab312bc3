from __future__ import annotations

import base64
import pickle
import zlib
from datetime import datetime
from typing import Callable, Optional

import pandas as pd

import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.visualization.labels as L
import mitzu.visualization.preprocessor.preprocessor_factory as PF
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.serialization as SE
from mitzu.helper import value_to_label
from mitzu.logger import LOGGER
from mitzu.visualization.preprocessor.base_preprocessor import (
    PreprocessedDataFrame,
)
from mitzu.webapp.model import WebappMetric


def get_group_by_field_name(segment: WM.WebappSegment) -> str:
    return value_to_label(
        ", ".join([value_to_label(gb.field_name) for gb in segment.group_by])
    )


def get_color_label(metric: WM.WebappMetric) -> str:
    if metric.metric_type == M.MetricType.SEGMENTATION and metric.segments[0].group_by:
        return get_group_by_field_name(metric.segments[0])  # FIxME
    elif metric.metric_type == M.MetricType.CONVERSION and len(metric.segments) > 0:
        segment = metric.segments[0]
        for seg in metric.segments[1:]:
            if len(seg.group_by) > 0:
                segment = seg
                break
        return get_group_by_field_name(segment)
    elif (
        metric.metric_type == M.MetricType.RETENTION
        and metric.config.chart_type == M.SimpleChartType.LINE
    ):
        return "Cohort"
    return ""


def get_default_chart_type(metric: WM.WebappMetric) -> M.SimpleChartType:
    if metric.config.time_group == M.TimeGroup.TOTAL:
        return M.SimpleChartType.BAR
    else:
        if metric.metric_type in [M.MetricType.SEGMENTATION, M.MetricType.CONVERSION]:
            return M.SimpleChartType.LINE
        elif metric.metric_type == M.MetricType.RETENTION:
            return M.SimpleChartType.HEATMAP
        else:
            raise ValueError(f"No default chart type defined for {type(metric)}")


def get_x_axis_label_func(
    chart_type: M.SimpleChartType, metric: WebappMetric
) -> Callable | None:
    x_axis_label_func = None

    if (
        chart_type not in (M.SimpleChartType.LINE, M.SimpleChartType.STACKED_AREA)
        and metric.config.time_group != M.TimeGroup.TOTAL
    ):
        x_axis_label_func = C.fix_date_label

    if metric.metric_type == M.MetricType.RETENTION:
        x_axis_label_func = C.retention_period_label

    return x_axis_label_func


def get_preprocessed_dataframe(
    result_df: pd.DataFrame,
    metric: WebappMetric,
    metric_context: MC.MetricContext,
    project: M.Project,
) -> PreprocessedDataFrame:
    original_df = result_df.copy(deep=True)
    try:
        preprocessor = PF.get_preprocessor(metric, metric_context, project)
        return preprocessor.preprocess_df(result_df)
    except Exception as exc:
        zipped_df = zlib.compress(pickle.dumps(original_df), 9)
        encoded_df = base64.urlsafe_b64encode(zipped_df).decode("utf-8")
        LOGGER.opt(exception=exc).bind(
            webapp_metric=SE.to_compressed_string(metric), original_df=encoded_df
        ).error("failed to preprocess result df")
        raise exc


def get_simple_chart(
    metric: WM.WebappMetric,
    result_df: pd.DataFrame,
    metric_context: MC.MetricContext,
    project: M.Project,
) -> C.SimpleChart:

    if metric.config.chart_type is None:
        chart_type = get_default_chart_type(metric)
    else:
        chart_type = metric.config.chart_type
    color_label = get_color_label(metric)
    if metric.config.formula:
        y_axis_label = ""
    elif chart_type == M.SimpleChartType.HEATMAP:
        y_axis_label = get_color_label(metric)
        color_label = L.agg_type_label(
            metric.config.aggregation, metric_context, metric.config
        )
    else:
        aggregation_config: Optional[WM.WebappAggregationConfig] = None
        if metric.metric_type == M.MetricType.SEGMENTATION:
            aggregation_config = metric.segments[0].aggregation
            for seg in metric.segments[1:]:
                if seg.aggregation != aggregation_config:
                    aggregation_config = None
                    break
        else:
            aggregation_config = metric.config.aggregation

        y_axis_label = (
            L.agg_type_label(aggregation_config, metric_context, metric.config)
            if aggregation_config
            else ""
        )
        if chart_type in (
            M.SimpleChartType.PERCENTAGE_STACKED_AREA,
            M.SimpleChartType.PERCENTAGE_STACKED_BAR,
        ):
            y_axis_label = f"{y_axis_label} (%)"

    preprocessed_df = get_preprocessed_dataframe(
        result_df, metric, metric_context, project
    )
    return C.SimpleChart(
        last_updated_at=datetime.now(),
        metric_json=SE.to_compressed_string(metric),
        x_axis_label="",
        y_axis_label=y_axis_label,
        color_label=color_label,
        chart_type=chart_type,
        yaxis_ticksuffix=preprocessed_df.suffix,
        dataframe=preprocessed_df.df,
        dataframe_version=C.CURRENT_CHART_VERSION,
        all_groups=len(preprocessed_df.all_groups),
    )
