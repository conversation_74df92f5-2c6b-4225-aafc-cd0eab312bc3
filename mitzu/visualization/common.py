from __future__ import annotations

import json
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import pkg_resources
import pycountry

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.model as WM
from mitzu.helper import create_unique_id, get_simple_hash
from mitzu.logger import LOGGER

TTC_RANGE_1_SEC = 600
TTC_RANGE_2_SEC = 7200
TTC_RANGE_3_SEC = 48 * 3600

ORIG_X_AXIS_COL = "orig_x"
X_AXIS_COL = "x"
Y_AXIS_COL = "y"
TEXT_COL = "_text"
COLOR_COL = "_color"
JSON_COL = "_json"
SUFFIX_COL = "_suffix"
AGG_TITLE = "_agg_title"
PERCENTAGE_COL = "_percentage"
IS_COMPARISON_COL = "_is_comparison"
ORIGINAL_DATETIME = "_original_datetime"
SHADES = OrderedDict(
    blue_7="#1c7ed6",
    lime_7="#74b816",
    orange_7="#f76707",
    violet_7="#7048e8",
    red_7="#f03e3e",
    grape_7="#ae3ec9",
    green_7="#37b24d",
    indigo_7="#4263eb",
    pink_7="#d6336c",
    cyan_7="#1098ad",
    teal_7="#0ca678",
    # Lighter extra colors
    blue_5="#339af0",
    lime_5="#94d82d",
    orange_5="#ff922b",
    violet_5="#845ef7",
    red_5="#ff6b6b",
    pink_5="#f06595",
    grape_5="#cc5de8",
    indigo_5="#5c7cfa",
    cyan_5="#22b8cf",
    teal_5="#20c997",
    green_5="#51cf66",
)
OTHER_GROUPS_COLOR = "rgba(128,128,128,0.6)"

SORTING_COL = "__sorting__"

CURRENT_CHART_VERSION = 3


def get_color_keys() -> List[str]:
    return [col.replace("_", ".") for col in SHADES.keys()]


def _get_color_for_group_or_index(
    color_list: List[str],
    consistent_coloring: bool,
    index: Optional[int],
    group: Optional[str],
) -> str:
    if consistent_coloring:
        if group is None:
            raise ValueError("Group is required for consistent coloring")
        # md5 hashes and uses modulo to get a color from the list
        color = color_list[get_simple_hash(group) % len(color_list)]
    else:
        if index is None:
            raise ValueError("Index is required for non-consistent coloring")
        color = color_list[index % len(color_list)]

    return color


def get_dmc_color_for_group_or_index(
    consistent_coloring: bool,
    index: Optional[int],
    group: Optional[str],
) -> str:
    return _get_color_for_group_or_index(
        get_color_keys(), consistent_coloring, index, group
    )


def get_plotly_color_for_group_or_index(
    consistent_coloring: bool,
    index: Optional[int],
    group: Optional[str],
) -> str:

    return _get_color_for_group_or_index(
        list(SHADES.values()), consistent_coloring, index, group
    )


class InvalidGEOCountryMetric(Exception):
    pass


def fetch_country_jsons() -> Dict[str, Any]:
    absolute_json_path = pkg_resources.resource_filename(
        "mitzu.webapp.assets", "countries_geo.json"
    )

    with open(absolute_json_path, "r") as file:
        return json.load(file)


COUNTRY_JSONS = fetch_country_jsons()


def convert_to_iso3(country_name_or_code: str) -> Optional[str]:
    # Try to find the country using its name
    try:
        country = pycountry.countries.get(name=country_name_or_code)
        if not country:
            country = pycountry.countries.get(alpha_2=country_name_or_code)
        if not country:
            country = pycountry.countries.get(alpha_3=country_name_or_code)
        if not country:
            res = pycountry.countries.search_fuzzy(query=country_name_or_code)
            if res and len(res) >= 1:
                country = res[0]
        if country:
            return country.alpha_3
        else:
            return None
    except Exception as exc:
        LOGGER.opt(exception=exc).warning(
            f"Couldn't find country_name in DB: {country_name_or_code}"
        )
    return None


def retention_period_label(
    val: Union[int, str, datetime], metric: WM.WebappMetric
) -> str:
    if metric.metric_type == M.MetricType.RETENTION and type(val) in [int, str]:
        if metric.config.retention_window is None:
            raise ValueError("Retention window is not configured")
        return f"{metric.config.retention_window.period.name.title()} {val}"
    return str(val)


def fix_date_label(val: pd.Timestamp, metric: WM.WebappMetric) -> str:
    if metric.config.time_window_chooser_config.time_group not in (
        M.TimeGroup.SECOND,
        M.TimeGroup.MINUTE,
        M.TimeGroup.HOUR,
    ):
        res = str(val.date())
        if metric.config.chart_type == M.SimpleChartType.HEATMAP:
            res = res + "."
        return res
    return str(val)


@dataclass(frozen=True)
class Annotation:
    project_id: str
    owner: WM.MembershipID
    created_at: datetime
    annotation_text: str
    annotation_datetime: datetime
    description: Optional[str]
    id: str = field(default_factory=create_unique_id)


@dataclass(frozen=True)
class SimpleChart:
    metric_json: str  # to track the metric changes
    last_updated_at: datetime
    x_axis_label: str
    y_axis_label: str
    color_label: str
    yaxis_ticksuffix: str
    chart_type: M.SimpleChartType
    dataframe: pd.DataFrame
    dataframe_version: int
    hover_mode: str = "closest"
    all_groups: int = 0

    def __eq__(self, value: object) -> bool:
        if not isinstance(value, SimpleChart):
            return False

        value_dict = value.__dict__
        for key, value in self.__dict__.items():
            if key not in ["dataframe"]:
                if value != value_dict[key]:
                    return False
        return True


@dataclass
class PlotCustomData:
    metric_type: M.MetricType
    chart_type: M.SimpleChartType

    unit: str
    color: str
    custom_json: Optional[str]
    agg_title: Optional[str]
    user_cols: List[str]
    user_counts: List[int]

    @classmethod
    def create(
        cls, metric_type: M.MetricType, chart_type: M.SimpleChartType, pdf: pd.DataFrame
    ) -> PlotCustomData:
        user_cols = [
            col for col in pdf.columns if col.startswith(GA.USER_COUNT_COL + "_")
        ]

        if len(user_cols) == 0:
            # We need this to support funnels both trends and steps.
            user_cols = [
                col for col in pdf.columns if col.startswith(GA.USER_COUNT_COL)
            ]

        return PlotCustomData(
            metric_type=metric_type,
            chart_type=chart_type,
            unit=SUFFIX_COL,
            color=COLOR_COL,
            custom_json=JSON_COL,
            agg_title=AGG_TITLE if metric_type == M.MetricType.SEGMENTATION else None,
            user_cols=user_cols,
            user_counts=[],
        )

    def serialize_to_list(self) -> List:
        # heatmap user counts are serialized as a vector in the dataframe column
        if self.metric_type == M.MetricType.SEGMENTATION:
            return [
                self.agg_title,
                self.unit,
                self.color,
                self.custom_json,
                *self.user_cols,
            ]
        return [self.unit, self.color, self.custom_json, *self.user_cols]

    @classmethod
    def deserialize_list(
        cls,
        metric_type: M.MetricType,
        chart_type: M.SimpleChartType,
        custom_data: Union[List, str],
    ) -> PlotCustomData:
        if isinstance(custom_data, str):
            if chart_type != M.SimpleChartType.HEATMAP:
                raise ValueError(
                    f"Only heatmap custom data can be parsed from strings, got: {chart_type}"
                )
            return PlotCustomData(
                metric_type=metric_type,
                chart_type=chart_type,
                unit="",
                color="",
                custom_json="",
                agg_title=None,
                user_cols=[],
                user_counts=[int(v) for v in custom_data.split(",")],
            )

        if metric_type == M.MetricType.SEGMENTATION:
            return PlotCustomData(
                metric_type=metric_type,
                chart_type=chart_type,
                agg_title=custom_data[0],
                unit=custom_data[1],
                color=custom_data[2],
                custom_json=custom_data[3],
                user_cols=custom_data[4:],
                user_counts=[],
            )

        return PlotCustomData(
            metric_type=metric_type,
            chart_type=chart_type,
            unit=custom_data[0],
            color=custom_data[1],
            custom_json=custom_data[2],
            user_cols=custom_data[3:],
            agg_title=None,
            user_counts=[],
        )


def generate_dts_between(
    start_date: datetime, end_date: datetime, timegroup: M.TimeGroup
) -> List[datetime]:
    curr_dt = start_date

    # Date trunc in python
    if timegroup == M.TimeGroup.SECOND:
        curr_dt = curr_dt.replace(microsecond=0)
        fixed_end = end_date.replace(microsecond=0)
    elif timegroup == M.TimeGroup.MINUTE:
        curr_dt = curr_dt.replace(second=0, microsecond=0)
        fixed_end = end_date.replace(second=0, microsecond=0)
    elif timegroup == M.TimeGroup.HOUR:
        curr_dt = curr_dt.replace(minute=0, second=0, microsecond=0)
        fixed_end = end_date.replace(minute=0, second=0, microsecond=0)
    elif timegroup == M.TimeGroup.DAY:
        curr_dt = curr_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        fixed_end = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif timegroup == M.TimeGroup.WEEK:
        curr_dt = (curr_dt - timedelta(days=(curr_dt.weekday() - 0) % 7)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        fixed_end = (end_date - timedelta(days=(end_date.weekday() - 0) % 7)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
    elif timegroup == M.TimeGroup.MONTH:
        curr_dt = curr_dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        fixed_end = end_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    elif timegroup == M.TimeGroup.YEAR:
        curr_dt = curr_dt.replace(
            month=1, day=1, hour=0, minute=0, second=0, microsecond=0
        )
        fixed_end = end_date.replace(
            month=1, day=1, hour=0, minute=0, second=0, microsecond=0
        )
    else:
        raise ValueError(f"Unexpected time group {timegroup}")

    dt_list = []
    rel_dt = timegroup.to_relativedelta()
    while curr_dt <= fixed_end:
        dt_list.append(curr_dt)
        if curr_dt < start_date:
            curr_dt += rel_dt
            # Can happen due to date_trunc
            continue
        curr_dt += rel_dt

    return dt_list


def _find_available_slots(slots: List[int]) -> int:
    slots = sorted(slots)
    for i, slot in enumerate(slots):
        if slot != i:
            return i
    return len(slots)


def make_timezone_aware(dt: datetime) -> datetime:
    if dt.tzinfo is None:
        # Convert naive datetime to timezone-aware (UTC)
        return dt.replace(tzinfo=timezone.utc)

    return dt


def prepare_annotations(
    annotations: List[Annotation],
    start_dt: datetime,
    end_dt: datetime,
    allowed_annotation_ratio: float = 0.2,
) -> List[Tuple[Annotation, int]]:
    start_dt = make_timezone_aware(start_dt)
    end_dt = make_timezone_aware(end_dt)
    filtered_annotations = [
        a
        for a in annotations
        if start_dt <= make_timezone_aware(a.annotation_datetime) <= end_dt
    ]
    filtered_annotations = sorted(
        filtered_annotations, key=lambda x: make_timezone_aware(x.annotation_datetime)
    )

    lookback_seconds = int(
        (end_dt - start_dt).total_seconds() * allowed_annotation_ratio
    )
    results: List[Tuple[Annotation, int]] = []
    for ant in filtered_annotations:
        slots = []
        bucket_end_secs = int(ant.annotation_datetime.timestamp())
        bucket_start_secs = bucket_end_secs - lookback_seconds
        for res, offset in results:
            if (
                bucket_start_secs
                <= res.annotation_datetime.timestamp()
                <= bucket_end_secs
            ):
                slots.append(offset)
        results.append((ant, _find_available_slots(slots)))

    return results


def get_graph_height(metric_type: M.MetricType) -> int:
    if metric_type == M.MetricType.JOURNEY:
        return 800
    return 500


def extend_df_with_sorting_column(
    metric: WM.WebappMetric, result_df: pd.DataFrame
) -> pd.DataFrame:
    user_col_1 = GA.USER_COUNT_COL + "_1"
    if metric.metric_type == M.MetricType.JOURNEY:
        # Journey is always tg = Total
        result_df[SORTING_COL] = result_df[user_col_1]
        return result_df

    if (
        metric.metric_type == M.MetricType.RETENTION
        and metric.config.time_window_chooser_config.time_group != M.TimeGroup.TOTAL
    ):

        result_df[SORTING_COL] = -result_df[GA.RETENTION_INDEX]
        return result_df
    if metric.metric_type == M.MetricType.SEGMENTATION:
        value_col = GA.AGG_VALUE_COL
        group_values = (
            result_df.groupby([GA.GROUP_COL, GA.EVENT_INDEX_COL])[value_col]
            .sum()
            .reset_index()
        )
        group_values[SORTING_COL] = group_values[value_col]

        result_df = result_df.merge(
            group_values[[SORTING_COL, GA.GROUP_COL, GA.EVENT_INDEX_COL]],
            on=[GA.GROUP_COL, GA.EVENT_INDEX_COL],
            how="left",
        )
        result_df[SORTING_COL] = result_df[SORTING_COL].fillna(0)
    else:
        value_col = user_col_1

        group_values = result_df.groupby(GA.GROUP_COL)[value_col].sum().reset_index()
        group_values[SORTING_COL] = group_values[value_col]

        result_df = result_df.merge(
            group_values[[SORTING_COL, GA.GROUP_COL]],
            on=[GA.GROUP_COL],
            how="left",
        )
    result_df[SORTING_COL] = result_df[SORTING_COL].fillna(0)
    return result_df


def get_value_suffix_by_agg_type(agg_type: M.AggType) -> str:
    if agg_type in [
        M.AggType.BY_EVENT_PROPERTY,
        M.AggType.COUNT_CONVERTED_USERS,
    ]:
        return ""
    else:
        return "%"
