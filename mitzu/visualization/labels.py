from __future__ import annotations

from typing import List, Optional

import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM


def conv_time_suffix(max_ttc: int) -> str:
    return (
        "secs"
        if max_ttc <= C.TTC_RANGE_1_SEC
        else "mins"
        if max_ttc <= C.TTC_RANGE_2_SEC
        else "hours"
        if max_ttc <= C.TTC_RANGE_3_SEC
        else "days"
    )


def unique_users_agg_label(
    agg_type: Optional[M.AggType],
    metric_config: WM.WebappMetricConfig,
    metric_context: MC.MetricContext,
    short_form: bool,
) -> str:
    subject = get_subject_label(metric_config, metric_context, plural=True)

    if agg_type == M.AggType.COUNT_CONVERTED_USERS:
        if short_form:
            return "Converted uniques"
        return f"Converted unique {subject}"
    return f"Unique {subject}"


def get_subject_label(
    metric_config: WM.WebappMetricConfig,
    metric_context: MC.MetricContext,
    plural: bool,
) -> str:
    if isinstance(metric_config.unique_field, M.EventFieldPath):
        subject = metric_context.get_display_name(metric_config.unique_field) + (
            "s" if plural else ""
        )
    else:
        subject = metric_context.get_entity_meta(
            metric_config.unique_field
        ).display_name.lower() + ("s" if plural else "")
    if subject.endswith("ss"):
        subject = subject[:-1]
    return subject


def agg_type_label(
    aggregation: WM.WebappAggregationConfig,
    metric_context: MC.MetricContext,
    metric_config: WM.WebappMetricConfig,
) -> str:

    if aggregation.type == M.AggType.CONVERSION:
        return "Conversion rate"
    if aggregation.type == M.AggType.RETENTION_RATE:
        return "Retention rate"
    if aggregation.type in [
        M.AggType.COUNT_UNIQUE_USERS,
        M.AggType.COUNT_CONVERTED_USERS,
    ]:
        return unique_users_agg_label(
            aggregation.type, metric_config, metric_context, short_form=False
        )
    if aggregation.type == M.AggType.COUNT_EVENTS:
        return "Event count"
    if aggregation.type == M.AggType.FREQUENCY:
        return "Events per " + get_subject_label(
            metric_config, metric_context, plural=False
        )

    if aggregation.type == M.AggType.AVERAGE_TIME_TO_CONV:
        return "Avg. time to convert"
    if aggregation.type == M.AggType.PERCENTILE_TIME_TO_CONV:
        return f"P{aggregation.conv_param} time to convert"

    if aggregation.type == M.AggType.BY_EVENT_PROPERTY:
        event_property_agg = aggregation.event_property_agg
        if event_property_agg is None or event_property_agg.field_def is None:
            raise ValueError("Event property aggregation settings are missing")

        field_name = metric_context.get_display_name(event_property_agg.field_def)
        if event_property_agg.agg_type == M.EventPropertyAggregationType.SUM:
            return f"Sum of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.AVG:
            return f"Average of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.MIN:
            return f"Minimum of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.MAX:
            return f"Maximum of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.COUNT_DISTINCT:
            return f"Distinct count of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.MEDIAN:
            return f"Median of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.P75:
            return f"75 percentile of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.P90:
            return f"90 percentile of {field_name}"
        if event_property_agg.agg_type == M.EventPropertyAggregationType.P95:
            return f"95 percentile of {field_name}"
        raise ValueError(
            f"Uknown event property aggregation type: {event_property_agg.agg_type}"
        )

    if metric_context.revenue_settings is None:
        raise ValueError("Revenue is not configured")

    if aggregation.type == M.AggType.TOTAL_MRR:
        return f"{ metric_context.revenue_settings.currency} MRR"

    if aggregation.type == M.AggType.COUNT_SUBSCRIBERS:
        return "Subscriber count"

    raise ValueError(f"Uknown aggregation type: {aggregation.type}")


def get_segment_label(
    segment: WM.WebappSegment,
    metric_context: MC.MetricContext,
    max_length: int = 50,
) -> str:
    def walk_segment(segment: WM.WebappSegment) -> List[str]:
        if isinstance(segment, WM.WebappSimpleSegment):
            return [
                metric_context.get_event_meta_or_default_values(
                    segment.event
                ).display_name
            ]
        if isinstance(segment, WM.WebappComplexSegment):
            return walk_segment(segment.left) + walk_segment(segment.right)
        raise ValueError(f"Unexpected segment type: {segment}")

    all_labels = walk_segment(segment)

    if len(all_labels) == 1:
        return all_labels[0]

    deduplicated = []
    for label in all_labels:
        if label not in deduplicated:
            deduplicated.append(label)

    joined = ", ".join(deduplicated)
    return joined[:max_length] + "..." if len(joined) > max_length else joined
