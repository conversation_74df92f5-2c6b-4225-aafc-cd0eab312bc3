from __future__ import annotations

import json
from base64 import b64encode
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import dateutil
import dateutil.parser
import numpy
import pandas as pd
import plotly.express as px
import plotly.figure_factory as ff
import plotly.graph_objects as go
import plotly.io as pio

import mitzu.adapters.generic_adapter as GA
import mitzu.helper as H
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.visualization.labels as L
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.adapters.model import TooManyDataPointsError
from mitzu.logger import LOGGER
from mitzu.visualization.charts import get_x_axis_label_func
from mitzu.visualization.preprocessor.common import (
    OTHER_GROUPS,
)
from mitzu.visualization.preprocessor.conversion_preprocessor import (
    IS_DROPOFF_COL,
)
from mitzu.visualization.sankey_components import (
    MIN_NODE_PADDING,
    get_sankey_chart_components,
    get_x_coordinates,
    get_y_coordinates,
)

MAX_LABEL_LENGTH = 57


def set_figure_style(
    fig: go.Figure,
    metric: M.Metric,
    pdf: pd.DataFrame,
    default_end_dt: datetime,
    y_range_config: Optional[WM.YRangeConfig],
) -> go.Figure:
    if metric.config.chart_type == M.SimpleChartType.LINE:
        fig.update_traces(
            line=dict(width=2.3),
            mode=("lines+markers" if pdf.shape[0] <= 200 else "lines"),
            textposition="top center",
            textfont_size=9,
        )
    elif metric.config.chart_type in (
        M.SimpleChartType.STACKED_AREA,
        M.SimpleChartType.PERCENTAGE_STACKED_AREA,
    ):

        if pdf.shape[0] <= 80:
            mode = "lines+markers+text"
        elif pdf.shape[0] <= 600:
            mode = "lines+markers"
        else:
            mode = "lines"
        fig.update_traces(
            textposition="top center",
            mode=mode,
            line=dict(width=1.8),
        )
    elif metric.config.chart_type in (
        M.SimpleChartType.STACKED_BAR,
        M.SimpleChartType.BAR,
        M.SimpleChartType.PERCENTAGE_STACKED_BAR,
        M.SimpleChartType.HORIZONTAL_BAR,
    ):
        fig.update_traces(textposition="auto", textfont_size=9)
    elif metric.config.chart_type == M.SimpleChartType.PIE:
        fig.update_traces(textposition="inside", textfont_size=10)
    elif metric.config.chart_type == M.SimpleChartType.GEO_COUNTRY:
        pass
    elif metric.config.chart_type == M.SimpleChartType.NUMBER:
        fig.update_traces(textposition="bottom center")
        fig.update_xaxes(visible=False)
        fig.update_yaxes(visible=False)
    else:
        fig.update_traces(textposition="top center", textfont_size=9)

    if metric.config.chart_type != M.SimpleChartType.HORIZONTAL_BAR:
        max_tick_count = min(pdf[C.X_AXIS_COL].unique().shape[0], 6)
        tick_format = H.get_metric_date_format(metric, default_end_dt)
    else:
        max_tick_count = 10
        tick_format = None

    bargap = get_bargap(fig, metric)
    fig.update_traces(hoverinfo="none", hovertemplate=None)

    y_min = (
        y_range_config.min
        if y_range_config and y_range_config.min is not None
        else None
    )
    y_max = (
        y_range_config.max
        if y_range_config and y_range_config.max is not None
        else None
    )

    fig.update_yaxes(
        rangemode="tozero",
        showline=True,
        linecolor="rgba(127,127,127,0.1)",
        gridcolor="rgba(127,127,127,0.1)",
        fixedrange=True,
        range=[y_min, y_max],
    )
    fig.update_xaxes(
        rangemode="tozero",
        showline=True,
        linecolor="rgba(127,127,127,0.3)",
        gridcolor="rgba(127,127,127,0.3)",
        fixedrange=True,
        showgrid=False,
        ticklen=5,
        ticks="outside",
        tickcolor="rgba(127,127,127,0.3)",
        tickformat=tick_format,
        nticks=max_tick_count,
    )
    fig.update_layout(
        autosize=True,
        bargap=bargap,
        bargroupgap=0.1,
        margin=dict(t=5, l=1, r=1, b=1, pad=0),
        uniformtext_minsize=7,
        height=600,
        paper_bgcolor="rgba(0,0,0,0)",
        plot_bgcolor="rgba(0,0,0,0)",
        hovermode="closest",
        showlegend=False,
        xaxis=dict(
            tickmode="linear"
            if metric.config.time_group == M.TimeGroup.TOTAL
            and metric.config.chart_type != M.SimpleChartType.HORIZONTAL_BAR
            else "auto",
        ),
        uniformtext_mode="hide",
        hoverdistance=12,
    )
    return fig


def get_bargap(fig: go.Figure, metric: M.Metric) -> Optional[float]:
    if metric.config.chart_type == M.SimpleChartType.BAR:
        bar_count = sum([len(data.x) for data in fig.data])
        bargap = (10 - min(bar_count, 9)) / 10
    elif metric.config.chart_type in (
        M.SimpleChartType.STACKED_BAR,
        M.SimpleChartType.PERCENTAGE_STACKED_BAR,
    ):
        max_bars_per_color = max([len(data.x) for data in fig.data])
        color_count = len(fig.data)
        bar_count = max(max_bars_per_color, color_count)
        bargap = (10 - min(bar_count, 9)) / 10
    else:
        bargap = None
    return bargap


def set_heatmap_figure_style(fig: go.Figure, simple_chart: C.SimpleChart) -> go.Figure:
    fig.update_traces(xgap=1, ygap=1, hoverinfo="none", hovertemplate=None)
    fig.update_xaxes(
        side="top",
        showgrid=False,
    )
    fig.update_yaxes(
        side="left",
        title=simple_chart.y_axis_label,
        showgrid=False,
    )
    fig["layout"]["yaxis"]["autorange"] = "reversed"

    fig.update_layout(
        showlegend=False,
        uniformtext_mode="hide",
        margin=dict(t=30, l=1, r=1, b=1, pad=0),
    )
    return fig


def pdf_to_heatmap(pdf: pd.DataFrame) -> Dict[str, List]:
    # Fixing sorting issue of columns in the pivoted table
    pdf[C.X_AXIS_COL] = pdf[C.ORIG_X_AXIS_COL].astype(str).str.pad(
        3, fillchar="0"
    ) + pdf[C.X_AXIS_COL].astype(str)

    pdf[GA.USER_COUNT_COL] = (
        pdf[GA.USER_COUNT_COL + "_1"].astype(str)
        + ","
        + pdf[GA.USER_COUNT_COL + "_2"].astype(str)
    )

    pdf[C.Y_AXIS_COL] = pdf[C.Y_AXIS_COL].fillna(0)

    def create_custom_data(row: pd.Series) -> str:
        res = json.dumps(
            C.PlotCustomData(
                metric_type=M.MetricType.RETENTION,
                chart_type=M.SimpleChartType.HEATMAP,
                agg_title=None,
                color="",
                custom_json=row[C.JSON_COL],
                user_cols=[
                    row[GA.USER_COUNT_COL + "_1"],
                    row[GA.USER_COUNT_COL + "_2"],
                ],
                user_counts=[],
                unit=row[C.SUFFIX_COL],
            ).serialize_to_list()
        )
        return res

    pdf["customdata"] = pdf.apply(
        create_custom_data,
        axis=1,
    )
    # mypy: 'first' is not listed as a valid literal value for aggfunc
    pdf = pd.pivot_table(  # type: ignore[call-overload]
        pdf,
        values=[C.Y_AXIS_COL, C.TEXT_COL, GA.USER_COUNT_COL, "customdata"],
        index=[C.COLOR_COL],
        columns=[C.X_AXIS_COL],
        aggfunc="first",
        sort=False,
    )

    color_vals = list(set(f"{val[1]}" for val in pdf.columns.tolist()))
    # resorting based on padded int as string
    color_vals.sort()
    # removing padding so it is not present anymore
    color_vals = [c[3:] for c in color_vals]
    pdf[C.Y_AXIS_COL] = pdf[C.Y_AXIS_COL].apply(
        lambda values: [0 if str(val) == "nan" else val for val in values]
    )
    pdf[C.TEXT_COL] = pdf[C.TEXT_COL].apply(
        lambda values: [" " if str(val) == "nan" else val for val in values]
    )

    x_vals = [str(v) + "." for v in pdf.index.tolist()]
    res = {
        "x": color_vals,
        "y": x_vals,
        "z": pdf[C.Y_AXIS_COL].values.tolist(),
        "annotation_text": pdf[C.TEXT_COL].values.tolist(),
        # customdata values should be parseable by the PlotCustomData class
        "customdata": pdf["customdata"].values.tolist(),
    }
    return res


def create_sankey_chart(
    pdf: pd.DataFrame,
    metric_context: MC.MetricContext,
    webapp_metric: WM.WebappMetric,
) -> go.Figure:
    components = get_sankey_chart_components(pdf, webapp_metric)
    analyze_by = L.unique_users_agg_label(
        webapp_metric.config.aggregation.type,
        webapp_metric.config,
        metric_context,
        short_form=False,
    )

    fig = go.Figure(
        data=[
            go.Sankey(
                arrangement="fixed",
                node=dict(
                    pad=MIN_NODE_PADDING,
                    thickness=60,
                    line=dict(color="white", width=0),
                    label=[node.label() for node in components.nodes],
                    color=[node.color for node in components.nodes],
                    customdata=[
                        node.custom_data(analyze_by).serialize()
                        for node in components.nodes
                    ],
                    x=get_x_coordinates(components.nodes),
                    y=get_y_coordinates(components.nodes),
                    hovertemplate=None,
                ),
                link=dict(
                    arrowlen=15,
                    source=[link.source_index for link in components.links],
                    target=[link.target_index for link in components.links],
                    value=[link.value for link in components.links],
                    color=[link.color for link in components.links],
                    hovercolor=[link.color for link in components.links],
                    customdata=[
                        data.serialize(analyze_by)
                        for data in components.link_customdata
                    ],
                ),
                hoverinfo="none",  # disable tooltips for now
            )
        ],
    )

    fig.update_layout(
        font_size=12,
        font_family='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,"'
        ' Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"',
        font_color="black",
        margin=dict(t=5, l=3, r=3, b=0, pad=0),
        uniformtext_minsize=7,
    )
    return fig


def apply_final_transformation(
    pdf: pd.DataFrame,
    simple_chart: C.SimpleChart,
    metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
) -> pd.DataFrame:
    pdf[C.ORIG_X_AXIS_COL] = pdf[C.X_AXIS_COL]

    x_axis_label_func = get_x_axis_label_func(simple_chart.chart_type, metric)
    if x_axis_label_func is not None:
        pdf[C.X_AXIS_COL] = pdf[C.X_AXIS_COL].apply(
            lambda val: (x_axis_label_func(val, metric))
        )
    pdf[C.SUFFIX_COL] = simple_chart.yaxis_ticksuffix

    if metric.metric_type == M.MetricType.SEGMENTATION:
        if GA.EVENT_INDEX_COL not in pdf.columns:
            pdf[GA.EVENT_INDEX_COL] = 0
        pdf[C.AGG_TITLE] = pdf[GA.EVENT_INDEX_COL].apply(
            lambda index: L.agg_type_label(
                metric.segments[index].aggregation or metric.config.aggregation,
                metric_context,
                metric.config,
            )
        )
    else:
        pdf[C.AGG_TITLE] = L.agg_type_label(
            metric.config.aggregation, metric_context, metric.config
        )

    return pdf


def generate_color_discrete_map(
    pdf: pd.DataFrame, use_consistent_coloring: bool
) -> Dict[str, str]:
    unique_groups = [g for g in pdf[C.COLOR_COL].unique() if g != OTHER_GROUPS]
    color_map = {
        group: C.get_plotly_color_for_group_or_index(use_consistent_coloring, i, group)
        for i, group in enumerate(unique_groups)
    }
    color_map[OTHER_GROUPS] = C.OTHER_GROUPS_COLOR
    return color_map


def plot_chart(
    simple_chart: C.SimpleChart,
    metric: M.Metric,
    metric_context: MC.MetricContext,
    annotations: List[C.Annotation],
    webapp_metric: WM.WebappMetric,
    default_end_dt: datetime,
) -> Tuple[go.Figure, bool]:
    size = simple_chart.dataframe.shape[0]
    if size > 3000 and metric.type != M.MetricType.JOURNEY:
        # For journey we do post processing to reduce the number of nodes with <other groups>
        raise TooManyDataPointsError(
            f"Too many data points to visualize ({size}), try reducing the scope."
        )

    if size == 0:
        return create_no_data_graph(), True

    pdf = simple_chart.dataframe.copy()
    if metric.type != M.MetricType.JOURNEY:
        pdf = apply_final_transformation(
            pdf,
            simple_chart,
            webapp_metric,
            metric_context,
        )

    ct = simple_chart.chart_type
    if webapp_metric.metric_type != M.MetricType.JOURNEY:
        color_options: Dict[str, Any] = dict(
            color_discrete_map=generate_color_discrete_map(
                pdf, webapp_metric.config.use_consistent_coloring
            )
        )

    else:
        color_options = dict(color_discrete_sequence=list(C.SHADES.values()))

    # backward compatibility: dataframes stored before v0.7.2 does not have a json column
    # containing extra data for the drilldown button
    if C.JSON_COL not in pdf.columns:
        pdf[C.JSON_COL] = None
    if ct == M.SimpleChartType.NUMBER:
        fig = create_fat_number_chart(pdf)
    elif ct == M.SimpleChartType.PIE:
        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
        pdf[C.Y_AXIS_COL] = pdf[C.Y_AXIS_COL].apply(lambda val: round(val, 3))
        fig = px.pie(
            pdf,
            values=C.Y_AXIS_COL,
            names=C.COLOR_COL,
            color=C.COLOR_COL,
            hole=0.7,
            custom_data=custom_data.serialize_to_list(),
            **color_options,
        )
        fig.update_traces(
            textinfo="value+percent+label",
        )
    elif ct in [
        M.SimpleChartType.BAR,
        M.SimpleChartType.STACKED_BAR,
        M.SimpleChartType.PERCENTAGE_STACKED_BAR,
    ]:
        if (
            webapp_metric.metric_type == M.MetricType.CONVERSION
            and webapp_metric.config.time_group == M.TimeGroup.TOTAL
            and webapp_metric.config.aggregation.type == M.AggType.CONVERSION
        ):
            color_map = color_map = generate_color_discrete_map(
                pdf, webapp_metric.config.use_consistent_coloring
            )
            group_col = C.COLOR_COL
            x_col = C.X_AXIS_COL
            y_col = C.Y_AXIS_COL
            traces: List[go.Bar] = []
            fix_bar_heights(pdf)
            for color in pdf[group_col].unique():
                group_df = pdf[pdf[group_col] == color]
                if IS_DROPOFF_COL in group_df.columns:
                    main_df = group_df[~group_df[IS_DROPOFF_COL]]
                    drop_df = group_df[group_df[IS_DROPOFF_COL]]
                else:
                    main_df = group_df
                    drop_df = pd.DataFrame()
                custom_data = C.PlotCustomData.create(metric.type, ct, main_df)
                traces.append(
                    go.Bar(
                        x=main_df[x_col],
                        y=main_df[y_col],
                        name=color,
                        offsetgroup=color,
                        customdata=main_df[custom_data.serialize_to_list()].values,
                        marker=dict(color=color_map[color]),
                        text=main_df[C.TEXT_COL],
                    )
                )

                if not drop_df.empty:
                    traces.append(
                        go.Bar(
                            x=drop_df[x_col],
                            y=drop_df[y_col],
                            name=f"{color} (drop-off)",
                            offsetgroup=color,
                            base=main_df[y_col].values,
                            customdata=drop_df[custom_data.serialize_to_list()].values,
                            opacity=0.3,
                            marker=dict(color=color_map[color]),
                            showlegend=False,  # used only when downloading the png image
                        )
                    )

            layout = go.Layout(
                xaxis_title="Step",
                yaxis_title="Conversion rate",
                barmode="group",
            )

            fig = go.Figure(
                data=traces,
                layout=layout,
            )
        else:
            custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
            barmode = "group" if ct in [M.SimpleChartType.BAR] else "relative"
            orientation = "v"

            if ct == M.SimpleChartType.BAR:
                fix_bar_heights(pdf)

            fig = px.bar(
                pdf,
                x=C.X_AXIS_COL,
                y=C.Y_AXIS_COL,
                text=C.TEXT_COL,
                color=C.COLOR_COL,
                barmode=barmode,
                orientation=orientation,
                custom_data=custom_data.serialize_to_list(),
                labels={
                    C.X_AXIS_COL: simple_chart.x_axis_label,
                    C.Y_AXIS_COL: simple_chart.y_axis_label,
                    C.COLOR_COL: simple_chart.color_label,
                },
                **color_options,
            )
            fig.update_layout(barcornerradius=2)

    elif ct == M.SimpleChartType.LINE:
        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
        fig = px.line(
            pdf,
            x=C.X_AXIS_COL,
            y=C.Y_AXIS_COL,
            text=C.TEXT_COL,
            color=C.COLOR_COL,
            custom_data=custom_data.serialize_to_list(),
            labels={
                C.X_AXIS_COL: simple_chart.x_axis_label,
                C.Y_AXIS_COL: simple_chart.y_axis_label,
                C.COLOR_COL: simple_chart.color_label,
            },
            **color_options,
        )

    elif ct in (
        M.SimpleChartType.STACKED_AREA,
        M.SimpleChartType.PERCENTAGE_STACKED_AREA,
    ):
        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
        fig = px.area(
            pdf,
            x=C.X_AXIS_COL,
            y=C.Y_AXIS_COL,
            text=C.TEXT_COL,
            color=C.COLOR_COL,
            custom_data=custom_data.serialize_to_list(),
            labels={
                C.X_AXIS_COL: simple_chart.x_axis_label,
                C.Y_AXIS_COL: simple_chart.y_axis_label,
                C.COLOR_COL: simple_chart.color_label,
            },
            **color_options,
        )
    elif ct == M.SimpleChartType.GEO_COUNTRY:
        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)

        pdf[C.COLOR_COL] = pdf[C.COLOR_COL].apply(lambda col: C.convert_to_iso3(col))
        max_val = pdf[~pdf[C.COLOR_COL].isna()][C.Y_AXIS_COL].max()
        min_val = pdf[~pdf[C.COLOR_COL].isna()][C.Y_AXIS_COL].min()
        fig = px.choropleth_mapbox(
            pdf,
            geojson=C.COUNTRY_JSONS,
            locations=C.COLOR_COL,
            color=C.Y_AXIS_COL,
            color_continuous_scale="BuPu",
            range_color=(min_val, max_val),
            mapbox_style="carto-positron",
            custom_data=custom_data.serialize_to_list(),
            zoom=1.1,
            center={"lat": 37.0902, "lon": 0},
            opacity=0.8,
        )

        fig.update(layout_coloraxis_showscale=False)
    elif ct == M.SimpleChartType.HEATMAP:
        args = pdf_to_heatmap(pdf)
        fig = ff.create_annotated_heatmap(
            colorscale="BuPu",
            **args,
        )
        fig.update_xaxes(fixedrange=True)
        fig.update_yaxes(fixedrange=True)
        fig = set_heatmap_figure_style(fig, simple_chart)
        for ann in fig.layout.annotations:
            ann.font.size = 10

        return fig, False
    elif ct == M.SimpleChartType.SANKEY:
        fig = create_sankey_chart(pdf, metric_context, webapp_metric)
        return fig, False
    elif (
        ct == M.SimpleChartType.HORIZONTAL_BAR
        and metric.type == M.MetricType.SEGMENTATION
    ):
        pdf[C.COLOR_COL] = pdf[C.COLOR_COL].apply(
            lambda x: x if len(x) <= MAX_LABEL_LENGTH else x[:MAX_LABEL_LENGTH] + "..."
        )

        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
        fig = px.bar(
            pdf,
            x=C.Y_AXIS_COL,
            y=C.COLOR_COL,
            text=C.TEXT_COL,
            color=C.COLOR_COL,
            orientation="h",
            custom_data=custom_data.serialize_to_list(),
            labels={
                C.X_AXIS_COL: simple_chart.x_axis_label,
                C.Y_AXIS_COL: simple_chart.y_axis_label,
                C.COLOR_COL: "",
            },
            **color_options,
        )

    elif (
        ct == M.SimpleChartType.HORIZONTAL_BAR
        and metric.type == M.MetricType.CONVERSION
    ):
        pdf[C.COLOR_COL] = pdf[C.COLOR_COL].apply(
            lambda x: x if len(x) <= MAX_LABEL_LENGTH else x[:MAX_LABEL_LENGTH] + "..."
        )
        color_map = color_map = generate_color_discrete_map(
            pdf, webapp_metric.config.use_consistent_coloring
        )
        custom_data = C.PlotCustomData.create(metric.type, ct, pdf)
        fix_bar_heights(pdf)
        traces = create_grouped_bar_chart(
            metric,
            pdf,
            ct,
            color_map,
            group_col=C.COLOR_COL,
            y_col=C.X_AXIS_COL,
            x_col=C.Y_AXIS_COL,
            orientation="h",
        )
        layout = go.Layout(
            barmode="stack",
            xaxis=dict(range=[0, pdf[C.X_AXIS_COL].max()], automargin=True),
        )
        fig = go.Figure(
            data=traces,
            layout=layout,
        )

    else:
        raise ValueError(f"Unknown char type {ct}")

    if (
        metric.config.chart_type
        in [
            M.SimpleChartType.LINE,
            M.SimpleChartType.BAR,
            M.SimpleChartType.STACKED_BAR,
            M.SimpleChartType.STACKED_AREA,
            M.SimpleChartType.PIE,
        ]
        and metric.config.time_group != M.TimeGroup.TOTAL
    ):
        add_annotations_to_figure(annotations, fig, pdf)
    if (
        webapp_metric.metric_type == M.MetricType.CONVERSION
        and webapp_metric.config.time_group != M.TimeGroup.TOTAL
    ):
        replace_incomplete_area_with_dotted_line(webapp_metric, default_end_dt, fig)
    if webapp_metric.config.chart_type == M.SimpleChartType.HORIZONTAL_BAR:

        fig.update_layout(xaxis_ticksuffix=simple_chart.yaxis_ticksuffix)
    else:
        fig.update_layout(yaxis_ticksuffix=simple_chart.yaxis_ticksuffix)

    fig = set_figure_style(
        fig=fig,
        metric=metric,
        pdf=pdf,
        default_end_dt=default_end_dt,
        y_range_config=webapp_metric.config.y_range_config,
    )

    return fig, False


def create_grouped_bar_chart(
    metric: M.Metric,
    pdf: pd.DataFrame,
    ct: M.SimpleChartType,
    color_map: Dict[str, str],
    group_col: str,
    x_col: str,
    y_col: str,
    orientation: str,
) -> List[go.Bar]:
    traces: List[go.Bar] = []

    for color in pdf[group_col].unique():
        if orientation == "h":
            pdf = pdf.iloc[::-1]
        group_df = pdf[pdf[group_col] == color]
        if IS_DROPOFF_COL in group_df.columns:
            main_df = group_df[~group_df[IS_DROPOFF_COL]]
            drop_df = group_df[group_df[IS_DROPOFF_COL]]
        else:
            main_df = group_df
            drop_df = pd.DataFrame()
        custom_data = C.PlotCustomData.create(metric.type, ct, main_df)

        traces.append(
            go.Bar(
                x=main_df[x_col],
                y=main_df[y_col],
                name=color,
                offsetgroup=color,
                customdata=main_df[custom_data.serialize_to_list()].values,
                marker=dict(color=color_map[color]),
                text=main_df[C.TEXT_COL],
                orientation=orientation,
            )
        )

        if not drop_df.empty:
            traces.append(
                go.Bar(
                    x=drop_df[x_col],
                    y=drop_df[y_col],
                    name=f"{color} (drop-off)",
                    offsetgroup=color,
                    customdata=drop_df[custom_data.serialize_to_list()].values,
                    opacity=0.3,
                    marker=dict(color=color_map[color]),
                    showlegend=False,
                    orientation=orientation,
                )
            )

    return traces


def replace_incomplete_area_with_dotted_line(
    webapp_metric: WM.WebappMetric, default_end_dt: datetime, fig: go.Figure
) -> None:
    if (
        webapp_metric.config.conv_window is None
        or len(webapp_metric.segments) <= 1
        or webapp_metric.config.conv_window_type
        == M.ConversionWindowType.BETWEEN_EACH_STEP
        or webapp_metric.config.chart_type != M.SimpleChartType.LINE
    ):
        return

    conversion_window = webapp_metric.config.conv_window.to_relative_delta()
    default_end_dt = C.make_timezone_aware(default_end_dt)
    dotted_area_start = default_end_dt - conversion_window
    max_x_vals: Optional[datetime] = None
    min_x_vals: Optional[datetime] = None
    for trace in fig.data:
        if "x" not in trace:
            continue

        for x_val in trace.x:
            x_dt = C.make_timezone_aware(ensure_datetime(x_val))
            if max_x_vals is None or max_x_vals < x_dt:
                max_x_vals = x_dt

            if min_x_vals is None or min_x_vals > x_dt:
                min_x_vals = x_dt

    if min_x_vals is None or max_x_vals is None or min_x_vals == max_x_vals:
        return

    x1 = min(default_end_dt, max_x_vals)
    dotted_area_start = max(dotted_area_start, min_x_vals)
    if dotted_area_start > x1:
        return

    new_traces = []
    for trace in fig.data:
        if "x" in trace and "y" in trace:
            x = [ensure_datetime(dt) for dt in trace.x]
            y = trace.y
            customdata = trace.customdata if "customdata" in trace else [None] * len(x)
            solid_segment_x = []
            solid_segment_y = []
            solid_segment_customdata = []
            dotted_segment_x = []
            dotted_segment_y = []
            dotted_segment_customdata = []

            for i, x_val in enumerate(x):
                x_dt = C.make_timezone_aware(ensure_datetime(x_val))

                if x_dt < dotted_area_start:
                    solid_segment_x.append(x_val)
                    solid_segment_y.append(y[i])
                    solid_segment_customdata.append(customdata[i])
                elif dotted_area_start <= x_dt <= x1:

                    dotted_segment_x.append(x_val)
                    dotted_segment_y.append(y[i])
                    dotted_segment_customdata.append(customdata[i])
            if (
                len(dotted_segment_x)
                and len(dotted_segment_y)
                and len(dotted_segment_customdata)
            ):
                solid_segment_x.append(dotted_segment_x[0])
                solid_segment_y.append(dotted_segment_y[0])
                solid_segment_customdata.append(dotted_segment_customdata[0])

            if len(solid_segment_x) > 0:
                new_traces.append(
                    go.Scatter(
                        x=solid_segment_x,
                        y=solid_segment_y,
                        mode=trace.mode,
                        line=dict(dash="solid", color=trace.line.color),
                        name=trace.name,
                        showlegend=trace.showlegend,
                        customdata=solid_segment_customdata,
                        hoverinfo="none",
                    )
                )
            if len(dotted_segment_x) > 0:
                new_traces.append(
                    go.Scatter(
                        x=dotted_segment_x,
                        y=dotted_segment_y,
                        mode=trace.mode,
                        line=dict(dash="dot", color=trace.line.color),
                        name=trace.name,
                        showlegend=trace.showlegend,
                        customdata=dotted_segment_customdata,
                        hoverinfo="none",
                    )
                )

    fig.data = ()
    fig.add_traces(new_traces)


def fix_bar_heights(pdf: pd.DataFrame) -> None:
    if IS_DROPOFF_COL in pdf.columns:
        mask = ~pdf[IS_DROPOFF_COL].fillna(False)
    else:
        mask = pd.Series(True, index=pdf.index)

    max_y_val = pdf.loc[mask, C.Y_AXIS_COL].max()
    pdf.loc[mask, C.Y_AXIS_COL] = pdf.loc[mask, C.Y_AXIS_COL].apply(
        lambda val: max_y_val / 100 if val < max_y_val / 100 else val
    )

    pdf.loc[~mask, C.Y_AXIS_COL] = pdf.loc[~mask, C.Y_AXIS_COL].apply(
        lambda val: max_y_val - max_y_val / 100
        if val > max_y_val - max_y_val / 100
        else val
    )


def format_float(num: Union[float, int]) -> str:
    return str(int(num)) if round(num) == num else f"{num:.4f}"


def create_fat_number_chart(pdf: pd.DataFrame) -> go.Figure:
    fig = go.Figure()
    vals = pdf.to_dict(orient="records")
    size = len(vals)
    texts = []
    ys = []
    xs = []
    for i, val in enumerate(vals):
        ys.append((i + 2) * 10)
        xs.append(0)
        prefix = "" if size == 1 else val[C.COLOR_COL] + ": "
        text = f"{prefix}{format_float(val[C.Y_AXIS_COL])}"
        texts.append(text)

    fig.add_trace(go.Scatter(x=xs, y=ys, text=texts, mode="text"))
    fig.update_traces(textfont_size=64 if size == 1 else 32)
    fig = fig.update_layout(
        yaxis_range=[min(ys) - 1, max(ys) + 1], xaxis_range=[min(xs) - 1, max(xs) + 1]
    )
    return fig


def ensure_datetime(dt: Union[str, datetime, date, numpy.datetime64]) -> datetime:
    if isinstance(dt, str):
        dt = dateutil.parser.parse(dt)

    if isinstance(dt, date) and not isinstance(dt, datetime):
        dt = datetime.combine(dt, datetime.min.time())

    if isinstance(dt, numpy.datetime64):
        return pd.Timestamp(dt).to_pydatetime()

    if not isinstance(dt, datetime):
        raise TypeError(f"Could not convert input to datetime (type: {type(dt)}).")

    return dt


def add_annotations_to_figure(
    annotations: List[C.Annotation], fig: go.Figure, pdf: pd.DataFrame
) -> None:
    try:
        start_dt = ensure_datetime(pdf[C.X_AXIS_COL].min())
        end_dt = ensure_datetime(pdf[C.X_AXIS_COL].max())

        filtered_annotations = C.prepare_annotations(
            annotations, start_dt=start_dt, end_dt=end_dt
        )
        for ann, _ in filtered_annotations:
            date_int = int(ann.annotation_datetime.timestamp()) * 1000
            text = (
                ann.annotation_text
                if len(ann.annotation_text) <= 15
                else ann.annotation_text[:15] + "..."
            )
            fig.add_vline(
                x=date_int,
                line_width=0.8,
                line_dash="dash",
                line_color="#4c6ef5",
                annotation_text=text,
            )
        if "annotations" in fig.to_dict()["layout"]:
            fig.update_layout(
                annotations=[
                    {
                        **a,
                        **{
                            "y": 1 - (filtered_annotations[i][1]) * 0.035,
                            "font": {"color": "gray", "size": 10},
                        },
                    }
                    for i, a in enumerate(fig.to_dict()["layout"]["annotations"])
                ]
            )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to add annotations to the plot")


def create_no_data_graph() -> go.Figure:
    fig = go.Figure()

    fig.add_layout_image(
        dict(
            source="/assets/state_images/empty_state-no_data.svg",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.6,
            sizex=0.8,
            sizey=0.8,
            xanchor="center",
            yanchor="middle",
            layer="above",
        )
    )

    fig.update_xaxes(
        fixedrange=True,
    )
    fig.update_yaxes(
        fixedrange=True,
    )
    fig.update_layout(
        xaxis={"visible": False},
        yaxis={"visible": False},
        plot_bgcolor="rgba(0, 0, 0, 0)",
        paper_bgcolor="rgba(0, 0, 0, 0)",
        annotations=[
            {
                "text": "No data to show",
                "xref": "paper",
                "yref": "paper",
                "showarrow": False,
                "font": {"size": 28},
                "x": 0.5,
                "y": 0.2,
                "xanchor": "center",
                "yanchor": "middle",
            }
        ],
        margin=dict(t=50),
    )

    return fig


def figure_to_base64_image(
    figure: go.Figure,
    scale: float = 1.0,
    kaleid_configs: Optional[Tuple] = None,
    format: str = "svg",
    base64_format: str = "data:image/svg+xml;base64,",
) -> str:
    try:
        if kaleid_configs is not None:
            pio.kaleido.scope.mathjax = None
            pio.kaleido.scope.chromium_args += kaleid_configs

        img_bytes = figure.to_image(
            format=format,
            scale=scale,
        )
        return f"{base64_format}" + b64encode(img_bytes).decode()
    except BaseException as exc:
        LOGGER.error("Failed to render saved insight %s", str(exc))
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mO8XQ8AAjsBXM7pODsAAAAASUVORK5CYII="
