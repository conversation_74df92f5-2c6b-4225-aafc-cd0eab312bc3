from __future__ import annotations

import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd

import mitzu.helper as H
import mitzu.model as M
import mitzu.webapp.model as WM
from mitzu.visualization.common import (
    SHADES,
    get_graph_height,
)
from mitzu.visualization.preprocessor.common import (
    OTHER_GROUPS,
)

MAX_NODE_PER_STEP = 10
DROP_OFF_GROUP = "<drop off>"
MAX_GROUP_LENGTH = 20
MIN_NODE_PADDING = 10
HIDDEN_LINK = "rgba(230,230,230,0.4)"
HIDDEN_NODE = "rgba(230,230,230,0.7)"
OTHER_GROUPS_COL = "rgba(220,220,250,1)"

PLACEHOLDER_GROUP = "##placeholder##"
PLACEHOLDER_COLOR = "rgba(255,255,255,0)"


@dataclass(frozen=True)
class SankeyLinkCustomData:
    source_user_count: int
    target_user_count: int
    source_step: int
    source_group: str
    target_group: str

    def serialize(self, analyze_by: str) -> str:
        return json.dumps(
            {
                "type": "sankey_link",
                "source_user_count": self.source_user_count,
                "target_user_count": self.target_user_count,
                "source_step": self.source_step,
                "source_group": self.source_group,
                "target_group": self.target_group,
                "analyze_by": analyze_by,
            }
        )


@dataclass(frozen=True)
class SankeyNodeCustomData:
    step: int
    group: Optional[str]
    user_count: int
    analyze_by: Optional[str]

    def serialize(self) -> str:
        return json.dumps(
            {
                "type": "sankey_node",
                "step": self.step,
                "group": self.group,
                "user_count": self.user_count,
                "analyze_by": self.analyze_by,
            }
        )

    @classmethod
    def parse(cls, data: Dict[str, Any]) -> SankeyNodeCustomData:
        return SankeyNodeCustomData(
            step=data["step"],
            group=data.get("group"),
            user_count=data["user_count"],
            analyze_by=data.get("analyze_by"),
        )


@dataclass(frozen=True)
class Node:
    step: int  # in the funnel, starting with 0
    group: str
    index: int  # index in the sankey chart
    user_count: int
    segment_title: Optional[str]

    def label(self) -> str:
        if self.group == PLACEHOLDER_GROUP:
            return ""

        group = self.group_str()

        if len(group) >= MAX_GROUP_LENGTH:
            group = group[:MAX_GROUP_LENGTH] + "..."

        return f"{group} ({self.user_count})"

    def group_str(self) -> str:
        if self.group == H.GROUP_MISSING_VALUE:
            group = "<n/a>"
        elif self.group == PLACEHOLDER_GROUP:
            return ""
        else:
            group = self.group

        if self.group == H.NO_GROUP:
            group = self.segment_title or f"Step {self.step}."
        elif self.segment_title is not None:
            group += " - " + self.segment_title

        return group

    @property
    def color(self) -> str:
        color_list = list(SHADES.values())
        if self.group == DROP_OFF_GROUP or self.user_count <= 0:
            return HIDDEN_NODE
        elif self.group == OTHER_GROUPS:
            return OTHER_GROUPS_COL
        elif self.group == PLACEHOLDER_GROUP:
            return PLACEHOLDER_COLOR
        return hex_to_rgba(color_list[self.index % len(color_list)], 1)

    def custom_data(self, analyze_by: str) -> SankeyNodeCustomData:
        return SankeyNodeCustomData(
            step=self.step,
            group=self.group_str()
            if self.group not in [H.NO_GROUP, PLACEHOLDER_GROUP]
            else None,
            user_count=self.user_count,
            analyze_by=analyze_by,
        )


@dataclass
class Link:
    source_index: int
    target_index: int
    value: int
    color: str


@dataclass
class SankeyChartComponents:
    nodes: List[Node]
    links: List[Link]
    link_customdata: List[SankeyLinkCustomData]


def hex_to_rgba(hex: str, opacity: float = 1.0) -> str:
    return f"rgba({int(hex[1:3], 16)}, {int(hex[3:5], 16)}, {int(hex[5:7], 16)}, {opacity})"


def normalize_pos(pos: float) -> float:
    if pos == 0:
        return 0.01
    if pos == 1:
        return 1 - 0.01
    return pos


def get_x_coordinates(nodes: List[Node]) -> List[float]:
    journey_length = max([n.step for n in nodes])
    res = []
    for node in nodes:
        res.append((node.step - 1) / (journey_length - 1))

    return [normalize_pos(x) for x in res]


def get_y_coordinates(nodes: List[Node]) -> List[float]:
    MAX_HEIGHT = get_graph_height(M.MetricType.JOURNEY)

    users_count_by_steps: Dict[int, int] = {}
    node_count_by_step: Dict[int, int] = {}
    number_of_steps = 1
    for node in nodes:
        if node.step not in node_count_by_step.keys():
            node_count_by_step[node.step] = 0
        node_count_by_step[node.step] += 1

        if node.step not in users_count_by_steps.keys():
            users_count_by_steps[node.step] = 0
        users_count_by_steps[node.step] += node.user_count

        if node.step > number_of_steps:
            number_of_steps = node.step

    # get the highest column with the most users or nodes and figure out the pixel per user ratio
    pixel_per_user = min(
        [
            (MAX_HEIGHT - ((node_count_by_step[index] - 1) * MIN_NODE_PADDING))
            / users_count_by_steps[index]
            for index in range(1, number_of_steps + 1)
        ]
    )
    offsets = {index: 0.01 for index in range(1, number_of_steps + 1)}
    paddings = {
        index: (MAX_HEIGHT - users_count_by_steps[index] * pixel_per_user)
        / (node_count_by_step[index] - 1)
        if node_count_by_step[index] > 1
        else 0
        for index in range(1, number_of_steps + 1)
    }
    res = []
    for node in nodes:
        center = node.user_count / 2 * pixel_per_user
        res.append((offsets[node.step] + center))
        offsets[node.step] += 2 * center + paddings[node.step]

    return [y / MAX_HEIGHT for y in res]


def get_sankey_chart_components(
    pdf: pd.DataFrame,
    webapp_metric: WM.WebappMetric,
) -> SankeyChartComponents:
    journey_length = len(webapp_metric.segments)
    nodes = {}
    node_index = 0
    color_list = list(SHADES.values())
    previous_step_user_count = 0

    for step in range(1, journey_length + 1):
        user_counts_by_groups: Dict[str, int] = {}
        current_step_user_count = 0
        for _, row in pdf.iterrows():
            _, group = row[f"_group_{step}"]
            user_count = row[f"_user_count_{step}"]

            if user_count < 1:  # avoid from adding empty nodes
                continue

            if group not in user_counts_by_groups.keys():
                user_counts_by_groups[group] = 0

            user_counts_by_groups[group] += user_count
            current_step_user_count += user_count

        sorted_groups = sorted(
            user_counts_by_groups.items(), key=lambda x: x[1], reverse=True
        )

        for group, user_count in sorted_groups[0:MAX_NODE_PER_STEP]:
            nodes[(step, group)] = Node(
                step,
                group,
                node_index,
                user_count,
                webapp_metric.segments[step - 1].title,
            )
            node_index += 1

        if len(sorted_groups) > MAX_NODE_PER_STEP:
            nodes[(step, OTHER_GROUPS)] = Node(
                step,
                OTHER_GROUPS,
                node_index,
                sum([g[1] for g in sorted_groups[MAX_NODE_PER_STEP:]]),
                webapp_metric.segments[step - 1].title,
            )
            node_index += 1

        if current_step_user_count < previous_step_user_count:
            nodes[(step, DROP_OFF_GROUP)] = Node(
                step,
                DROP_OFF_GROUP,
                node_index,
                previous_step_user_count - current_step_user_count,
                webapp_metric.segments[step - 1].title,
            )
            node_index += 1

        previous_step_user_count = current_step_user_count

    # Create links based on user counts
    links: Dict[Tuple[int, int], Link] = {}

    # these are a workaround to fix the chart rendering by making all nodes a bit smaller
    nodes[(1, PLACEHOLDER_GROUP)] = Node(1, PLACEHOLDER_GROUP, node_index, 1, None)
    links[(node_index, node_index)] = Link(node_index, node_index, 1, PLACEHOLDER_COLOR)

    for _, row in pdf.iterrows():
        for step in range(1, journey_length):
            next_user_count = row[f"_user_count_{step+1}"]
            source_id = row[f"_group_{step}"]

            if next_user_count > 0:
                if source_id not in nodes.keys():
                    source_id = (source_id[0], OTHER_GROUPS)
                if source_id not in nodes.keys():
                    continue
                source = nodes[source_id]

                target_id = row[f"_group_{step+1}"]
                if target_id not in nodes.keys():
                    target_id = (target_id[0], OTHER_GROUPS)
                if target_id not in nodes.keys():
                    continue
                target = nodes[target_id]

                key = (source.index, target.index)
                if key in links.keys():
                    links[key].value += next_user_count
                else:
                    links[key] = Link(
                        source_index=source.index,
                        target_index=target.index,
                        value=next_user_count,
                        color=hex_to_rgba(
                            color_list[source.index % len(color_list)], 0.5
                        )
                        if target.user_count > 0 and next_user_count > 0
                        else HIDDEN_LINK,
                    )

            dropoff_user_count = row[f"_user_count_{step}"] - next_user_count
            if dropoff_user_count > 0:
                if source_id not in nodes.keys():
                    source_id = (source_id[0], OTHER_GROUPS)
                source = nodes[source_id]

                target_id = (step + 1, DROP_OFF_GROUP)
                target = nodes[target_id]

                key = (source.index, target.index)
                if key in links.keys():
                    links[key].value += dropoff_user_count
                else:
                    links[key] = Link(
                        source_index=source.index,
                        target_index=target.index,
                        value=dropoff_user_count,
                        color=HIDDEN_LINK,
                    )

    link_customdata: List[SankeyLinkCustomData] = []

    sorted_nodes: List[Node] = sorted(nodes.values(), key=lambda node: node.index)
    link_list = list(links.values())

    for link in link_list:
        src_node = sorted_nodes[link.source_index]
        tgt_node = sorted_nodes[link.target_index]
        link_customdata.append(
            SankeyLinkCustomData(
                source_user_count=src_node.user_count,
                target_user_count=link.value,
                source_step=src_node.step,
                source_group=src_node.group_str() or "",
                target_group=tgt_node.group_str() or "",
            )
        )

    return SankeyChartComponents(
        nodes=sorted_nodes,
        links=link_list,
        link_customdata=link_customdata,
    )
