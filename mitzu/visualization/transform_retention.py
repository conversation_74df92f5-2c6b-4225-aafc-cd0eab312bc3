from __future__ import annotations

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.model as WM


def fix_retention(pdf: pd.DataFrame, metric: WM.WebappMetric) -> pd.DataFrame:
    pdf[GA.AGG_VALUE_COL] = round(pdf[GA.AGG_VALUE_COL], 2)
    if metric.config.time_window_chooser_config.time_group not in [
        M.TimeGroup.HOUR,
        M.TimeGroup.MINUTE,
        M.TimeGroup.SECOND,
        M.TimeGroup.TOTAL,
    ]:
        pdf[GA.DATETIME_COL] = pdf[GA.DATETIME_COL].dt.date
    return pdf


def rename_retention_df_columns(
    pdf: pd.DataFrame, metric: WM.WebappMetric
) -> pd.DataFrame:
    mapping = {
        GA.AGG_VALUE_COL: C.Y_AXIS_COL,
    }
    if metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL:
        mapping[GA.RETENTION_INDEX] = C.X_AXIS_COL
    else:
        if (
            metric.segments[0].group_by
            and metric.config.chart_type == M.SimpleChartType.HEATMAP
        ):
            raise Exception(
                "Break downs are not supported for retention over time metric in heatmap view."
            )
        if metric.config.chart_type == M.SimpleChartType.HEATMAP:
            mapping[GA.RETENTION_INDEX] = C.X_AXIS_COL
        else:
            mapping[GA.DATETIME_COL] = C.X_AXIS_COL
    return pdf.rename(columns=mapping)
