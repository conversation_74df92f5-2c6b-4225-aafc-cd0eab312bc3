from pandas import DataFrame

from mitzu.model import Project
from mitzu.visualization.comparison import (
    create_shifted_comparison_chart,
    should_show_comparison_chart,
)
from mitzu.visualization.preprocessor.common import (
    fix_empty_group_by_cols,
    should_handle_null_as_dropoff,
    subtitute_segment_labels_in_group_bys,
)
from mitzu.webapp.metric_context import MetricContext
from mitzu.webapp.model import WebappMetric


def apply_post_processing_to_dataframe(
    df: DataFrame,
    webapp_metric: WebappMetric,
    project: Project,
    metric_context: MetricContext,
) -> DataFrame:

    if (
        should_show_comparison_chart(df, webapp_metric)
        and webapp_metric.config.comparison_options
    ):
        df = fix_empty_group_by_cols(df, should_handle_null_as_dropoff(webapp_metric))
        df = subtitute_segment_labels_in_group_bys(
            df,
            webapp_metric=webapp_metric,
            metric_context=metric_context,
        )
        df = create_shifted_comparison_chart(df, webapp_metric, project)
    return df
