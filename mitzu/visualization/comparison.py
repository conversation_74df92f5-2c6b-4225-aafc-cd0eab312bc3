from typing import Dict

import pandas as pd

import mitzu.visualization.common as C
from mitzu.adapters.generic_adapter import DATETIME_COL
from mitzu.constants import N_PER_A
from mitzu.model import Project, TimeGroup
from mitzu.webapp.model import WebappMetric


def should_show_comparison_chart(df: pd.DataFrame, metric: WebappMetric) -> bool:
    return (
        metric.config.comparison_options is not None
        and metric.config.comparison_options.time_window is not None
        and len(metric.segments) > 0
        and metric.config.time_group != TimeGroup.TOTAL
        and not df.empty
    )


def create_shifted_comparison_chart(
    df: pd.DataFrame,
    webapp_metric: WebappMetric,
    project: Project,
) -> pd.DataFrame:
    """Create a chart based on the existing charts shifted by a certain time_window value"""
    metric_config = webapp_metric.config

    if not metric_config.comparison_options:
        return df
    original_df = df.copy()
    original_start_date = metric_config.calculate_start_dt(project.get_default_end_dt())
    original_end_date = metric_config.calculate_end_dt(project.get_default_end_dt())
    comparison_options = metric_config.comparison_options
    if comparison_options.time_window.period == TimeGroup.DAY:
        shift_offset = pd.DateOffset(days=comparison_options.time_window.value)
    elif comparison_options.time_window.period == TimeGroup.WEEK:
        shift_offset = pd.DateOffset(weeks=comparison_options.time_window.value)
    elif comparison_options.time_window.period == TimeGroup.MONTH:
        shift_offset = pd.DateOffset(months=comparison_options.time_window.value)
    elif comparison_options.time_window.period == TimeGroup.YEAR:
        shift_offset = pd.DateOffset(years=comparison_options.time_window.value)
    else:
        raise ValueError("Invalid time group")

    shifted_rows = []
    for _, row in original_df.iterrows():
        shifted_row = row.copy()
        shifted_row[DATETIME_COL] = row[DATETIME_COL] + shift_offset
        if shifted_row[DATETIME_COL].tz_localize(None) <= original_end_date:
            shifted_row[C.IS_COMPARISON_COL] = True
            shifted_row[C.ORIGINAL_DATETIME] = row[DATETIME_COL]
            shifted_rows.append(shifted_row)

    original_df[C.IS_COMPARISON_COL] = False
    original_df[C.ORIGINAL_DATETIME] = original_df[DATETIME_COL]
    shifted_df = pd.DataFrame(shifted_rows)
    result_df = pd.concat([shifted_df, original_df]).reset_index(drop=True)
    result_df = result_df[
        (result_df[DATETIME_COL].dt.tz_localize(None) >= original_start_date)
        & (result_df[DATETIME_COL].dt.tz_localize(None) <= original_end_date)
    ]
    return result_df


def get_finalized_label(
    current_label: str, is_compaison_row: bool, label_mapping: Dict[str, str]
) -> str:
    if current_label == N_PER_A and is_compaison_row:
        label = "previous"
    elif is_compaison_row:
        label = f"{current_label} (previous)"
    else:
        label = current_label

    return label_mapping.get(label, label)
