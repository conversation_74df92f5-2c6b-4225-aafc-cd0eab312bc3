from __future__ import annotations

from typing import List

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.visualization.labels import get_segment_label
from mitzu.visualization.preprocessor.common import (
    ORIGINAL_GROUP_COL,
)

STEP_COL = "_step"
STEP_INDEX = "_step_index"


def get_conversion_cols(metric: WM.WebappMetric) -> List[str]:
    funnel_length = len(metric.segments)
    return [f"{GA.AGG_VALUE_COL}_{index}" for index in range(1, funnel_length + 1)]


def fix_conv_times_pdf(pdf: pd.DataFrame, metric: WM.WebappMetric) -> pd.DataFrame:
    cols = get_conversion_cols(metric)
    max_ttc = pdf[cols].max(axis=1).max(axis=0)

    for key in cols:
        if 0 <= max_ttc <= C.TTC_RANGE_1_SEC:
            pdf[key] = pdf[key].round(1)
        elif C.TTC_RANGE_1_SEC < max_ttc <= C.TTC_RANGE_2_SEC:
            pdf[key] = pdf[key].div(60).round(1)
        elif C.TTC_RANGE_2_SEC < max_ttc <= C.TTC_RANGE_3_SEC:
            pdf[key] = pdf[key].div(3600).round(1)
        else:
            pdf[key] = pdf[key].div(86400).round(1)
    return pdf


def fix_conversion_steps_na_cols(
    pdf: pd.DataFrame, metric: WM.WebappMetric
) -> pd.DataFrame:
    funnel_length = len(metric.segments)
    cols = [f"{GA.AGG_VALUE_COL}_{i}" for i in range(1, funnel_length + 1)]
    pdf[cols] = pdf[cols].fillna(0)
    return pdf


def get_melted_conv_column(
    column_prefix: str,
    display_name: str,
    pdf: pd.DataFrame,
    webapp_metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
) -> pd.DataFrame:

    if webapp_metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY:
        i = len(webapp_metric.segments)
        value_vars = [f"{column_prefix}{i}"]
        column_names = {
            f"{column_prefix}{i}": get_segment_label(
                webapp_metric.segments[i - 1], metric_context
            )
        }
    else:
        zfill = 2 if len(webapp_metric.segments) > 9 else 1

        value_vars = [
            f"{column_prefix}{i+1}" for i, _ in enumerate(webapp_metric.segments)
        ]
        column_names = {
            f"{column_prefix}{i+1}": f"{str(i + 1).zfill(zfill)}. "
            + get_segment_label(webapp_metric.segments[i], metric_context)
            for i, val in enumerate(webapp_metric.segments)
        }

    res = pd.melt(
        pdf,
        id_vars=[GA.GROUP_COL, ORIGINAL_GROUP_COL, C.SORTING_COL],
        value_vars=value_vars,
        var_name=STEP_COL,
        value_name=display_name,
    )
    res[STEP_COL] = res[STEP_COL].replace(column_names)
    return res


def get_melted_conv_pdf(
    pdf: pd.DataFrame, webapp_metric: WM.WebappMetric, metric_context: MC.MetricContext
) -> pd.DataFrame:
    pdf1 = get_melted_conv_column(
        f"{GA.AGG_VALUE_COL}_", GA.AGG_VALUE_COL, pdf, webapp_metric, metric_context
    )
    pdf2 = get_melted_conv_column(
        f"{GA.USER_COUNT_COL}_", GA.USER_COUNT_COL, pdf, webapp_metric, metric_context
    )
    res = pdf1
    res = pd.merge(
        res,
        pdf2,
        left_on=[GA.GROUP_COL, ORIGINAL_GROUP_COL, STEP_COL, C.SORTING_COL],
        right_on=[GA.GROUP_COL, ORIGINAL_GROUP_COL, STEP_COL, C.SORTING_COL],
    )

    # This is needed so we could retrieve the index of the segment properly in conversion metrics using group_bys
    res[STEP_INDEX] = (res[STEP_COL] != res[STEP_COL].shift()).cumsum() - 1
    return res
