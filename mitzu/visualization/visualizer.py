from __future__ import annotations

from dataclasses import dataclass

import pandas as pd

from mitzu.model import Metric, Project
from mitzu.visualization.charts import get_simple_chart
from mitzu.visualization.common import SimpleChart
from mitzu.visualization.post_processing import (
    apply_post_processing_to_dataframe,
)
from mitzu.visualization.tables import (
    prepare_dataframe_for_event_list_table,
    prepare_dataframe_for_table,
)
from mitzu.webapp.metric_context import MetricContext
from mitzu.webapp.model import WebappMetric


@dataclass(frozen=True)
class Visualizer:
    dataframe: pd.DataFrame
    webapp_metric: WebappMetric
    project: Project
    metric_context: MetricContext
    metric: Metric

    @classmethod
    def create_visualizer(
        cls,
        dataframe: pd.DataFrame,
        webapp_metric: WebappMetric,
        project: Project,
        metric_context: MetricContext,
        metric: Metric,
    ) -> Visualizer:
        processed_dataframe = apply_post_processing_to_dataframe(
            dataframe, webapp_metric, project, metric_context
        )
        return cls(
            dataframe=processed_dataframe,
            webapp_metric=webapp_metric,
            project=project,
            metric_context=metric_context,
            metric=metric,
        )

    def generate_chart(self) -> SimpleChart:
        return get_simple_chart(
            self.webapp_metric, self.dataframe, self.metric_context, self.project
        )

    def generate_table(self) -> pd.DataFrame:
        return prepare_dataframe_for_table(
            self.webapp_metric,
            self.dataframe,
            self.metric_context,
            is_event_list_modal=False,
        )

    def generate_list_events_table(self) -> pd.DataFrame:
        return prepare_dataframe_for_event_list_table(
            self.webapp_metric, self.dataframe, self.metric_context
        )
