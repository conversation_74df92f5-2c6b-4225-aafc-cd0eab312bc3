from typing import List, Tuple

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.visualization.labels as L
import mitzu.webapp.model as WM
from mitzu.helper import get_segment_letter
from mitzu.visualization.comparison import (
    get_finalized_label,
)
from mitzu.visualization.preprocessor.base_preprocessor import (
    BasePreprocessor,
    PreprocessedDataFrame,
)
from mitzu.visualization.preprocessor.common import (
    AGG_VALUE_DIFF_COL,
    ORIGINAL_GROUP_COL,
    calculate_percentage_change,
    filter_top_groups,
    fix_empty_group_by_cols,
    format_custom_json_datetime,
    format_number,
    get_readable_group_by_values,
    split_by_separator,
)


class SegmentationPreprocessor(BasePreprocessor):
    def preprocess_df(
        self,
        result_df: pd.DataFrame,
    ) -> PreprocessedDataFrame:
        result_df = fix_empty_group_by_cols(result_df, handle_null_as_dropoff=False)
        if C.IS_COMPARISON_COL in result_df.columns:
            comparison_df = result_df[result_df[C.IS_COMPARISON_COL]]
            non_comparison_df = result_df[~result_df[C.IS_COMPARISON_COL]]

            (
                comparison_pdf,
                comparison_groups,
            ) = self._get_preprocessed_segmentation_dataframe(comparison_df)
            (
                non_comparison_pdf,
                non_comparison_groups,
            ) = self._get_preprocessed_segmentation_dataframe(non_comparison_df)

            pdf = pd.concat([comparison_pdf, non_comparison_pdf])
            all_groups = comparison_groups + non_comparison_groups
        else:
            pdf, all_groups = self._get_preprocessed_segmentation_dataframe(result_df)

        return PreprocessedDataFrame(pdf, all_groups, suffix="")

    def _get_preprocessed_segmentation_dataframe(
        self,
        pdf: pd.DataFrame,
    ) -> Tuple[pd.DataFrame, List[str]]:
        if pdf.shape[0] == 0:
            return pdf, []
        if self.project.insight_settings.fill_missing_values_with_zero:
            pdf = self.fill_segmentation_missing_with_zeros(pdf)
        pdf = self._apply_segmentation_chart_post_processing(pdf)
        pdf, all_groups = filter_top_groups(
            pdf, self.metric, order_by_col=GA.AGG_VALUE_COL
        )

        pdf = C.extend_df_with_sorting_column(self.metric, pdf)

        self._add_percentage_column_if_needed(pdf)
        if self.metric.config.formula:

            if self.metric.config.time_group != M.TimeGroup.TOTAL:
                pdf = pdf[~pdf[GA.DATETIME_COL].isnull()]
            pdf = pdf[~pdf[GA.AGG_VALUE_COL].isnull()].reset_index(drop=True)
            if pdf.empty:
                return pdf, all_groups
            pdf = calculate_percentage_change(pdf)

            pdf[GA.GROUP_COL] = pdf.apply(
                lambda row: (
                    get_finalized_label(
                        self._get_group_by_label_with_segment_details(row),
                        is_compaison_row=C.IS_COMPARISON_COL in row
                        and row[C.IS_COMPARISON_COL],
                        label_mapping=self.metric.label_mapping,
                    )
                ),
                axis=1,
            )
            pdf[C.JSON_COL] = pdf.apply(
                lambda row: self._get_tooltip_custom_json_with_formula_expression(
                    row
                ).to_json(),
                axis=1,
            )

        else:
            pdf[GA.GROUP_COL] = pdf.apply(
                lambda row: get_finalized_label(
                    self._get_group_by_label_with_segment_details(row),
                    is_compaison_row=C.IS_COMPARISON_COL in row
                    and row[C.IS_COMPARISON_COL],
                    label_mapping=self.metric.label_mapping,
                ),
                axis=1,
            )

            pdf = calculate_percentage_change(pdf)
            pdf[C.JSON_COL] = pdf.apply(
                lambda row: self._get_tooltip_custom_json(row).to_json(),
                axis=1,
            )

        pdf[C.COLOR_COL] = pdf[GA.GROUP_COL].fillna("")
        pdf[C.X_AXIS_COL] = pdf[GA.DATETIME_COL].fillna("")
        if self.metric.config.chart_type in (
            M.SimpleChartType.PERCENTAGE_STACKED_AREA,
            M.SimpleChartType.PERCENTAGE_STACKED_BAR,
        ):
            pdf[C.Y_AXIS_COL] = pdf[C.PERCENTAGE_COL]
        else:
            pdf[C.Y_AXIS_COL] = pdf[GA.AGG_VALUE_COL]

        pdf[C.TEXT_COL] = round(pdf[C.Y_AXIS_COL], 2)
        pdf = pdf.sort_values([C.X_AXIS_COL, C.SORTING_COL], ascending=[True, False])

        pdf.drop(
            columns=[
                C.SORTING_COL,
                GA.GROUP_COL,
                GA.DATETIME_COL,
                C.PERCENTAGE_COL,
                GA.AGG_VALUE_COL,
            ],
            inplace=True,
            errors="ignore",  # some columns may not exist
        )
        return pdf, all_groups

    def _apply_segmentation_chart_post_processing(
        self, pdf: pd.DataFrame
    ) -> pd.DataFrame:
        prcs = self.metric.config.chart_post_processing
        if prcs:
            pdf.sort_values(by=GA.DATETIME_COL, inplace=True)
            grouped = pdf.groupby([GA.GROUP_COL, GA.EVENT_INDEX_COL])
            rw = prcs.rolling_window_size if prcs.rolling_window_size else 1
            if prcs.post_processing_type == M.ChartPostProcessingType.ROLLING_AVG:
                pdf[GA.AGG_VALUE_COL] = (
                    grouped[GA.AGG_VALUE_COL]
                    .rolling(window=rw, min_periods=1)
                    .mean()
                    .reset_index(level=[0, 1], drop=True)
                )

            elif prcs.post_processing_type == M.ChartPostProcessingType.CUMMULATIVE_SUM:
                pdf[GA.AGG_VALUE_COL] = grouped[GA.AGG_VALUE_COL].cumsum()

        return pdf

    def _add_percentage_column_if_needed(
        self,
        pdf: pd.DataFrame,
    ) -> None:
        if self.metric.config.chart_type in (
            M.SimpleChartType.PERCENTAGE_STACKED_AREA,
            M.SimpleChartType.PERCENTAGE_STACKED_BAR,
        ):
            if (
                self.metric.config.time_window_chooser_config.time_group
                == M.TimeGroup.TOTAL
            ):
                total_values_by_date = pdf[GA.AGG_VALUE_COL].sum()
                pdf[C.PERCENTAGE_COL] = round(
                    (pdf[GA.AGG_VALUE_COL] / total_values_by_date) * 100, 2
                )
            else:
                total_values_by_date = pdf.groupby(GA.DATETIME_COL)[
                    GA.AGG_VALUE_COL
                ].transform("sum")
                pdf[C.PERCENTAGE_COL] = round(
                    (pdf[GA.AGG_VALUE_COL] / total_values_by_date) * 100, 2
                )

    def _get_tooltip_custom_json_with_formula_expression(
        self, row: pd.Series
    ) -> WM.TooltipCustomJson:
        return WM.TooltipCustomJson(
            segment_index=WM.FORMULA_SEGMENT_INDEX,
            version=1,
            break_down=None,
            event_label=format_custom_json_datetime(row, self.metric.config.time_group),
            event_value=row[GA.GROUP_COL],
            title_label="Calculated formula",
            title_value=(
                format_number(row[GA.AGG_VALUE_COL], "")
                if GA.AGG_VALUE_COL in row and row[GA.AGG_VALUE_COL] is not None
                else None
            ),
            subtitle=(
                format_number(row[AGG_VALUE_DIFF_COL], "%")
                if not pd.isna(row[AGG_VALUE_DIFF_COL])
                else None
            ),
            normalized_percentage=row[C.PERCENTAGE_COL]
            if C.PERCENTAGE_COL in row
            else None,
            is_comparison=row[C.IS_COMPARISON_COL] is True
            if C.IS_COMPARISON_COL in row
            else None,
            direction="bottom"
            if self.metric.config.chart_type == M.SimpleChartType.HORIZONTAL_BAR
            else None,
        )

    def _get_tooltip_custom_json(self, row: pd.Series) -> WM.TooltipCustomJson:
        return WM.TooltipCustomJson(
            segment_index=row[GA.EVENT_INDEX_COL],
            version=1,
            # We need to add the check because if group_by is empty the GROUP_COL contains the event's name
            break_down=(
                split_by_separator(row[ORIGINAL_GROUP_COL])
                if self.metric.segments[row[GA.EVENT_INDEX_COL]].group_by
                else None
            ),
            event_label=format_custom_json_datetime(row, self.metric.config.time_group),
            event_value=row[GA.GROUP_COL],
            title_label=(
                L.agg_type_label(
                    self.metric.segments[row[GA.EVENT_INDEX_COL]].aggregation,
                    self.metric_context,
                    self.metric.config,
                )
                if self.metric.segments[row[GA.EVENT_INDEX_COL]].aggregation is not None
                else None
            ),
            title_value=(
                format_number(row[GA.AGG_VALUE_COL], "")
                if GA.AGG_VALUE_COL in row and row[GA.AGG_VALUE_COL] is not None
                else None
            ),
            subtitle=(
                format_number(row[AGG_VALUE_DIFF_COL], "%")
                if not pd.isna(row[AGG_VALUE_DIFF_COL])
                and row[GA.DATETIME_COL] is not None
                else None
            ),
            normalized_percentage=row[C.PERCENTAGE_COL]
            if C.PERCENTAGE_COL in row
            else None,
            is_comparison=row[C.IS_COMPARISON_COL] is True
            if C.IS_COMPARISON_COL in row
            else None,
            direction="bottom"
            if self.metric.config.chart_type == M.SimpleChartType.HORIZONTAL_BAR
            else None,
        )

    def _get_group_by_label_with_segment_details(self, row: pd.Series) -> str:
        segment_letter = get_segment_letter(row[GA.EVENT_INDEX_COL]) + ". "
        has_group_by = len(self.metric.segments[row[GA.EVENT_INDEX_COL]].group_by) > 0
        if self.metric.config.formula is not None:
            segment_title = "Formula calculation"
            segment_letter = ""
            has_group_by = False
            for segment in self.metric.segments:
                if len(segment.group_by) > 0:
                    has_group_by = True
                    break

        elif self.metric.segments[row[GA.EVENT_INDEX_COL]].title is not None:
            segment_title = self.metric.segments[row[GA.EVENT_INDEX_COL]].title
        else:
            segment_title = L.get_segment_label(
                self.metric.segments[row[GA.EVENT_INDEX_COL]],
                self.metric_context,
            )

        substituted_group_by_vals = get_readable_group_by_values(
            row[GA.GROUP_COL], self.metric, self.metric_context
        )

        serialized_group_by = (
            ", ".join(substituted_group_by_vals) if has_group_by else ""
        )
        if len(self.metric.segments) > 1:
            return (
                serialized_group_by
                + (" - " if serialized_group_by != "" else "")
                + segment_letter
                + (segment_title)
            )
        elif self.metric.segments[row[GA.EVENT_INDEX_COL]].group_by:
            return serialized_group_by + (
                "" if self.metric.config.formula is None else " - Formula calculation"
            )
        else:
            return segment_title

    def fill_segmentation_missing_with_zeros(
        self,
        pdf: pd.DataFrame,
    ) -> pd.DataFrame:
        if self.metric.config.time_group == M.TimeGroup.TOTAL:
            return pdf
        start_dt, end_dt = self.metric.config.calculate_start_dt(
            self.project.get_default_end_dt()
        ), self.metric.config.calculate_end_dt(self.project.get_default_end_dt())
        # We need to fill all missing date with date_trunc kept in mind.
        # We need to reproduce the exact same dates that are returned from SQL
        all_dates = C.generate_dts_between(
            start_dt,
            end_dt,
            self.metric.config.time_group,
        )
        all_groups = pdf[GA.GROUP_COL].unique()
        all_events = pdf[GA.EVENT_INDEX_COL].unique()
        # Create a new DataFrame with all combinations of dates and groups
        if C.IS_COMPARISON_COL in pdf.columns:
            all_is_comparison = pdf[C.IS_COMPARISON_COL].unique()
            index = pd.MultiIndex.from_product(
                [all_dates, all_groups, all_events, all_is_comparison],
                names=[
                    GA.DATETIME_COL,
                    GA.GROUP_COL,
                    GA.EVENT_INDEX_COL,
                    C.IS_COMPARISON_COL,
                ],
            )
        else:
            index = pd.MultiIndex.from_product(
                [all_dates, all_groups, all_events],
                names=[
                    GA.DATETIME_COL,
                    GA.GROUP_COL,
                    GA.EVENT_INDEX_COL,
                ],
            )
        new_df = pd.DataFrame(index=index).reset_index()
        # Some adapters may return a different datetime type
        pdf[GA.DATETIME_COL] = pdf[GA.DATETIME_COL].astype("datetime64[ns]")

        # Merge the new DataFrame with the original DataFrame to fill missing values with zeros
        # After the merge
        pdf = new_df.merge(
            pdf,
            on=[GA.DATETIME_COL, GA.GROUP_COL, GA.EVENT_INDEX_COL]
            + ([C.IS_COMPARISON_COL] if C.IS_COMPARISON_COL in pdf.columns else []),
            how="left",
        )

        # Only fill numeric columns
        numeric_cols = pdf.select_dtypes(include="number").columns
        pdf[numeric_cols] = pdf[numeric_cols].fillna(0)
        pdf[ORIGINAL_GROUP_COL] = pdf[GA.GROUP_COL]

        if pdf.shape[0] > 1:
            # Drop last point if 0
            pdf = pdf.drop(
                pdf[
                    (pdf[GA.DATETIME_COL] == end_dt) & (pdf[GA.AGG_VALUE_COL] == 0)
                ].index
            )

        return pdf
