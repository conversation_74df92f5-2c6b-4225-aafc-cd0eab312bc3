import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.visualization.preprocessor.base_preprocessor import (
    BasePreprocessor,
)
from mitzu.visualization.preprocessor.conversion_preprocessor import (
    ConversionPreprocessor,
)
from mitzu.visualization.preprocessor.journey_preprocessor import (
    JourneyPreprocessor,
)
from mitzu.visualization.preprocessor.retention_preprocessor import (
    RetentionPreprocessor,
)
from mitzu.visualization.preprocessor.segmentation_preprocessor import (
    SegmentationPreprocessor,
)


def get_preprocessor(
    metric: WM.WebappMetric, metric_context: MC.MetricContext, project: M.Project
) -> BasePreprocessor:
    if metric.metric_type == M.MetricType.SEGMENTATION:
        return SegmentationPreprocessor(metric, metric_context, project)

    elif metric.metric_type == M.MetricType.CONVERSION:
        return ConversionPreprocessor(metric, metric_context, project)

    elif metric.metric_type == M.MetricType.JOURNEY:
        return JourneyPreprocessor(metric, metric_context, project)

    elif metric.metric_type == M.MetricType.RETENTION:
        return RetentionPreprocessor(metric, metric_context, project)

    else:
        raise Exception(
            f"Unsupported metric type for visualization {metric.metric_type}"
        )
