from __future__ import annotations

from typing import Callable, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.constants import EMPTY_VALUE, N_PER_A, NOT_SET
from mitzu.helper import JOURNEY_SEPARATOR, STRING_SEPARATOR
from mitzu.logger import LOGGER

AGG_VALUE_DIFF_COL = "_agg_value_diff_col"
ORIGINAL_GROUP_COL = "_og_group"
OTHER_GROUPS = "<other groups>"


def trim_long_event_name(event_name: str) -> str:
    if len(event_name) > 35:
        return event_name[:35] + "..."
    else:
        return event_name


def split_by_separator(input_string: str) -> List[str]:
    return input_string.split(STRING_SEPARATOR)


def format_custom_json_datetime(
    pdf_row: pd.Series, time_group: M.TimeGroup
) -> Optional[str]:
    format_string = "%a, %Y-%m-%d"
    if time_group == M.TimeGroup.HOUR:
        format_string = "%a, %Y-%m-%d %H:%M"
    try:
        if C.IS_COMPARISON_COL in pdf_row and pdf_row[C.IS_COMPARISON_COL] is True:
            datetime_value = pdf_row[C.ORIGINAL_DATETIME]
        else:
            datetime_value = pdf_row[GA.DATETIME_COL]
        if time_group == M.TimeGroup.TOTAL:
            return None
        return datetime_value.strftime(format_string)
    except Exception as exc:
        LOGGER.opt(exception=exc).warning(
            f"Cannot format timestamp value: {pdf_row[GA.DATETIME_COL]}"
        )
        return None


# it's a helper for the fix_group_by_cols, used in the tables.py too
def should_handle_null_as_dropoff(webapp_metric: WM.WebappMetric) -> bool:
    if webapp_metric.metric_type == M.MetricType.CONVERSION:
        for seg in webapp_metric.segments[1:]:
            if len(seg.group_by) > 0:
                return True
    return False


def get_supported_agg_types() -> set:
    return {
        M.AggType.COUNT_UNIQUE_USERS,
        M.AggType.COUNT_EVENTS,
        M.AggType.COUNT_CONVERTED_USERS,
        M.AggType.CONVERSION,
        M.AggType.RETENTION_RATE,
        M.AggType.AVERAGE_TIME_TO_CONV,
        M.AggType.TOTAL_MRR,
        M.AggType.COUNT_SUBSCRIBERS,
        M.AggType.FREQUENCY,
        M.AggType.PERCENTILE_TIME_TO_CONV,
        M.AggType.BY_EVENT_PROPERTY,
    }


def supports_other_groups(webapp_metric: WM.WebappMetric) -> bool:
    return (
        (
            webapp_metric.metric_type == M.MetricType.SEGMENTATION
            and len(webapp_metric.segments) == 1
            and webapp_metric.segments[0].aggregation is not None
            and webapp_metric.segments[0].aggregation.type in get_supported_agg_types()
        )
        or (
            webapp_metric.metric_type in [M.MetricType.CONVERSION]
            and webapp_metric.config.aggregation is not None
            and webapp_metric.config.aggregation.type in get_supported_agg_types()
            and len(webapp_metric.segments) > 1
        )
        or (
            webapp_metric.metric_type == M.MetricType.RETENTION
            and webapp_metric.config.aggregation is not None
            and webapp_metric.config.aggregation.type in get_supported_agg_types()
            and len(webapp_metric.segments) > 1
            and webapp_metric.config.time_group == M.TimeGroup.TOTAL
        )
    )


def get_agg_func(
    aggregation: WM.WebappAggregationConfig,
) -> Union[str, Callable]:
    if aggregation.type == M.AggType.BY_EVENT_PROPERTY:
        sub = aggregation.event_property_agg
        if not sub or not sub.agg_type:
            raise ValueError("Missing sub-aggregation in BY_EVENT_PROPERTY")

        sub_agg_type = sub.agg_type
        if sub_agg_type == M.EventPropertyAggregationType.SUM:
            return "sum"
        elif sub_agg_type == M.EventPropertyAggregationType.AVG:
            return "mean"
        elif sub_agg_type == M.EventPropertyAggregationType.MIN:
            return "min"
        elif sub_agg_type == M.EventPropertyAggregationType.MAX:
            return "max"
        elif sub_agg_type == M.EventPropertyAggregationType.MEDIAN:
            return "median"
        elif sub_agg_type == M.EventPropertyAggregationType.P75:
            return lambda x: x.quantile(0.75)
        elif sub_agg_type == M.EventPropertyAggregationType.P90:
            return lambda x: x.quantile(0.9)
        elif sub_agg_type == M.EventPropertyAggregationType.P95:
            return lambda x: x.quantile(0.95)
        elif sub_agg_type == M.EventPropertyAggregationType.COUNT_DISTINCT:
            return "sum"
        else:
            raise ValueError(f"Unsupported sub-aggregation type: {sub_agg_type}")

    agg_type = aggregation.type
    if agg_type in {
        M.AggType.COUNT_UNIQUE_USERS,
        M.AggType.COUNT_EVENTS,
        M.AggType.COUNT_CONVERTED_USERS,
    }:
        return "sum"
    elif agg_type in {
        M.AggType.CONVERSION,
        M.AggType.RETENTION_RATE,
        M.AggType.AVERAGE_TIME_TO_CONV,
        M.AggType.TOTAL_MRR,
        M.AggType.COUNT_SUBSCRIBERS,
        M.AggType.FREQUENCY,
    }:
        return "mean"
    elif agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
        agg_param = aggregation.conv_param
        if agg_param is None or agg_param > 100 or agg_param < 0:
            raise ValueError("Percentile aggregation requires a percentile parameter")

        percentile = agg_param / 100
        return lambda x: x.quantile(percentile)

    raise ValueError(f"Unsupported aggregation type: {agg_type}")


def aggregate_other_group_rows(
    not_in_top: pd.DataFrame,
    pdf_columns: List[str],
    segments: List[WM.WebappSegment],
    metric_config: WM.WebappMetricConfig,
    metric_type: M.MetricType,
) -> pd.DataFrame:

    if not_in_top.empty:
        return pd.DataFrame(columns=pdf_columns)
    if metric_type in [M.MetricType.CONVERSION] and len(segments) > 1:
        aggregation = metric_config.aggregation
        agg_func = get_agg_func(aggregation)

        aggs = {
            f"{GA.AGG_VALUE_COL}_{i}": agg_func for i in range(1, len(segments) + 1)
        }
        user_count_aggs = {
            f"{GA.USER_COUNT_COL}_{i}": "sum" for i in range(1, len(segments) + 1)
        }
        aggs.update(user_count_aggs)
        if metric_config.time_group == M.TimeGroup.TOTAL:
            other = pd.DataFrame([not_in_top.agg(aggs)])
        else:
            other = not_in_top.groupby([GA.DATETIME_COL], as_index=False).agg(aggs)
    elif metric_type == M.MetricType.SEGMENTATION and len(segments) == 1:
        if segments[0].aggregation is None:
            raise ValueError("Aggregation is not set")
        aggregation = segments[0].aggregation
        agg_func = get_agg_func(aggregation)
        aggs = {GA.AGG_VALUE_COL: agg_func}
        if metric_config.time_group == M.TimeGroup.TOTAL:
            other = not_in_top.groupby([GA.EVENT_INDEX_COL], as_index=False).agg(aggs)
        else:
            other = not_in_top.groupby(
                [GA.DATETIME_COL, GA.EVENT_INDEX_COL], as_index=False
            ).agg(aggs)
    elif metric_type == M.MetricType.RETENTION:
        if metric_config.aggregation is None:
            raise ValueError("Aggregation is not set")
        agg_func = get_agg_func(metric_config.aggregation)

        agg_cols = [GA.AGG_VALUE_COL] + [
            f"{GA.USER_COUNT_COL}_{i}" for i in range(1, len(segments) + 1)
        ]
        aggs = {col: "sum" for col in agg_cols}
        aggs[GA.AGG_VALUE_COL] = agg_func
        groupby_cols = [GA.RETENTION_INDEX]
        if metric_config.time_group != M.TimeGroup.TOTAL:
            groupby_cols.insert(0, GA.DATETIME_COL)
        other = not_in_top.groupby(groupby_cols, as_index=False).agg(aggs)
    else:
        raise ValueError(
            f"Unsupported metric type: {metric_type} with {len(segments)} segments"
        )
    other[GA.GROUP_COL] = OTHER_GROUPS
    other[ORIGINAL_GROUP_COL] = OTHER_GROUPS
    for col in pdf_columns:
        if col not in other.columns:
            other[col] = None
    return other[pdf_columns]


def filter_top_groups(
    pdf: pd.DataFrame,
    metric: WM.WebappMetric,
    order_by_col: str,
) -> Tuple[pd.DataFrame, List[str]]:
    max = metric.config.max_group_count

    if metric.config.chart_type == M.SimpleChartType.GEO_COUNTRY:
        if metric.metric_type == M.MetricType.SEGMENTATION and (
            len(metric.segments) != 1 or len(metric.segments[0].group_by) != 1
        ):
            raise C.InvalidGEOCountryMetric(
                """GEO Country visualization requires a single event segmentation
                with a breakdown property of ISO2, ISO3 or english country names breakdown."""
            )
        if (
            metric.metric_type == M.MetricType.CONVERSION
            and sum([len(segment.group_by) for segment in metric.segments]) != 1
        ):
            raise C.InvalidGEOCountryMetric(
                """GEO Country visualization requires funnel with a breakdown property
                of ISO2, ISO3 or english country names breakdown."""
            )
        if metric.metric_type == M.MetricType.RETENTION:
            raise C.InvalidGEOCountryMetric(
                "GEO Country visualization is supported with funnel or segmentation insight types."
            )
        max = 243

    pdf_simple = pdf[[GA.GROUP_COL, order_by_col]]

    groupped = pdf_simple.groupby(GA.GROUP_COL)[order_by_col]
    summed = groupped.sum()
    g_users = summed.reset_index()

    if g_users.shape[0] > 0:
        g_users = g_users.sort_values([order_by_col, GA.GROUP_COL], ascending=False)

    g_users = g_users.head(max)
    top_groups = list(g_users[GA.GROUP_COL].values)

    in_top = pdf[pdf[GA.GROUP_COL].isin(top_groups)]

    if not metric.config.show_other_groups or not supports_other_groups(metric):
        return in_top, list(pdf_simple[GA.GROUP_COL].unique())
    not_in_top = pdf[~pdf[GA.GROUP_COL].isin(top_groups)]
    if not not_in_top.empty:
        other = aggregate_other_group_rows(
            not_in_top,
            pdf_columns=pdf.columns.tolist(),
            segments=metric.segments,
            metric_config=metric.config,
            metric_type=metric.metric_type,
        )
        if not other.empty:
            return pd.concat([in_top, other], ignore_index=True), list(
                pdf_simple[GA.GROUP_COL].unique()
            )

    return in_top, list(pdf_simple[GA.GROUP_COL].unique())


# helper for the replace_subsegment_labels
def get_group_by_segment_index(group_by: str, webapp_metric: WM.WebappMetric) -> int:
    if group_by.startswith(GA.SUBSEGMENT_INDEX):
        event_index, _ = [
            int(val)
            for val in group_by.replace(f"{GA.SUBSEGMENT_INDEX}_", "").split("_")
        ]
        return event_index
    elif webapp_metric.metric_type == M.MetricType.CONVERSION:
        for i, segment in enumerate(webapp_metric.segments):
            if segment.group_by:
                return i
        raise ValueError("None of the segments has a group by")
    elif webapp_metric.metric_type == M.MetricType.RETENTION:
        return 0
    raise ValueError(
        f"subsegment index missing or unknown metric type {group_by} {webapp_metric.metric_type}"
    )


# helper for the fix_group_by_cols
def replace_subsegment_labels(
    webapp_metric: WM.WebappMetric,
    group_by_values: str,
    metric_context: MC.MetricContext,
    segment_index: Optional[int] = None,
) -> str:
    result = []
    label_mapping = webapp_metric.label_mapping
    for step_index, step_group_by_values in enumerate(
        group_by_values.split(JOURNEY_SEPARATOR)
    ):
        if step_group_by_values in label_mapping:
            result.append(label_mapping[step_group_by_values])
        else:
            step_result = []
            for group_by in step_group_by_values.split(STRING_SEPARATOR):
                if group_by == N_PER_A or group_by == f"{N_PER_A} (previous)":
                    step_result.append(group_by)
                elif group_by.startswith(GA.SUBSEGMENT_INDEX):
                    segment_index, event_index = [
                        int(val)
                        for val in group_by.replace(
                            f"{GA.SUBSEGMENT_INDEX}_", ""
                        ).split("_")
                    ]
                    segment = webapp_metric.segments[segment_index]

                    event = segment.get_events_in_order()[event_index]
                    name = f"{metric_context.get_event_meta_or_default_values(event).display_name} ({event_index+1})"
                    step_result.append(name)

                else:
                    if segment_index is None:
                        if webapp_metric.metric_type == M.MetricType.JOURNEY:
                            segment_index = step_index
                        else:
                            segment_index = get_group_by_segment_index(
                                group_by, webapp_metric
                            )
                    segment = webapp_metric.segments[segment_index]
                    group_by = webapp_metric.label_mapping.get(group_by, group_by)
                    step_result.append(group_by)

            result.append(STRING_SEPARATOR.join(step_result))
    return JOURNEY_SEPARATOR.join(result)


def fix_empty_group_by_cols(
    pdf: pd.DataFrame,
    handle_null_as_dropoff: bool,
) -> pd.DataFrame:
    if GA.GROUP_COL in pdf.columns:
        pdf[ORIGINAL_GROUP_COL] = pdf[GA.GROUP_COL]
        if handle_null_as_dropoff:
            pdf[GA.GROUP_COL] = pdf[GA.GROUP_COL].fillna(NOT_SET)
        else:
            pdf[GA.GROUP_COL] = pdf[GA.GROUP_COL].fillna(N_PER_A)
        pdf[GA.GROUP_COL] = pdf[GA.GROUP_COL].apply(
            lambda val: val if val != "" else EMPTY_VALUE
        )
    return pdf


def subtitute_segment_labels_in_group_bys(
    pdf: pd.DataFrame,
    webapp_metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
) -> pd.DataFrame:
    if GA.GROUP_COL in pdf.columns:
        if not pdf.empty:
            pdf[GA.GROUP_COL] = pdf.apply(
                lambda row: replace_subsegment_labels(
                    webapp_metric,
                    row[GA.GROUP_COL],
                    metric_context,
                    row[GA.EVENT_INDEX_COL] if GA.EVENT_INDEX_COL in row else None,
                ),
                axis=1,
            )

        pdf[GA.GROUP_COL] = (
            pdf[GA.GROUP_COL]
            .astype(str)
            .apply(lambda val: val[0:400] + "…" if val and len(val) > 401 else val)
        )
        return pdf
    return pdf


def get_readable_group_by_values(
    encoded_group_by_values: str,
    metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
) -> List[str]:
    substituted_group_by_vals = []
    for group_by in split_by_separator(encoded_group_by_values):
        if group_by.startswith(GA.SUBSEGMENT_INDEX):
            segment_index, event_index = [
                int(val)
                for val in group_by.replace(f"{GA.SUBSEGMENT_INDEX}_", "").split("_")
            ]
            segment = metric.segments[segment_index]

            event = segment.get_events_in_order()[event_index]
            name = f"{metric_context.get_event_meta_or_default_values(event).display_name} ({event_index+1})"
            substituted_group_by_vals.append(name)
        else:
            substituted_group_by_vals.append(group_by)
    return substituted_group_by_vals


def format_number(num: Union[float, int], suffix: str) -> str:
    num = float(round(num, 2))
    formatted_num = int(num) if num.is_integer() else num

    return f"{formatted_num} {suffix}".strip()


def calculate_percentage_change(pdf: pd.DataFrame) -> pd.DataFrame:
    pdf = pdf.sort_values([GA.GROUP_COL, GA.DATETIME_COL])
    key_cols = [GA.GROUP_COL]
    if C.IS_COMPARISON_COL in pdf.columns:
        key_cols.append(C.IS_COMPARISON_COL)
    if GA.EVENT_INDEX_COL in pdf.columns:
        key_cols.append(GA.EVENT_INDEX_COL)

    pdf[AGG_VALUE_DIFF_COL] = pdf.groupby(key_cols)[GA.AGG_VALUE_COL].transform(
        lambda x: ((x - x.shift(1)) / x.shift(1).replace({0: np.nan}).abs() * 100)
    )
    pdf = pdf.reset_index(level=0, drop=True)
    return pdf
