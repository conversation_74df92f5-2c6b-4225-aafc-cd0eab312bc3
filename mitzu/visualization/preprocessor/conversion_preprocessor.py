from typing import List, Optional

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.visualization.labels as L
import mitzu.visualization.transform_conv as TC
import mitzu.webapp.model as WM
from mitzu.visualization.comparison import (
    get_finalized_label,
)
from mitzu.visualization.preprocessor.base_preprocessor import (
    BasePreprocessor,
    PreprocessedDataFrame,
)
from mitzu.visualization.preprocessor.common import (
    AGG_VALUE_DIFF_COL,
    calculate_percentage_change,
    filter_top_groups,
    fix_empty_group_by_cols,
    format_custom_json_datetime,
    format_number,
    get_readable_group_by_values,
    should_handle_null_as_dropoff,
    split_by_separator,
    trim_long_event_name,
)

STEP_COL = "_step"
IS_DROPOFF_COL = "is_dropoff"
STEP_DROP_PCT = "_step_drop_pct"


def rename_conversion_df_columns(
    pdf: pd.DataFrame, metric: WM.WebappMetric
) -> pd.DataFrame:
    mapping = {
        GA.AGG_VALUE_COL: C.Y_AXIS_COL,
    }
    if metric.config.aggregation.type == M.AggType.COUNT_CONVERTED_USERS:
        pdf[C.Y_AXIS_COL] = pdf[GA.USER_COUNT_COL]
        mapping = {}

    if metric.config.time_group == M.TimeGroup.TOTAL:
        mapping[STEP_COL] = C.X_AXIS_COL
    else:
        mapping[GA.DATETIME_COL] = C.X_AXIS_COL
    return pdf.rename(columns=mapping)


def get_conversion_value_suffix(pdf: pd.DataFrame, metric: WM.WebappMetric) -> str:
    if metric.config.aggregation.type in [
        M.AggType.AVERAGE_TIME_TO_CONV,
        M.AggType.PERCENTILE_TIME_TO_CONV,
    ]:
        cols = TC.get_conversion_cols(metric)
        max_ttc = pdf[cols].max(axis=1).max(axis=0)
        return (
            "secs"
            if max_ttc <= C.TTC_RANGE_1_SEC
            else "mins"
            if max_ttc <= C.TTC_RANGE_2_SEC
            else "hours"
            if max_ttc <= C.TTC_RANGE_3_SEC
            else "days"
        )

    return (
        C.get_value_suffix_by_agg_type(metric.config.aggregation.type)
        if metric.config.aggregation.type
        else ""
    )


class ConversionPreprocessor(BasePreprocessor):
    def preprocess_df(
        self,
        result_df: pd.DataFrame,
    ) -> PreprocessedDataFrame:
        result_df = fix_empty_group_by_cols(
            result_df, should_handle_null_as_dropoff(self.metric)
        )
        suffix = get_conversion_value_suffix(result_df, self.metric)
        if result_df.shape[0] == 0:
            return PreprocessedDataFrame(result_df, [], suffix)

        agg_col = (
            GA.AGG_VALUE_COL
            if self.metric.config.aggregation.type != M.AggType.COUNT_CONVERTED_USERS
            else GA.USER_COUNT_COL
        )
        group_by = self._get_funnel_group_by(self.metric)
        result_df = TC.fix_conversion_steps_na_cols(result_df, self.metric)
        if self.metric.config.aggregation.type in [
            M.AggType.AVERAGE_TIME_TO_CONV,
            M.AggType.PERCENTILE_TIME_TO_CONV,
        ]:
            result_df = TC.fix_conv_times_pdf(result_df, self.metric)

        result_df, all_groups = filter_top_groups(
            result_df, self.metric, order_by_col=f"{GA.USER_COUNT_COL}_1"
        )

        result_df = C.extend_df_with_sorting_column(self.metric, result_df)

        title_label = L.agg_type_label(
            self.metric.config.aggregation, self.metric_context, self.metric.config
        )
        unique_field_label = L.unique_users_agg_label(
            self.metric.config.aggregation.type,
            self.metric.config,
            self.metric_context,
            short_form=True,
        )
        if self.metric.config.time_group == M.TimeGroup.TOTAL:
            if self.metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY:
                agg_value_index = len(self.metric.segments)
                result_df = result_df.drop(
                    [
                        prefix + "_" + str(index)
                        for prefix in [GA.AGG_VALUE_COL, GA.USER_COUNT_COL]
                        for index in range(1, agg_value_index)
                    ],
                    axis=1,
                )
            result_df = TC.get_melted_conv_pdf(
                result_df, self.metric, self.metric_context
            )
            result_df[C.COLOR_COL] = result_df.apply(
                lambda row: self._get_color_col(row),
                axis=1,
            )
            if (
                self.metric.metric_type == M.MetricType.CONVERSION
                and self.metric.config.time_group == M.TimeGroup.TOTAL
                and self.metric.config.aggregation.type == M.AggType.CONVERSION
            ):
                result_df = self.compute_dropoff(result_df)
            if result_df.shape[0] > 0:
                zfill = 2 if len(self.metric.segments) > 9 else 1

                result_df[STEP_COL] = result_df.apply(
                    lambda row: (
                        f"{str(row[TC.STEP_INDEX] + 1).zfill(zfill)}. {self.metric.segments[row[TC.STEP_INDEX]].title}"
                        if self.metric.segments[row[TC.STEP_INDEX]].title is not None
                        else trim_long_event_name(row[STEP_COL])
                    ),
                    axis=1,
                )
        else:
            funnel_length = len(self.metric.segments)
            result_df[GA.AGG_VALUE_COL] = result_df[f"{agg_col}_{funnel_length}"]
            result_df[GA.USER_COUNT_COL] = result_df[
                f"{GA.USER_COUNT_COL}_{funnel_length}"
            ]
            result_df = calculate_percentage_change(result_df)
            result_df[C.COLOR_COL] = result_df.apply(
                lambda row: self._get_color_col(row),
                axis=1,
            )

        if (
            self.metric.config.time_group == M.TimeGroup.TOTAL
            and self.metric.config.aggregation.type == M.AggType.CONVERSION
        ):
            result_df = self.calculate_precentage_from_previous_step(result_df)
            title_col = STEP_DROP_PCT
        else:
            title_col = C.Y_AXIS_COL

        if result_df.shape[0] != 0:

            if self.metric.config.time_group == M.TimeGroup.TOTAL:

                result_df[C.JSON_COL] = result_df.apply(
                    lambda row: self._get_tooltip_custom_json_with_total_time_group(
                        row, unique_field_label, suffix, title_label, group_by, agg_col
                    ).to_json(),
                    axis=1,
                )
            else:
                funnel_length = len(self.metric.segments)
                result_df[C.JSON_COL] = result_df.apply(
                    lambda row: self._get_tooltip_custom_json(
                        row,
                        unique_field_label,
                        suffix,
                        title_label,
                        group_by,
                        funnel_length,
                    ).to_json(),
                    axis=1,
                )

        result_df = rename_conversion_df_columns(result_df, self.metric)

        result_df[C.TEXT_COL] = result_df.apply(
            lambda row: self._get_text_col(row, suffix, title_col), axis=1
        )

        result_df = result_df.sort_values(
            [C.X_AXIS_COL, C.SORTING_COL, C.COLOR_COL, GA.USER_COUNT_COL],
            ascending=[True, False, True, False],
        )
        result_df.drop(columns=[C.SORTING_COL, GA.GROUP_COL], inplace=True)
        return PreprocessedDataFrame(result_df, all_groups, suffix)

    def compute_dropoff(self, df: pd.DataFrame) -> pd.DataFrame:
        dropoff_df = df.copy()
        y_diff = (
            dropoff_df.groupby(C.COLOR_COL)[GA.AGG_VALUE_COL].shift(1)
            - dropoff_df[GA.AGG_VALUE_COL]
        )
        user_diff = (
            dropoff_df.groupby(C.COLOR_COL)[GA.USER_COUNT_COL].shift(1)
            - dropoff_df[GA.USER_COUNT_COL]
        )

        dropoff_df[GA.AGG_VALUE_COL] = y_diff.where(pd.notna(y_diff), 0)
        dropoff_df[GA.USER_COUNT_COL] = user_diff.where(pd.notna(user_diff), 0).astype(
            int
        )
        dropoff_df[IS_DROPOFF_COL] = True
        df[IS_DROPOFF_COL] = False
        return pd.concat([df, dropoff_df]).reset_index(drop=True)

    def _get_funnel_group_by(self, metric: WM.WebappMetric) -> List[M.FieldPath]:
        for segment in metric.segments:
            if len(segment.group_by) > 0:
                return segment.group_by
        return []

    def _get_color_col(self, row: pd.Series) -> str:
        return get_finalized_label(
            ", ".join(
                get_readable_group_by_values(
                    row[GA.GROUP_COL], self.metric, self.metric_context
                )
            ),
            row[C.IS_COMPARISON_COL] if C.IS_COMPARISON_COL in row else False,
            label_mapping=self.metric.label_mapping,
        )

    def _get_text_col(self, row: pd.Series, suffix: str, title_col: str) -> str:
        if self.metric.config.aggregation.type == M.AggType.COUNT_CONVERTED_USERS:
            title = str(row[title_col])
            subtitle = f"{round(row[GA.AGG_VALUE_COL], 2)} %"
        else:
            title = f"{round(row[title_col], 2)} {suffix}"
            subtitle = str(row[GA.USER_COUNT_COL])

        return f"{title}<br><b>{subtitle}</b>"

    def _get_tooltip_custom_json_with_total_time_group(
        self,
        row: pd.Series,
        unique_field_label: str,
        suffix: str,
        title_label: str,
        group_by: List[M.FieldPath],
        agg_col: str,
    ) -> WM.TooltipCustomJson:
        def get_title_value(row: pd.Series) -> Optional[str]:
            if self.metric.config.aggregation.type == M.AggType.COUNT_CONVERTED_USERS:
                return (
                    (
                        str(row[GA.USER_COUNT_COL])
                        + (f" ({(format_number(row[GA.AGG_VALUE_COL], suffix))})")
                    )
                    if agg_col in row and row[agg_col] is not None
                    else None
                )
            elif self.metric.config.aggregation.type == M.AggType.CONVERSION:
                return (
                    (
                        format_number(row[STEP_DROP_PCT], "%")
                        + (
                            f" ({(row[GA.USER_COUNT_COL])})"
                            if GA.USER_COUNT_COL in row
                            else ""
                        )
                    )
                    if STEP_DROP_PCT in row and row[STEP_DROP_PCT] is not None
                    else None
                )
            else:
                return (
                    (
                        format_number(row[agg_col], suffix)
                        + (
                            f" ({(row[GA.USER_COUNT_COL])})"
                            if GA.USER_COUNT_COL in row
                            else ""
                        )
                    )
                    if agg_col in row and row[agg_col] is not None
                    else None
                )

        return WM.TooltipCustomJson(
            segment_index=row[TC.STEP_INDEX],
            version=1,
            break_down=(split_by_separator(row[GA.GROUP_COL]) if group_by else None),
            event_label=(
                f"{row[TC.STEP_INDEX] + 1}. {self.metric.segments[row[TC.STEP_INDEX]].title}"
                if self.metric.segments[row[TC.STEP_INDEX]].title is not None
                else row[STEP_COL]
            ),
            event_value=(row[C.COLOR_COL] if group_by else None),
            title_label=f'Drop-off (from step {row[TC.STEP_INDEX] if self.metric.config.time_group == M.TimeGroup.TOTAL else "1"}.)'
            if IS_DROPOFF_COL in row and row[IS_DROPOFF_COL]
            else title_label,
            title_value=get_title_value(row),
            unique_field_label=unique_field_label,
            is_comparison=row[C.IS_COMPARISON_COL]
            if C.IS_COMPARISON_COL in row
            else None,
            is_drop_off=row[IS_DROPOFF_COL] if IS_DROPOFF_COL in row else None,
            direction="bottom"
            if self.metric.config.chart_type == M.SimpleChartType.HORIZONTAL_BAR
            else None,
        )

    def _get_tooltip_custom_json(
        self,
        row: pd.Series,
        unique_field_label: str,
        suffix: str,
        title_label: str,
        group_by: List[M.FieldPath],
        funnel_length: int,
    ) -> WM.TooltipCustomJson:
        return WM.TooltipCustomJson(
            segment_index=funnel_length - 1,
            version=1,
            break_down=(split_by_separator(row[GA.GROUP_COL]) if group_by else None),
            event_label=format_custom_json_datetime(row, self.metric.config.time_group),
            event_value=(row[C.COLOR_COL] if group_by else None),
            title_label=title_label,
            title_value=(
                format_number(row[GA.AGG_VALUE_COL], suffix)
                if GA.AGG_VALUE_COL in row and row[GA.AGG_VALUE_COL] is not None
                else None
            ),
            subtitle=(
                format_number(row[AGG_VALUE_DIFF_COL], "%")
                if not pd.isna(row[AGG_VALUE_DIFF_COL])
                else None
            ),
            unique_field_label=unique_field_label,
            is_comparison=row[C.IS_COMPARISON_COL]
            if C.IS_COMPARISON_COL in row
            else None,
            is_drop_off=row[IS_DROPOFF_COL] if IS_DROPOFF_COL in row else None,
        )

    def calculate_precentage_from_previous_step(
        self, pdf: pd.DataFrame
    ) -> pd.DataFrame:
        if TC.STEP_INDEX in pdf.columns:
            if IS_DROPOFF_COL in pdf.columns:
                main_df = pdf[~pdf[IS_DROPOFF_COL]].copy().reset_index(drop=True)
                drop_df = pdf[pdf[IS_DROPOFF_COL]].copy().reset_index(drop=True)
            else:
                main_df = pdf.copy()
                drop_df = pd.DataFrame()
            main_df = main_df.sort_values([GA.GROUP_COL, TC.STEP_INDEX])
            main_df["prev_user_count"] = main_df.groupby([GA.GROUP_COL])[
                GA.USER_COUNT_COL
            ].shift(1)

            main_df[STEP_DROP_PCT] = (
                main_df[GA.USER_COUNT_COL] / main_df["prev_user_count"] * 100
            ).where(main_df[TC.STEP_INDEX] >= 2, other=main_df[GA.AGG_VALUE_COL])
            main_df[STEP_DROP_PCT] = main_df[STEP_DROP_PCT].fillna(0)
            main_df.drop(columns=["prev_user_count"], inplace=True)
            if drop_df.shape[0] > 0:
                drop_df = drop_df.sort_values([GA.GROUP_COL, TC.STEP_INDEX])
                drop_df[STEP_DROP_PCT] = 100 - main_df[STEP_DROP_PCT]

                pdf = pd.concat([main_df, drop_df], ignore_index=True)
            else:
                pdf = main_df

        else:
            pdf[STEP_DROP_PCT] = pdf[GA.AGG_VALUE_COL]

        return pdf
