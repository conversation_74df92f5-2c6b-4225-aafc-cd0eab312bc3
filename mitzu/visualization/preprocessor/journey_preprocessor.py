import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.helper as H
import mitzu.model as M
from mitzu.helper import (
    DROP_OFF_PLACEHOLDER,
    JOURNEY_SEPARATOR,
    NO_GROUP,
    STRING_SEPARATOR,
)
from mitzu.visualization.preprocessor.base_preprocessor import (
    BasePreprocessor,
    PreprocessedDataFrame,
)
from mitzu.visualization.preprocessor.common import (
    fix_empty_group_by_cols,
    subtitute_segment_labels_in_group_bys,
)


def add_dropoff_placeholder(row: pd.Series) -> str:
    group_parts = row[GA.GROUP_COL].split(JOURNEY_SEPARATOR)
    user_counts = [row[f"{GA.USER_COUNT_COL}_{i+1}"] for i in range(len(group_parts))]

    updated_group = [
        DROP_OFF_PLACEHOLDER if part == NO_GROUP and user_counts[i] == 0 else part
        for i, part in enumerate(group_parts)
    ]

    new_group_str = JOURNEY_SEPARATOR.join(updated_group)
    return new_group_str


class JourneyPreprocessor(BasePreprocessor):
    def preprocess_df(
        self,
        result_df: pd.DataFrame,
    ) -> PreprocessedDataFrame:
        if result_df.empty:
            return PreprocessedDataFrame(result_df, [], "")
        result_df = fix_empty_group_by_cols(result_df, handle_null_as_dropoff=False)
        result_df[GA.GROUP_COL] = result_df.apply(add_dropoff_placeholder, axis=1)

        result_df = subtitute_segment_labels_in_group_bys(
            result_df,
            webapp_metric=self.metric,
            metric_context=self.metric_context,
        )
        suffix = "%"
        if result_df.shape[0] == 0:
            return PreprocessedDataFrame(result_df, [], suffix)
        if (
            self.metric.config.time_group != M.TimeGroup.TOTAL
            or self.metric.config.aggregation.type != M.AggType.CONVERSION
            or self.metric.config.chart_type != M.SimpleChartType.SANKEY
        ):
            raise ValueError(
                f"""Journey is not supported for: 
                {self.metric.config.time_group}, 
                {self.metric.config.aggregation.type}, 
                {self.metric.config.chart_type}"""
            )

        has_group_by = False
        for segment in self.metric.segments:
            if len(segment.group_by) > 0:
                has_group_by = True
                break

        # this is maybe a bug in the query, just ignore these columns right now
        result_df.query(
            f"not(_group.str.contains('{H.GROUP_MISSING_VALUE}', na=False))",
            inplace=True,
        )

        if has_group_by:
            result_df["_group"] = result_df["_group"].apply(
                lambda val: [
                    (i + 1, ", ".join(v.split(STRING_SEPARATOR)))
                    for i, v in enumerate(val.split(JOURNEY_SEPARATOR))
                ]
            )
        else:
            result_df["_group"] = result_df["_group"].apply(
                lambda _: [
                    (i, NO_GROUP) for i in range(1, len(self.metric.segments) + 1)
                ]
            )

        exploded_df = result_df["_group"].apply(pd.Series).add_prefix("_group_")
        journey_length = len(self.metric.segments)

        if not exploded_df.empty:
            # explode will index the columns starting with 0
            for index in range(journey_length, 0, -1):
                exploded_df[f"_group_{index}"] = exploded_df[f"_group_{index - 1}"]

            result_df = exploded_df.drop(columns=["_group_0"]).join(
                result_df.drop(columns=["_group", "_datetime"])
            )
            result_df = result_df.drop(
                columns=[c for c in result_df.columns if c.startswith(GA.AGG_VALUE_COL)]
            )

            all_groups = list(
                pd.concat(
                    [*[result_df[f"_group_{i +1}"] for i in range(0, journey_length)]]
                ).unique()
            )
        else:
            all_groups = []
        return PreprocessedDataFrame(result_df, all_groups, suffix)
