from typing import List

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.common as C
import mitzu.visualization.labels as L
import mitzu.visualization.transform_retention as TR
import mitzu.webapp.model as WM
from mitzu.constants import N_PER_A
from mitzu.visualization.comparison import (
    get_finalized_label,
)
from mitzu.visualization.preprocessor.base_preprocessor import (
    BasePreprocessor,
    PreprocessedDataFrame,
)
from mitzu.visualization.preprocessor.common import (
    filter_top_groups,
    fix_empty_group_by_cols,
    format_number,
    get_readable_group_by_values,
    split_by_separator,
)


class RetentionPreprocessor(BasePreprocessor):
    def preprocess_df(
        self,
        result_df: pd.DataFrame,
    ) -> PreprocessedDataFrame:
        result_df = fix_empty_group_by_cols(result_df, handle_null_as_dropoff=False)
        if self.metric.config.aggregation.type is None:
            raise ValueError("metric aggregation is not set")

        suffix = C.get_value_suffix_by_agg_type(self.metric.config.aggregation.type)
        if result_df.shape[0] == 0:
            return PreprocessedDataFrame(result_df, [], suffix)
        size = result_df.shape[0]

        result_df = TR.fix_retention(result_df, self.metric)

        result_df, all_groups = filter_top_groups(
            result_df, self.metric, order_by_col=GA.USER_COUNT_COL + "_1"
        )
        result_df = C.extend_df_with_sorting_column(self.metric, result_df)

        result_df[C.COLOR_COL] = result_df.apply(
            lambda row: get_finalized_label(
                self._get_color_col(row),
                row[C.IS_COMPARISON_COL] if C.IS_COMPARISON_COL in row else False,
                label_mapping=self.metric.label_mapping,
            ),
            axis=1,
        )
        if (
            self.metric.segments[0].group_by
            and self.metric.config.time_group != M.TimeGroup.TOTAL
        ):
            result_df[GA.RETENTION_GROUP] = (
                result_df[GA.RETENTION_INDEX].astype(str)
                + " - "
                + result_df[C.COLOR_COL]
            )
        result_df = TR.rename_retention_df_columns(result_df, self.metric)

        unique_field_label = L.unique_users_agg_label(
            self.metric.config.aggregation.type,
            self.metric.config,
            self.metric_context,
            short_form=False,
        )

        if self.metric.config.aggregation.type == M.AggType.RETENTION_RATE:
            result_df[C.TEXT_COL] = result_df[C.Y_AXIS_COL].apply(
                lambda val: f"{val:.1f}%" if val > 0 and size <= 200 else ""
            )
        else:
            result_df[C.TEXT_COL] = result_df[C.Y_AXIS_COL].apply(
                lambda val: f"{val:.1f}" if val > 0 and size <= 200 else ""
            )

        # assembling df with TOTAL time group
        if self.metric.config.time_group == M.TimeGroup.TOTAL:
            result_df[C.JSON_COL] = result_df.apply(
                lambda row: self._get_tooltip_custom_json_with_total_time_group(
                    row, unique_field_label, suffix
                ).to_json(),
                axis=1,
            )
            if GA.GROUP_COL in result_df.columns:
                result_df.drop(columns=[GA.GROUP_COL], inplace=True)
        # assembling HEATMAP df
        elif self.metric.config.chart_type == M.SimpleChartType.HEATMAP:
            result_df[C.JSON_COL] = result_df.apply(
                lambda row: self._get_tooltip_custom_json(
                    row, unique_field_label, suffix, C.COLOR_COL
                ).to_json(),
                axis=1,
            )
        # assembling df for all other cases
        else:
            result_df[C.JSON_COL] = result_df.apply(
                lambda row: self._get_tooltip_custom_json(
                    row, unique_field_label, suffix, GA.RETENTION_INDEX
                ).to_json(),
                axis=1,
            )

        # finalizing the df, ordering, dropping extra columns
        result_df = result_df.sort_values(
            [C.X_AXIS_COL, C.SORTING_COL, C.COLOR_COL],
            ascending=[True, False, True],
        )
        result_df.drop(
            columns=self._get_unnecessary_columns(),
            inplace=True,
            errors="ignore",  # some columns may not exist
        )

        return PreprocessedDataFrame(result_df, all_groups, suffix)

    def _get_unnecessary_columns(self) -> List[str]:
        cols = [C.SORTING_COL]

        if self.metric.config.time_group == M.TimeGroup.TOTAL:
            cols.append(GA.RETENTION_INDEX)
        elif self.metric.config.chart_type == M.SimpleChartType.HEATMAP:
            cols.extend([GA.RETENTION_INDEX, GA.DATETIME_COL])
        else:
            cols.append(GA.DATETIME_COL)
            if self.metric.segments[0].group_by:
                cols.append(GA.RETENTION_GROUP)
            else:
                cols.append(GA.RETENTION_INDEX)

        return cols

    def _get_color_col(self, row: pd.Series) -> str:
        readable_group_by = ", ".join(
            get_readable_group_by_values(
                row[GA.GROUP_COL], self.metric, self.metric_context
            )
        )

        if self.metric.config.time_group == M.TimeGroup.TOTAL:
            return readable_group_by
        if self.metric.config.chart_type == M.SimpleChartType.HEATMAP:
            return str(row[GA.DATETIME_COL])

        if self.metric.segments[0].group_by:
            value = str(row[GA.RETENTION_INDEX]) + " - " + readable_group_by
        else:
            value = str(row[GA.RETENTION_INDEX])
        return C.retention_period_label(value, self.metric)

    def _get_tooltip_custom_json_with_total_time_group(
        self, row: pd.Series, unique_field_label: str, suffix: str
    ) -> WM.TooltipCustomJson:

        return WM.TooltipCustomJson(
            segment_index=-1,
            version=1,
            event_label=(
                row[C.COLOR_COL]
                if C.COLOR_COL in row and row[C.COLOR_COL] not in [None, N_PER_A]
                else None
            ),
            event_value=C.retention_period_label(
                row[C.ORIGINAL_DATETIME].date(), self.metric
            )
            if C.IS_COMPARISON_COL in row and row[C.IS_COMPARISON_COL] is True
            else C.retention_period_label(row[C.X_AXIS_COL], self.metric),
            title_label=L.agg_type_label(
                self.metric.config.aggregation,
                self.metric_context,
                self.metric.config,
            ),
            title_value=(
                format_number(row[C.Y_AXIS_COL], suffix)
                if C.Y_AXIS_COL in row and row[C.Y_AXIS_COL] is not None
                else None
            ),
            break_down=(
                split_by_separator(row[GA.GROUP_COL])
                if self.metric.segments[0].group_by
                and GA.GROUP_COL in row
                and row[GA.GROUP_COL] is not None
                else None
            ),
            unique_field_label=unique_field_label,
            retention_index=int(row[C.X_AXIS_COL]),
            is_comparison=row[C.IS_COMPARISON_COL] is True
            if C.IS_COMPARISON_COL in row
            else None,
        )

    def _get_tooltip_custom_json(
        self,
        row: pd.Series,
        unique_field_label: str,
        suffix: str,
        retention_index_col: str,
    ) -> WM.TooltipCustomJson:
        return WM.TooltipCustomJson(
            segment_index=-1,
            version=1,
            break_down=(
                split_by_separator(row[GA.GROUP_COL])
                if self.metric.segments[0].group_by
                and GA.GROUP_COL in row
                and row[GA.GROUP_COL] is not None
                else None
            ),
            event_value=(
                (
                    row[C.COLOR_COL]
                    if self.metric.config.chart_type != M.SimpleChartType.HEATMAP
                    else C.retention_period_label(row[C.X_AXIS_COL], self.metric)
                )
                if C.COLOR_COL in row and row[C.COLOR_COL] is not None
                else None
            ),
            event_label=(
                C.retention_period_label(row[C.ORIGINAL_DATETIME].date(), self.metric)
                if C.IS_COMPARISON_COL in row and row[C.IS_COMPARISON_COL] is True
                else C.retention_period_label(row[C.X_AXIS_COL], self.metric)
            )
            if self.metric.config.chart_type != M.SimpleChartType.HEATMAP
            else row[C.COLOR_COL],
            title_label=L.agg_type_label(
                self.metric.config.aggregation,
                self.metric_context,
                self.metric.config,
            ),
            title_value=(
                format_number(row[C.Y_AXIS_COL], suffix)
                if C.Y_AXIS_COL in row and row[C.Y_AXIS_COL] is not None
                else None
            ),
            unique_field_label=unique_field_label,
            retention_index=int(row[retention_index_col])
            if self.metric.config.chart_type != M.SimpleChartType.HEATMAP
            else int(row[C.X_AXIS_COL]),
            is_comparison=row[C.IS_COMPARISON_COL] is True
            if C.IS_COMPARISON_COL in row
            else None,
        )
