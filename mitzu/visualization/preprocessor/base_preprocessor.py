from dataclasses import dataclass
from typing import List

import pandas as pd

import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.model import Project


@dataclass(frozen=True)
class PreprocessedDataFrame:
    df: pd.DataFrame
    all_groups: List[str]
    suffix: str


class BasePreprocessor:
    def __init__(
        self,
        metric: WM.WebappMetric,
        metric_context: MC.MetricContext,
        project: Project,
    ) -> None:
        self.metric = metric
        self.metric_context = metric_context
        self.project = project

    def preprocess_df(
        self,
        result_df: pd.DataFrame,
    ) -> PreprocessedDataFrame:
        raise NotImplementedError()
