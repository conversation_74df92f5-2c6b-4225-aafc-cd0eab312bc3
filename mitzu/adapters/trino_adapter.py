from __future__ import annotations

import urllib.parse as P
from datetime import date, datetime
from typing import (
    Any,
    Dict,
    Iterable,
    List,
    Optional,
    Tuple,
    Union,
    cast,
)
from urllib import parse

import pandas as pd
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.types as SA_TYPES
import trino
import trino.sqlalchemy
import trino.sqlalchemy.datatype as SA_T
import trino.sqlalchemy.dialect
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.model as M
from mitzu.adapters.helper import (
    dataframe_str_to_datetime,
    pdf_string_json_array_to_array,
)
from mitzu.adapters.model import (
    AggregatedField,
    ExecutableType,
    FieldReference,
    SelectType,
    fix_col_index,
    is_select_type,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import CacheKeyType
from mitzu.helper import STRING_SEPARATOR, create_unique_id
from mitzu.logger import LOGGER

ROLE_EXTRA_CONFIG = "role"


class TrinoAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, trino.exceptions.TrinoUserError):
            if exc.orig.error_name == "DIVISION_BY_ZERO":
                return GA.DivisionByZeroUserError(exc)

            if (
                exc.orig.error_name == "INVALID_CAST_ARGUMENT"
                or exc.orig.error_name == "TYPE_MISMATCH"
            ):
                return GA.NotNumericFieldUserError(exc)

            if exc.orig.error_name == "ADMINISTRATIVELY_KILLED":
                return GA.QueryKilledError(exc)

            if exc.orig.error_name in ["GENERIC_INTERNAL_ERROR", "REMOTE_TASK_ERROR"]:
                return GA.GenericInternalError(exc)

        if isinstance(exc.orig, trino.exceptions.TrinoQueryError):
            if exc.orig.error_name == "REMOTE_TASK_ERROR":
                return GA.GenericInternalError(exc)

        return None

    def _get_connection_url(self, con: M.Connection) -> str:
        user_name = "" if con.user_name is None else P.quote(con.user_name)
        password = "" if con.password is None else f":{P.quote(con.password)}"
        host_str = "" if con.host is None else str(con.host)
        if con.user_name is not None and con.host is not None:
            host_str = f"@{host_str}"
        port_str = "" if con.port is None else ":" + str(con.port)
        schema_str = "" if con.schema is None else f"/{con.schema}"
        url_params_str = "" if con.url_params is None else con.url_params
        if url_params_str != "" and url_params_str[0] != "?":
            url_params_str = "?" + url_params_str
        catalog_str = "" if con.catalog is None else f"/{con.catalog}"

        if url_params_str:
            params = parse.parse_qs(url_params_str.replace("?", ""))
            if "role" in params.keys():
                user_name = f"{user_name}%2F{params['role'][0]}"

        protocol = con.get_protocol().lower()
        res = f"{protocol}://{user_name}{password}{host_str}{port_str}{catalog_str}{schema_str}{url_params_str}"

        return res

    def _parse_type_name(self, type_name: str) -> M.DataType:
        return self.map_type(SA_T.parse_sqltype(type_name))

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.from_unixtime(field, "UTC")

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        result: Dict[str, M.DatabaseSchema] = {
            s.schema: M.DatabaseSchema({}) for s in schema_requests
        }
        (
            result,
            schema_tables_to_fetch,
        ) = self._get_all_table_columns_cache_key_and_object(
            schema_requests, ignore_cache
        )

        df = self._get_table_columns_df(schema_tables_to_fetch)

        for schema, table_name, field_name, d_type in zip(
            df["table_schema"], df["table_name"], df["column_name"], df["data_type"]
        ):
            if table_name not in result[schema].tables:
                result[schema].tables[table_name] = M.DatabaseTable({}, {})
            field = self._sqlalchemy_column_to_field(
                SA_T.parse_sqltype(d_type),
                field_name=field_name,
                sa_table=None,
            )
            if field:
                result[schema].tables[table_name].add_field(field, d_type)

        for schema_table_request in schema_tables_to_fetch:
            cache_key = self._cache.get_key(
                self._connection_object,
                CacheKeyType.TABLE_COLUMNS,
                schema=schema_table_request.schema,
                table_name=schema_table_request.table,
            )

            if (
                schema_table_request.table
                in result[schema_table_request.schema].tables.keys()
            ):
                self._cache_object(
                    cache_key,
                    result[schema_table_request.schema].tables[
                        schema_table_request.table
                    ],
                )

        return result

    def _get_conversion_df(
        self, metric: M.Metric, cte_prefix: Optional[str]
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix)
        if metric.config.aggregation.type not in M.LIST_USER_AGGS:
            df = dataframe_str_to_datetime(df, GA.DATETIME_COL)
            for index in range(1, len(metric.segments) + 1):
                df[f"{GA.AGG_VALUE_COL}_{index}"] = df[
                    f"{GA.AGG_VALUE_COL}_{index}"
                ].astype(float)
        return df

    def _get_segmentation_df(self, metric: M.Metric) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric)
        if metric.config.aggregation.type not in M.LIST_USER_AGGS:
            df = dataframe_str_to_datetime(df, GA.DATETIME_COL)
            df[GA.AGG_VALUE_COL] = df[GA.AGG_VALUE_COL].astype(float)
        return df

    def _get_retention_df(
        self, metric: M.Metric, cte_prefix: Optional[str]
    ) -> pd.DataFrame:
        df = super()._get_retention_df(metric, cte_prefix)
        if metric.config.aggregation.type not in M.LIST_USER_AGGS:
            df = dataframe_str_to_datetime(df, GA.DATETIME_COL)
            df[GA.AGG_VALUE_COL] = df[GA.AGG_VALUE_COL].astype(float)
        return df

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
            query = str(
                query.compile(
                    compile_kwargs={"literal_binds": True},
                    dialect=trino.sqlalchemy.dialect.TrinoDialect(),
                )
            )

        if self.adapter_context:
            query = (
                "--" + self.adapter_context.get_query_context_json() + "\n" + str(query)
            )

        return super().execute_query(
            query=query, log_error_as_warning=log_error_as_warning
        )

    def get_field_reference(
        self, field: M.Field, sa_table: Union[SA.Table, SEL.CTE]
    ) -> FieldReference:
        if field.parent is not None and field.parent.type == M.DataType.MAP:
            map_key = field.name
            property = SA.literal_column(f"{sa_table.name}.{field.parent._get_name()}")  # type: ignore[union-attr]
            return SA.func.element_at(property, map_key)

        return super().get_field_reference(field, sa_table)

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, SA_T.ROW):
            return M.DataType.STRUCT
        if isinstance(sa_type, SA_T.MAP):
            return M.DataType.MAP
        if isinstance(sa_type, SA_TYPES.ARRAY):
            return M.DataType.ARRAY
        return super().map_type(sa_type)

    def _get_row_field_type(self, struct_type: SA_T.ROW, field_path: str) -> Any:
        paths = field_path.split(".")
        struct_fields = dict(self._get_struct_sub_types(struct_type))
        if len(paths) == 1:
            return struct_fields[paths[0]]

        return self._get_row_field_type(struct_fields[paths[0]], ".".join(paths[1:]))

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        array_type = SA_T.parse_sqltype(sa_type)
        parent = None

        if isinstance(array_type, SA_T.ROW):
            name_parts = name.split(".")
            parent_name = ".".join(name_parts[0:-1])
            field_name = name_parts[-1]
            array_type = self._get_row_field_type(
                array_type, ".".join(name.split(".")[1:])
            )
            parent = M.Field(
                name=parent_name,
                type=M.DataType.STRUCT,
            )
        else:
            field_name = name

        if not isinstance(array_type, SA_TYPES.ARRAY):
            raise ValueError(f"Cannot parse type as a array type: {sa_type}")

        item_type = self.map_type(array_type.item_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=item_type)])
        return M.Field(
            name=field_name, type=M.DataType.ARRAY, sub_fields=sfs, parent=parent
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        LOGGER.debug(f"Discovering map: {name}")
        # example sa_type value: 'map(varchar(7), integer)'
        map_type = SA_T.parse_sqltype(sa_type)
        if not isinstance(map_type, SA_T.MAP):
            raise ValueError(f"Cannot parse type as a map type: {sa_type}")

        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        if type(dt) == date:
            return SA.literal_column(f"date('{dt}')")
        else:
            return SA.literal_column(f"timestamp '{dt}'")

    def _get_struct_type(self) -> TypeEngine:
        return SA_T.ROW

    def _get_floating_type(self) -> TypeEngine:
        return SA_T.DOUBLE  # type: ignore

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        return SA.func.array_agg(SA.distinct(field_ref))

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.func.contains(field_ref, value)

    def _get_array_like_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        arr_str = SA.func.array_join(field_ref, STRING_SEPARATOR)
        if self.insight_settings and self.insight_settings.case_insensetive_filtering:
            value = value.lower()
            arr_str = SA.func.lower(arr_str)
        return arr_str.like(f"%{value}%")

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte=dataset_cte,
            data_table=data_table,
            fields=fields,
            event_names=event_names,
            indexing_context=indexing_context,
        )

        # we cannot serialize datetime.datetime types in the discovered project
        # and it may causes other issues, so it's better to convert them to string
        # right here and our codebase is already prepared to recognise them as timestamps
        def fix_timestamp_type(df_cell_val: Any) -> Any:
            if isinstance(df_cell_val, List):
                res = []
                for val in df_cell_val:
                    if isinstance(val, datetime):
                        res.append(str(val))
                    else:
                        res.append(val)
                return res
            return df_cell_val

        # need a newer pandas version to resolve this linter issue
        df = df.applymap(fix_timestamp_type)  # type: ignore[operator]
        return pdf_string_json_array_to_array(df)

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        return SA.func.date_add(
            timewindow.period.name.lower(),
            timewindow.value,
            field_ref,
        )

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.date_add(time_group.name.lower(), val_ref, field_ref)

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            if metric._agg_param is None or 0 < metric._agg_param > 100:
                raise ValueError(
                    "Conversion percentile parameter must be between 0 and 100"
                )
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return self._get_percentile_agg(
                self.date_diff(t1, t2, "second"), metric._agg_param
            )
        if metric._agg_type == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return SA.func.avg(self.date_diff(t1, t2, "second"))
        else:
            return super()._get_conv_aggregation(metric, cte, index)

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return a / SA.func.nullif(b, 0)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.date_diff(period, start_dt, end_dt)

    def get_date_partition_filter(
        self,
        edt: M.EventDataTable,
        table: SA.Table,
        start_dt: datetime,
        end_dt: datetime,
        time_group: Optional[M.TimeGroup],
    ) -> ELM.ColumnElement:
        if time_group == M.TimeGroup.HOUR:
            # in trino date_trunc(hour, date) raises an error
            time_group = M.TimeGroup.DAY

        return super().get_date_partition_filter(
            edt, table, start_dt, end_dt, time_group
        )

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return SA.func.approx_percentile(col, percentile / 100)

    def cancel_query(self, query_id: str) -> None:
        this_query_id = create_unique_id()
        try:
            user_name = self._connection_object.user_name
            con = self.get_engine().connect()
            row = con.execute(
                f"""select 
                        query_id 
                    from system.runtime.queries 
                    where STATE not in ('FAILED', 'FINISHED')  
                        and created >= now() - interval '2' hour 
                        and user = '{user_name}' 
                        and query like '%{query_id}%'
                        and query not like '%{this_query_id}%' 
                    limit 1"""
            ).fetchone()

            if not row or len(row) == 0:
                LOGGER.info(f"Nothing to cancel for query_id: {query_id}")
                return
            trino_query_id = row[0]

            con.execute(
                f"call system.runtime.kill_query(query_id=> '{trino_query_id}', message=>'Cancelled by {user_name}')"
            )

        except Exception as exc:
            LOGGER.opt(exception=exc).warning(str(exc))

    def _parse_query_state_type(self, state_str: str) -> GA.QueryState:
        try:
            if state_str.upper() == "WAITING_FOR_RESOURCES":
                return GA.QueryState.QUEUED
            if state_str.upper() == "FINISHING":
                return GA.QueryState.FINISHED
            return GA.QueryState[state_str.upper()]
        except KeyError:
            return GA.QueryState.UNKNOWN

    def get_cluster_info_df(self) -> pd.DataFrame:
        return self.execute_query(
            """select 
                state, query, null as statistics
                from system.runtime.queries 
                where created >= now() - interval '4' hour
                    and lower(query) not like '%system.runtime.queries%'
                """
        )

    def is_cluster_info_supported(self) -> bool:
        return True

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:
        return AH.LeadIgnoreNulls(
            SA.case(
                (
                    identifier == step,
                    target_col,
                ),
                else_=None,
            )
        )

    def get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return None

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.to_unixtime(field_ref) % 2973
        if field.type == M.DataType.NUMBER:
            return super().get_field_numeric_value(field_ref, field)
        return SA.func.codepoint(SA_EXP.cast(field_ref, SA.VARCHAR(1))) * 31

    def _is_array_type_supported(self) -> bool:
        return True
