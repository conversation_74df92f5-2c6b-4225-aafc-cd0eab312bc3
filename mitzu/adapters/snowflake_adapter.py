from __future__ import annotations

import json
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import snowflake.connector.errors as SFE
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from snowflake.sqlalchemy.custom_types import (
    ARRAY,
    OBJECT,
    TIMESTAMP_NTZ,
    TIMESTAMP_TZ,
    VARIANT,
)
from snowflake.sqlalchemy.snowdialect import ischema_names
from sqlalchemy import event
from sqlalchemy.types import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.model as M
from mitzu.adapters.indexer import Indexer
from mitzu.adapters.model import (
    FieldReference,
    FilterCondition,
    NoDataError,
    fix_col_index,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import MitzuCache
from mitzu.helper import STRING_SEPARATOR, create_unique_id
from mitzu.logger import LOGGER


class SnowflakeAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def __init__(
        self,
        connection: M.Connection,
        cache: MitzuCache,
        end_date: datetime,
        insight_settings: M.InsightSettings,
        revenue_settings: Optional[M.RevenueSettings] = None,
        adapter_context: Optional[GA.AdapterContext] = None,
    ):
        super().__init__(
            connection,
            cache,
            end_date,
            insight_settings,
            revenue_settings,
            adapter_context,
        )

    def _get_connection_url(self, con: M.Connection) -> str:
        url = super()._get_connection_url(con)
        extra_args = {}

        if "warehouse" in con.extra_configs.keys():
            extra_args["warehouse"] = con.extra_configs["warehouse"]

        if "role" in con.extra_configs.keys():
            extra_args["role"] = con.extra_configs["role"]

        if len(extra_args) > 0:
            url += "?" + "&".join(
                f"{key}={value}" for (key, value) in extra_args.items()
            )

        return url

    def _get_connect_args(self) -> Dict[str, Any]:
        private_key = self._connection_object.password
        if self._connection_object.auth_type != M.ConnectionAuthType.KEY_PAIR:
            return {}

        if private_key is None:
            raise ValueError("Private key is not none")

        p_key = serialization.load_pem_private_key(
            private_key.encode(),
            password=None,
            backend=default_backend(),
        )

        pkb = p_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        return {
            "private_key": pkb,
        }

    def get_engine(self) -> Any:
        engine = SA.create_engine(
            self._get_connection_url(self._connection_object),
            connect_args=self._get_connect_args(),
        )
        # This is needed for Query cancelling

        def add_comment(
            conn: Any, cursor: Any, statement: str, parameters: Any, context: Any, executemany: Any  # type: ignore[no-untyped-def]
        ) -> Tuple[str, Any]:
            if self.adapter_context:
                safe_json = self.adapter_context.get_query_context_json().replace(
                    "%", "%%"
                )
                return f"{statement}\n-- {safe_json}", parameters
            else:
                return statement, parameters

        event.listen(engine, "before_cursor_execute", add_comment, retval=True)
        return engine

    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, SFE.ProgrammingError):
            if exc.orig.errno == 100051:
                return GA.DivisionByZeroUserError(exc)
            if exc.orig.errno in [100038, 1007]:
                return GA.NotNumericFieldUserError(exc)

        return None

    def _map_key_type_to_field(self, key_type: str) -> Optional[M.Field]:
        key, str_type = key_type.split(STRING_SEPARATOR)
        str_type = str_type.upper()

        if str_type in ischema_names:
            return M.Field(key, self.map_type(ischema_names[str_type]()))

        return None

    def _parse_type_name(self, type_name: str) -> M.DataType:
        if (
            "INT" in type_name
            or "FLOAT" in type_name
            or "DOUBLE" in type_name
            or type_name == "REAL"
        ):
            return M.DataType.NUMBER

        if "DATE" in type_name or "TIMESTAMP" in type_name:
            return M.DataType.DATETIME

        raise ValueError(f"Cannot parse type name string: {type_name}")

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.to_timestamp(field)

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        cte = Indexer(indexing_context, self).get_dataset_discovery_cte(
            data_table,
            columns_filter=[name],
            text_filter=None,
        )
        F = SA.func
        query = SA.select(
            columns=[
                SA.literal_column(
                    f"'{M.ARRAY_TYPE_SUBFIELD_NAME}{STRING_SEPARATOR}' || typeof(obj_1.value)"
                )
                .distinct()
                .label("key_type")
            ],
            whereclause=SA.text(f"typeof({cte.columns[name]}) is not null"),
        )
        query = query.select_from(
            cte, SA.lateral(F.flatten(cte.columns[name]).alias("obj"))
        )
        df = self.execute_query(query)

        if df.shape[0] == 0:
            return M.Field(name=name, type=M.DataType.ARRAY)
        key_types = df.to_dict(orient="list")["key_type"]
        if not key_types:
            raise NoDataError(f"Could not find any sub fields for field {name}")

        sub_fields: Dict[str, M.Field] = {}

        for kt in key_types:
            f = self._map_key_type_to_field(kt)
            if f is not None:
                if f.name not in sub_fields:
                    sub_fields[f.name] = f
                else:
                    # an array in snowflake may contains multiple value types
                    # in this case we fallback to string
                    sub_fields[f.name] = M.Field(f.name, M.DataType.STRING)
                    break

        return M.Field(
            name=name, type=M.DataType.ARRAY, sub_fields=tuple(sub_fields.values())
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        if sa_type not in ("OBJECT()", "VARIANT()"):
            raise ValueError(f"Cannot parse type as a MAP type: {sa_type}")

        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, TIMESTAMP_NTZ) or isinstance(sa_type, TIMESTAMP_TZ):
            return M.DataType.DATETIME

        if isinstance(sa_type, OBJECT) or isinstance(sa_type, VARIANT):
            return M.DataType.MAP

        if isinstance(sa_type, ARRAY):
            return M.DataType.ARRAY

        return super().map_type(sa_type)

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        return SA.func.dateadd(
            timewindow.period.name.lower(),
            timewindow.value,
            field_ref,
        )

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return a / SA.func.nullif(b, 0)

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        return SA.func.TO_TIMESTAMP(dt)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.datediff(period, start_dt, end_dt)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.dateadd(time_group.name.lower(), val_ref, field_ref)

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table,
            fields,
            event_names,
            indexing_context=indexing_context,
        )

        def parse_columns(col_val: Optional[str]) -> Any:
            if col_val is None:
                return None

            data = json.loads(col_val)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, list):
                        return col_val  # nested arrays are not supported
            return data

        for field in df.columns:
            df[field] = df[field].apply(parse_columns)
        return df

    def _get_column_cardinality(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_F.Function:
        if field.type == M.DataType.ARRAY:
            return SA.func.array_size(
                SA.func.array_distinct(
                    SA.func.array_flatten(
                        SA.func.array_compact(SA.func.array_agg(field_ref))
                    )
                )
            )
        else:
            return SA.func.count(SA.distinct(field_ref))

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        if field.type == M.DataType.ARRAY:
            return SA.func.array_compact(
                SA.func.array_distinct(
                    SA.func.array_flatten(
                        SA.func.array_compact(SA.func.array_agg(field_ref))
                    )
                )
            )

        if field.type == M.DataType.MAP:
            json_field = SA.func.to_json(field_ref)
            if compress_map_type:
                return SA.func.array_agg(
                    SA.func.BASE64_ENCODE(
                        SA.func.compress(
                            json_field,
                            "zlib(10)",
                        ),
                    )
                )
            else:
                return SA.func.array_agg(json_field)

        return SA.func.array_agg(SA.distinct(field_ref))

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.func.array_contains(SA.func.to_variant(value), field_ref)

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> SA_F.Function:
        if isinstance(field_ref, str) or isinstance(field_ref, datetime):
            field_ref = SA.func.to_timestamp(field_ref)
        return SA.func.date_trunc(time_group.name, field_ref)

    def _get_conv_aggregation(self, metric: M.Metric, cte: SEL.CTE, index: int) -> Any:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            if metric._agg_param is None or 0 < metric._agg_param > 100:
                raise ValueError(
                    "Conversion percentile parameter must be between 0 and 100"
                )
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return SA.cast(
                SA.func.approx_percentile(
                    self.date_diff(t1, t2, "second"), metric._agg_param / 100.0
                ),
                SA_T.FLOAT,
            )
        if metric._agg_type == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return SA.cast(SA.func.avg(self.date_diff(t1, t2, "second")), SA_T.FLOAT)
        else:
            return super()._get_conv_aggregation(metric, cte, index)

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return SA.func.approx_percentile(col, percentile / 100.0)

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:
        return AH.LeadIgnoreNulls(
            SA.case(
                (
                    identifier == step,
                    target_col,
                ),
                else_=None,
            )
        )

    def get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return None

    def cancel_query(self, query_id: str) -> None:
        this_query_id = create_unique_id()
        try:
            con = self.get_engine().connect()
            row = con.execute(
                f"""select query_id
                    from TABLE(INFORMATION_SCHEMA.QUERY_HISTORY(
                            dateadd('hours',-4,current_timestamp()),
                            current_timestamp()
                        ))
                    where query_text like '%{query_id}%'
                        and query_text not like '%{this_query_id}%' 
                    limit 1"""
            ).fetchone()

            if not row or len(row) == 0:
                LOGGER.bind(query_id=query_id).info("Nothing to cancel for query_id.")
                return

            sf_query_id = row[0]
            con.execute(f"select SYSTEM$CANCEL_QUERY('{sf_query_id}')").fetchone()
        except Exception as exc:
            LOGGER.opt(exception=exc).error("Failed to cancel query")

    def _parse_query_state_type(self, state_str: str) -> GA.QueryState:
        try:
            state_upper = state_str.upper()
            if "QUEUED" in state_upper:
                return GA.QueryState.QUEUED
            if "FAIL" in state_upper:
                return GA.QueryState.FAILED
            if state_upper == "SUCCESS":
                return GA.QueryState.FINISHED
            if state_upper == "RUNNING":
                return GA.QueryState.RUNNING

            if state_upper == "CANCELED":
                return GA.QueryState.CANCELLED
            return GA.QueryState[state_upper]
        except KeyError:
            return GA.QueryState.UNKNOWN

    def get_cluster_info_df(self) -> pd.DataFrame:
        res = self.execute_query(
            SA.text(
                """                
                select
                    execution_status as state,
                    query_text as query,
                    execution_time,
                    credits_used_cloud_services,
                    total_elapsed_time
                from
                    TABLE(INFORMATION_SCHEMA.QUERY_HISTORY(
                        dateadd('hours',-4,current_timestamp()),
                        current_timestamp()
                    ))
                where upper(query_text) not like '%INFORMATION_SCHEMA.QUERY_HISTORY%'
                """
            )
        )
        res["statistics"] = res.apply(
            lambda row: {
                "execution_time (ms)": row["execution_time"],
                "cloud_credits_used": row["credits_used_cloud_services"],
                "total_elapsed_time (ms)": row["total_elapsed_time"],
            },
            axis=1,
        )
        res = res.drop(
            columns=[
                "execution_time",
                "credits_used_cloud_services",
                "total_elapsed_time",
            ]
        )
        return res

    def is_cluster_info_supported(self) -> bool:
        return True

    def _is_array_type_supported(self) -> bool:
        return True

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.date_part("epoch_second", field_ref) % 2973
        return super().get_field_numeric_value(field_ref, field)

    def sample_events(
        self,
        sa_table: SA.Table,
        ed_table: M.EventDataTable,
        event_sampling: float,
        where_clause: FilterCondition,
    ) -> Tuple[SA.Table, FilterCondition]:
        if event_sampling > 0:
            sa_table = AH.TablePercentSample(
                sa_table, event_sampling * 100, include_percent=False
            )
        return sa_table, where_clause
