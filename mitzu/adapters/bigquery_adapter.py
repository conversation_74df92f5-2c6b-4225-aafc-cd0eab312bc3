from __future__ import annotations

import json
import random
from datetime import datetime, timed<PERSON>ta
from typing import (
    Any,
    Dict,
    Iterable,
    List,
    Optional,
    Union,
    cast,
)

import pandas as pd
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
import sqlparse
from google.cloud import bigquery
from google.oauth2 import service_account
from sqlalchemy.dialects import (  # type: ignore[attr-defined]
    registry,
)
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.type_api import TypeEngine
from sqlalchemy.sql.visitors import TraversibleType

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu.adapters.helper import (
    aware_split,
    bytes_to_human_readable,
    pdf_string_json_array_to_array,
    string_array_to_array,
)
from mitzu.adapters.model import (
    AggregatedField,
    ExecutableType,
    FieldReference,
    SelectType,
    fix_col_index,
    is_select_type,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import CacheKeyType, MitzuCache
from mitzu.logger import LOGGER
from mitzu.vendor.sqlalchemy_bigquery import (
    FLOAT,
    STRUCT,
    _types,
    dialect,
)


class FirstValueIgnoreNulls(SA_F.FunctionElement):
    name = "first_value"


class ApproxQuantiles(SA_F.FunctionElement):
    name = "approx_quantiles"


@compiles(ApproxQuantiles)
def compile_approx_quantiles(
    element: Any, compiler: Any, **kwargs: Any
) -> str:  # noqa: F811
    clauses = list(element.clauses)
    target_col = compiler.process(clauses[0], **kwargs)
    percentile = compiler.process(clauses[1], **kwargs)

    return f"APPROX_QUANTILES({target_col}, 100)[OFFSET({percentile})]"


@compiles(FirstValueIgnoreNulls)
def compile_first_value_ignore_nulls(
    element: Any, compiler: Any, **kwargs: Any
) -> str:  # noqa: F811
    compiled_args = ", ".join(
        [compiler.process(arg, **kwargs) for arg in element.clauses]
    )
    return f"FIRST_VALUE({compiled_args}  IGNORE NULLS)"


JSON_LIST_KEY_VALS = '''
CREATE TEMP FUNCTION 
json_object_list(INPUT JSON)
RETURNS ARRAY<String>
LANGUAGE js AS """
if (INPUT != null) {{
  return Object.keys(INPUT);
}}
return null;
""";     
'''

registry.register("bigquery", "mitzu.vendor.sqlalchemy_bigquery", "BigQueryDialect")


def _parse_type_string(type_str: str) -> Union[None, TraversibleType, STRUCT, SA.ARRAY]:
    for k, v in _types._type_map.items():
        if k == type_str.upper():
            return v
        if type_str.upper().startswith(k):
            if "<" not in type_str:
                return v
            if v == STRUCT:
                attr = (
                    type_str.removeprefix("struct<")
                    .removeprefix("STRUCT<")[:-1]
                    .strip()
                )
                attr_types = []
                for split in aware_split(attr):
                    attr_name, attr_type_str = aware_split(
                        split.strip(), delimiter=" ", maxsplit=1
                    )
                    attr_type = _parse_type_string(attr_type_str)
                    if attr_type:
                        attr_types.append((attr_name, attr_type))
                if attr_types:
                    return STRUCT(*attr_types)
                else:
                    return None
            if v == SA.ARRAY:
                item_type_str = (
                    type_str.removeprefix("array<").removeprefix("ARRAY<")[:-1].strip()
                )
                item_type = _parse_type_string(item_type_str)
                return SA.ARRAY(item_type)

    LOGGER.bind(type_str=type_str).error(f"Failed to parse bigquery type: {type_str}")
    return None


class BigQueryAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def __init__(
        self,
        connection: M.Connection,
        cache: MitzuCache,
        end_date: datetime,
        insight_settings: M.InsightSettings,
        revenue_settings: Optional[M.RevenueSettings] = None,
        adapter_context: Optional[GA.AdapterContext] = None,
    ):
        super().__init__(
            connection,
            cache,
            end_date,
            insight_settings,
            revenue_settings,
            adapter_context,
            pseudocolumns=["_PARTITIONTIME", "_PARTITIONDATE"],
        )
        if connection.password is None:
            raise ValueError("Connection password is not set")
        passw = connection.password.replace("\n", "\\n")
        self._credentials = json.loads(passw)

    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        orig_error = str(exc.orig)
        if "division by zero" in orig_error:
            return GA.DivisionByZeroUserError(exc)

        if "Bad double value" in orig_error or (
            "Invalid cast" in orig_error and "to FLOAT64" in orig_error
        ):
            return GA.NotNumericFieldUserError(exc)

        return None

    def _get_connection_url(self, con: M.Connection) -> str:
        """
        The password and the username fields are intentionally omitted here
        Since there is not username and the password field contains the credential json
        which should be passed not in the connection string
        """
        host_str = "" if con.host is None else str(con.host)
        port_str = "" if con.port is None else f":{con.port}"
        schema_str = "" if con.schema is None else f"/{con.schema}"
        url_params_str = "" if con.url_params is None else con.url_params
        if url_params_str != "" and url_params_str[0] != "?":
            url_params_str = "?" + url_params_str
        catalog_str = "" if con.catalog is None else f"/{con.catalog}"

        protocol = con.get_protocol().lower()
        res = f"{protocol}://{host_str}{port_str}{catalog_str}{schema_str}{url_params_str}"

        return res

    def _parse_type_name(self, type_name: str) -> M.DataType:
        field_type = _parse_type_string(type_name)
        if field_type is None:
            raise ValueError(f"Cannot parse type name: {type_name}")
        return self.map_type(field_type)  # type: ignore[arg-type]

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.DATETIME(SA.func.TIMESTAMP_SECONDS(SA.cast(field, SA_T.INTEGER)))

    def _get_table_columns_df(
        self,
        schema_table_requests: Iterable[GA.SchemaTableRequest],
    ) -> pd.DataFrame:
        schema_tables: Dict[str, List[str]] = {}
        for req in schema_table_requests:
            if req.schema not in schema_tables.keys():
                schema_tables[req.schema] = []

            schema_tables[req.schema].append(req.table)

        if len(schema_tables) == 0:
            return pd.DataFrame(
                {
                    "table_schema": [],
                    "table_name": [],
                    "column_name": [],
                    "data_type": [],
                }
            )

        results = []
        for schema, tables in schema_tables.items():
            query = SA.text(
                f"""
                SELECT table_schema, table_name, column_name, data_type
                FROM `{schema}`.INFORMATION_SCHEMA.COLUMNS
                WHERE table_schema = '{schema}' AND table_name IN ('{"', '".join(tables)}')
                """
            )
            try:
                results.append(self.execute_query(query))
            except Exception as exc:
                LOGGER.opt(exception=exc).warning(
                    "failed to query table columns schema={schema}, tables={tables}"
                )

        return pd.concat(results, ignore_index=True)

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        result: Dict[str, M.DatabaseSchema] = {
            s.schema: M.DatabaseSchema({}) for s in schema_requests
        }
        (
            result,
            schema_tables_to_fetch,
        ) = self._get_all_table_columns_cache_key_and_object(
            schema_requests, ignore_cache
        )

        df = self._get_table_columns_df(schema_tables_to_fetch)

        for schema, table_name, field_name, d_type in zip(
            df["table_schema"], df["table_name"], df["column_name"], df["data_type"]
        ):
            if field_name in self.pseudocolumns:
                continue

            if table_name not in result[schema].tables:
                result[schema].tables[table_name] = M.DatabaseTable({}, {})
            bq_type = _parse_type_string(d_type)
            if bq_type:
                field = self._sqlalchemy_column_to_field(
                    bq_type, field_name=field_name, sa_table=None
                )
                if field:
                    result[schema].tables[table_name].add_field(field, d_type)

        for schema_table_request in schema_tables_to_fetch:
            cache_key = self._cache.get_key(
                self._connection_object,
                CacheKeyType.TABLE_COLUMNS,
                schema=schema_table_request.schema,
                table_name=schema_table_request.table,
            )

            if (
                schema_table_request.table
                in result[schema_table_request.schema].tables.keys()
            ):
                self._cache_object(
                    cache_key,
                    result[schema_table_request.schema].tables[
                        schema_table_request.table
                    ],
                )

        return result

    def get_engine(self) -> Any:
        return SA.create_engine(
            self._connection_url, credentials_info=self._credentials
        )

    def _format_query(self, raw_query: Any) -> str:
        if type(raw_query) != str:
            raw_query = str(
                raw_query.compile(
                    compile_kwargs={"literal_binds": True}, dialect=dialect()
                )
            )

        return sqlparse.format(raw_query, reindent=True, keyword_case="upper")

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
            query = str(
                query.compile(compile_kwargs={"literal_binds": True}, dialect=dialect())
            )
            query = query.replace(
                "%", "%%"
            )  # bugfix for bigquery, which has string formatting

        if self.adapter_context:
            query = (
                "--" + self.adapter_context.get_query_context_json() + "\n" + str(query)
            )

        return super().execute_query(
            query=query, log_error_as_warning=log_error_as_warning
        )

    def get_field_reference_for_date_partitioning(
        self,
        field: M.Field,
        sa_table: Union[SA.Table, SEL.CTE],
    ) -> FieldReference:
        # in compare to the get_field_reference the column shouldn't be put into a datetime func
        return super().get_field_reference(field, sa_table)

    def get_field_reference(
        self, field: M.Field, sa_table: Union[SA.sql.TableClause, SEL.CTE]
    ) -> FieldReference:
        if field.parent and field.parent.type == M.DataType.MAP:
            res: Any = SA.literal_column(
                f"json_value({sa_table.name}.{field.parent._get_name()}['{field.name}'])"  # type: ignore[union-attr]
            )
            # json_value always returns string
            if field.type == M.DataType.NUMBER:
                res = SA.cast(res, SA_T.FLOAT)
            if field.type == M.DataType.BOOL:
                res = SA.cast(res, SA_T.BOOLEAN)
        else:
            res = super().get_field_reference(field, sa_table)

        if field.type == M.DataType.DATETIME:
            res = SA.func.datetime(res)
        return res

    def default_group_by_col(self, metric_type: M.MetricType) -> FieldReference:
        # bigquery recognizes nulls as an int column
        return self.group_by_col_cast(super().default_group_by_col(metric_type))

    def _get_random_function(self) -> SA_F.Function:
        return SA.func.rand()

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        if field.type == M.DataType.MAP:  # distinct does not work on JSON fields
            return SA.func.to_json(SA.func.array_agg(field_ref))
        if field.type == M.DataType.ARRAY:  # array agg does not work nested arrays
            return SA.func.to_json(SA.func.array_concat_agg(field_ref))
        return SA.func.to_json(SA.func.array_agg(SA.distinct(field_ref)))

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.literal(value).op("in")(SA.func.unnest(field_ref))

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> Any:
        if time_group == M.TimeGroup.WEEK:
            return SA.func.TIMESTAMP_TRUNC(field_ref, SA.literal_column("ISOWEEK"))
        return SA.func.TIMESTAMP_TRUNC(field_ref, SA.literal_column(time_group.name))

    def get_modulo(self, field: FieldReference, modulus: float) -> FieldReference:
        return SA.func.mod(field, modulus)

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table=data_table,
            fields=fields,
            event_names=event_names,
            indexing_context=indexing_context,
        )
        return pdf_string_json_array_to_array(df)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        return SA.func.datetime_add(
            field_ref,
            SA.literal_column(
                f"interval ({multiplier}*{value_field_ref})+{(adding)} {time_group.name.lower()}"
            ),
        )

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return SA.func.safe_divide(a, b)

    def get_date_partition_filter(
        self,
        edt: M.EventDataTable,
        table: SA.Table,
        start_dt: datetime,
        end_dt: datetime,
        time_group: Optional[M.TimeGroup],
    ) -> ELM.ColumnElement:
        if edt.date_partition_field is None:
            raise ValueError("Date partition field is not set")

        dt_part = self.get_field_reference_for_date_partitioning(
            edt.date_partition_field, table
        )
        if edt.date_partition_field.type == M.DataType.STRING:
            start_date = start_dt
            end_date = end_dt
            if time_group is not None and time_group != M.TimeGroup.TOTAL:
                start_date = self._get_date_trunc_string(time_group, start_date)
            return (dt_part >= str(start_date.date())) & (
                dt_part <= str(end_date.date())
            )
        if edt.date_partition_field.type == M.DataType.DATETIME:
            if edt.date_partition_field_type is not None:
                parsed_type = _parse_type_string(edt.date_partition_field_type)
                if isinstance(parsed_type, TraversibleType):
                    dt_column_type = parsed_type()
                else:
                    dt_column_type = SA_T.TIMESTAMP()
            else:
                dt_column_type = SA_T.TIMESTAMP()

            if time_group is not None and time_group != M.TimeGroup.TOTAL:
                if time_group == M.TimeGroup.WEEK:
                    trunc_group = SA.literal_column("ISOWEEK")
                elif time_group in [
                    M.TimeGroup.SECOND,
                    M.TimeGroup.MINUTE,
                    M.TimeGroup.HOUR,
                ]:
                    # bigquery cannot partition by second, mintute or hours
                    trunc_group = SA.literal_column(M.TimeGroup.DAY.name)
                else:
                    trunc_group = SA.literal_column(time_group.name)
            else:
                trunc_group = SA.literal_column("DAY")

            if isinstance(dt_column_type, SA_T.DATETIME):
                _start_date = SA.func.date_trunc(start_dt.date(), trunc_group)
                _end_date: Union[FieldReference, datetime] = end_dt
            elif isinstance(dt_column_type, SA_T.TIMESTAMP):
                _start_date = SA.func.timestamp(
                    SA.func.date_trunc(start_dt, trunc_group)
                )
                _end_date = SA.func.timestamp(end_dt)
            elif isinstance(dt_column_type, SA_T.DATE):
                _start_date = SA.func.date(start_dt.date())
                _end_date = SA.func.date(end_dt.date())
            else:
                raise ValueError(f"Unsupported date partition type: {dt_column_type}")
            return (dt_part >= _start_date) & (dt_part <= _end_date)
        else:
            raise ValueError("Date partition field must be Date or Text type.")

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return ApproxQuantiles(col, percentile)

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            if metric._agg_param is None or 0 < metric._agg_param > 100:
                raise ValueError(
                    "Conversion percentile parameter must be between 0 and 100"
                )
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            time_diff = self.date_diff(t1, t2, "second")
            percentile_query = self._get_percentile_agg(time_diff, metric._agg_param)

            return percentile_query.label("percentile_time_to_conv")
        if metric._agg_type != M.AggType.AVERAGE_TIME_TO_CONV:
            return super()._get_conv_aggregation(metric, cte, index)

        t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
        t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
        return SA.func.avg(self.date_diff(t1, t2, "second"))

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.date_diff(end_dt, start_dt, SA.literal_column(period))

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix, collection_cte_value_cols)
        return df

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, STRUCT):
            return M.DataType.STRUCT
        if isinstance(sa_type, SA_T.JSON):
            return M.DataType.MAP
        if isinstance(sa_type, SA_T.ARRAY):
            return M.DataType.ARRAY
        return super().map_type(sa_type)

    def _get_struct_sub_types(self, struct_val: Any) -> Any:
        return struct_val._STRUCT_fields

    def _get_struct_field_type(self, struct_type: STRUCT, field_path: str) -> Any:
        paths = field_path.split(".")
        struct_fields = dict(self._get_struct_sub_types(struct_type))
        if len(paths) == 1:
            return struct_fields[paths[0]]

        return self._get_struct_field_type(struct_fields[paths[0]], ".".join(paths[1:]))

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        array_type = _parse_type_string(sa_type)
        parent = None

        if isinstance(array_type, STRUCT):
            name_parts = name.split(".")
            parent_name = ".".join(name_parts[0:-1])
            field_name = name_parts[-1]
            array_type = self._get_struct_field_type(
                array_type, ".".join(name.split(".")[1:])
            )
            parent = M.Field(
                name=parent_name,
                type=M.DataType.STRUCT,
            )
        else:
            field_name = name

        if not isinstance(array_type, SA_T.ARRAY):
            raise ValueError(f"Cannot parse type as a array type: {sa_type}")

        item_type = self.map_type(array_type.item_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=item_type)])
        return M.Field(
            name=field_name,
            type=M.DataType.ARRAY,
            sub_fields=sfs,
            parent=parent,
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _get_struct_type(self) -> TypeEngine:
        return STRUCT  # type: ignore

    def _get_floating_type(self) -> TypeEngine:
        return FLOAT  # type: ignore

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        if (
            metric.config is not None
            and metric.config.aggregation is not None
            and metric.config.aggregation.type in M.LIST_USER_AGGS
        ):
            return df
        return string_array_to_array(df)

    def should_cast_cols_in_union(self) -> bool:
        return True

    def normalize_field_path(self, field_path: M.FieldPath) -> str:
        return str(field_path).replace(":", "__").replace(".", "__")

    def search_dimensions_df(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        df = super().search_dimensions_df(user_search_query)

        renamings = {
            self.normalize_field_path(f.field_path): str(f.field_path)
            for f in user_search_query.select_field_defs
        }
        df.rename(columns=renamings, inplace=True)

        return df

    def get_retention_alias(self) -> Optional[str]:
        suffix = random.randint(0, 10000)
        return f"ret_{suffix}"

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:
        return FirstValueIgnoreNulls(
            SA.case(
                (
                    identifier == step,
                    target_col,
                ),
                else_=None,
            )
        )

    def _is_array_type_supported(self) -> bool:
        return True

    def get_job_by_query_id(
        self, query_id: str, state_filter: str
    ) -> Optional[bigquery.QueryJob]:
        credentials = service_account.Credentials.from_service_account_info(
            self._credentials
        )
        client = bigquery.Client(
            credentials=credentials, project=self._connection_object.host
        )
        jobs: List[bigquery.QueryJob] = list(
            client.list_jobs(
                max_results=100,
                state_filter=state_filter,
                min_creation_time=datetime.now() - timedelta(hours=4),
            )
        )
        for job in jobs:
            if query_id in job.query:
                return job
        return None

    def cancel_query(self, query_id: str) -> None:
        try:
            job = self.get_job_by_query_id(query_id, state_filter="RUNNING")
            if job is None:
                LOGGER.info(f"Job with {query_id} not found")
                return

            LOGGER.info(f"Cancelling job {job.job_id}")
            job.cancel()
        except Exception as exc:
            LOGGER.opt(exception=exc).error(
                f"Failed to cancel query {query_id} on bigquery"
            )

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return self.get_modulo(
                SA.func.unix_seconds(SA.func.timestamp(field_ref)), 2973
            )
        return super().get_field_numeric_value(field_ref, field)

    def get_cluster_info_df(self) -> pd.DataFrame:
        credentials = service_account.Credentials.from_service_account_info(
            self._credentials
        )
        client = bigquery.Client(
            credentials=credentials, project=self._connection_object.host
        )

        jobs: List[bigquery.QueryJob] = list(
            client.list_jobs(
                max_results=100,
                min_creation_time=datetime.now() - timedelta(hours=4),
            )
        )

        job_data = [
            {
                "state": job.state,
                "query": job.query,
                "statistics": {
                    "bytes_billed": bytes_to_human_readable(job.total_bytes_billed)
                    if job.total_bytes_billed
                    else None,
                    "slot_time_millis": job.slot_millis,
                    "duration_seconds": (job.ended - job.started).total_seconds()
                    if job.ended and job.started
                    else None,
                },
            }
            for job in jobs
            if hasattr(job, "query")
        ]
        results = pd.DataFrame(job_data)

        return results

    def is_cluster_info_supported(self) -> bool:
        return True

    def _parse_query_state_type(self, state_str: str) -> GA.QueryState:
        state_mapping = {
            "PENDING": GA.QueryState.QUEUED,
            "RUNNING": GA.QueryState.RUNNING,
            "DONE": GA.QueryState.FINISHED,
            "FAILED": GA.QueryState.FAILED,
            "CANCELLED": GA.QueryState.CANCELLED,
        }

        return state_mapping.get(state_str.upper(), GA.QueryState.UNKNOWN)

    def dry_run_sql(self, query_str: str) -> Optional[GA.DryRunResult]:
        credentials = service_account.Credentials.from_service_account_info(
            self._credentials
        )
        client = bigquery.Client(
            credentials=credentials, project=self._connection_object.host
        )
        job_config = bigquery.QueryJobConfig(
            dry_run=True,
            use_query_cache=False,  # Optional: disable cache to get fresh estimates
        )

        query_job = client.query(query_str, job_config=job_config)

        return GA.DryRunResult(
            bytes_processed=query_job.total_bytes_processed,
            bytes_billed=query_job.total_bytes_billed,
            duration_seconds=(query_job.ended - query_job.started).total_seconds()
            if query_job.ended and query_job.started
            else 0,
        )

    def supports_dry_run(self) -> bool:
        return True
