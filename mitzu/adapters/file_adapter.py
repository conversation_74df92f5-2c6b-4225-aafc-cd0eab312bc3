from __future__ import annotations

import json
import pathlib
from datetime import datetime
from typing import Any, Optional

import pandas as pd

import mitzu.model as M
from mitzu.adapters.generic_adapter import AdapterContext
from mitzu.adapters.sqlite_adapter import SQLiteAdapter
from mitzu.cache import MitzuCache


class FileAdapter(SQLiteAdapter):
    def __init__(
        self,
        connection: M.Connection,
        cache: MitzuCache,
        end_date: datetime,
        insight_settings: M.InsightSettings,
        revenue_settings: Optional[M.RevenueSettings] = None,
        adapter_context: Optional[AdapterContext] = None,
    ):
        super().__init__(
            connection,
            cache,
            end_date,
            insight_settings,
            revenue_settings,
            adapter_context,
        )
        self._file_type = connection.extra_configs["file_type"]
        self._base_path = connection.extra_configs["path"]
        self._tables = connection.extra_configs["tables"]

    def get_engine(self) -> Any:
        engine = super().get_engine()
        for table_name in self._tables:
            df = self._read_file(table_name)
            df.to_sql(
                name=table_name,
                con=engine,
                index=False,
            )
        return engine

    def _read_file(self, table_name: str) -> pd.DataFrame:
        extension: str = self._file_type
        path = self._base_path
        file_loc = pathlib.Path(path, f"{table_name}.{extension}")
        if extension.endswith("csv"):
            df = pd.read_csv(file_loc, header=0)
        elif extension.endswith("json"):
            df = pd.read_json(file_loc)
        elif extension.endswith("parquet"):
            df = pd.read_parquet(
                file_loc,
            )
        else:
            raise Exception("Extension not supported: " + extension)
        df["event_time"] = pd.to_datetime(df["event_time"])
        return self._fix_complex_types(df)

    def _fix_complex_types(self, df: pd.DataFrame) -> pd.DataFrame:
        for col in df.columns:
            obj = df[col][0]
            if pd.api.types.is_dict_like(obj):
                df[col] = df[col].apply(lambda val: json.dumps(val, default=str))
            elif pd.api.types.is_list_like(obj):
                if type(obj) == tuple:
                    df[col] = df[col].apply(
                        lambda val: json.dumps(dict(val), default=str)
                    )
                else:
                    df[col] = df[col].apply(lambda val: json.dumps(val, default=str))
        return df
