from __future__ import annotations

import warnings
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union, cast

import clickhouse_driver
import clickhouse_driver.errors
import clickhouse_sqlalchemy.drivers.base as drivers_base
import clickhouse_sqlalchemy.exceptions as CH_E
import clickhouse_sqlalchemy.types.common as CH_T
import pandas as pd
import sqlalchemy as SA
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from sqlalchemy import Float
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.expression import CompoundSelect, Join
from sqlalchemy.sql.functions import FunctionElement
from sqlalchemy.sql.selectable import Select
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.helper import (
    dataframe_str_to_datetime,
    pdf_string_json_array_to_array,
)
from mitzu.adapters.model import (
    SIMPLE_TYPE_MAPPINGS,
    AggregatedField,
    ExecutableType,
    FieldReference,
    GroupByFieldReference,
    SelectType,
    fix_col_index,
    is_select_type,
)
from mitzu.adapters.query_builder.conversion import (
    ConversionQueryBuilder,
)
from mitzu.adapters.query_builder.retention import (
    RetentionQueryBuilder,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.helper import (
    JOURNEY_SEPARATOR,
    NO_GROUP,
    STRING_SEPARATOR,
)
from mitzu.logger import LOGGER


class AsofLeftOuterJoin(Join):
    """
    Official sqlalchemy does not support the joining options of the clickhouse
    therefore we need a custom clause element representing it.
    It can be used as a normal join but it is compiled with some extra keywords
    """


@compiles(AsofLeftOuterJoin)
def compile_asof_join(element, compiler, **kw) -> str:  # type: ignore[no-untyped-def]
    """
    The join-rsplit ensures that only the last join will be transformed into an
    ASOF join. This way we can join multiple tables combining normal an ASOF joins
    """
    orig_sql = compiler.visit_join(element, **kw)
    return "ASOF LEFT OUTER JOIN".join(orig_sql.rsplit("JOIN", 1))


class QuantileFunc(FunctionElement):
    """
    Official sqlalchemy does not support SQL functions with multiple argument list
    but the quantile method in clickhouse needs 2 argument groups
    eg. quantile(0.99)(column)

    This class instance can be used as it were returned from a SA.func.xxx(yyy) call,
    the method below will ensure that it will be compiled properly to have 2 argument groups

    https://clickhouse.com/docs/en/sql-reference/aggregate-functions/reference/quantile
    """

    name = "quantile"


@compiles(QuantileFunc)
def compile_quantile_func(element, compiler, **kw) -> str:  # type: ignore[no-untyped-def]
    """
    example:
    orig_sql = "quantile(0.1, expr)"
    return "quantile(0.1)(expr)"
    """
    orig_sql = compiler.visit_function(element, **kw)
    return ")(".join(orig_sql.split(",", 1))


class DistinctUnion(CompoundSelect):
    """
    Official sqlalchemy does not support distinct union but clickhouse requires either union all
    or union distinct
    """


@compiles(DistinctUnion)
def compile_distinct_union(element, compiler, **kw) -> str:  # type: ignore[no-untyped-def]
    orig_sql = compiler.visit_compound_select(element, **kw)
    return orig_sql.replace("UNION", "UNION DISTINCT")


class ClickhouseAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        with warnings.catch_warnings():
            """
            sqlalchemy adapter raises false warnings because we use null values in the
            isNull, isNotNull and assumeNotNull methods, which are needed because of the
            clickhouse type checks
            """
            warnings.filterwarnings(
                "ignore",
                message=r".*comparisons to NULL should not use operators outside of 'is' or 'is not'.*",
            )

            if is_select_type(query):
                query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
                query = (
                    str(query.compile(compile_kwargs={"literal_binds": True}))
                    + " SETTINGS join_use_nulls=1"
                ).replace(
                    ":", "\:"  # noqa: W605
                )
                query = SA.text(query)

            return super().execute_query(
                query=query, log_error_as_warning=log_error_as_warning
            )

    def _format_query(self, raw_query: Any) -> str:
        with warnings.catch_warnings():
            """
            sqlalchemy adapter raises false warnings because we use null values in the
            isNull, isNotNull and assumeNotNull methods, which are needed because of the
            clickhouse type checks
            """
            warnings.filterwarnings(
                "ignore",
                message=r".*comparisons to NULL should not use operators outside of 'is' or 'is not'.*",
            )
            return super()._format_query(raw_query) + "\nSETTINGS join_use_nulls=1"

    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if isinstance(exc, CH_E.DatabaseException):
            if isinstance(exc.orig, clickhouse_driver.errors.NetworkError):
                return GA.DWHServerError(exc)
            elif (
                isinstance(exc.orig, clickhouse_driver.errors.ServerException)
                and "authentication failed" in exc.orig.message.lower()
            ):
                return GA.DWHServerError(exc)

        return None

    def _get_random_function(self) -> SA_F.Function:
        return SA.func.rand()

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        # json serialization fixes th df encoding over the http protocol
        return SA.func.toJSONString(SA.func.groupUniqArray(field_ref))

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.func.indexOf(field_ref, value) != 0

    def _get_array_like_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        arr_str = SA.func.arrayStringConcat(field_ref, STRING_SEPARATOR)
        if self.insight_settings and self.insight_settings.case_insensetive_filtering:
            value = value.lower()
            arr_str = SA.func.lower(arr_str)
        return arr_str.like(f"%{value}%")

    def _get_null_discovered_value(self) -> Any:
        return SA.func.array()

    def _get_floating_type(self) -> TypeEngine:
        """
        Clickhouse types distinguish nullable an not nullable types
        Nullable should be used else casting before an aggregation methods eg. sum(cast(field as float))
        raise an error when checking the types for the casting
        """
        return CH_T.Nullable(SA_T.FLOAT)  # type: ignore

    def _parse_type_name(self, type_name: str) -> M.DataType:
        if "Int" in type_name or "Float" in type_name:
            return M.DataType.NUMBER

        lower_type_name = type_name.lower()
        if "date" in lower_type_name or "time" in lower_type_name:
            return M.DataType.DATETIME

        raise ValueError(f"Cannot parse type name: {type_name}")

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.toDateTime(field)

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, CH_T.Nullable):
            return self.map_type(sa_type.nested_type)

        if isinstance(sa_type, CH_T.Map):
            return M.DataType.MAP

        if isinstance(sa_type, CH_T.Tuple):
            return M.DataType.STRUCT

        if isinstance(sa_type, CH_T.Array):
            return M.DataType.ARRAY

        for sa_t, data_type in SIMPLE_TYPE_MAPPINGS.items():
            if (
                isinstance(sa_type, sa_t)
                or sa_type == sa_t
                or (
                    isinstance(sa_type, type) and issubclass(sa_type, sa_t)
                )  # workaround for map support
            ):
                return data_type

        try:
            sa_type_str = str(sa_type)
        except Exception:  # avoid errors like: Can't generate DDL for NullType();
            sa_type_str = "cannot convert sa_type to string"
        LOGGER.warning(f"Unknown type: {type(sa_type)}, {sa_type_str}")
        return M.DataType.STRING

    def _serialize_field_type_to_str(self, field_type: Any) -> str:
        # we can parse only the str repr of the map types
        if isinstance(field_type, CH_T.Map):
            return str(field_type)
        return super()._serialize_field_type_to_str(field_type)

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        # example sa_type value: 'Array(Nullable(String()))'
        dialect = drivers_base.ClickHouseDialect()
        array_type = dialect._get_column_type(name, sa_type.replace("()", ""))
        if not isinstance(array_type, CH_T.Array):
            raise ValueError(f"Cannot parse type as a array type: {sa_type}")

        item_type = self.map_type(array_type.item_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=item_type)])

        return M.Field(
            name=name,
            type=M.DataType.ARRAY,
            sub_fields=sfs,
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        # example sa_type value: 'Map(String, String)'
        dialect = drivers_base.ClickHouseDialect()
        map_type = dialect._get_column_type(name, sa_type)
        if not isinstance(map_type, CH_T.Map):
            raise ValueError(f"Cannot parse type as a map type: {sa_type}")

        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _get_struct_type(self) -> TypeEngine:
        return CH_T.Tuple

    def _parse_struct_type(
        self, sa_type: str, name: str, path: str, sa_table: Optional[SA.Table]
    ) -> M.Field:
        if sa_table is None:
            raise ValueError("SA table is not set to parse clickhouse structs")

        LOGGER.debug(f"Discovering tuple: {name}, {path}")
        dialect = drivers_base.ClickHouseDialect()

        query = SAH.select(
            columns=[SA.func.toTypeName(sa_table.columns[name])], limit=1
        )
        df = self.execute_query(query)

        # something like: 'Tuple(s String, i Int32, b Bool)'
        named_tuple_type = df.iloc[0][0]

        tuple_fields = [f.strip() for f in named_tuple_type[6:-1].split(",")]

        sub_fields: List[M.Field] = []
        for field in tuple_fields:
            field_name, field_type = field.split(" ")
            model_type = self.map_type(dialect._get_column_type(field_type, field_type))
            sub_fields.append(M.Field(field_name, model_type))

        return M.Field(name=name, type=M.DataType.STRUCT, sub_fields=tuple(sub_fields))

    def join_revenue_ctes(self, rev_dt_cte: SEL.CTE, segment_cte: SEL.CTE) -> SEL.Join:
        return rev_dt_cte.join(
            segment_cte,
            SA.literal(True),
            isouter=True,
        )

    def union(self, cte_a: SelectType, cte_b: SelectType) -> SelectType:
        return DistinctUnion(CompoundSelect.UNION, cte_a, cte_b)

    def get_revenue_aggregation_in_segmentation(
        self, at: M.AggType, cte: SEL.CTE
    ) -> FieldReference:
        filter = (
            cte.columns[GA.CTE_DATETIME_COL] >= cte.columns[GA.CTE_REV_PERIOD_START]
        ) & (cte.columns[GA.CTE_DATETIME_COL] <= cte.columns[GA.CTE_REV_PERIOD_END])
        if at == M.AggType.TOTAL_MRR:
            return SA.func.sum(
                SA.case(
                    (filter, cte.columns[GA.CTE_REV_AMOUNT]),
                    else_=0,
                )
            )
        elif at == M.AggType.COUNT_SUBSCRIBERS:
            return SA.func.count(
                SA.case(
                    (filter, cte.columns[GA.CTE_USER_ID_ALIAS_COL]),
                    else_=None,
                ).distinct()
            )
        else:
            raise ValueError(
                f"Aggregation type {at.name} is not supported for segmentation"
            )

    def get_field_reference(
        self,
        field: M.Field,
        sa_table: Union[SA.sql.TableClause, SEL.CTE],
    ) -> FieldReference:
        if field.parent and field.parent.type == M.DataType.STRUCT:
            return SA.func.tupleElement(
                self.get_field_reference(field.parent, sa_table), field.name
            )

        return super().get_field_reference(field, sa_table)

    def get_field_reference_for_dimension_search(
        self,
        field: M.Field,
        sa_table: Union[SA.Table, SEL.CTE],
    ) -> FieldReference:
        # json is needed only for the http interface
        return SA.func.toJSONString(self.get_field_reference(field, sa_table))

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table=data_table,
            fields=fields,
            event_names=event_names,
            indexing_context=indexing_context,
        )

        # we cannot serialize datetime.datetime types in the discovered project
        # and it may causes other issues, so it's better to convert them to string
        # right here and our codebase is already prepared to recognise them as timestamps
        def fix_timestamp_type(df_cell_val: Any) -> Any:
            if isinstance(df_cell_val, List):
                res: List[Any] = []
                for val in df_cell_val:
                    if isinstance(val, list) or isinstance(val, tuple):
                        res.append(val)
                    elif isinstance(val, datetime):
                        res.append(val.isoformat())
                    else:
                        res.append(val)
                return res
            return df_cell_val

        df = pdf_string_json_array_to_array(df)
        # need a newer pandas version to resolve this linter issue
        df = df.applymap(fix_timestamp_type)  # type: ignore[operator]

        return df

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return QuantileFunc(percentile / 100, col)

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> Any:
        if isinstance(field_ref, str) or isinstance(field_ref, datetime):
            field_ref = SA.func.toDateTime(field_ref)
        return SA.func.date_trunc(time_group.name.lower(), field_ref)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        """
        The only difference here is that the time group should not put between quotes
        """
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.date_add(
            SA.text(f"{time_group.name.lower()}"),
            val_ref,
            field_ref,
        )

    def concatenate_string_fields(
        self, field_a: FieldReference, field_b: FieldReference
    ) -> FieldReference:
        return SA.func.concat(field_a, SA.literal(STRING_SEPARATOR), field_b)

    def group_by_col_cast(self, col: FieldReference) -> FieldReference:
        return col.cast(CH_T.Nullable(SA.VARCHAR))

    def get_conversion_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> Any:
        """
        this is mostly copy-pasted from the query_builder/conversion.py but clickhouse
        has some limitations with the joins so some changes had to be applied

         - ASOF LEFT OUTER JOIN needs to be used and since it is not supported by the
           official sqlalchemy adapter the AsofLeftOuterJoin does the necessary workaround
           to generate a proper query
         - count(distinct _cte_user_id) is extended with a condition because
           the left join filles the missing values with an empty string and not with null values
           and since the asof join can contain only one range condition (step n time < step n+1 time)
           the other range condition is added here (step n time <= step 1. time + conv window)

        Other than this everything is the same and many things could be generalized to
        lower the code duplications but since the clickhouse support is in a discovery phase
        it's better not making the sqlaclhemy_adapter more complicated just because the clickhouse.
        Later this may change and we generalize parts of this method.
        """
        if not isinstance(metric.config, M.ConversionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionMetricConfig"
            )

        query_builder = ConversionQueryBuilder(self)
        segment_ctes = query_builder._get_conversion_segments_ctes(
            metric, collection_cte_value_cols
        )
        first_cte = segment_ctes[0]

        group_by_col: FieldReference = SA.literal(None)
        if metric.type == M.MetricType.JOURNEY:
            args: List[Union[SA_F.Function, SA_EXP.ColumnElement]] = [
                SA.func.coalesce(
                    segment_ctes[0].columns[GA.CTE_GROUP_COL],
                    NO_GROUP,
                )
            ]
            for index, segment_cte in enumerate(segment_ctes[1:], start=1):
                args.extend(
                    [
                        SA.literal(JOURNEY_SEPARATOR),
                        SA.func.coalesce(
                            segment_cte.columns[GA.CTE_GROUP_COL],
                            NO_GROUP,
                        ),
                    ]
                )
            group_by_col = SA.func.concat(*args)
        else:
            for index, segment in enumerate(metric.segments):
                if len(segment.group_by) > 0:
                    group_by_col = segment_ctes[index].columns[GA.CTE_GROUP_COL]
                    break

        time_group = metric.config.time_group
        if time_group != M.TimeGroup.TOTAL:
            first_evt_time_group = self.get_date_trunc(
                field_ref=first_cte.columns[GA.CTE_DATETIME_COL],
                time_group=time_group,
            )
        else:
            first_evt_time_group = SA.literal(None)

        steps = [first_cte]
        other_selects: List[FieldReference] = []
        joined_source = first_cte
        last_index = len(segment_ctes[1:]) - 1

        curr_cte = first_cte

        for i, curr_cte in enumerate(segment_ctes[1:]):
            prev_table = steps[i]
            prev_cols = prev_table.columns
            curr_cols = curr_cte.columns

            steps.append(curr_cte)
            user_count_col: Union[SA_EXP.ColumnElement, SA_F.Function] = SA.func.count(
                SA.case(
                    (
                        (
                            curr_cols.get(GA.CTE_DATETIME_COL)
                            <= self.get_datetime_interval(
                                (
                                    first_cte.columns[GA.CTE_DATETIME_COL]
                                    if metric.config.conv_window_type
                                    != M.ConversionWindowType.BETWEEN_EACH_STEP
                                    else prev_cols[GA.CTE_DATETIME_COL]
                                ),
                                metric.config.time_window,
                            )
                        ),
                        curr_cte.columns[GA.CTE_USER_ID_ALIAS_COL],
                    ),
                    else_=None,
                ).distinct()
            )

            other_selects.extend(
                [
                    user_count_col.label(fix_col_index(i + 2, GA.USER_COUNT_COL)),
                    (
                        self._get_clickhouse_conv_aggregation(
                            metric, curr_cte, first_cte, None
                        )
                        if (
                            metric._agg_type != M.AggType.BY_EVENT_PROPERTY
                            or i == last_index
                        )
                        and metric._agg_type != M.AggType.RETURN_DROP_OFF_USERS
                        else SA.literal(None)
                    ).label(fix_col_index(i + 2, GA.AGG_VALUE_COL)),
                ]
            )

            if len(metric.config.custom_holding_constants) == 0:
                holding_constants_condition = (
                    prev_cols[GA.CTE_USER_ID_ALIAS_COL]
                    == curr_cols[GA.CTE_USER_ID_ALIAS_COL]
                )
            else:
                holding_constants_condition = (
                    prev_cols[GA.CTE_HOLDING_CONSTANT_COL]
                    == curr_cols[GA.CTE_HOLDING_CONSTANT_COL]
                )

            joined_source = AsofLeftOuterJoin(  # type: ignore[assignment]
                joined_source,
                curr_cte,
                holding_constants_condition
                & (SA.func.isNotNull(prev_cols[GA.CTE_DATETIME_COL]))
                & (SA.func.isNotNull(curr_cols[GA.CTE_DATETIME_COL]))
                & (
                    SA.func.assumeNotNull(curr_cols[GA.CTE_DATETIME_COL])
                    > SA.func.assumeNotNull(prev_cols[GA.CTE_DATETIME_COL])
                ),
            )

        if (
            metric.config is not None
            and metric.config.aggregation is not None
            and metric.config.aggregation.type in M.LIST_USER_AGGS
        ):
            group_by: Optional[List[GroupByFieldReference]] = None
            if len(segment_ctes) >= 2:
                # Drop-off is only supported for more than 1 step funnels
                prev_cte = segment_ctes[-2]
            else:
                prev_cte = segment_ctes[0]
            columns = [
                (
                    self._get_clickhouse_conv_aggregation(
                        metric,
                        curr_cte=curr_cte,
                        first_cte=first_cte,
                        prev_cte=prev_cte,
                    )
                ).label(fix_col_index(1, GA.AGG_VALUE_COL)),
            ]
        else:
            first_user_count_col: Union[
                SA_EXP.ColumnElement, SA_F.Function
            ] = SA.func.count(
                first_cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct(),
            )

            columns = [
                first_evt_time_group.label(GA.DATETIME_COL),
                group_by_col.label(GA.GROUP_COL),
                first_user_count_col.label(fix_col_index(1, GA.USER_COUNT_COL)),
                (
                    self._get_clickhouse_conv_aggregation(
                        metric, first_cte, first_cte, prev_cte=None
                    )
                    if metric._agg_type != M.AggType.BY_EVENT_PROPERTY
                    else SA.literal(None)
                ).label(fix_col_index(1, GA.AGG_VALUE_COL)),
            ]
            columns.extend(other_selects)
            group_by = self.get_group_by_expression([GA.DATETIME_COL, GA.GROUP_COL], [])

        return SAH.select(
            columns=columns,
            group_by=group_by,
            select_from=joined_source,
        )

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        if type(dt) == datetime:
            dt = dt.replace(microsecond=0)
        return SA.func.toDateTime(dt)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.date_diff(period, end_dt, start_dt)

    def _fix_datetime_col(
        self, dt: Union[None, pd.Timestamp, datetime, date]
    ) -> Union[None, pd.Timestamp, datetime]:
        # datetrunc in clickhouse return date and not datetime
        if dt is None or isinstance(dt, datetime) or isinstance(dt, pd.Timestamp):
            return dt
        return datetime(dt.year, dt.month, dt.day)

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        if GA.DATETIME_COL in df.columns:
            df[GA.DATETIME_COL] = df[GA.DATETIME_COL].apply(self._fix_datetime_col)
        return df

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix, collection_cte_value_cols)
        if GA.DATETIME_COL in df.columns:
            df[GA.DATETIME_COL] = df[GA.DATETIME_COL].apply(self._fix_datetime_col)
        return df

    def _get_clickhouse_conv_aggregation(
        self,
        metric: M.Metric,
        curr_cte: SEL.CTE,
        first_cte: SEL.CTE,
        prev_cte: Optional[SEL.CTE],
    ) -> Any:
        """
        copy-pasted from the sqlachlemy_adapter.py the only difference is the
        count(distinct ... ) extra filters, explained in the _get_conversion_select method
        """
        if not isinstance(metric.config, M.ConversionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionMetricConfig"
            )

        at = metric._agg_type
        converted_users: FieldReference = SA.case(
            (
                (
                    curr_cte.columns[GA.CTE_DATETIME_COL]
                    <= self.get_datetime_interval(
                        first_cte.columns[GA.CTE_DATETIME_COL],
                        metric.config.time_window,
                    )
                ),
                curr_cte.columns[GA.CTE_USER_ID_ALIAS_COL],
            ),
            else_=None,
        )
        if at == M.AggType.CONVERSION or at == M.AggType.COUNT_CONVERTED_USERS:
            return (
                SA.func.count(converted_users.distinct())
                * 100.0
                / SA.func.count(first_cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct())
            )
        elif at == M.AggType.BY_EVENT_PROPERTY:
            return self.get_event_property_aggregation(
                metric.config.aggregation, curr_cte.columns[GA.CTE_EVENT_PROPERTY_COL]
            )
        elif (
            at == M.AggType.PERCENTILE_TIME_TO_CONV
            and metric.config.aggregation.conv_param is not None
        ):
            t1 = first_cte.columns.get(GA.CTE_DATETIME_COL)
            t2 = curr_cte.columns.get(GA.CTE_DATETIME_COL)
            diff = SA.func.toUnixTimestamp(t2) - SA.func.toUnixTimestamp(t1)
            return self._get_percentile_agg(diff, metric.config.aggregation.conv_param)
        elif at == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = first_cte.columns.get(GA.CTE_DATETIME_COL)
            t2 = curr_cte.columns.get(GA.CTE_DATETIME_COL)
            diff = SA.func.toUnixTimestamp(t2) - SA.func.toUnixTimestamp(t1)
            return SA.func.avg(diff)
        elif at == M.AggType.RETURN_DROP_OFF_USERS:
            if prev_cte is None:
                raise ValueError("Prev CTE is missing for drop-off user listing")
            prev_col = prev_cte.columns[GA.CTE_USER_ID_ALIAS_COL]
            curr_col = converted_users
            return SA.case(
                (
                    SA.func.count(curr_col).over(partition_by=prev_col) == 0,
                    prev_col,
                ),
                else_=SA.null(),
            )
        elif at in M.LIST_USER_AGGS:
            return converted_users.distinct()
        else:
            raise ValueError(f"Aggregation type {at} is not supported for conversion")

    def get_retention_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> Any:
        """
        this is mostly copy-pasted from the query_builder/retention.py but clickhouse
        has some limitations with the joins so some changes had to be applied

         - LEFT OUTER JOIN needs to be used without any conditions on the _cte_datetime since it is not supported
           by the clickhouse to have any non-matching conditions. ASOF joins support one non-matchin condition
           but it returns only a single matching record and not the cartesion product of all matching records
         - count(distinct _user_count_2) and other agg expressions are extended with a condition on the _cte_datetime
           because the left join does not have them and having them in the where clause we may filter out values from
           the initial event cte too

        Other than this everything is the same and many things could be generalized to
        lower the code duplications but since the clickhouse support is in a discovery phase
        it's better not making the sqlaclhemy_adapter more complicated just because the clickhouse.
        Later this may change and we generalize parts of this method.
        """
        if not isinstance(metric.config, M.RetentionMetricConfig):
            raise ValueError("Retention query generation require RetentionMetricConfig")

        query_builder = RetentionQueryBuilder(self)
        (
            initial_cte,
            retaining_cte,
            retention_index_cte,
        ) = query_builder._get_retention_segments_ctes(
            metric, collection_cte_value_cols
        )

        ret_dt_col = retaining_cte.columns[GA.CTE_DATETIME_COL]

        initial_group_by = (
            initial_cte.columns[GA.CTE_GROUP_COL]
            if metric.segments[0].group_by
            else SA.literal(None)
        )

        time_group = (
            self.get_date_trunc(
                metric.config.time_group, initial_cte.columns[GA.CTE_DATETIME_COL]
            )
            if metric.config.time_group != M.TimeGroup.TOTAL
            else SA.literal(None)
        )

        retention_interval_func = self.get_dynamic_datetime_interval(
            field_ref=initial_cte.columns[GA.CTE_DATETIME_COL],
            value_field_ref=retention_index_cte.columns[GA.RETENTION_INDEX],
            time_group=metric.config.time_window.period,
        )

        if len(metric.config.custom_holding_constants) == 0:
            holding_constants_condition = (
                initial_cte.c[GA.CTE_USER_ID_ALIAS_COL]
                == retaining_cte.c[GA.CTE_USER_ID_ALIAS_COL]
            )
        else:
            holding_constants_condition = (
                initial_cte.c[GA.CTE_HOLDING_CONSTANT_COL]
                == retaining_cte.c[GA.CTE_HOLDING_CONSTANT_COL]
            )
        if metric.config.consider_first_event_as_retained:
            joined_record_filter_condition = (
                self.convert_to_datetime_if_needed(ret_dt_col)
                >= retention_interval_func
            )
        else:
            joined_record_filter_condition = (
                self.convert_to_datetime_if_needed(ret_dt_col) > retention_interval_func
            )

        if (
            metric.config.retention_attribution_type
            != M.RetentionAttributionType.RETURN_ON_OR_AFTER
        ):
            joined_record_filter_condition = joined_record_filter_condition & (
                self.convert_to_datetime_if_needed(ret_dt_col)
                <= self.get_datetime_interval(
                    retention_interval_func,
                    metric.config.time_window,
                )
            )

        # it's not the best from the performance perspective but
        # clickhouse does not support any other way to do this
        # only asof join supports a single non-exact match condition but
        # it returns only a single record and not the cartesian product of all matching records
        joined_source = initial_cte.join(
            retention_index_cte,
            True,  # type: ignore[arg-type]
        ).join(
            retaining_cte,
            holding_constants_condition,
            isouter=True,
        )
        user_count_1_col: Union[SA_EXP.ColumnElement, SA_F.Function] = SA.func.count(
            initial_cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct()
        )

        user_count_2_col: Union[SA_EXP.ColumnElement, SA_F.Function] = SA.func.count(
            SA.case(
                (
                    joined_record_filter_condition,
                    retaining_cte.columns[GA.CTE_USER_ID_ALIAS_COL],
                ),
                else_=None,
            ).distinct()
        )

        if metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY:
            agg_col = self.get_event_property_aggregation(
                metric.config.aggregation,
                SA.case(
                    (
                        joined_record_filter_condition,
                        retaining_cte.columns[GA.CTE_EVENT_PROPERTY_COL],
                    ),
                    else_=None,
                ),
            )
        elif metric.config.aggregation.type in M.LIST_USER_AGGS:
            return SAH.select(
                columns=[
                    SA.case(
                        (
                            joined_record_filter_condition,
                            retaining_cte.columns[GA.CTE_USER_ID_ALIAS_COL],
                        ),
                        else_=None,
                    ).label(fix_col_index(1, GA.AGG_VALUE_COL))
                ],
                select_from=joined_source,
            ).distinct()
        else:
            agg_col = (
                user_count_2_col
                * 100.0
                / SA.func.count(
                    initial_cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct()
                )
            )

        columns = [
            time_group.label(GA.DATETIME_COL),
            initial_group_by.label(GA.GROUP_COL),
            retention_index_cte.columns[GA.RETENTION_INDEX].label(GA.RETENTION_INDEX),
            user_count_1_col.label(fix_col_index(1, GA.USER_COUNT_COL)),
            user_count_2_col.label(fix_col_index(2, GA.USER_COUNT_COL)),
            agg_col.label(GA.AGG_VALUE_COL),
        ]

        if metric.config.cut_off_incomplete_data:
            cut_off_incomplete_filter = initial_cte.columns[
                GA.CTE_DATETIME_COL
            ] < self.get_dynamic_datetime_interval(
                field_ref=self.correct_timestamp(self.end_date),
                value_field_ref=(
                    -(retention_index_cte.columns[GA.RETENTION_INDEX] + 1)
                ),
                time_group=metric.config.time_window.period,
            )
        else:
            cut_off_incomplete_filter = None

        return SAH.select(
            columns=columns,
            whereclause=cut_off_incomplete_filter,
            group_by=(
                self.get_group_by_expression(
                    [
                        GA.DATETIME_COL,
                        GA.GROUP_COL,
                        GA.RETENTION_INDEX,
                    ],
                    [],
                )
            ),
            select_from=joined_source,
        )

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_retention_df(metric, cte_prefix, collection_cte_value_cols)
        df = dataframe_str_to_datetime(df, GA.DATETIME_COL)
        return df

    def search_dimensions_df(
        self, dimension_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        df = super().search_dimensions_df(dimension_search_query)
        return pdf_string_json_array_to_array(df)

    def _is_array_type_supported(self) -> bool:
        return True

    def get_event_property_aggregation(
        self,
        aggregation: M.AggregationConfig,
        col: ELM.ColumnElement,
    ) -> AggregatedField:
        ep_agg = aggregation.event_property_agg
        if ep_agg is None:
            raise ValueError("Event property aggregation details are not set")
        if ep_agg.agg_type == M.EventPropertyAggregationType.COUNT_DISTINCT:
            return SA.cast(SA.func.count(col.distinct()), self._get_floating_type())
        return super().get_event_property_aggregation(aggregation, col)

    def fix_segment_queries_for_union(
        self, segment_queries: List[Select]
    ) -> List[Select]:
        modified_queries = []

        for query in segment_queries:
            query_cte = query.cte()
            datetime_col = query_cte.columns.get(GA.DATETIME_COL)
            group_col = query_cte.columns.get(GA.GROUP_COL)
            agg_value_col = query_cte.columns.get(GA.AGG_VALUE_COL)
            event_index_col = query_cte.columns.get(GA.EVENT_INDEX_COL)
            if not (
                datetime_col is None
                or group_col is None
                or agg_value_col is None
                or event_index_col is None
            ):
                modified_queries.append(
                    SAH.select(
                        columns=[
                            datetime_col.label(GA.DATETIME_COL),
                            group_col.label(GA.GROUP_COL),
                            SA.cast(agg_value_col, Float).label(GA.AGG_VALUE_COL),
                            event_index_col.label(GA.EVENT_INDEX_COL),
                        ],
                    )
                )
            else:
                raise ValueError("The query is not valid. The columns are missing.")
        return modified_queries

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.toUnixTimestamp(field_ref) % 2973
        return super().get_field_numeric_value(field_ref, field)

    def get_collection_inclusion_condition(
        self,
        ref: FieldReference,
        agg_values_col: FieldReference,
    ) -> Union[ELM.ColumnElement, ELM.BooleanClauseList]:
        return ref.in_(
            SAH.select(
                columns=[agg_values_col], whereclause=agg_values_col.is_not(None)
            )
        )
