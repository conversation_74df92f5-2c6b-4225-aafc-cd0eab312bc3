from __future__ import annotations

import json
import urllib.parse as P
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import requests
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from sqlalchemy import event
from sqlalchemy.dialects import (  # type: ignore[attr-defined]
    registry,
)
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.model as M
import mitzu.vendor.databricks.sql.exc as DA_EXC
import mitzu.vendor.databricks.sqlalchemy.datatype as DA_T
from mitzu.adapters.helper import (
    TablePercentSample,
    pdf_string_json_array_to_array,
    string_array_to_array,
)
from mitzu.adapters.model import (
    Aggregated<PERSON>ield,
    FieldReference,
    FilterCondition,
    fix_col_index,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import MitzuCache
from mitzu.helper import STRING_SEPARATOR
from mitzu.logger import LOGGER

MITZU_USER_AGENT = "Mitzu/1.0"

registry.register(
    "databricks", "mitzu.vendor.databricks.sqlalchemy.dialect", "DatabricksDialect"
)


class DatabricksAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def __init__(
        self,
        connection: M.Connection,
        cache: MitzuCache,
        end_date: datetime,
        insight_settings: M.InsightSettings,
        revenue_settings: Optional[M.RevenueSettings] = None,
        adapter_context: Optional[GA.AdapterContext] = None,
    ):
        super().__init__(
            connection,
            cache,
            end_date,
            insight_settings,
            revenue_settings,
            adapter_context,
        )
        http_path = connection.extra_configs.get("http_path")
        if http_path is None:
            raise Exception(
                "Connection extra_configs must contain http_path. (extra_configs={'http_path':'<path>'}"
            )
        self._connection_http_path = http_path

    def _get_connection_url(self, con: M.Connection) -> str:
        user_name = "" if con.user_name is None else P.quote(con.user_name)
        password = "" if con.password is None else f":{P.quote(con.password)}"
        host_str = "" if con.host is None else str(con.host)
        if con.user_name is not None and con.host is not None:
            host_str = f"@{host_str}"
        port_str = "" if con.port is None else f":{str(con.port)}"

        query_args = {}
        if con.catalog is not None:
            query_args["catalog"] = con.catalog

        if con.schema is not None:
            query_args["schema"] = con.schema

        query_string = "?" + (
            "&".join(f"{key}={val}" for key, val in query_args.items())
        )

        if con.url_params is not None:
            if query_string == "?":
                query_string += con.url_params
            else:
                query_string += "&" + con.url_params

        if query_string == "?":
            query_string = ""

        res = f"databricks://{user_name}{password}{host_str}{port_str}{query_string}"
        return res

    def get_engine(self) -> Any:
        engine = SA.create_engine(
            self._connection_url,
            connect_args={
                "http_path": self._connection_http_path,
                "_user_agent_entry": MITZU_USER_AGENT,  # this is a requirement from Databricks for Partner connection.
            },
        )

        def add_comment(
            conn: Any, cursor: Any, statement: str, parameters: Any, context: Any, executemany: Any  # type: ignore[no-untyped-def]
        ) -> Tuple[str, Any]:
            # We need to add the query context as a comment to the query this way,
            # Databricks doesn't work well with literal binds (due to % characters in the query)
            if self.adapter_context:
                safe_json = self.adapter_context.get_query_context_json().replace(
                    "%", "%%"
                )
                return f"{statement}\n-- {safe_json}", parameters
            else:
                return statement, parameters

        event.listen(engine, "before_cursor_execute", add_comment, retval=True)

        return engine

    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, DA_EXC.ServerOperationError):
            if "DIVIDE_BY_ZERO" in exc.orig.message:
                return GA.DivisionByZeroUserError(exc)

            if "CAST_INVALID_INPUT" in exc.orig.message:
                return GA.NotNumericFieldUserError(exc)

        return None

    def _parse_type_name(self, type_name: str) -> M.DataType:
        return self.map_type(DA_T.parse_sqltype(type_name))

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.to_timestamp(field)

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, DA_T.MAP):
            return M.DataType.MAP
        if isinstance(sa_type, DA_T.STRUCT):
            return M.DataType.STRUCT
        return super().map_type(sa_type)

    def _get_struct_sub_types(self, struct_val: DA_T.STRUCT) -> Any:
        return struct_val.attr_types

    def _get_struct_field_type(self, struct_type: DA_T.STRUCT, field_path: str) -> Any:
        paths = field_path.split(".")
        struct_fields = dict(self._get_struct_sub_types(struct_type))
        if len(paths) == 1:
            return struct_fields[paths[0]]

        return self._get_struct_field_type(struct_fields[paths[0]], ".".join(paths[1:]))

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        array_type = DA_T.parse_sqltype(sa_type)
        parent = None

        if isinstance(array_type, DA_T.STRUCT):
            name_parts = name.split(".")
            parent_name = ".".join(name_parts[0:-1])
            field_name = name_parts[-1]
            array_type = self._get_struct_field_type(
                array_type, ".".join(name.split(".")[1:])
            )
            parent = M.Field(
                name=parent_name,
                type=M.DataType.STRUCT,
            )
        else:
            field_name = name

        if not isinstance(array_type, SA_T.ARRAY):
            raise ValueError(f"Cannot parse type as a array type: {sa_type}")

        item_type = self.map_type(array_type.item_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=item_type)])
        return M.Field(
            name=field_name,
            type=M.DataType.ARRAY,
            sub_fields=sfs,
            parent=parent,
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        # example sa_type value: 'MAP(ARRAY(VARCHAR()), ARRAY(INTEGER()))'
        map_type = DA_T.parse_sqltype(sa_type)
        if not isinstance(map_type, DA_T.MAP):
            raise ValueError(f"Cannot parse type as a map type: {sa_type}")
        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _get_struct_type(self) -> TypeEngine:
        return DA_T.STRUCT  # type: ignore

    def _get_floating_type(self) -> TypeEngine:
        return DA_T.DOUBLE  # type: ignore

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            if metric._agg_param is None or 0 < metric._agg_param > 100:
                raise ValueError(
                    "Conversion percentile parameter must be between 0 and 100"
                )
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return self._get_percentile_agg(
                SA.cast(t2 - t1, SA_T.BIGINT), metric._agg_param
            )
        if metric._agg_type == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return SA.func.avg(SA.cast(t2 - t1, SA_T.BIGINT))
        else:
            return super()._get_conv_aggregation(metric, cte, index)

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return SA.func.approx_percentile(col, percentile / 100, 100)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.timestampadd(
            SA.literal_column(time_group.name.upper()), val_ref, field_ref
        )

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return a / SA.func.nullif(b, 0)

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        if type(dt) == date:
            return SA.func.date(dt.isoformat())
        else:
            return SA.func.timestamp(dt.isoformat())

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.datediff(SA.literal_column(period), start_dt, end_dt)

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table,
            fields,
            event_names,
            indexing_context=indexing_context,
        )
        return pdf_string_json_array_to_array(df)

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        if field.type == M.DataType.ARRAY:
            return SA.func.to_json(
                SA.func.array_distinct(SA.func.flatten(SA.func.array_agg(field_ref)))
            )
        else:
            return SA.func.to_json(SA.func.array_agg(SA.distinct(field_ref)))

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.func.array_contains(field_ref, value)

    def _get_array_like_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        arr_str = SA.func.concat_ws(STRING_SEPARATOR, field_ref)
        if self.insight_settings and self.insight_settings.case_insensetive_filtering:
            value = value.lower()
            arr_str = SA.func.lower(arr_str)
        return arr_str.like(f"%{value}%")

    def _get_column_cardinality(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_F.Function:
        if field.type == M.DataType.ARRAY:
            return SA.func.array_size(
                SA.func.array_distinct(SA.func.flatten(SA.func.array_agg(field_ref)))
            )
        else:
            return SA.func.count(SA.distinct(field_ref))

    def _get_segmentation_df(self, metric: M.Metric) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric)
        if (
            metric.config is not None
            and metric.config.aggregation is not None
            and metric.config.aggregation.type in M.LIST_USER_AGGS
        ):
            return df
        return string_array_to_array(df)

    def _get_conversion_df(
        self, metric: M.Metric, cte_prefix: Optional[str]
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix)
        return df

    def normalize_field_path(self, field_path: M.FieldPath) -> str:
        return str(field_path).replace(":", "__").replace(".", "__")

    def search_dimensions_df(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        df = super().search_dimensions_df(user_search_query)

        renamings = {
            self.normalize_field_path(f.field_path): str(f.field_path)
            for f in user_search_query.select_field_defs
        }
        df.rename(columns=renamings, inplace=True)

        return df

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:

        return AH.LeadIgnoreNulls(
            SA.case(
                (
                    identifier == step,
                    target_col,
                ),
                else_=None,
            )
        )

    def get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return None

    def _get_cluster_statements(self) -> Dict:
        curr_dt = datetime.now().timestamp()
        payload = {
            "filter_by": {
                "query_start_time_range": {
                    "end_time_ms": int(curr_dt * 1000),
                    "start_time_ms": int(curr_dt * 1000) - 3600 * 1000 * 4,
                },
                "statuses": ["QUEUED", "RUNNING", "FINISHED", "CANCELLED", "FAILED"],
            },
            "max_results": 1000,
        }
        header = {
            "Authorization": f"Bearer {self._connection_object.password}",
            "Content-Type": "application/json",
        }
        url = f"https://{self._connection_object.host}/api/2.0/sql/history/queries"
        response = requests.get(url, headers=header, data=json.dumps(payload))
        if response.status_code != 200:
            LOGGER.warning(
                f"Failed to get cluster info: {response.status_code}, {response.text}"
            )
            return {}
        return response.json()

    def cancel_query(self, query_id: str) -> None:
        try:
            header = {
                "Authorization": f"Bearer {self._connection_object.password}",
                "Content-Type": "application/json",
            }
            response = self._get_cluster_statements()
            results = response.get("res", [])
            for res in results:
                if query_id in res.get("query_text"):
                    statement_id = res.get("query_id")
                    url = f"https://{self._connection_object.host}/api/2.0/sql/statements/{statement_id}/cancel"
                    cancel_resp = requests.post(url, headers=header)
                    if cancel_resp.status_code != 200:
                        LOGGER.bind(
                            query_id=query_id,
                            status_code=cancel_resp.status_code,
                            text=cancel_resp.text,
                        ).error("Failed to cancel query")
                    LOGGER.info(f"Query canceled with id {query_id}.")
                    return
            LOGGER.bind(query_id=query_id).warning(
                "Query not found in the cluster statements."
            )
        except Exception as exc:
            LOGGER.opt(exception=exc).error("Failed to cancel query")

    def _parse_query_state_type(self, state_str: str) -> GA.QueryState:
        try:
            # Supported Databricks states ["QUEUED", "RUNNING", "CANCELED", "FAILED", "FINISHED"]
            return GA.QueryState[state_str.upper()]
        except KeyError:
            return GA.QueryState.UNKNOWN

    def get_cluster_info_df(self) -> pd.DataFrame:
        response = self._get_cluster_statements()
        results = pd.DataFrame(response.get("res", []))

        if results.empty:
            return results
        results["state"] = results["status"]
        results["query"] = results["query_text"]
        results["statistics"] = results.apply(
            lambda row: {"duration_seconds": row.get("duration", 0) / 1000}, axis=1
        )
        return results.drop(columns=["status", "query_text"])

    def is_cluster_info_supported(self) -> bool:
        return True

    def fix_types_for_filter(
        self, value: M.EVENT_FILTER_BASE_TYPE
    ) -> Union[M.EVENT_FILTER_BASE_TYPE, FieldReference]:
        if isinstance(value, bool):
            return SA.literal_column(str(value))
        return super().fix_types_for_filter(value)

    def _is_array_type_supported(self) -> bool:
        return True

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.unix_timestamp(field_ref) % 2973
        return super().get_field_numeric_value(field_ref, field)

    def sample_events(
        self,
        sa_table: SA.Table,
        ed_table: M.EventDataTable,
        event_sampling: float,
        where_clause: FilterCondition,
    ) -> Tuple[SA.Table, FilterCondition]:
        if event_sampling > 0:
            sa_table = TablePercentSample(
                sa_table, event_sampling * 100, table_sample_str="TABLESAMPLE"
            )
        return sa_table, where_clause
