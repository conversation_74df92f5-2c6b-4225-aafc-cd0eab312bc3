from __future__ import annotations

from typing import Any, Dict, List, <PERSON><PERSON>

import sqlalchemy as SA

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import SelectType
from mitzu.adapters.query_builder.query_builder import (
    QueryBuilder,
)


class SingleDimensionQueryBuilder(QueryBuilder):
    def get_query(
        self, dim_id: Any, dim_field_defs: Dict[str, M.DimensionFieldDef]
    ) -> SelectType:
        table_column_map: Dict[
            M.DimensionDataTable, List[Tuple[str, M.DimensionFieldDef]]
        ] = {}
        for label, dfd in dim_field_defs.items():
            table = dfd.dimension_data_table
            if table not in table_column_map:
                table_column_map[table] = []
            table_column_map[table].append((label, dfd))

        combined_select_cte = SAH.select(
            columns=[SA.literal(dim_id).label(GA.FINAL_DIM_PRIMARY_KEY_COL)]
        ).cte()
        joins: List[SAH.Join] = []
        join_columns = list(combined_select_cte.columns)
        dim_id_col_first_cte = combined_select_cte.columns[GA.FINAL_DIM_PRIMARY_KEY_COL]
        for dim_table, fields in table_column_map.items():
            sa_table = self.adapter.get_table_by_name(
                dim_table.schema, dim_table.table_name, auto_load=False
            )
            dim_id_col = self.adapter.get_field_reference(
                dim_table.primary_key, sa_table
            ).label(GA.FINAL_DIM_PRIMARY_KEY_COL)
            columns = [dim_id_col] + [
                self.adapter.get_field_reference(field_def.field, sa_table).label(label)
                for label, field_def in fields
            ]
            select_cte = SAH.select(
                columns=columns,
                select_from=sa_table,
                whereclause=(dim_id_col == dim_id),
            ).cte()
            join_columns.extend(select_cte.columns)
            cte_dim_id_col = select_cte.columns[GA.FINAL_DIM_PRIMARY_KEY_COL]
            joins.append(
                SAH.Join(
                    select_cte, dim_id_col_first_cte == cte_dim_id_col, isouter=True
                )
            )

        all_columns = []
        counter = 0

        for column in join_columns:
            if column.name == GA.FINAL_DIM_PRIMARY_KEY_COL:
                all_columns.append(column.label(f"{column.name}_{counter}"))
                counter += 1
            else:
                all_columns.append(column.label(column.name))

        return SAH.select(all_columns, joins=joins)
