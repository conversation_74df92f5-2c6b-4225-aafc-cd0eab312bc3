from __future__ import annotations

from typing import Dict, List, Optional, Set, Tuple, Union

import sqlalchemy as SA
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import SelectType
from mitzu.adapters.query_builder.query_builder import (
    QueryBuilder,
)


class SearchDimensionsQueryBuilder(QueryBuilder):
    def get_dimension_ctes(
        self,
        dimension_search_query: M.DimensionsSearchQuery,
        select_df_map: Dict[M.DimensionDataTable, List[Tuple[str, M.Field]]],
    ) -> <PERSON>ple[Set[str], List[Tuple[str, SEL.CTE]]]:
        res: List[Tuple[str, SEL.CTE]] = []
        required_table_ids: Set[str] = set()
        for dim_table, fields in select_df_map.items():
            sa_table = self.adapter.get_table_by_name(
                dim_table.schema, dim_table.table_name, auto_load=False
            )

            where_conditions = []
            for dim_filter in dimension_search_query.filters:
                if not isinstance(dim_filter.left, M.DimensionFieldDef):
                    raise ValueError(
                        f"Only dimension field def is allowed in search dimension query, {dim_filter.left}"
                    )

                if not dim_filter.left.dimension_data_table.id == dim_table.id:
                    continue
                required_table_ids.add(dim_table.id)

                where_conditions.append(
                    self.get_simple_segment_condition(
                        sa_table,
                        dim_filter,
                        collection_cte_value_cols={},  # cohorts are not supported in lookup
                    )
                )

            dim_id_col = self.adapter.get_field_reference(
                dim_table.primary_key, sa_table
            ).label(GA.FINAL_DIM_PRIMARY_KEY_COL)

            columns = [dim_id_col] + [
                self.adapter.get_field_reference_for_dimension_search(
                    field, sa_table
                ).label(label)
                for label, field in fields
            ]
            select_cte = SAH.select(
                columns=columns,
                select_from=sa_table,
                whereclause=self.adapter.combine_filters(where_conditions)
                if len(where_conditions) > 0
                else None,
            ).cte()
            res.append((dim_table.id, select_cte))
        return required_table_ids, res

    def get_query(self, dimension_search_query: M.DimensionsSearchQuery) -> SelectType:
        select_df_map = self._map_dimension_table_columns(
            {
                self.adapter.normalize_field_path(v.field_path): v
                for v in dimension_search_query.select_field_defs
            }
        )

        combined_select_cte: Union[None, SEL.CTE, SEL.Join] = None
        dim_id_col_first_cte = None
        required_table_ids, cte_tables = self.get_dimension_ctes(
            dimension_search_query, select_df_map
        )
        table_id_filters = []
        for table_id, dim_cte in cte_tables:
            cte_dim_id_col = dim_cte.columns[GA.FINAL_DIM_PRIMARY_KEY_COL]
            if table_id in required_table_ids:
                table_id_filters.append(cte_dim_id_col.is_not(None))
            if combined_select_cte is None:
                combined_select_cte = dim_cte
                dim_id_col_first_cte = cte_dim_id_col
            else:
                combined_select_cte = combined_select_cte.join(
                    dim_cte,
                    dim_id_col_first_cte == cte_dim_id_col,
                    isouter=True,
                    full=True,
                )

        all_columns = []
        counter = 0

        if combined_select_cte is None:
            raise ValueError("Invalid state, combined_select_cte is None")

        for column in combined_select_cte.columns:
            if column.name == GA.FINAL_DIM_PRIMARY_KEY_COL:
                all_columns.append(column.label(f"{column.name}_{counter}"))
                counter += 1
            else:
                all_columns.append(column.label(column.name))

        search_filter = self._create_search_dimension_where_clause(
            combined_select_cte,
            dimension_search_query.search_tokens,
            dimension_search_query.search_field_defs,
        )
        combined_select = SAH.select(
            columns=all_columns,
            whereclause=self.adapter.combine_filters(
                table_id_filters + [search_filter]
            ),
            select_from=combined_select_cte,
        )
        return combined_select

    def get_cohort_query(
        self, dimension_search_query: M.DimensionsSearchQuery
    ) -> SA_EXP.Select:

        select_df_map: Dict[M.DimensionDataTable, List[Tuple[str, M.Field]]] = {}
        for field_def in dimension_search_query.select_field_defs:
            dim_table = field_def.dimension_data_table
            if dim_table not in select_df_map.keys():
                select_df_map[dim_table] = []

            if len(dimension_search_query.search_tokens) > 0:
                select_df_map[dim_table].append(
                    (
                        self.adapter.normalize_field_path(field_def.field_path),
                        field_def.field,
                    )
                )

        combined_select_cte: Union[None, SEL.CTE, SEL.Join] = None
        dim_id_col_first_cte = None
        required_table_ids, cte_tables = self.get_dimension_ctes(
            dimension_search_query, select_df_map
        )
        table_id_filters = []
        for table_id, dim_cte in cte_tables:
            cte_dim_id_col = dim_cte.columns[GA.FINAL_DIM_PRIMARY_KEY_COL]
            if table_id in required_table_ids:
                table_id_filters.append(cte_dim_id_col.is_not(None))
            if combined_select_cte is None:
                combined_select_cte = dim_cte
                dim_id_col_first_cte = cte_dim_id_col
            else:
                combined_select_cte = combined_select_cte.join(
                    dim_cte,
                    dim_id_col_first_cte == cte_dim_id_col,
                    isouter=True,
                    full=True,
                )

        all_columns: List = []

        if combined_select_cte is None:
            raise ValueError("Invalid state, combined_select_cte is None")

        for column in combined_select_cte.columns:
            if column.name == GA.FINAL_DIM_PRIMARY_KEY_COL:
                all_columns.append(column.label(GA.CTE_USER_ID_ALIAS_COL))
                break

        search_filter = self._create_search_dimension_where_clause(
            combined_select_cte,
            dimension_search_query.search_tokens,
            dimension_search_query.search_field_defs,
        )
        combined_select = SAH.select(
            columns=all_columns,
            whereclause=self.adapter.combine_filters(
                table_id_filters + [search_filter]
            ),
            select_from=combined_select_cte,
        )
        return combined_select

    def _map_dimension_table_columns(
        self, dim_field_defs: Dict[str, M.DimensionFieldDef]
    ) -> Dict[M.DimensionDataTable, List[Tuple[str, M.Field]]]:
        table_column_map: Dict[M.DimensionDataTable, List[Tuple[str, M.Field]]] = {}
        for label, dfd in dim_field_defs.items():
            table = dfd.dimension_data_table
            if table not in table_column_map:
                table_column_map[table] = []
            table_column_map[table].append((label, dfd.field))
        return table_column_map

    def _create_search_dimension_where_clause(
        self,
        cte: Union[SEL.CTE, SEL.Join],
        tokens: List[str],
        search_field_defs: List[M.DimensionFieldDef],
    ) -> Union[None, ELM.ColumnElement, ELM.BooleanClauseList]:
        def find_sfd_in_cols(
            sfd: M.DimensionFieldDef,
        ) -> Optional[SA_EXP.ColumnElement]:
            cols = [
                c
                for c in cte.columns
                if self.adapter.normalize_field_path(sfd.field_path) == c.name
            ]
            if len(cols) == 1:
                return cols[0]
            return None

        and_clauses: Union[None, ELM.ColumnElement, ELM.BooleanClauseList] = None
        for token in tokens:
            or_clauses: Union[None, ELM.ColumnElement, ELM.BooleanClauseList] = None
            for sfd in search_field_defs:
                col = find_sfd_in_cols(sfd)
                if col is None:
                    continue
                clause = self._create_search_dimension_field_def_token_clause(
                    sfd, token, col, inclusive=True
                )
                if clause is not None:
                    if or_clauses is None:
                        or_clauses = clause
                    else:
                        or_clauses = SA.or_(or_clauses, clause)

            if or_clauses is None:
                continue

            if and_clauses is None:
                and_clauses = or_clauses
            else:
                and_clauses = SA.and_(or_clauses, and_clauses)

        return and_clauses

    def _create_search_dimension_field_def_token_clause(
        self,
        sfd: M.DimensionFieldDef,
        token: str,
        cte_column: SA_EXP.ColumnElement,
        inclusive: bool,
    ) -> Union[None, ELM.ColumnElement, ELM.BooleanClauseList]:
        clause: Union[None, ELM.ColumnElement, ELM.BooleanClauseList] = None
        token = token.lower()
        if sfd.field.type == M.DataType.STRING:
            clause = SA.func.lower(cte_column).like(f"%{token}%")
            if not inclusive:
                clause = SA.not_(clause)
        return clause
