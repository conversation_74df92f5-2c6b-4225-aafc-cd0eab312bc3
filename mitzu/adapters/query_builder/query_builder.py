from __future__ import annotations

from dataclasses import replace
from datetime import datetime, timedelta
from typing import (
    TYPE_CHECKING,
    Callable,
    Dict,
    List,
    Optional,
    Tuple,
    Union,
    cast,
)

import sqlalchemy as SA
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import (
    DimensionTableRefs,
    FieldReference,
    FilterCondition,
    MissingDimensionForeignKeyError,
    MultiTableSegment,
    SegmentContext,
    SingleTableSegment,
    TableSegment,
)
from mitzu.constants import N_PER_A
from mitzu.logger import LOGGER

if TYPE_CHECKING:
    from mitzu.adapters.sqlalchemy_adapter import (
        SQLAlchemyAdapter,
    )


class QueryBuilder:
    def __init__(self, adapter: SQLAlchemyAdapter):
        self.adapter = adapter

    def _get_referenced_dimension_tables(
        self, metric: M.Metric
    ) -> Dict[str, DimensionTableRefs]:
        dimension_tables: Dict[str, DimensionTableRefs] = {}

        def add_table_if_not_exists(dim_table: M.DimensionDataTable) -> None:
            if dim_table.table_name not in dimension_tables:
                dimension_tables[dim_table.table_name] = DimensionTableRefs(
                    sa_table=self.adapter.get_table(dim_table, auto_load=False),
                    dimension_data_table=dim_table,
                )

        def add_table_from_aggregation(agg: Optional[M.AggregationConfig]) -> None:
            if (
                agg is not None
                and agg.type == M.AggType.BY_EVENT_PROPERTY
                and agg.event_property_agg is not None
            ):
                agg_field = agg.event_property_agg.field_def
                if isinstance(agg_field, M.DimensionFieldDef):
                    add_table_if_not_exists(agg_field.dimension_data_table)

        for segment in metric.segments:
            field_defs = [
                event_filter.left for event_filter in segment.get_event_filters()
            ] + segment.group_by
            for field_def in field_defs:
                if isinstance(field_def, M.DimensionFieldDef):
                    add_table_if_not_exists(field_def.dimension_data_table)

            add_table_from_aggregation(segment.aggregation)

        add_table_from_aggregation(metric.config.aggregation)

        if isinstance(metric.config.unique_field, M.DimensionFieldDef):
            add_table_if_not_exists(metric.config.unique_field.dimension_data_table)

        return dimension_tables

    def _get_collection_cte_value_cols(
        self, metric: M.Metric
    ) -> Dict[str, FieldReference]:
        collection_value_cols = {}
        for segment in metric.segments:
            for filter in segment.get_event_filters():
                if not isinstance(filter.right, M.Collection) or isinstance(
                    filter.right, M.StaticCollection
                ):
                    continue

                collection = filter.right
                if isinstance(collection, M.DynamicCollection):

                    if collection.metric.type == M.MetricType.SEGMENTATION:
                        metric_query = self.adapter.get_segmentation_select(
                            collection.metric
                        )
                    elif collection.metric.type in [
                        M.MetricType.CONVERSION,
                        M.MetricType.JOURNEY,
                    ]:
                        metric_query = self.adapter.get_conversion_select(
                            collection.metric, cte_prefix=f"collection_{collection.id}"
                        )
                    elif collection.metric.type == M.MetricType.RETENTION:
                        metric_query = self.adapter.get_retention_select(
                            collection.metric, cte_prefix=f"collection_{collection.id}"
                        )
                    else:
                        raise ValueError(
                            f"Unknown metric type: {collection.metric.type}"
                        )

                    metric_cte = metric_query.cte(name=f"collection_{collection.id}")
                    agg_value_col = AH.select_agg_value_column_for_cohort(
                        collection.metric.type, M.CohortType.DYNAMIC
                    )
                    collection_value_cols[collection.id] = metric_cte.columns[
                        agg_value_col
                    ]
                elif isinstance(collection, M.DimensionCollection):
                    metric_query = (
                        self.adapter.get_search_dimension_as_collection_select(
                            collection.search_query
                        )
                    )
                    metric_cte = metric_query.cte(name=f"collection_{collection.id}")
                    agg_values_col = metric_cte.columns[GA.CTE_USER_ID_ALIAS_COL]
                    collection_value_cols[collection.id] = agg_values_col
                else:
                    raise ValueError(f"Unknown collection type: {collection}")

        return collection_value_cols

    def _get_segment_time_window_for_metric_step(
        self,
        metric: M.Metric,
        step: int,
    ) -> tuple[datetime, datetime]:
        start_date = metric.calculate_start_dt(self.adapter.end_date)

        if metric.config.end_dt is None:
            end_date = metric.calculate_end_dt(self.adapter.end_date)
        else:
            # if a metric time window is defined it's hard to calculate the ret indexes
            # this is the same how amplitude works now
            if metric.type == M.MetricType.RETENTION and step > 0:
                end_date = self.adapter.end_date
            else:
                # if the time window is a custom date we need to make sure
                # it ends on the given date, not starts
                end_date = metric.calculate_end_dt(self.adapter.end_date) + timedelta(
                    days=1
                )

        if metric.type in (M.MetricType.CONVERSION, M.MetricType.JOURNEY):
            if not isinstance(metric.config, M.ConversionRetentionMetricConfig):
                raise ValueError(
                    "Conversion query generation require ConversionRetentionMetricConfig"
                )

            if (
                isinstance(metric.config, M.ConversionMetricConfig)
                and metric.config.conv_orientation == M.ConversionOrientation.REVERSE
            ):
                multiplier = len(metric.segments) - step
                if step < len(metric.segments) - 1 and isinstance(
                    metric.config, M.ConversionRetentionMetricConfig
                ):
                    if (
                        isinstance(metric.config, M.ConversionMetricConfig)
                        and metric.config.conv_window_type
                        == M.ConversionWindowType.BETWEEN_EACH_STEP
                    ):
                        start_date = start_date - (
                            metric.config.time_window.to_relative_delta() * multiplier
                        )
                    else:
                        start_date = (
                            start_date - metric.config.time_window.to_relative_delta()
                        )
            else:
                if step > 0 and isinstance(
                    metric.config, M.ConversionRetentionMetricConfig
                ):
                    if (
                        isinstance(metric.config, M.ConversionMetricConfig)
                        and metric.config.conv_window_type
                        == M.ConversionWindowType.BETWEEN_EACH_STEP
                    ):
                        end_date = end_date + (
                            metric.config.time_window.to_relative_delta() * step
                        )
                    else:
                        end_date = (
                            end_date + metric.config.time_window.to_relative_delta()
                        )

        if metric.config.resolution == M.Resolution.ONE_USER_EVENT_PER_DAY:
            start_date = start_date.replace(hour=0, minute=0, second=0)
        elif metric.config.resolution == M.Resolution.ONE_USER_EVENT_PER_HOUR:
            start_date = start_date.replace(minute=0, second=0)
        elif metric.config.resolution == M.Resolution.ONE_USER_EVENT_PER_MINUTE:
            start_date = start_date.replace(second=0)

        start_date = start_date.replace(microsecond=0)
        end_date = end_date.replace(microsecond=0)

        return start_date, end_date

    def __get_segment_context(
        self,
        root_segment: M.Segment,
        metric: M.Metric,
        segment_index: int,
        dimension_tables: Dict[str, DimensionTableRefs],
    ) -> SegmentContext:
        start_date, end_date = self._get_segment_time_window_for_metric_step(
            metric, segment_index
        )
        break_down_by_subsegments = False
        revenue_settings = self.adapter.revenue_settings
        for field in root_segment.group_by:
            if field == M.SubsegmentFieldDef() or (
                revenue_settings is not None
                and isinstance(field.field_path, M.EventFieldPath)
                and field.field_path.event_name_path == revenue_settings.revenue_event
            ):
                break_down_by_subsegments = True
                break

        aggregation = (
            metric.config.aggregation
            if metric.type != M.MetricType.SEGMENTATION
            else root_segment.aggregation
        )

        if aggregation is None:
            raise ValueError("aggregation is not set")

        if (
            aggregation.type == M.AggType.BY_EVENT_PROPERTY
            and aggregation.event_property_agg is not None
            and isinstance(
                aggregation.event_property_agg.field_def.field_path, M.EventFieldPath
            )
            and revenue_settings is not None
            and aggregation.event_property_agg.field_def.field_path.event_name_path
            == revenue_settings.revenue_event
        ):
            break_down_by_subsegments = True

        dimension_tables_to_join: List[M.DimensionDataTable] = []
        for group_by_field in root_segment.group_by:
            if isinstance(group_by_field, M.DimensionFieldDef):
                dimension_tables_to_join.append(group_by_field.dimension_data_table)

        if isinstance(metric.config.unique_field, M.DimensionFieldDef):
            dimension_tables_to_join.append(
                metric.config.unique_field.dimension_data_table
            )

        return SegmentContext(
            start_date=start_date,
            end_date=end_date,
            is_break_down_by_subsegments=break_down_by_subsegments,
            dimension_tables={
                dim_table.table_name: dimension_tables[dim_table.table_name].sa_table
                for dim_table in dimension_tables_to_join
            },
        )

    def _get_segment_sub_query(
        self,
        segment: M.Segment,
        metric: M.Metric,
        segment_index: int,
        dimension_tables: Dict[str, DimensionTableRefs],
        collection_cte_value_cols: Dict[str, FieldReference],
        root_segment: Optional[M.Segment],
        custom_event_key: Optional[str],
    ) -> TableSegment:
        where_clauses: List[Optional[ELM.ColumnElement]] = []

        if root_segment is None:
            root_segment = segment

        segment_context = self.__get_segment_context(
            root_segment=root_segment,
            metric=metric,
            segment_index=segment_index,
            dimension_tables=dimension_tables,
        )

        dimension_tables_to_join: Dict[str, SA.Table] = segment_context.dimension_tables

        def add_dimension_table(dim_table: M.DimensionDataTable) -> SA.Table:
            dim_table_name = dim_table.table_name
            dimension_tables_to_join[dim_table_name] = dimension_tables[
                dim_table_name
            ].sa_table
            return dimension_tables_to_join[dim_table_name]

        # segment_agg can be None when it's a segmentation metric then the
        # aggregation is not set in the SimpleSegments of a ComplexSegment
        segment_agg = None
        if metric.type == M.MetricType.SEGMENTATION:
            segment_agg = root_segment.aggregation
        elif metric.type in [
            M.MetricType.CONVERSION,
            M.MetricType.RETENTION,
            M.MetricType.JOURNEY,
        ]:
            segment_agg = metric.config.aggregation
        else:
            raise ValueError(f"Unexpected metric type: {metric.type}")

        add_property_agg_filter = metric.type == M.MetricType.SEGMENTATION or (
            metric.type
            in [M.MetricType.CONVERSION, M.MetricType.RETENTION, M.MetricType.JOURNEY]
            and (segment_index == len(metric.segments) - 1)
        )

        if add_property_agg_filter:
            where_clauses.append(
                self._get_dimension_field_agg_where_clause(
                    segment_agg, add_dimension_table
                )
            )

        if isinstance(segment, M.SimpleSegment):
            s = cast(M.SimpleSegment, segment)
            edt = s.event.event_data_table
            sa_table = self.adapter.get_table(edt, auto_load=False)
            revenue_settings = self.adapter.revenue_settings
            if (
                revenue_settings is None
                or s.event.event_name_path != revenue_settings.revenue_event
                or segment_agg is None
                or segment_agg.type
                not in [
                    M.AggType.TOTAL_MRR,
                    M.AggType.COUNT_SUBSCRIBERS,
                    M.AggType.RETURN_SUBSCRIBERS,
                ]
            ):
                evt_name_col = self.adapter.get_event_name_field(edt, sa_table)

                if add_property_agg_filter:
                    where_clauses.append(
                        self._get_event_field_agg_where_clause(
                            event=s.event,
                            aggregation=segment_agg,
                            sa_table=sa_table,
                        )
                    )

                where_clauses.append(
                    (evt_name_col == s.event.event_name)
                    if s.event.event_name != M.ANY_EVENT_NAME
                    else None
                )
                if root_segment.filter not in [
                    M.SegmentFilter.FIRST_EVENT_EVER,
                    M.SegmentFilter.LAST_EVENT_EVER,
                ] and (
                    segment_agg is None
                    or (
                        segment_agg.type
                        not in [
                            M.AggType.TOTAL_MRR,
                            M.AggType.RETURN_SUBSCRIBERS,
                            M.AggType.COUNT_SUBSCRIBERS,
                        ]
                    )
                ):
                    # If we are looking for first ever filter, we shouldn't filter on date partition
                    # We should look at all data in that case
                    # Same for Last event ever
                    if edt.date_partition_field is not None:
                        where_clauses.append(
                            self.adapter.get_date_partition_filter(
                                edt,
                                sa_table,
                                segment_context.start_date,
                                segment_context.end_date,
                                metric.config.time_group,
                            )
                        )

                if s.event_filter is not None:
                    if isinstance(s.event_filter.left, M.EventFieldDef):
                        where_clauses.append(
                            self.get_simple_segment_condition(
                                sa_table,
                                segment.event_filter,
                                collection_cte_value_cols,
                            )
                        )
                    elif isinstance(s.event_filter.left, M.DimensionFieldDef):
                        dim_sa_table = add_dimension_table(
                            s.event_filter.left.dimension_data_table
                        )
                        where_clauses.append(
                            self.get_simple_segment_condition(
                                dim_sa_table,
                                segment.event_filter,
                                collection_cte_value_cols,
                            )
                        )
                    else:
                        raise ValueError(f"Unknown field def {s.event_filter.left}")

                return SingleTableSegment(
                    context=segment_context,
                    table_ref=sa_table,
                    where_clause=self.adapter.combine_filters(where_clauses),
                    dimension_tables=dimension_tables_to_join,
                    event_data_table=s.event.event_data_table,
                    events=[s.event],
                    extra_columns={},
                    segment_index=segment_index,
                    subsegment_index=0,  # will be updated later
                    custom_event_key=custom_event_key,
                )
            else:
                if revenue_settings is None:
                    raise ValueError("Revenue is not configured")

                amount_field = s.event.get_field_def(
                    revenue_settings.amount_field.field_name
                ).field
                period_start_field = s.event.get_field_def(
                    revenue_settings.period_start_field.field_name
                ).field
                period_end_field = s.event.get_field_def(
                    revenue_settings.period_end_field.field_name
                ).field

                # If we are looking for first ever filter, we shouldn't filter on date partition
                # We should look at all data in that case
                # Same for Last event ever
                if edt.date_partition_field is not None and (
                    segment_agg is None
                    or (
                        segment_agg.type
                        not in [
                            M.AggType.TOTAL_MRR,
                            M.AggType.RETURN_SUBSCRIBERS,
                            M.AggType.COUNT_SUBSCRIBERS,
                        ]
                    )
                ):
                    where_clauses.append(
                        self.adapter.get_date_partition_filter(
                            edt,
                            sa_table,
                            segment_context.start_date,
                            segment_context.end_date,
                            metric.config.time_group,
                        )
                    )

                if s.event_filter is not None:
                    if isinstance(s.event_filter.left, M.EventFieldDef):
                        where_clauses.append(
                            self.get_simple_segment_condition(
                                sa_table,
                                segment.event_filter,
                                collection_cte_value_cols,
                            )
                        )
                    elif isinstance(s.event_filter.left, M.DimensionFieldDef):
                        dim_sa_table = add_dimension_table(
                            s.event_filter.left.dimension_data_table
                        )
                        where_clauses.append(
                            self.get_simple_segment_condition(
                                dim_sa_table,
                                segment.event_filter,
                                collection_cte_value_cols,
                            )
                        )
                    else:
                        raise ValueError(f"Unknown field def {s.event_filter.left}")

                extra_columns = {
                    GA.CTE_REV_PERIOD_START: self.adapter.get_field_reference(
                        period_start_field, sa_table
                    ),
                    GA.CTE_REV_PERIOD_END: self.adapter.get_field_reference(
                        period_end_field, sa_table
                    ),
                }

                if segment_agg is not None and segment_agg.type == M.AggType.TOTAL_MRR:
                    extra_columns[GA.CTE_REV_AMOUNT] = (
                        self.adapter.get_field_reference(amount_field, sa_table)
                        * revenue_settings.multiplier
                    )

                return SingleTableSegment(
                    context=segment_context,
                    table_ref=sa_table,
                    where_clause=self.adapter.combine_filters(where_clauses),
                    dimension_tables=dimension_tables_to_join,
                    event_data_table=s.event.event_data_table,
                    events=[s.event],
                    extra_columns=extra_columns,
                    segment_index=segment_index,
                    subsegment_index=0,  # will be updated later
                    custom_event_key=custom_event_key,
                )
        elif isinstance(segment, M.ComplexSegment):
            c = cast(M.ComplexSegment, segment)
            l_query = self._get_segment_sub_query(
                c.left,
                metric,
                segment_index,
                dimension_tables=dimension_tables,
                collection_cte_value_cols=collection_cte_value_cols,
                root_segment=root_segment,
                custom_event_key=segment.expanded_custom_event_key,
            )
            r_query = self._get_segment_sub_query(
                c.right,
                metric,
                segment_index,
                dimension_tables=dimension_tables,
                collection_cte_value_cols=collection_cte_value_cols,
                root_segment=root_segment,
                custom_event_key=segment.expanded_custom_event_key,
            )
            # sub segments are not aware of group bys and the ddts needed for it
            l_query.dimension_tables.update(dimension_tables_to_join)
            r_query.dimension_tables.update(dimension_tables_to_join)
            if c.operator == M.BinaryOperator.AND:
                if not isinstance(l_query, SingleTableSegment) or not isinstance(
                    r_query, SingleTableSegment
                ):
                    raise Exception(
                        "And (&) operator can only be between the same events (e.g. page_view & page_view)"
                    )
                return SingleTableSegment(
                    context=segment_context,
                    table_ref=l_query.table_ref,
                    event_data_table=l_query.event_data_table,
                    where_clause=self.adapter.combine_filters(
                        where_clauses + [l_query.where_clause, r_query.where_clause]
                    ),
                    dimension_tables={
                        **l_query.dimension_tables,
                        **r_query.dimension_tables,
                        **dimension_tables_to_join,
                    },
                    events=l_query.events + r_query.events,
                    extra_columns={**l_query.extra_columns, **r_query.extra_columns},
                    segment_index=segment_index,
                    subsegment_index=0,  # will be updated later
                    custom_event_key=segment.expanded_custom_event_key,
                )
            elif (
                isinstance(l_query, SingleTableSegment)
                and isinstance(r_query, SingleTableSegment)
                and l_query.event_data_table == r_query.event_data_table
                and not segment_context.is_break_down_by_subsegments
            ):
                return SingleTableSegment(
                    context=segment_context,
                    table_ref=l_query.table_ref,
                    event_data_table=l_query.event_data_table,
                    where_clause=self.adapter.combine_filters(
                        where_clauses + [(l_query.where_clause | r_query.where_clause)]
                    ),
                    dimension_tables={
                        **l_query.dimension_tables,
                        **r_query.dimension_tables,
                        **dimension_tables_to_join,
                    },
                    events=l_query.events + r_query.events,
                    extra_columns={**l_query.extra_columns, **r_query.extra_columns},
                    segment_index=segment_index,
                    subsegment_index=0,  # will be updated later
                    custom_event_key=segment.expanded_custom_event_key,
                )
            else:
                return MultiTableSegment(
                    context=l_query.context,
                    dimension_tables={
                        **l_query.dimension_tables,
                        **r_query.dimension_tables,
                        **dimension_tables_to_join,
                    },
                    extra_columns={
                        **l_query.extra_columns,
                        **r_query.extra_columns,
                    },
                    events=l_query.events + r_query.events,
                    left=l_query,
                    right=r_query,
                )
        else:
            raise ValueError(f"Segment of type {type(segment)} is not supported.")

    def get_simple_segment_condition(
        self,
        table: SA.Table,
        filter: Optional[M.EventFilter],
        collection_cte_value_cols: Dict[str, FieldReference],
    ) -> FilterCondition:
        if filter is None:
            return SA.literal(True)

        ref = self.adapter.get_field_reference(filter.left.field, table)

        if (
            self.adapter.insight_settings
            and self.adapter.insight_settings.case_insensetive_filtering
            and filter.left.field.type == M.DataType.STRING
        ):
            ref = SA.func.lower(ref)
            if isinstance(filter.right, str):
                filter = replace(filter, right=filter.right.lower())
            elif isinstance(filter.right, list) or isinstance(filter.right, tuple):
                filter = replace(
                    filter,
                    right=[
                        x.lower() if isinstance(x, str) else x for x in filter.right
                    ],
                )

        op = filter.operator
        if op == M.Operator.IS_NULL:
            return ref.is_(None)
        if op == M.Operator.IS_NOT_NULL:
            return ref.is_not(None)
        original_right_value = filter.right
        if original_right_value is None:
            # raise ValueError("Binary operator is set without a right hand value")
            # technically this should be an error but the same model is used by the UI
            # as well where the metrics can be half configured
            return SA.literal(True)
        if isinstance(original_right_value, tuple) or isinstance(
            original_right_value, list
        ):
            right: Union[
                Tuple[Union[M.EVENT_FILTER_BASE_TYPE, FieldReference], ...],
                M.EVENT_FILTER_BASE_TYPE,
                FieldReference,
            ] = tuple(
                self.adapter.fix_types_for_filter(v) for v in original_right_value
            )
        else:
            right = self.adapter.fix_types_for_filter(original_right_value)

        if op == M.Operator.EQ:
            return ref == right
        if op == M.Operator.NEQ:
            return ref != right
        if op == M.Operator.GT:
            return ref > right
        if op == M.Operator.LT:
            return ref < right
        if op == M.Operator.GT_EQ:
            return ref >= right
        if op == M.Operator.LT_EQ:
            return ref <= right
        if op == M.Operator.LIKE:
            if isinstance(right, tuple) and len(right) > 0:
                condition = ref.like(f"%{right[0]}%")
                for value in right[1:]:
                    condition = SA.or_(condition, ref.like(f"%{value}%"))
                return condition
            else:
                return ref.like(f"%{right}%")
        if op == M.Operator.NOT_LIKE:
            if isinstance(right, tuple) and len(right) > 0:
                condition = ref.like(f"%{right[0]}%")
                for value in right[1:]:
                    condition = SA.or_(condition, ref.like(f"%{value}%"))
                return SA.or_(
                    ref.is_(None),
                    SA.not_(condition),
                )
            else:
                return SA.or_(ref.is_(None), SA.not_(ref.like(f"%{right}%")))
        if op == M.Operator.ANY_OF:
            if not isinstance(right, tuple):
                raise ValueError(f"Unexpected value type {right}")
            return SA.literal(True) if len(right) == 0 else ref.in_(right)
        if op == M.Operator.NONE_OF:
            if not isinstance(right, tuple):
                raise ValueError(f"Unexpected value type {right}")
            if len(right) == 0:
                return SA.literal(True)
            return SA.or_(SA.not_(ref.in_(right)), ref.is_(None))
        if op in [M.Operator.IN_COHORT, M.Operator.NOT_IN_COHORT]:
            collection = cast(M.Collection, right)

            if isinstance(collection, M.StaticCollection):
                condition = ref.in_(collection.values)
            elif isinstance(collection, (M.DynamicCollection, M.DimensionCollection)):
                condition = self.adapter.get_collection_inclusion_condition(
                    ref, collection_cte_value_cols[collection.id]
                )
            else:
                raise ValueError(f"Unknown collection type: {collection}")

            if op == M.Operator.IN_COHORT:
                return condition
            else:
                return SA.not_(condition)

        if op == M.Operator.ARRAY_CONTAINS:
            if not isinstance(right, tuple):
                return self.adapter._get_array_contains_condition(ref, right)
            if len(right) == 0:
                return SA.literal(True)
            cond = cast(
                SA_EXP.ColumnElement[SA.Boolean],
                self.adapter._get_array_contains_condition(ref, right[0]),
            )
            for val in right[1:]:
                cond = SA.or_(
                    cond, self.adapter._get_array_contains_condition(ref, val)
                )
            return cond
        if op == M.Operator.ARRAY_NOT_CONTAINS:
            if not isinstance(right, tuple):
                return SA.not_(self.adapter._get_array_contains_condition(ref, right))
            if len(right) == 0:
                return SA.literal(True)
            cond = cast(
                SA_EXP.ColumnElement[SA.Boolean],
                self.adapter._get_array_contains_condition(ref, right[0]),
            )
            for val in right[1:]:
                cond = SA.or_(
                    cond, self.adapter._get_array_contains_condition(ref, val)
                )
            return SA.not_(cond)
        if op == M.Operator.ARRAY_LIKE:
            if not isinstance(right, tuple):
                return self.adapter._get_array_like_condition(ref, right)
            if len(right) == 0:
                return SA.literal(True)
            cond = cast(
                SA_EXP.ColumnElement[SA.Boolean],
                self.adapter._get_array_like_condition(ref, right[0]),
            )
            for val in right[1:]:
                cond = SA.or_(cond, self.adapter._get_array_like_condition(ref, val))
            return cond
        if op == M.Operator.ARRAY_NOT_LIKE:
            if not isinstance(right, tuple):
                return SA.not_(self.adapter._get_array_like_condition(ref, right))
            if len(right) == 0:
                return SA.literal(True)
            cond = cast(
                SA_EXP.ColumnElement[SA.Boolean],
                self.adapter._get_array_like_condition(ref, right[0]),
            )
            for val in right[1:]:
                cond = SA.or_(cond, self.adapter._get_array_like_condition(ref, val))
            return SA.not_(cond)
        raise ValueError(f"Operator {op} is not supported by SQLAlchemy Adapter.")

    def _get_dimension_field_agg_where_clause(
        self,
        aggregation: Optional[M.AggregationConfig],
        add_dimension_table: Callable,
    ) -> Optional[ELM.ColumnElement]:
        if (
            aggregation is None
            or aggregation.type != M.AggType.BY_EVENT_PROPERTY
            or aggregation.event_property_agg is None
            or aggregation.event_property_agg.field_def is None
        ):
            return None

        agg_field = aggregation.event_property_agg.field_def
        if not isinstance(agg_field, M.DimensionFieldDef):
            return None

        dim_table = agg_field.dimension_data_table
        sa_table = add_dimension_table(dim_table)

        return self.adapter.get_field_reference(agg_field.field, sa_table).is_not(None)

    def _get_event_field_agg_where_clause(
        self,
        event: M.EventDef,
        aggregation: Optional[M.AggregationConfig],
        sa_table: SA.Table,
    ) -> Optional[ELM.ColumnElement]:
        if (
            aggregation is None
            or aggregation.type != M.AggType.BY_EVENT_PROPERTY
            or aggregation.event_property_agg is None
            or aggregation.event_property_agg.field_def is None
        ):
            return None

        agg_field = aggregation.event_property_agg.field_def
        if not isinstance(agg_field, M.EventFieldDef):
            return None

        for f in event.fields:
            if f.field._get_name() == agg_field.field._get_name():
                return self.adapter.get_field_reference(
                    agg_field.field, sa_table
                ).is_not(None)

        # filter out all records because the event property field is not present
        return SA.literal(False)

    def _get_single_table_segment_subquery_select(
        self,
        metric_config: M.MetricConfig,
        metric_type: M.MetricType,
        sub_query: SingleTableSegment,
        group_field: List[M.FieldDef],
        property_agg_field: Optional[M.FieldDef],
        dimension_tables: Dict[str, DimensionTableRefs],
        segment_identifier: Optional[int],
        is_root_segment_multi_table_segment: bool,
    ) -> SEL.Select:
        unique_field = metric_config.unique_field
        event_sampling = metric_config.event_sampling
        holding_constants = (
            metric_config.custom_holding_constants
            if isinstance(metric_config, M.ConversionRetentionMetricConfig)
            else []
        )
        resolution = metric_config.resolution

        sa_table = sub_query.table_ref
        ed_table = sub_query.event_data_table
        where_clause = sub_query.where_clause

        if event_sampling:
            sa_table, where_clause = self.adapter.sample_events(
                sa_table, ed_table, event_sampling, where_clause
            )

        group_by_col: FieldReference = self.adapter.default_group_by_col(metric_type)
        if len(group_field) > 1:
            # never remains None
            group_by_col = None  # type: ignore[assignment]
            for gb in group_field:
                part = SA.func.coalesce(
                    self.adapter.group_by_col_cast(
                        self.adapter.get_field_reference_or_null(gb, sub_query)
                    ),
                    N_PER_A,
                )
                group_by_col = (
                    self.adapter.concatenate_string_fields(group_by_col, part)
                    if group_by_col is not None
                    else part
                )
        elif len(group_field) == 1:
            group_by_col = SA.func.coalesce(
                self.adapter.group_by_col_cast(
                    self.adapter.get_field_reference_or_null(group_field[0], sub_query)
                ),
                N_PER_A,
            )

        event_property_agg_col: FieldReference = self.adapter.missing_string_value()
        if property_agg_field:
            event_property_agg_col = self.adapter.get_field_reference_or_null(
                property_agg_field, sub_query
            )

            if (
                is_root_segment_multi_table_segment
                and self.adapter.should_cast_cols_in_union()
                and event_property_agg_col != self.adapter.missing_string_value()
            ):
                event_property_agg_col = SA.cast(event_property_agg_col, SA.String)

        if (
            self.adapter.revenue_settings is None
            or self.adapter.revenue_settings.revenue_event not in sub_query.events
        ):

            datetime_col = self.adapter.get_event_time_reference(ed_table, sa_table)

            if isinstance(unique_field, M.FieldDef):
                event_unique_id_col = self.adapter.get_field_reference_or_null(
                    unique_field, sub_query
                )
            else:
                fkey = unique_field
                fkey_field = ed_table.foreign_keys.get(fkey)
                if fkey_field is None:
                    LOGGER.bind(
                        event_table_name=ed_table.get_full_name(),
                        foreign_key=fkey.name,
                    ).warning("missing dimension foreign key")
                    raise MissingDimensionForeignKeyError(ed_table.get_full_name())

                event_unique_id_col = self.adapter.get_field_reference(
                    fkey_field, sa_table
                )
        else:
            event = sub_query.events[0]
            revenue_settings = self.adapter.revenue_settings
            if revenue_settings is None:
                raise ValueError("Revenue is not configured")

            period_start_field = event.get_field_def(
                revenue_settings.period_start_field.field_name
            ).field
            datetime_col = self.adapter.get_field_reference(
                period_start_field, sub_query.table_ref
            )
            event_unique_id_col = self.adapter.get_field_reference(
                ed_table.user_id_field, sub_query.table_ref
            )

        columns = [
            event_unique_id_col.label(GA.CTE_USER_ID_ALIAS_COL),
            datetime_col.label(GA.CTE_DATETIME_COL),
            group_by_col.label(GA.CTE_GROUP_COL),
            event_property_agg_col.label(GA.CTE_EVENT_PROPERTY_COL),
        ]
        columns += [
            field_ref.label(key) for key, field_ref in sub_query.extra_columns.items()
        ]
        if segment_identifier is not None:
            columns.append(
                SA.literal(segment_identifier).label(GA.CTE_SEGMENT_IDENTIFIER_COL)
            )

        if len(holding_constants) > 0:
            holding_constants_col = self.adapter.get_field_reference(
                holding_constants[0].field, sa_table
            )

            if len(holding_constants) > 1:
                holding_constants_col = SA.cast(
                    holding_constants_col, self.adapter._get_text_type()
                )
                for col in holding_constants[1:]:
                    col_field = SA.cast(
                        self.adapter.get_field_reference(col.field, sa_table),
                        self.adapter._get_text_type(),
                    )
                    holding_constants_col = (
                        holding_constants_col + SA.literal(", ") + col_field
                    )
            columns.append(holding_constants_col.label(GA.CTE_HOLDING_CONSTANT_COL))

        if resolution != M.Resolution.EVERY_EVENT and ed_table.date_partition_field:
            partition_col = self.adapter.get_field_reference(
                ed_table.date_partition_field, sa_table
            )
            columns.append(partition_col.label(GA.CTE_PARTITION_COL))

        select = SAH.select(
            columns=columns, whereclause=where_clause, select_from=sa_table
        )

        foreign_keys = ed_table.foreign_keys
        table_full_name = ed_table.get_full_name()

        for join_table_name in sub_query.dimension_tables.keys():
            dim_table = dimension_tables[join_table_name]
            dim_primary_key_col = self.adapter.get_field_reference(
                dim_table.dimension_data_table.primary_key,
                dim_table.sa_table,
            )
            foreign_key_field = foreign_keys.get(dim_table.dimension_data_table.entity)
            if foreign_key_field is None:
                LOGGER.bind(
                    event_table_name=table_full_name,
                    foreign_key=dim_table.dimension_data_table.entity.name,
                ).warning("missing dimension foreign key")
                raise MissingDimensionForeignKeyError(
                    event_table_name=table_full_name,
                )
            edt_foreign_key_col = self.adapter.get_field_reference(
                foreign_key_field,
                sa_table,
            )
            select = select.join(
                dim_table.sa_table,
                dim_primary_key_col == edt_foreign_key_col,
                isouter=True,
            )
        return select

    def _get_segment_sub_query_cte(
        self,
        metric_type: M.MetricType,
        sub_query: TableSegment,
        metric_config: M.MetricConfig,
        group_field: Optional[List[M.FieldDef]],
        property_agg_field: Optional[M.FieldDef],
        segment_filter: Optional[M.SegmentFilter],
        dimension_tables: Dict[str, DimensionTableRefs],
        aggregation: M.AggregationConfig,
        segment_identifier: Optional[int] = None,
    ) -> SEL.CTE:
        time_group = metric_config.time_group
        resolution = metric_config.resolution

        if group_field is None:
            group_field = []

        is_root_segment_multi_table_segment = isinstance(sub_query, MultiTableSegment)

        def get_all_single_table_segments(
            any_table_segment: TableSegment,
        ) -> List[SingleTableSegment]:
            if isinstance(any_table_segment, SingleTableSegment):
                return [any_table_segment]
            elif isinstance(any_table_segment, MultiTableSegment):
                return get_all_single_table_segments(
                    any_table_segment.left
                ) + get_all_single_table_segments(any_table_segment.right)
            else:
                raise ValueError(f"Unknown table segment: {any_table_segment}")

        selects = []
        segment_index: Optional[int] = None
        subsegment_index = 0
        last_custom_event_key: Optional[str] = None
        for single_table_segment in get_all_single_table_segments(sub_query):
            if segment_index != single_table_segment.segment_index:
                segment_index = single_table_segment.segment_index
                subsegment_index = 0
                last_custom_event_key = single_table_segment.custom_event_key
            elif (
                single_table_segment.custom_event_key is None
                or last_custom_event_key != single_table_segment.custom_event_key
            ):
                subsegment_index += 1
                last_custom_event_key = single_table_segment.custom_event_key

            single_table_segment = single_table_segment.with_subsegment_index(
                subsegment_index
            )

            selects.append(
                self._get_single_table_segment_subquery_select(
                    metric_config,
                    metric_type,
                    single_table_segment,
                    group_field,
                    property_agg_field,
                    dimension_tables,
                    segment_identifier,
                    is_root_segment_multi_table_segment,
                )
            )

        segment_cte = SA.union_all(*selects).cte()

        if segment_filter in (
            M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,
            M.SegmentFilter.LAST_EVENT_IN_TIME_WINDOW,
            M.SegmentFilter.FIRST_EVENT_PER_PERIOD,
            M.SegmentFilter.LAST_EVENT_PER_PERIOD,
        ):
            segment_cte = self._apply_time_filter_and_intermediate_aggs(
                segment_cte,
                sub_query.context.start_date,
                sub_query.context.end_date,
                time_group,
                aggregation=aggregation,
                comparison_options=metric_config.comparison_options,
            )
            segment_cte = self._apply_segment_filter(
                segment_cte, segment_filter, time_group
            )

        elif segment_filter in (
            M.SegmentFilter.FIRST_EVENT_EVER,
            M.SegmentFilter.LAST_EVENT_EVER,
        ):
            segment_cte = self._apply_segment_filter(segment_cte, segment_filter, None)
            segment_cte = self._apply_time_filter_and_intermediate_aggs(
                segment_cte,
                sub_query.context.start_date,
                sub_query.context.end_date,
                time_group,
                aggregation=aggregation,
                comparison_options=metric_config.comparison_options,
            )
        else:
            segment_cte = self._apply_time_filter_and_intermediate_aggs(
                segment_cte,
                sub_query.context.start_date,
                sub_query.context.end_date,
                time_group,
                aggregation=aggregation,
                comparison_options=metric_config.comparison_options,
            )

        resolution_tg = resolution.get_time_group()
        if resolution_tg is None:
            return segment_cte

        rn_cte = self.create_resolution_cte(segment_cte, resolution_tg)

        return SAH.select(
            columns=self._copy_cte_columns(
                rn_cte, skip_column=[GA.CTE_SAMPLING_ROW_NUMBER]
            ),
            whereclause=rn_cte.columns[GA.CTE_SAMPLING_ROW_NUMBER] == 1,
        ).cte()

    def create_resolution_cte(
        self, segment_cte: SEL.CTE, resolution_tg: M.TimeGroup
    ) -> SEL.CTE:
        datetime_col = self.adapter.get_date_trunc(
            field_ref=segment_cte.c[GA.CTE_DATETIME_COL],
            time_group=resolution_tg,
        )
        part_cols = [
            segment_cte.c[GA.CTE_USER_ID_ALIAS_COL],
            segment_cte.c[GA.CTE_GROUP_COL],
            segment_cte.c[GA.CTE_EVENT_PROPERTY_COL],
            datetime_col,
        ]
        if GA.CTE_HOLDING_CONSTANT_COL in segment_cte.columns:
            part_cols.append(segment_cte.c[GA.CTE_HOLDING_CONSTANT_COL])

        if GA.CTE_SEGMENT_IDENTIFIER_COL in segment_cte.columns:
            part_cols.append(segment_cte.c[GA.CTE_SEGMENT_IDENTIFIER_COL])

        return SAH.select(
            columns=self._copy_cte_columns(segment_cte)
            + [
                SA.func.row_number()
                .over(
                    partition_by=part_cols,
                    order_by=segment_cte.c[GA.CTE_DATETIME_COL].asc(),
                )
                .label(GA.CTE_SAMPLING_ROW_NUMBER)
            ],
        ).cte()

    def _apply_time_filter_and_intermediate_aggs(
        self,
        segment_cte: SEL.CTE,
        start_date: datetime,
        end_date: datetime,
        time_group: M.TimeGroup,
        aggregation: M.AggregationConfig,
        comparison_options: Optional[M.ComparisonOptions],
    ) -> SEL.CTE:
        _start_date = self.adapter.correct_timestamp(start_date)
        _end_date = self.adapter.correct_timestamp(end_date)
        columns = self._copy_cte_columns(segment_cte, skip_column=[GA.CTE_REV_AMOUNT])
        is_revenue_segment = aggregation.type in [
            M.AggType.TOTAL_MRR,
            M.AggType.COUNT_SUBSCRIBERS,
            M.AggType.RETURN_SUBSCRIBERS,
        ]

        date_time_col = segment_cte.c[GA.CTE_DATETIME_COL]
        comparison_start: Optional[FieldReference] = None
        comparison_end: Optional[FieldReference] = None
        if comparison_options and time_group != M.TimeGroup.TOTAL:
            time_delta = comparison_options.time_window.to_relative_delta()
            comparison_start = self.adapter.correct_timestamp(start_date - time_delta)
            comparison_end = self.adapter.correct_timestamp(end_date - time_delta)

        if is_revenue_segment:
            date_filter = (segment_cte.c[GA.CTE_REV_PERIOD_START] <= _end_date) & (
                segment_cte.c[GA.CTE_REV_PERIOD_END] >= _start_date
            )

            if comparison_start is not None and comparison_end is not None:
                comparison_date_filter = (
                    segment_cte.c[GA.CTE_REV_PERIOD_START] <= comparison_end
                ) & (segment_cte.c[GA.CTE_REV_PERIOD_END] >= comparison_start)
                date_filter = SA.or_(date_filter, comparison_date_filter)

            if aggregation.type == M.AggType.TOTAL_MRR:
                rev_amount = segment_cte.c[GA.CTE_REV_AMOUNT] / SA.func.greatest(
                    1,
                    self.adapter.date_diff(
                        segment_cte.c[GA.CTE_REV_PERIOD_START],
                        segment_cte.c[GA.CTE_REV_PERIOD_END],
                        "month",
                    ),
                )
                columns.append(
                    (
                        rev_amount
                        if self.adapter._connection_object.connection_type
                        != M.ConnectionType.SNOWFLAKE
                        else SA.cast(rev_amount, SA.Float)
                    ).label(GA.CTE_REV_AMOUNT)
                )
        else:
            if time_group != M.TimeGroup.TOTAL and not is_revenue_segment:
                _start_date = self.adapter.get_date_trunc(time_group, _start_date)

            # Handle the primary date range
            date_filter = (date_time_col >= _start_date) & (date_time_col <= _end_date)

            if comparison_start is not None and comparison_end is not None:
                comparison_date_filter = (
                    date_time_col
                    >= self.adapter.get_date_trunc(time_group, comparison_start)
                ) & (date_time_col <= comparison_end)
                date_filter = SA.or_(date_filter, comparison_date_filter)

        return SAH.select(
            columns=columns,
            whereclause=date_filter,
        ).cte()

    def _apply_segment_filter(
        self,
        segment_cte: SEL.CTE,
        segment_filter: M.SegmentFilter,
        time_group: Optional[M.TimeGroup],
    ) -> SEL.CTE:
        cte_datetime = segment_cte.c[GA.CTE_DATETIME_COL]
        user_col = segment_cte.c[GA.CTE_USER_ID_ALIAS_COL]
        dt_partition_col = None
        if segment_filter in [
            M.SegmentFilter.FIRST_EVENT_PER_PERIOD,
            M.SegmentFilter.LAST_EVENT_PER_PERIOD,
        ]:
            if time_group is None:
                raise ValueError(
                    "Time group is missing for FIRST_EVENT_PER_PERIOD and LAST_EVENT_PER_PERIOD"
                )
            if time_group == M.TimeGroup.TOTAL:
                dt_partition_col = None
            else:
                dt_partition_col = self.adapter.get_date_trunc(time_group, cte_datetime)

        cte_with_row_number = SAH.select(
            columns=self._copy_cte_columns(segment_cte)
            + [
                SA.func.row_number()
                .over(
                    partition_by=(
                        user_col
                        if dt_partition_col is None
                        else [user_col, dt_partition_col]
                    ),
                    order_by=(
                        cte_datetime.asc()
                        if segment_filter
                        in (
                            M.SegmentFilter.FIRST_EVENT_EVER,
                            M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW,
                            M.SegmentFilter.FIRST_EVENT_PER_PERIOD,
                        )
                        else cte_datetime.desc()
                    ),
                )
                .label(GA.CTE_ROW_NUMBER),
            ],
        ).cte()

        return SAH.select(
            columns=self._copy_cte_columns(cte_with_row_number),
            whereclause=cte_with_row_number.c[GA.CTE_ROW_NUMBER] == 1,
        ).cte()

    def _copy_cte_columns(
        self, cte: SEL.CTE, skip_column: Optional[List[str]] = None
    ) -> List[FieldReference]:
        if skip_column is None:
            skip_column = []
        result: List[FieldReference] = []
        for col in [
            GA.CTE_USER_ID_ALIAS_COL,
            GA.CTE_DATETIME_COL,
            GA.CTE_GROUP_COL,
            GA.CTE_EVENT_PROPERTY_COL,
            GA.CTE_HOLDING_CONSTANT_COL,
            GA.CTE_SEGMENT_IDENTIFIER_COL,
            GA.CTE_SAMPLING_ROW_NUMBER,
            GA.CTE_PARTITION_COL,
            GA.CTE_REV_AMOUNT,
            GA.CTE_REV_PERIOD_START,
            GA.CTE_REV_PERIOD_END,
        ]:
            if col in skip_column:
                continue
            if col not in cte.columns:
                continue
            result.append(cte.c[col].label(col))
        return result

    def _generate_time_series_cte(
        self, start_dt: datetime, end_dt: datetime, time_group: M.TimeGroup
    ) -> SEL.CTE:
        if time_group == M.TimeGroup.TOTAL:
            return SAH.select(
                columns=[self.adapter.correct_timestamp(start_dt).label(GA.REV_DT)]
            ).cte()

        selects = []
        curr_dt = start_dt.replace(hour=0, minute=0, second=0, microsecond=0)

        while curr_dt <= end_dt:
            selects.append(
                SAH.select(
                    columns=[self.adapter.correct_timestamp(curr_dt).label(GA.REV_DT)]
                )
            )

            curr_dt += time_group.to_relativedelta()
        return SA.union_all(*selects).cte()
