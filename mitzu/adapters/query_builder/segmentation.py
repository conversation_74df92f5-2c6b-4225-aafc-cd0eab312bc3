from __future__ import annotations

from typing import Dict, Union

import sqlalchemy as SA
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
from sqlalchemy.orm import aliased

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import (
    AggregatedField,
    DimensionTableRefs,
    FieldReference,
    SelectType,
)
from mitzu.adapters.query_builder.formula import (
    eval_formula,
)
from mitzu.adapters.query_builder.query_builder import (
    QueryBuilder,
)
from mitzu.constants import N_PER_A
from mitzu.formula import parse_formula


class SegmentationQueryBuilder(QueryBuilder):
    def get_query(self, metric: M.Metric) -> SelectType:
        if not isinstance(metric.config, M.SegmentationMetricConfig):
            raise ValueError(
                "Segmentation query generation require SegmentationMetricConfig"
            )
        dimension_tables: Dict[
            str, DimensionTableRefs
        ] = self._get_referenced_dimension_tables(metric)
        collection_cte_value_cols = self._get_collection_cte_value_cols(metric)
        segment_queries = []
        for index, segment in enumerate(metric.segments):
            sub_query = self._get_segment_sub_query(
                segment,
                metric,
                segment_index=index,
                dimension_tables=dimension_tables,
                collection_cte_value_cols=collection_cte_value_cols,
                root_segment=segment,
                custom_event_key=segment.expanded_custom_event_key
                if isinstance(segment, M.ComplexSegment)
                else None,
            )

            # for backward compatibility fallback to the metric.config.aggregation
            # until the model is restructured to store all aggregations in the segments
            aggregation = (
                segment.aggregation
                if segment.aggregation is not None
                else metric.config.aggregation
            )

            property_agg_field = None
            if aggregation.type == M.AggType.BY_EVENT_PROPERTY:
                if aggregation.event_property_agg is None:
                    raise ValueError("Event property aggregation field is not set")
                property_agg_field = aggregation.event_property_agg.field_def

            cte: SEL.CTE = aliased(
                self._get_segment_sub_query_cte(
                    metric.type,
                    sub_query,
                    group_field=segment.group_by,
                    metric_config=metric.config,
                    property_agg_field=property_agg_field,
                    segment_filter=segment.filter,
                    dimension_tables=dimension_tables,
                    aggregation=aggregation,
                )
            )
            if aggregation.type in [
                M.AggType.TOTAL_MRR,
                M.AggType.COUNT_SUBSCRIBERS,
                M.AggType.RETURN_SUBSCRIBERS,
            ]:
                start_date = sub_query.context.start_date
                end_date = sub_query.context.end_date
                rev_dt_cte = self._generate_time_series_cte(
                    start_date,
                    end_date,
                    metric.config.time_group,
                )
                comparison_options = metric.config.comparison_options
                if comparison_options and metric.config.time_group != M.TimeGroup.TOTAL:
                    time_delta = comparison_options.time_window.to_relative_delta()
                    comparison_start = start_date - time_delta
                    comparison_end = end_date - time_delta
                    comparison_rev_dt_cte = self._generate_time_series_cte(
                        comparison_start, comparison_end, metric.config.time_group
                    )

                    rev_dt_cte = self.adapter.union(
                        rev_dt_cte.select(), comparison_rev_dt_cte.select()
                    ).cte()

                cte_with_dt = self.adapter.join_revenue_ctes(rev_dt_cte, cte)

                columns = (
                    [
                        rev_dt_cte.c[GA.REV_DT].label(GA.CTE_DATETIME_COL),
                    ]
                    + (
                        [
                            SA.func.coalesce(cte.c[GA.CTE_REV_AMOUNT], 0).label(
                                GA.CTE_REV_AMOUNT
                            )
                        ]
                        if aggregation.type == M.AggType.TOTAL_MRR
                        else []
                    )
                    + self._copy_cte_columns(
                        cte, skip_column=[GA.CTE_DATETIME_COL, GA.CTE_REV_AMOUNT]
                    )
                )
                cte = SAH.select(columns=columns, select_from=cte_with_dt).cte()

            evt_time_group = (
                self.adapter.get_date_trunc(
                    field_ref=cte.columns[GA.CTE_DATETIME_COL],
                    time_group=metric.config.time_group,
                )
                if metric.config.time_group != M.TimeGroup.TOTAL
                else SA.literal(None)
            )

            if len(segment.group_by) == 0:
                # NOTE: maybe we could just use cte.columns[GA.CTE_GROUP_COL] but tat requires way more testing
                group_by: FieldReference = self.adapter.default_group_by_col(
                    metric.type
                )
            elif len(segment.group_by) == 1 and len(metric.segments) > 1:
                # avoid unioning selects with different group by column types
                group_by = self.adapter.group_by_col_cast(cte.columns[GA.CTE_GROUP_COL])
            else:
                group_by = cte.columns[GA.CTE_GROUP_COL]

            if aggregation.type in M.LIST_USER_AGGS:
                group_by_expression = None
                # the aggregation part will call a distinct on the user_id column
                # all other columns should be removed else the query will fail
                columns = [
                    self._get_aggregation(aggregation, metric.config, cte).label(
                        GA.AGG_VALUE_COL
                    ),
                ]
            else:
                group_by_expression = self.adapter.get_group_by_expression(
                    GA.DATETIME_COL, GA.GROUP_COL
                )

                if (
                    segment.aggregation
                    and segment.aggregation.type
                    in [
                        M.AggType.TOTAL_MRR,
                        M.AggType.COUNT_SUBSCRIBERS,
                    ]
                    and len(segment.group_by) > 0
                ):
                    # group_by column can be null because of the left outer join in case of MRR calculation
                    # later it causes bigger issues so make sure null groups are n/a groups
                    group_by = SA.func.coalesce(group_by, N_PER_A)

                columns = [
                    evt_time_group.label(GA.DATETIME_COL),
                    group_by.label(GA.GROUP_COL),
                    self._get_aggregation(aggregation, metric.config, cte).label(
                        GA.AGG_VALUE_COL
                    ),
                    SA.literal(index).label(GA.EVENT_INDEX_COL),
                ]

            query = SAH.select(
                columns=columns,
                group_by=group_by_expression,
            )
            segment_queries.append(query)

        if metric.config.formula is None:
            if len(segment_queries) > 1:
                segment_queries = self.adapter.fix_segment_queries_for_union(
                    segment_queries
                )
                return SA.union_all(*segment_queries)

            elif len(segment_queries) == 1:
                return segment_queries[0]
            else:
                raise ValueError("No segment queries found")
        else:
            queries_with_group_by = [
                segment_query
                for segment, segment_query in zip(metric.segments, segment_queries)
                if len(segment.group_by) > 0
            ]

            query_with_group_by = (
                queries_with_group_by[0] if len(queries_with_group_by) == 1 else None
            )

            variables = {}

            first_cte = segment_queries[0].cte()
            joined_source = first_cte
            variables[self._get_variable_name_from_index(0)] = joined_source.c[
                GA.AGG_VALUE_COL
            ]
            prev_cols = first_cte.columns
            group_by_join_condition = None
            group_by_cte = first_cte
            for index, query in enumerate(segment_queries[1:], start=1):
                cte = query.cte()
                curr_cols = cte.columns
                variables[self._get_variable_name_from_index(index)] = cte.c[
                    GA.AGG_VALUE_COL
                ]

                if len(queries_with_group_by) > 1:
                    # Handle multiple group_by conditions
                    group_by_join_condition = (
                        curr_cols[GA.GROUP_COL] == prev_cols[GA.GROUP_COL]
                    )
                elif (
                    len(queries_with_group_by) == 1
                    and query_with_group_by is not None
                    and index == segment_queries.index(query_with_group_by)
                ):
                    group_by_cte = cte
                    group_by_join_condition = None
                else:
                    group_by_join_condition = None

                datetime_join_condition = (
                    (curr_cols[GA.DATETIME_COL] == prev_cols[GA.DATETIME_COL])
                    if metric.config.time_group != M.TimeGroup.TOTAL
                    else None
                )

                join_condition = self.adapter.combine_filters(
                    [
                        datetime_join_condition,
                        group_by_join_condition,
                    ]
                    + (  # empty list fallbacks to True but redshift cannot handle it
                        [
                            prev_cols[GA.EVENT_INDEX_COL] + 1
                            == cte.columns[GA.EVENT_INDEX_COL],
                        ]
                        if self.adapter._connection_object.connection_type
                        == M.ConnectionType.REDSHIFT
                        else []
                    )
                )

                joined_source = joined_source.join(  # type: ignore[assignment]
                    cte,
                    join_condition,
                    isouter=True,
                    full=True,
                )
                prev_cols = curr_cols

            formula_exp = parse_formula(metric.config.formula.upper())
            formula_result = eval_formula(formula_exp, variables, self.adapter)

            return SAH.select(
                columns=[
                    group_by_cte.c[GA.DATETIME_COL].label(GA.DATETIME_COL),
                    group_by_cte.c[GA.GROUP_COL].label(GA.GROUP_COL),
                    SA.literal(0).label(GA.EVENT_INDEX_COL),
                    formula_result.label(GA.AGG_VALUE_COL),
                ],
                select_from=joined_source,
            )

    def _get_aggregation(
        self,
        aggregation: M.AggregationConfig,
        metric_config: M.MetricConfig,
        cte: SEL.CTE,
    ) -> AggregatedField:
        at = aggregation.type

        if at is None:
            raise ValueError("Aggregation type is not set")
        if at == M.AggType.COUNT_EVENTS:
            event_count: Union[SA_F.Function, SA_EXP.ColumnElement] = SA.func.count(
                cte.columns.get(GA.CTE_DATETIME_COL)
            )
            if metric_config.event_sampling:
                event_count = event_count * SA.literal(
                    int(1.0 / metric_config.event_sampling)
                )
            return event_count

        elif at == M.AggType.COUNT_UNIQUE_USERS:
            user_count: Union[SA_F.Function, SA_EXP.ColumnElement] = SA.func.count(
                cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct()
            )
            return user_count
        if at == M.AggType.FREQUENCY:
            event_count = SA.func.count(cte.columns.get(GA.CTE_DATETIME_COL))
            user_count = SA.func.count(cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct())
            if metric_config.event_sampling:
                event_count = event_count * SA.literal(
                    int(1.0 / metric_config.event_sampling)
                )
            return self.adapter.safe_divide(event_count * 1.0, user_count)
        elif at == M.AggType.BY_EVENT_PROPERTY:
            return self.adapter.get_event_property_aggregation(
                aggregation, cte.columns[GA.CTE_EVENT_PROPERTY_COL]
            )
        elif at in M.LIST_USER_AGGS:
            return cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct()
        elif at in [M.AggType.TOTAL_MRR, M.AggType.COUNT_SUBSCRIBERS]:
            return self.adapter.get_revenue_aggregation_in_segmentation(at, cte)
        else:
            raise ValueError(
                f"Aggregation type {at.name} is not supported for segmentation"
            )

    def _get_variable_name_from_index(self, index: int) -> str:
        return chr(65 + index)
