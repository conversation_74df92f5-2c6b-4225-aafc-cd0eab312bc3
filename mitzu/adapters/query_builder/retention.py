from __future__ import annotations

from typing import Dict, Optional, Tuple

import sqlalchemy.sql.selectable as SEL

import mitzu.model as M
from mitzu.adapters.model import (
    DimensionTableRefs,
    SelectType,
)
from mitzu.adapters.query_builder.conversion import (
    ConversionQueryBuilder,
)


class RetentionQueryBuilder(ConversionQueryBuilder):
    def get_query(
        self, metric: M.Metric, cte_prefix: Optional[str] = None
    ) -> SelectType:
        if not isinstance(metric.config, M.RetentionMetricConfig):
            raise ValueError("Retention query generation require RetentionMetricConfig")

        return super().get_query(metric, cte_prefix)

    def _get_retention_segments_ctes(
        self, metric: M.Metric
    ) -> Tuple[SEL.CTE, SEL.CTE, SEL.CTE]:
        # Todo remove once clickhouse adapter is cleaned up
        if not isinstance(metric.config, M.RetentionMetricConfig):
            raise ValueError("Retention query generation require RetentionMetricConfig")

        dimension_tables: Dict[
            str, DimensionTableRefs
        ] = self._get_referenced_dimension_tables(metric)
        collection_cte_value_cols = self._get_collection_cte_value_cols(metric)
        return (
            self._get_segment_sub_query_cte(
                metric.type,
                self._get_segment_sub_query(
                    metric.segments[0],
                    metric,
                    segment_index=0,
                    dimension_tables=dimension_tables,
                    collection_cte_value_cols=collection_cte_value_cols,
                    root_segment=metric.segments[0],
                    custom_event_key=metric.segments[0].expanded_custom_event_key
                    if isinstance(metric.segments[0], M.ComplexSegment)
                    else None,
                ),
                group_field=metric.segments[0].group_by,
                metric_config=metric.config,
                property_agg_field=None,
                segment_filter=metric.segments[0].filter,
                dimension_tables=dimension_tables,
                aggregation=metric.config.aggregation,
            ),
            self._get_segment_sub_query_cte(
                metric.type,
                self._get_segment_sub_query(
                    metric.segments[1],
                    metric,
                    segment_index=1,
                    dimension_tables=dimension_tables,
                    collection_cte_value_cols=collection_cte_value_cols,
                    root_segment=metric.segments[1],
                    custom_event_key=metric.segments[1].expanded_custom_event_key
                    if isinstance(metric.segments[1], M.ComplexSegment)
                    else None,
                ),
                group_field=None,
                metric_config=metric.config,
                property_agg_field=(
                    metric.config.aggregation.event_property_agg.field_def
                    if metric.config.aggregation.event_property_agg is not None
                    else None
                ),
                segment_filter=metric.segments[1].filter,
                dimension_tables=dimension_tables,
                aggregation=metric.config.aggregation,
            ),
            self._generate_retention_series_cte(
                metric.config.retention_indices,
            ),
        )
