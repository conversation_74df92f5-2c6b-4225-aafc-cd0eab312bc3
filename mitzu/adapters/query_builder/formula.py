import ast
from typing import TYPE_CHECKING, Any, Dict

import sqlalchemy as SA

from mitzu.adapters.model import FieldReference
from mitzu.formula import (
    BinaryOperator,
    Const,
    FormulaExpression,
    FormulaSyntaxError,
    Variable,
    operator_to_method,
)

if TYPE_CHECKING:
    from mitzu.adapters.sqlalchemy_adapter import (
        SQLAlchemyAdapter,
    )
else:
    SQLAlchemyAdapter = Any


def eval_formula(
    formula: FormulaExpression,
    variable_mapping: Dict[str, Any],
    adapter: SQLAlchemyAdapter,
) -> FieldReference:
    if isinstance(formula, Variable):
        if formula.name not in variable_mapping:
            raise FormulaSyntaxError(f"Unknown variable: {formula.name}")
        return variable_mapping[formula.name]

    if isinstance(formula, Const):
        return SA.literal(formula.value)

    if isinstance(formula, BinaryOperator):
        if formula.operator == ast.Div:
            return adapter.safe_divide(
                eval_formula(formula.left, variable_mapping, adapter) * 1.0,
                eval_formula(formula.right, variable_mapping, adapter),
            )
        else:
            return operator_to_method[formula.operator](
                eval_formula(formula.left, variable_mapping, adapter),
                eval_formula(formula.right, variable_mapping, adapter),
            )

    raise FormulaSyntaxError(f"Unknown formula element {formula}")
