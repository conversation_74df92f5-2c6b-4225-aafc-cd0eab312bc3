from __future__ import annotations

from dataclasses import dataclass, replace
from typing import Dict, <PERSON>, Optional, Tuple, Union

import sqlalchemy as SA
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from sqlalchemy.sql.base import ImmutableColumnCollection

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import (
    AggregatedField,
    DimensionTableRefs,
    FieldReference,
    GroupByFieldReference,
    SelectType,
    TooManyDataPointsError,
    fix_col_index,
)
from mitzu.adapters.query_builder.query_builder import (
    QueryBuilder,
)
from mitzu.helper import (
    GROUP_MISSING_VALUE,
    JOURNEY_SEPARATOR,
    NO_GROUP,
)


@dataclass(frozen=True)
class JoinCondition:
    # This class will be replaced by the cleaned up MetricConversionConfig
    time_window: M.TimeWindow
    window_type: M.ConversionWindowType
    bounded_conversion: bool
    consider_first_event_converted: bool
    holding_constant_col_name: str
    conv_orientation: Optional[M.ConversionOrientation]


class ConversionQueryBuilder(QueryBuilder):
    def get_query(self, metric: M.Metric, cte_prefix: Optional[str]) -> SelectType:
        if not isinstance(metric.config, M.ConversionRetentionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionRetentionMetricConfig"
            )

        if cte_prefix is None:
            cte_prefix = ""
        else:
            cte_prefix = f"{cte_prefix}_"

        segment_ctes = self._get_conversion_segments_ctes(metric)
        if metric.config.conv_attribution == M.ConversionAttribution.FIRST_EVENT:
            aggregation_source = self._get_conversion_select_first_event_cte(
                segment_ctes, metric, cte_prefix
            )
        else:
            aggregation_source = self._get_conversion_select_every_event_cte(
                segment_ctes, metric
            )

        if metric.config.aggregation.type in M.LIST_USER_AGGS:
            group_by: Optional[List[GroupByFieldReference]] = None
            columns = [
                self._get_conv_aggregation(
                    metric, aggregation_source, index=len(segment_ctes) - 1
                ).label(fix_col_index(1, GA.AGG_VALUE_COL))
            ]
        else:
            group_by_cols = [GA.DATETIME_COL, GA.GROUP_COL]
            if metric.type == M.MetricType.RETENTION:
                group_by_cols.append(GA.RETENTION_INDEX)

            group_by = self.adapter.get_group_by_expression(*group_by_cols)
            columns = self._get_conversion_final_conversion_select(
                aggregation_source, metric
            )
        res = SAH.select(columns, select_from=aggregation_source)
        if group_by:
            res = res.group_by(*group_by)
        return res

    def _get_conversion_segments_ctes(self, metric: M.Metric) -> List[SEL.CTE]:
        if not isinstance(metric.config, M.ConversionRetentionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionRetentionMetricConfig"
            )
        last_segment_index = len(metric.segments) - 1
        dimension_tables: Dict[
            str, DimensionTableRefs
        ] = self._get_referenced_dimension_tables(metric)
        collection_cte_value_cols = self._get_collection_cte_value_cols(metric)
        property_agg_field = (
            metric.event_property_agg.field_def
            if metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY
            else None
        )
        segment_ctes: List[SEL.CTE] = []
        for i, seg in enumerate(metric.segments):
            seg_cte = self._get_segment_sub_query_cte(
                metric.type,
                self._get_segment_sub_query(
                    seg,
                    metric,
                    segment_index=i,
                    dimension_tables=dimension_tables,
                    collection_cte_value_cols=collection_cte_value_cols,
                    root_segment=seg,
                    custom_event_key=seg.expanded_custom_event_key
                    if isinstance(seg, M.ComplexSegment)
                    else None,
                ),
                metric_config=metric.config,
                group_field=seg.group_by,
                property_agg_field=(
                    property_agg_field if i == last_segment_index else None
                ),
                segment_filter=seg.filter,
                dimension_tables=dimension_tables,
                segment_identifier=(
                    i
                    if metric.config.conv_attribution
                    == M.ConversionAttribution.FIRST_EVENT
                    else None
                ),
                aggregation=metric.config.aggregation,
            )
            segment_ctes.append(seg_cte)
        return segment_ctes

    def _get_conversion_select_every_event_cte(
        self, segment_ctes: List[SEL.CTE], metric: M.Metric
    ) -> SEL.CTE:
        if not isinstance(metric.config, M.ConversionRetentionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionRetentionMetricConfig"
            )
        if (
            isinstance(metric.config, M.ConversionMetricConfig)
            and metric.config.conv_orientation == M.ConversionOrientation.REVERSE
        ):
            # Reverse funnel must be reordered to be processed correctly
            segment_ctes = segment_ctes[::-1]

        first_cte = segment_ctes[0]
        joined_source: Union[SEL.CTE, SEL.Join] = first_cte
        prev_cols = joined_source.columns
        retention_indexes_cte: Optional[SEL.CTE] = None
        retention_index_col: Optional[SA_EXP.ColumnElement] = None
        if isinstance(metric.config, M.RetentionMetricConfig):
            retention_indexes_cte = self._generate_retention_series_cte(
                metric.config.retention_indices
            )
            retention_index_col = retention_indexes_cte.columns[GA.RETENTION_INDEX]
        join_condition = self._create_join_condition(metric.config)

        for seg_cte in segment_ctes[1:]:
            curr_cols = seg_cte.columns

            if (
                metric.type == M.MetricType.RETENTION
                and retention_indexes_cte is not None
            ):
                joined_source = joined_source.join(
                    retention_indexes_cte, onclause=SA.literal(True)
                )

            joined_source = joined_source.join(  # type: ignore[assignment]
                seg_cte,
                onclause=self._get_conversion_condition(
                    first_cte=first_cte,
                    prev_cols=prev_cols,
                    curr_cols=curr_cols,
                    join_condition=join_condition,
                    retention_index_col=retention_index_col,
                ),
                isouter=True,
            )
            prev_cols = curr_cols

        if (
            isinstance(metric.config, M.ConversionMetricConfig)
            and metric.config.conv_orientation == M.ConversionOrientation.REVERSE
        ):
            # We need to re-order to the normal order of the funnel
            segment_ctes = segment_ctes[::-1]

        group_by_col: FieldReference = SA.literal(None)
        for index, seg in enumerate(metric.segments):
            if len(seg.group_by) > 0:
                group_by_col = segment_ctes[index].columns[GA.CTE_GROUP_COL]
                break

        columns = self._get_columns_for_every_event_cte(
            segment_ctes, metric, retention_indexes_cte, group_by_col
        )
        final_condition = None
        if (
            isinstance(metric.config, M.RetentionMetricConfig)
            and retention_indexes_cte is not None
            and metric.config.cut_off_incomplete_data
        ):
            final_condition = self._get_cut_off_condition_cte(
                metric.config,
                first_cte.columns[GA.CTE_DATETIME_COL].label(GA.DATETIME_COL),
                retention_indexes_cte.columns[GA.RETENTION_INDEX].label(
                    GA.RETENTION_INDEX
                ),
            )

        return SAH.select(
            columns, select_from=joined_source, whereclause=final_condition
        ).cte()

    def _get_columns_for_every_event_cte(
        self,
        segment_ctes: List[SEL.CTE],
        metric: M.Metric,
        retention_indexes_cte: Optional[SEL.CTE],
        group_by_col: FieldReference,
    ) -> List:
        columns: List = [group_by_col.label(GA.CTE_GROUP_COL)]
        if metric.type == M.MetricType.RETENTION and retention_indexes_cte is not None:
            columns.append(
                retention_indexes_cte.columns[GA.RETENTION_INDEX].label(
                    GA.RETENTION_INDEX
                )
            )
        for i, seg_cte in enumerate(segment_ctes):
            columns.append(
                seg_cte.columns[GA.CTE_USER_ID_ALIAS_COL].label(
                    fix_col_index(i, GA.CTE_USER_ID_ALIAS_COL)
                )
            )
            columns.append(
                seg_cte.columns[GA.CTE_DATETIME_COL].label(
                    fix_col_index(i, GA.CTE_DATETIME_COL)
                )
            )
            if metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY and (
                i == len(metric.segments) - 1
            ):
                columns.append(
                    seg_cte.columns[GA.CTE_EVENT_PROPERTY_COL].label(
                        fix_col_index(i, GA.CTE_EVENT_PROPERTY_COL)
                    )
                )

        return columns

    def _get_cut_off_condition_cte(
        self,
        metric_config: M.RetentionMetricConfig,
        source_dt_col: SA_EXP.ColumnElement,
        retention_index_col: SA_EXP.ColumnElement,
    ) -> FieldReference:
        return source_dt_col < self._get_dynamic_datetime_interval(
            field_ref=self.adapter.correct_timestamp(self.adapter.end_date),
            value_field_ref=retention_index_col,
            time_group=metric_config.time_window.period,
            multiplier=-1,
            adding=-1,
        )

    def _create_join_condition(
        self, metric_config: M.ConversionRetentionMetricConfig
    ) -> JoinCondition:
        if isinstance(metric_config, M.RetentionMetricConfig):
            return JoinCondition(
                window_type=M.ConversionWindowType.ENTIRE_FUNNEL,
                time_window=metric_config.time_window,
                bounded_conversion=(
                    metric_config.retention_attribution_type
                    == M.RetentionAttributionType.RETURN_ON
                ),
                consider_first_event_converted=metric_config.consider_first_event_as_retained,
                holding_constant_col_name=GA.CTE_USER_ID_ALIAS_COL
                if len(metric_config.custom_holding_constants) == 0
                else GA.CTE_HOLDING_CONSTANT_COL,
                conv_orientation=None,
            )

        elif isinstance(metric_config, M.ConversionMetricConfig):
            return JoinCondition(
                window_type=metric_config.conv_window_type,
                time_window=metric_config.time_window,
                bounded_conversion=True,
                consider_first_event_converted=False,
                holding_constant_col_name=GA.CTE_USER_ID_ALIAS_COL
                if len(metric_config.custom_holding_constants) == 0
                else GA.CTE_HOLDING_CONSTANT_COL,
                conv_orientation=metric_config.conv_orientation,
            )
        else:
            raise ValueError(f"Invalid metric config type: {type(metric_config)}")

    def _get_conversion_condition(
        self,
        first_cte: SEL.CTE,
        prev_cols: ImmutableColumnCollection[SA_EXP.ColumnElement],
        curr_cols: ImmutableColumnCollection[SA_EXP.ColumnElement],
        join_condition: JoinCondition,
        retention_index_col: Optional[SA_EXP.ColumnElement] = None,
    ) -> SA_EXP.ColumnElement:
        curr_dt = self.adapter.convert_to_datetime_if_needed(
            curr_cols[GA.CTE_DATETIME_COL]
        )
        prev_dt = self.adapter.convert_to_datetime_if_needed(
            prev_cols[GA.CTE_DATETIME_COL]
        )
        first_dt = self.adapter.convert_to_datetime_if_needed(
            first_cte.columns[GA.CTE_DATETIME_COL]
        )
        hc_name = join_condition.holding_constant_col_name

        res_condition = prev_cols[hc_name] == curr_cols[hc_name]

        if retention_index_col is not None:
            start = self.adapter.get_dynamic_datetime_interval(
                first_dt, retention_index_col, join_condition.time_window.period
            )

            res_condition = res_condition & (
                (curr_dt >= start)
                if join_condition.consider_first_event_converted
                else (curr_dt > start)
            )
            if join_condition.bounded_conversion:
                res_condition = res_condition & (
                    curr_dt
                    <= self.adapter.get_datetime_interval(
                        start,
                        join_condition.time_window,
                    )
                )
        else:
            is_backwards_looking = (
                join_condition.conv_orientation == M.ConversionOrientation.REVERSE
            )
            if is_backwards_looking:
                res_condition = res_condition & (
                    (curr_dt <= prev_dt)
                    if join_condition.consider_first_event_converted
                    else (curr_dt < prev_dt)
                )
            else:
                res_condition = res_condition & (
                    (curr_dt >= prev_dt)
                    if join_condition.consider_first_event_converted
                    else (curr_dt > prev_dt)
                )

            if join_condition.bounded_conversion:
                tw_value = join_condition.time_window.value
                dt_interval = self.adapter.get_datetime_interval(
                    (
                        first_dt
                        if join_condition.window_type
                        != M.ConversionWindowType.BETWEEN_EACH_STEP
                        else prev_dt
                    ),
                    timewindow=replace(
                        join_condition.time_window,
                        value=-tw_value if is_backwards_looking else tw_value,
                    ),
                )
                res_condition = res_condition & (
                    (curr_dt >= dt_interval)
                    if is_backwards_looking
                    else (curr_dt <= dt_interval)
                )
        return res_condition

    def _get_conversion_select_first_event_cte(
        self, segment_ctes: List[SEL.CTE], metric: M.Metric, cte_prefix: str
    ) -> SEL.CTE:
        if not isinstance(metric.config, M.ConversionRetentionMetricConfig):
            raise ValueError(
                "Conversion query generation require ConversionRetentionMetricConfig"
            )
        unoined_sources = self._get_unioned_first_event_conversion_sources(segment_ctes)
        should_use_cte_property = (
            metric.config.aggregation.type == M.AggType.BY_EVENT_PROPERTY
        )
        join_condition = self._create_join_condition(metric.config)

        join_col = (
            GA.CTE_HOLDING_CONSTANT_COL
            if len(metric.config.custom_holding_constants) > 0
            else GA.CTE_USER_ID_ALIAS_COL
        )
        curr_step: SEL.CTE = unoined_sources.cte(
            name=cte_prefix + fix_col_index(0, "step")
        )
        funnel_length = len(metric.segments)
        retention_indexes_cte: Optional[SEL.CTE] = None
        if isinstance(metric.config, M.RetentionMetricConfig):
            retention_indexes_cte = self._generate_retention_series_cte(
                metric.config.retention_indices
            )

        for step in range(1, funnel_length):
            prev_dt_col = curr_step.columns[
                fix_col_index(step - 1, GA.CTE_DATETIME_COL)
            ]
            first_dt_col = curr_step.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            identifier = curr_step.columns[GA.CTE_SEGMENT_IDENTIFIER_COL]
            partition_by_cols = [curr_step.columns[join_col]]
            if retention_indexes_cte is not None:
                order_dt: FieldReference = SA.case(
                    (
                        identifier == 0,
                        self.adapter.get_dynamic_datetime_interval(
                            SA.func.coalesce(prev_dt_col, first_dt_col),
                            retention_indexes_cte.columns[GA.RETENTION_INDEX],
                            join_condition.time_window.period,
                        ),
                    ),
                    else_=SA.func.coalesce(prev_dt_col, first_dt_col),
                )
            else:
                order_dt = SA.func.coalesce(prev_dt_col, first_dt_col)

            sorting_order = [
                order_dt,
                identifier != step,  # we need to make sure next step is taken as first
            ]
            window_frame = self._get_first_event_conversion_window_frame()
            cols = [
                *curr_step.columns,
                self._create_first_event_window_function(
                    join_condition=join_condition,
                    partition_by_cols=partition_by_cols,
                    curr_step=curr_step,
                    step=step,
                    target_col=first_dt_col,
                    identifier=identifier,
                    sorting_order=sorting_order,
                    window_frame=window_frame,
                ).label(fix_col_index(step, GA.CTE_DATETIME_COL)),
            ]

            if (
                metric.segments[step].group_by is not None
                and len(metric.segments[step].group_by) > 0
            ):
                group_wf = self._create_first_event_window_function(
                    join_condition=join_condition,
                    partition_by_cols=partition_by_cols,
                    curr_step=curr_step,
                    step=step,
                    target_col=curr_step.columns[fix_col_index(0, GA.CTE_GROUP_COL)],
                    identifier=identifier,
                    sorting_order=sorting_order,
                    window_frame=window_frame,
                ).label(fix_col_index(step, GA.CTE_GROUP_COL))
                cols.append(group_wf)
            else:
                cols.append(
                    SA.literal(NO_GROUP).label(fix_col_index(step, GA.CTE_GROUP_COL))
                )
            if step == funnel_length - 1 and should_use_cte_property:
                evt_prop_wf = self._create_first_event_window_function(
                    join_condition=join_condition,
                    partition_by_cols=partition_by_cols,
                    curr_step=curr_step,
                    step=step,
                    target_col=curr_step.columns[GA.CTE_EVENT_PROPERTY_COL],
                    identifier=identifier,
                    sorting_order=sorting_order,
                    window_frame=window_frame,
                ).label(fix_col_index(step, GA.CTE_EVENT_PROPERTY_COL))
                cols.append(evt_prop_wf)

            final_select: Union[SEL.CTE, SEL.Join] = curr_step
            final_condition = None
            if (
                step == 1
                and retention_indexes_cte is not None
                and isinstance(metric.config, M.RetentionMetricConfig)
            ):
                final_select = curr_step.join(
                    retention_indexes_cte,
                    onclause=(curr_step.columns[GA.CTE_SEGMENT_IDENTIFIER_COL] == 0),
                    isouter=True,
                )
                cols.append(
                    retention_indexes_cte.columns[GA.RETENTION_INDEX].label(
                        GA.RETENTION_INDEX
                    )
                )
                if metric.config.cut_off_incomplete_data:
                    final_condition = SA.or_(
                        self._get_cut_off_condition_cte(
                            metric.config,
                            curr_step.columns[fix_col_index(0, GA.CTE_DATETIME_COL)],
                            retention_indexes_cte.columns[GA.RETENTION_INDEX],
                        ),
                        curr_step.columns[GA.CTE_SEGMENT_IDENTIFIER_COL] > 0,
                    )

            curr_step = SAH.select(
                columns=cols, select_from=final_select, whereclause=final_condition
            ).cte(name=f"{cte_prefix}step_{step}")

        first_dt = curr_step.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
        first_user_id = curr_step.columns[GA.CTE_USER_ID_ALIAS_COL]
        final_group_col: Union[SA_F.Function, SA_EXP.ColumnElement] = SA.func.coalesce(
            curr_step.columns[fix_col_index(0, GA.CTE_GROUP_COL)],
            NO_GROUP,
        )

        final_cols = [
            first_user_id.label(fix_col_index(0, GA.CTE_USER_ID_ALIAS_COL)),
            first_dt.label(fix_col_index(0, GA.CTE_DATETIME_COL)),
        ]
        if isinstance(metric.config, M.RetentionMetricConfig):
            final_cols.append(
                curr_step.columns[GA.RETENTION_INDEX].label(GA.RETENTION_INDEX)
            )

        for step in range(1, funnel_length):
            curr_dt = curr_step.columns[fix_col_index(step, GA.CTE_DATETIME_COL)]
            prev_dt = curr_step.columns[fix_col_index(step - 1, GA.CTE_DATETIME_COL)]

            retention_index_col = curr_step.columns.get(GA.RETENTION_INDEX)
            if retention_index_col is not None:
                start = self.adapter.get_dynamic_datetime_interval(
                    first_dt, retention_index_col, join_condition.time_window.period
                )
                res_condition = (
                    (curr_dt >= start)
                    if join_condition.consider_first_event_converted
                    else (curr_dt > start)
                )
                if join_condition.bounded_conversion:
                    res_condition = res_condition & (
                        curr_dt
                        <= self.adapter.get_datetime_interval(
                            start,
                            join_condition.time_window,
                        )
                    )
            else:
                res_condition = (
                    curr_dt >= prev_dt
                    if join_condition.consider_first_event_converted
                    else curr_dt > prev_dt
                )
                if join_condition.bounded_conversion:
                    res_condition = SA.and_(
                        res_condition,
                        curr_dt
                        <= self.adapter.get_datetime_interval(
                            (
                                first_dt
                                if join_condition.window_type
                                != M.ConversionWindowType.BETWEEN_EACH_STEP
                                else prev_dt
                            ),
                            timewindow=join_condition.time_window,
                        ),
                    )
            final_cols.append(
                SA.case((res_condition, first_user_id), else_=None).label(
                    fix_col_index(step, GA.CTE_USER_ID_ALIAS_COL)
                )
            )
            final_cols.append(
                SA.case((res_condition, curr_dt), else_=None).label(
                    fix_col_index(step, GA.CTE_DATETIME_COL)
                )
            )
            final_group_col = (
                final_group_col
                + JOURNEY_SEPARATOR
                + SA.case(
                    (
                        res_condition,
                        SA.func.coalesce(
                            curr_step.columns[fix_col_index(step, GA.CTE_GROUP_COL)],
                            GROUP_MISSING_VALUE,
                        ),
                    ),
                    else_=NO_GROUP,
                )
            )

            if step == funnel_length - 1 and should_use_cte_property:
                final_cols.append(
                    SA.case(
                        (
                            res_condition,
                            curr_step.columns[
                                fix_col_index(step, GA.CTE_EVENT_PROPERTY_COL)
                            ],
                        ),
                        else_=None,
                    ).label(fix_col_index(step, GA.CTE_EVENT_PROPERTY_COL))
                )
        final_cols.append(final_group_col.label(GA.CTE_GROUP_COL))

        filtered_results = SAH.select(
            columns=final_cols,
            whereclause=(curr_step.columns[GA.CTE_SEGMENT_IDENTIFIER_COL] == 0),
            select_from=curr_step,
        ).cte(f"{cte_prefix}filtered_conversions")
        return filtered_results

    def _get_first_event_window_condition(
        self,
        step: int,
        curr_step: SEL.CTE,
        join_condition: JoinCondition,
    ) -> SA_EXP.ColumnElement:
        if step < 2 or join_condition.time_window is None:
            return SA.literal(True)
        first_dt = curr_step.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
        prev_dt = curr_step.columns[fix_col_index(step - 1, GA.CTE_DATETIME_COL)]
        prev_prev_dt = curr_step.columns[fix_col_index(step - 2, GA.CTE_DATETIME_COL)]
        res_condition = (
            prev_dt >= prev_prev_dt
            if join_condition.consider_first_event_converted
            else prev_dt > prev_prev_dt
        )
        if join_condition.bounded_conversion:
            res_condition = res_condition & (
                prev_dt
                <= self.adapter.get_datetime_interval(
                    (
                        first_dt
                        if join_condition.window_type
                        != M.ConversionWindowType.BETWEEN_EACH_STEP
                        else prev_prev_dt
                    ),
                    join_condition.time_window,
                )
            )
        return res_condition

    def _create_first_event_window_function(
        self,
        join_condition: JoinCondition,
        partition_by_cols: List[FieldReference],
        curr_step: SEL.CTE,
        step: int,
        target_col: FieldReference,
        identifier: FieldReference,
        sorting_order: List[FieldReference],
        window_frame: Optional[Tuple],
    ) -> FieldReference:
        # Thus we need to create a new column with the first event conversion window function.
        # This window functions is applicable for every step in the funnel
        return SA.case(
            (
                SA.and_(
                    curr_step.columns[GA.CTE_SEGMENT_IDENTIFIER_COL] == 0,
                    self._get_first_event_window_condition(
                        step=step,
                        curr_step=curr_step,
                        join_condition=join_condition,
                    ),
                ),
                self.adapter._get_first_event_conversion_window_function(
                    identifier, step, target_col
                ).over(
                    partition_by=partition_by_cols,
                    order_by=sorting_order,
                    rows=window_frame,
                ),
            ),
            else_=None,
        )

    def _get_unioned_first_event_conversion_sources(
        self, segment_ctes: List[SEL.CTE]
    ) -> SEL.CompoundSelect:
        # This functions unions all segments for preparing them to be processed by the window functions.
        selects = []
        for seg_cte in segment_ctes:
            cols = [
                col
                for col in seg_cte.columns
                if col.name
                not in (
                    GA.CTE_DATETIME_COL,
                    GA.CTE_GROUP_COL,
                    GA.CTE_EVENT_PROPERTY_COL,
                )
            ]
            group_col = seg_cte.columns[GA.CTE_GROUP_COL]
            cte_dt_col = seg_cte.columns[GA.CTE_DATETIME_COL]
            if self._should_cast_cols_in_union():
                group_col = SA.cast(group_col, SA_T.String)

            cols.append(group_col.label(fix_col_index(0, GA.CTE_GROUP_COL)))
            cols.append(cte_dt_col.label(fix_col_index(0, GA.CTE_DATETIME_COL)))

            if GA.CTE_EVENT_PROPERTY_COL in seg_cte.columns.keys():
                cte_evt_prop_col = seg_cte.columns[GA.CTE_EVENT_PROPERTY_COL]
                if self._should_cast_cols_in_union():
                    cte_evt_prop_col = SA.cast(cte_evt_prop_col, SA_T.String)
                cols.append(cte_evt_prop_col.label(GA.CTE_EVENT_PROPERTY_COL))
            selects.append(
                SAH.select(
                    columns=cols,
                    select_from=seg_cte,
                )
            )
        return SA.union_all(*selects)

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        return self.adapter._get_conv_aggregation(metric, cte, index)

    def _get_conversion_final_conversion_select(
        self, final_cte: SEL.CTE, metric: M.Metric
    ) -> List:
        if metric.config.time_group != M.TimeGroup.TOTAL:
            time_group_col: Union[
                FieldReference, SA_F.Function
            ] = self.adapter.get_date_trunc(
                field_ref=final_cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)],
                time_group=metric.config.time_group,
            )
        else:
            time_group_col = SA.literal(None)

        if len([seg for seg in metric.segments if seg.group_by]) > 0:
            group_by_col = final_cte.columns[GA.CTE_GROUP_COL]
        else:
            group_by_col = SA.literal(None)

        columns = [
            time_group_col.label(GA.DATETIME_COL),
            group_by_col.label(GA.GROUP_COL),
        ]
        if metric.type == M.MetricType.RETENTION:
            columns.append(
                final_cte.columns[GA.RETENTION_INDEX].label(GA.RETENTION_INDEX)
            )

        for i, _ in enumerate(metric.segments):
            user_count: Union[SA_F.Function, SA_EXP.ColumnElement] = SA.func.count(
                final_cte.columns[fix_col_index(i, GA.CTE_USER_ID_ALIAS_COL)].distinct()
            )

            columns.append(user_count.label(fix_col_index(i + 1, GA.USER_COUNT_COL)))
            columns.append(
                (
                    self._get_conv_aggregation(metric, final_cte, index=i)
                    if metric.config.aggregation.type != M.AggType.BY_EVENT_PROPERTY
                    or (i == len(metric.segments) - 1)
                    else SA.literal(None)
                ).label(fix_col_index(i + 1, GA.AGG_VALUE_COL)),
            )
        return columns

    def _should_cast_cols_in_union(self) -> bool:
        return self.adapter.should_cast_cols_in_union()

    def _get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return self.adapter.get_first_event_conversion_window_frame()

    def _generate_retention_series_cte(
        self,
        indices: List[int],
    ) -> SEL.CTE:
        if len(indices) > 64:
            raise TooManyDataPointsError(
                "Too many retention periods to calculate. Try reducing the time window or change the retention period."
            )
        selects = [
            SAH.select(columns=[SA.literal(index).label(GA.RETENTION_INDEX)])
            for index in indices
        ]
        alias = self.adapter.get_retention_alias()
        cte = SA.union_all(*selects).cte()
        if alias:
            return cte.alias(alias)
        return cte

    def _get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> SA_F.Function:
        return self.adapter.get_dynamic_datetime_interval(
            field_ref, value_field_ref, time_group, multiplier, adding
        )
