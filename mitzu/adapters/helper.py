from __future__ import annotations

import ast
import base64
import itertools
import json
import pickle
import re
import zlib
from datetime import date, datetime
from typing import (
    Any,
    Dict,
    Iterator,
    List,
    Optional,
    Set,
    Tuple,
    Union,
)

import boto3
import pandas as pd
from sqlalchemy import Table
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.functions import FunctionElement

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu import configs
from mitzu.helper import (
    GROUP_MISSING_VALUE,
    JOURNEY_SEPARATOR,
    NO_GROUP,
    STRING_SEPARATOR,
    create_unique_id,
    flatten_dict,
)
from mitzu.logger import LOGGER


def select_agg_value_column_for_cohort(
    metric_type: M.MetricType, cohort_type: M.CohortType = M.CohortType.DYNAMIC
) -> str:
    if (
        metric_type in (M.MetricType.RETENTION, M.MetricType.CONVERSION)
        and cohort_type == M.CohortType.DYNAMIC
    ):
        # we only have one agg_value col with RETURN_UNIQUE_USERS aggregation
        return f"{GA.AGG_VALUE_COL}_{1}"

    return GA.AGG_VALUE_COL


def str_to_datetime(val: Optional[Union[str, pd.Timestamp]]) -> Optional[datetime]:
    if val is None:
        return None
    if type(val) == str:
        return datetime.fromisoformat(val)
    elif type(val) == pd.Timestamp:
        return val.to_pydatetime()
    elif type(val) == date:
        return datetime(val.year, val.month, val.day)
    raise ValueError(f"invalid datetime type: {type(val)}")


def dataframe_str_to_datetime(pdf: pd.DataFrame, column: str) -> pd.DataFrame:
    if column in pdf.columns:
        pdf[column] = pdf[column].apply(str_to_datetime)
    return pdf


def normalize_column_name(val: str) -> str:
    return re.sub("[^0-9a-zA-Z]+", "_", val)


def pdf_string_json_array_to_array(pdf: pd.DataFrame) -> pd.DataFrame:
    for col in pdf.columns:
        if col not in [GA.EVENT_NAME_ALIAS_COL, GA.FINAL_DIM_PRIMARY_KEY_COL]:
            pdf[col] = pdf[col].apply(
                lambda val: json.loads(val) if type(val) == str else val
            )
    return pdf


def string_array_to_array(pdf: pd.DataFrame) -> pd.DataFrame:
    """
    Created to support STATIC cohort queries.
    We use this function to convert the string arrays that return from specific DWHs.
    This ensures that we have arrays instead of strings in the _agg_value column.
    """
    if "_agg_value" in pdf.columns and all(
        isinstance(val, str) for val in pdf._agg_value
    ):
        pdf._agg_value = pdf._agg_value.apply(ast.literal_eval)
    return pdf


def create_type_mapping(
    booleans: List[str],
    numbers: List[str],
    strings: List[str],
    datetimes: List[str],
    maps: List[str],
    structs: List[str],
    arrays: List[str],
    others: Optional[List[str]] = None,
) -> Dict[str, M.DataType]:
    type_mapping: Dict[M.DataType, List[str]] = {
        M.DataType.STRING: strings + (others if others else []),
        M.DataType.NUMBER: numbers,
        M.DataType.BOOL: booleans,
        M.DataType.DATETIME: datetimes,
        M.DataType.MAP: maps,
        M.DataType.STRUCT: structs,
        M.DataType.ARRAY: arrays,
    }
    return {val: d_type for d_type, vals in type_mapping.items() for val in vals}


def aware_split(
    string: str,
    delimiter: str = ",",
    maxsplit: int = -1,
    quote: str = "`",
    escaped_quote: str = r"\`",
    open_bracket: str = "<",
    close_bracket: str = ">",
) -> Iterator[str]:
    """
    A split function that is aware of quotes and brackets/parentheses.

    :param string: string to split
    :param delimiter: string defining where to split, usually a comma or space
    :param maxsplit: Maximum number of splits to do. -1 (default) means no limit.
    :param quote: string, either a single or a double quote
    :param escaped_quote: string representing an escaped quote
    :param open_bracket: string, either [, {, < or (
    :param close_bracket: string, either ], }, > or )
    """
    parens = 0
    quotes = False
    i = 0
    if maxsplit < -1:
        raise ValueError(f"maxsplit must be >= -1, got {maxsplit}")
    elif maxsplit == 0:
        yield string
        return
    for j, character in enumerate(string):
        complete = parens == 0 and not quotes
        if complete and character == delimiter:
            if maxsplit != -1:
                maxsplit -= 1
            yield string[i:j]
            i = j + len(delimiter)
            if maxsplit == 0:
                break
        elif character == open_bracket:
            parens += 1
        elif character == close_bracket:
            parens -= 1
        elif character == quote:
            left = j - len(escaped_quote) + 1
            right = j + 1
            if quotes and string[left:right] != escaped_quote:
                quotes = False
            elif not quotes:
                quotes = True
    yield string[i:]


class TablePercentSample(Table):
    __visit_name__ = "table_percent_sample"

    def __init__(
        self,
        table: Table,
        percent: float,
        include_percent: bool = True,
        table_sample_str: str = "TABLESAMPLE SYSTEM",
    ):
        super().__init__()
        self.name = table.name
        self.percent = percent
        self.include_percent = include_percent
        self.table_sample_str = table_sample_str

        self.original = table
        self._columns = table.columns


@compiles(TablePercentSample)
def compile_table_percent_sample(
    element: Any, compiler: Any, **kwargs: Dict[str, Any]
) -> str:  # noqa: F811
    table = compiler.process(element.original, asfrom=True)
    if " AS " in table:
        parts = table.split(" AS ")
        table = parts[0]
        alias = f"AS {parts[1]}"
    else:
        table = element
        alias = ""

    percent = int(element.percent)
    if element.include_percent:
        return f"{table} {element.table_sample_str} ({percent} PERCENT) {alias}"
    else:
        return f"{table} {element.table_sample_str} ({percent}) {alias}"


class LeadIgnoreNulls(FunctionElement):
    name = "lead"


@compiles(LeadIgnoreNulls)
def compile_lead_ignore_nulls(
    element: Any, compiler: Any, **kwargs: Any
) -> str:  # noqa: F811
    compiled_args = ", ".join(
        [compiler.process(arg, **kwargs) for arg in element.clauses]
    )
    return f"LEAD({compiled_args}) IGNORE NULLS"


def _get_values_type(values: Set[Any]) -> Tuple[M.DataType, Set[Any]]:
    if len(values) == 0:
        raise ValueError("Values cannot be empty")
    first_val = None
    tp = None
    for val in values:
        if val is not None:
            first_val = val
            if tp is None:
                tp = type(val)
            elif tp != type(val):  # type: ignore[unreachable]
                return M.DataType.STRING, values

    if first_val is None:
        return M.DataType.STRING, values

    if tp == str:
        try:
            if datetime.fromisoformat(first_val):
                return M.DataType.DATETIME, {datetime.fromisoformat(v) for v in values}
        except Exception:
            return M.DataType.STRING, values
    elif tp in (int, float):
        return M.DataType.NUMBER, values
    elif tp == bool:
        return M.DataType.BOOL, values
    elif tp == datetime:
        return M.DataType.DATETIME, values
    elif tp in (list, tuple, set):
        # Todo handle ARRAY SUBTYPES
        return M.DataType.ARRAY, set(itertools.chain.from_iterable(list(values)))

    return M.DataType.STRING, values


def convert_to_tuple(value: Any) -> Tuple:
    return tuple(
        convert_to_tuple(val) if type(val) in (list, tuple, set) else val
        for val in value
    )


def expand_map_event_field(
    field: M.Field,
    json_values: List[str],
    event_name_path: M.EventNamePath,
    compressed: bool,
) -> List[M.EventFieldDef]:
    res_vals: Dict[M.EventFieldPath, set[Any]] = {}
    now = datetime.now()
    for val in json_values:
        try:
            if type(val) == str:
                if compressed:
                    dict_val = json.loads(zlib.decompress(base64.b64decode(val)))
                else:
                    dict_val = json.loads(val)
            else:
                dict_val = val

            if dict_val is None:
                continue

            flat_dict = flatten_dict(
                dict_val, separator=STRING_SEPARATOR, flatten_lists=False
            )
            for key, value in flat_dict.items():
                try:
                    efp = M.EventFieldPath(
                        source_table=event_name_path.source_table,
                        event_name=event_name_path.event_name,
                        field_name=key.replace(STRING_SEPARATOR, "."),
                    )
                    if efp not in res_vals.keys():
                        res_vals[efp] = set()

                    if type(value) in (list, tuple, set):
                        value = convert_to_tuple(value)

                    if value is not None:
                        res_vals[efp].add(value)
                except Exception as exc:
                    LOGGER.opt(exception=exc).bind(
                        event_name_path=event_name_path,
                        field_name=field.name,
                        value=str(val),
                        field_value=str(value),
                        field_key=key,
                    ).warning("failed to parse map field value")

        except Exception as exc:
            LOGGER.opt(exception=exc).bind(
                event_name_path=event_name_path, field_name=field.name, value=str(val)
            ).warning("failed to parse map field")
    res: List[M.EventFieldDef] = []

    for efp, values in res_vals.items():
        if len(values) == 0:
            continue

        leaf_type, values = _get_values_type(values)
        fnames = efp.field_name.split(".")
        depth = len(fnames) - 1
        cf = field
        for i, fn in enumerate(fnames):
            cf = M.Field(
                name=fn,
                type=(leaf_type if i == depth else M.DataType.MAP),
                parent=cf,
            )

        res.append(
            M.EventFieldDef(
                field=cf,
                event_name_path=event_name_path,
                custom_values=set(),
                discovered_values=values,
                last_discovered=now,
            )
        )

    return res


def expand_map_dimension_field(
    field: M.Field,
    json_values: List[str],
    dimension_data_table: M.DimensionDataTable,
    compressed: bool,
) -> List[M.DimensionFieldDef]:
    res_vals: Dict[M.DimensionFieldPath, set[Any]] = {}
    now = datetime.now()
    for val in json_values:
        try:
            if type(val) == str:
                if compressed:
                    dict_val = json.loads(zlib.decompress(base64.b64decode(val)))
                else:
                    dict_val = json.loads(val)
            else:
                dict_val = val

            if dict_val is None:
                continue

            flat_dict = flatten_dict(
                dict_val, separator=STRING_SEPARATOR, flatten_lists=False
            )
            for key, value in flat_dict.items():
                try:
                    efp = M.DimensionFieldPath(
                        source_table=dimension_data_table.table_name,
                        field_name=key.replace(STRING_SEPARATOR, "."),
                    )
                    if efp not in res_vals.keys():
                        res_vals[efp] = set()

                    if type(value) in (list, tuple, set):
                        value = tuple(value)

                    if value is not None:
                        res_vals[efp].add(value)
                except Exception as exc:
                    LOGGER.opt(exception=exc).bind(
                        table_name=dimension_data_table.table_name,
                        field_name=field.name,
                        value=str(val),
                        field_value=str(value),
                        field_key=key,
                    ).warning("failed to parse map field value")

        except Exception as exc:
            LOGGER.opt(exception=exc).bind(
                table_name=dimension_data_table.table_name,
                field_name=field.name,
                value=str(val),
            ).warning("failed to parse map field")
    res: List[M.DimensionFieldDef] = []

    for efp, values in res_vals.items():
        if len(values) == 0:
            continue

        leaf_type, values = _get_values_type(values)
        fnames = efp.field_name.split(".")
        depth = len(fnames) - 1
        cf = field
        for i, fn in enumerate(fnames):
            cf = M.Field(
                name=fn,
                type=(leaf_type if i == depth else M.DataType.MAP),
                parent=cf,
            )

        res.append(
            M.DimensionFieldDef(
                field=cf,
                custom_values=set(),
                discovered_values=values,
                last_discovered=now,
                dimension_data_table=dimension_data_table,
            )
        )

    return res


def save_dataframe_for_debugging(project_id: str, df: pd.DataFrame) -> str:
    try:
        target_key = f"{configs.ENVIRONMENT}/{project_id}/{create_unique_id()}.pickle"
        s3_client = boto3.client("s3")
        s3_client.put_object(
            Body=pickle.dumps(df),
            Bucket=configs.DEBUG_DATAFRAME_S3_BUCKET,
            Key=target_key,
        )
        return target_key
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to save dataframe for debugging")
        return "upload-failed"


def bytes_to_human_readable(num_bytes: float) -> str:
    if num_bytes < 0:
        raise ValueError("num_bytes must be non-negative")

    suffixes = ["bytes", "KB", "MB", "GB", "TB"]

    if num_bytes == 0:
        return "0 bytes"

    magnitude = 0
    while num_bytes >= 1024 and magnitude < len(suffixes) - 1:
        num_bytes /= 1024
        magnitude += 1

    # Format the number to two decimal places
    return f"{num_bytes:.1f} {suffixes[magnitude]}"


def cleanup_group_by_col(group_val: Optional[str]) -> Optional[str]:
    if group_val is None:
        return None
    group_val = (
        group_val.replace(JOURNEY_SEPARATOR, "")
        .replace(NO_GROUP, "")
        .replace(GROUP_MISSING_VALUE, "")
    )
    if not group_val:
        return None
    return group_val
