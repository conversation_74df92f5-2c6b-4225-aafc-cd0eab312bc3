from datetime import datetime
from itertools import chain
from typing import Any, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Optional

import sqlalchemy as SA
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.helper as AH
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu.adapters.model import FieldReference
from mitzu.adapters.query_builder.query_builder import (
    QueryBuilder,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.logger import LOGGER


class Indexer:
    def __init__(
        self,
        indexing_context: M.IndexingContext,
        adapter: SQLAlchemyAdapter,
    ) -> None:
        self.indexing_context = indexing_context
        self.adapter = adapter

    def get_event_table_field_enums(
        self,
        event_data_table: M.EventDataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        text_filter: Optional[M.EventFilter] = None,
    ) -> Dict[<PERSON><PERSON>, M.EventDef]:
        self._validate_event_filter_for_discovery(text_filter)
        enums = self._get_enums_from_table(
            event_data_table, fields, event_names, text_filter
        )
        res: Dict[<PERSON><PERSON>, M.Event<PERSON>ef] = {}
        field_defs: List[M.EventFieldDef] = []
        for evt, values in enums.items():
            field_defs = []
            for f in fields:
                field_values = values[f._get_name()]
                if f.type == M.DataType.ARRAY:
                    field_values = self._sanitize_array_values(field_values)

                vals = (
                    [v for v in field_values if v is not None] if field_values else []
                )

                if len(vals) > 0 and isinstance(vals[0], list):
                    try:
                        vals = self._merge_array_values(vals, event_data_table, f)
                    except ValueError:
                        continue

                if f.type == M.DataType.MAP:
                    evt_name_path = M.EventNamePath(event_data_table.table_name, evt)
                    map_fields = AH.expand_map_event_field(
                        field=f,
                        json_values=vals,
                        event_name_path=evt_name_path,
                        compressed=self.indexing_context.compress_map_type,
                    )
                    max_cardinality = self.indexing_context.max_map_key_cardinality
                    map_fields = map_fields[:max_cardinality]
                    field_defs.extend(map_fields)
                else:
                    vals = self._post_dwh_text_filter(text_filter, vals)
                    if len(vals) >= self.indexing_context.max_enum_cardinality:
                        # Too many values, we should store them only if required
                        vals = (
                            []
                            if not self.indexing_context.keep_enums_for_high_cardinality
                            else vals[: self.indexing_context.max_enum_cardinality]
                        )
                    field_defs.append(
                        M.EventFieldDef(
                            event_name_path=M.EventNamePath(
                                event_data_table.table_name, evt
                            ),
                            field=f,
                            discovered_values=set(vals),
                            custom_values=set(),
                            last_discovered=datetime.now(),
                        )
                    )

            event_def = M.EventDef(
                event_name=evt,
                fields=field_defs,
                event_data_table=event_data_table,
                last_discovered=datetime.now(),
            )
            res[event_def.event_name_path] = event_def
        return res

    def _get_enums_from_table(
        self,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        text_filter: Optional[M.EventFilter] = None,
    ) -> Dict[str, Dict[str, Any]]:

        chunks = self.indexing_context.discovery_rows_chunk_size or 1
        enums: Dict[str, Dict[str, Any]] = {}
        for step in range(0, chunks):
            if isinstance(data_table, M.EventDataTable):
                offset = step * self.indexing_context.min_property_sample_size
            else:
                offset = step * self.indexing_context.min_dim_property_sample_size

            root_column_name = [f._get_name().split(".")[0] for f in fields]
            dataset_cte = self.get_dataset_discovery_cte(
                data_table, text_filter, root_column_name, event_names, offset=offset
            )
            res: Dict[str, Dict[str, Any]] = self.adapter._get_field_values_df(  # type: ignore[assignment]
                dataset_cte,
                data_table,
                fields,
                event_names,
                self.indexing_context,
            ).to_dict(
                "index"
            )
            if len(enums) == 0:
                # We are not sure that the _get_field_values_df returns dicts/list/tuples
                # With chunks = 1 or None, we skip the merge operation
                enums = res
            else:
                enums = Indexer._merge_dicts(enums, res)

        return enums

    def get_dimension_fields(
        self,
        dimension_data_table: M.DimensionDataTable,
        fields: List[M.Field],
        text_filter: Optional[M.EventFilter] = None,
    ) -> List[M.DimensionFieldDef]:
        self._validate_event_filter_for_discovery(text_filter)

        enums = self._get_enums_from_table(
            dimension_data_table, fields, None, text_filter
        )
        res: List[M.DimensionFieldDef] = []
        for _, values in enums.items():
            field_defs: List[M.DimensionFieldDef] = []
            for f in fields:
                field_values = values[f._get_name()]
                vals = (
                    [v for v in field_values if v is not None] if field_values else []
                )

                if len(vals) > 0 and isinstance(vals[0], list):
                    try:
                        vals = self._merge_array_values(vals, dimension_data_table, f)
                    except ValueError:
                        continue

                if f.type == M.DataType.MAP:
                    map_fields = AH.expand_map_dimension_field(
                        field=f,
                        json_values=vals,
                        dimension_data_table=dimension_data_table,
                        compressed=self.indexing_context.compress_map_type,
                    )
                    max_cardinality = self.indexing_context.max_map_key_cardinality
                    map_fields = map_fields[:max_cardinality]
                    field_defs.extend(map_fields)
                else:
                    vals = self._post_dwh_text_filter(text_filter, vals)
                    if len(vals) >= self.indexing_context.max_enum_cardinality:
                        # Too many values, we should store them only if required
                        vals = (
                            []
                            if not self.indexing_context.keep_enums_for_high_cardinality
                            else vals[: self.indexing_context.max_enum_cardinality]
                        )
                    field_defs.append(
                        M.DimensionFieldDef(
                            field=f,
                            discovered_values=set(vals),
                            custom_values=set(),
                            last_discovered=datetime.now(),
                            dimension_data_table=dimension_data_table,
                        )
                    )

            res.extend(field_defs)
        return res

    def _validate_event_filter_for_discovery(
        self, text_filter: Optional[M.EventFilter]
    ) -> None:
        if (
            text_filter
            and type(text_filter.right) != str
            and not type(text_filter.operator)
            in (M.Operator.LIKE, M.Operator.ARRAY_LIKE)
        ):
            raise ValueError(
                "Filter value must be a string and operator must be LIKE, ARRAY_LIKE"
            )

    def _merge_array_values(
        self,
        vals: List[Any],
        dt: M.DataTable,
        field: M.Field,
    ) -> List[Any]:
        vals = list(chain.from_iterable(vals))
        if len(vals) > 0 and (isinstance(vals[0], dict) or isinstance(vals[0], list)):
            LOGGER.bind(
                table_name=dt.get_full_name(),
                field_name=field._get_name(),
            ).warning(f"complex type in array is not supported {len(vals)}")
            raise ValueError("Complex type in array is not supported")

        return vals

    def _sanitize_array_values(self, arr: Optional[List]) -> Optional[List]:
        if arr:
            for val in arr:
                if not isinstance(val, Hashable):
                    return arr
            return list(set(arr))

        return arr

    def _post_dwh_text_filter(
        self,
        text_filter: Optional[M.EventFilter],
        vals: List[Any],
    ) -> List[Any]:
        if text_filter:
            filter_str = str(text_filter.right)
            # If text filter is applied we must filter the possible array values in python as well.
            # The DWH returns entire arrays of any value.
            if self.indexing_context.case_insensetive_filtering:
                vals = [v for v in vals if filter_str.lower() in str(v).lower()]
            else:
                vals = [v for v in vals if filter_str in str(v)]
        return vals

    def get_dataset_discovery_cte(
        self,
        data_table: M.DataTable,
        text_filter: Optional[M.EventFilter],
        columns_filter: Optional[List[str]] = None,
        event_names: Optional[List[str]] = None,
        offset: Optional[int] = None,
    ) -> SEL.CTE:
        if offset == 0:
            offset = None

        sa_table = self.adapter.get_table(data_table, auto_load=True)
        other_filters: FieldReference = SA.literal(True)
        if text_filter is not None:
            qb = QueryBuilder(self.adapter)
            other_filters = other_filters & qb.get_simple_segment_condition(
                table=sa_table,
                filter=text_filter,
                collection_cte_value_cols={},  # cohorts are not supported in discovery
            )

        timestamp_filter: FieldReference = SA.literal(True)
        row_number: FieldReference = SA.literal(1)

        start_date = (
            self.indexing_context.end_dt
            - self.indexing_context.lookback_window.to_relative_delta()
        )
        end_date = self.indexing_context.end_dt

        if isinstance(data_table, M.EventDataTable):
            dt_field = self.adapter.get_event_time_reference(data_table, sa_table)
            event_name_field = self.adapter.get_event_name_field(data_table, sa_table)  # type: ignore[arg-type]
            timestamp_filter = (
                dt_field >= self.adapter.correct_timestamp(start_date)
            ) & (dt_field <= self.adapter.correct_timestamp(end_date))
            # As discovery is always sampled, it doesn't matter if we filter on exact dates.
            if data_table.date_partition_field is not None:
                timestamp_filter = (
                    timestamp_filter
                    & self.adapter.get_date_partition_filter(
                        data_table,
                        sa_table,  # type: ignore[arg-type]
                        start_date,
                        end_date,
                        None,
                    )
                )

            if not data_table.is_single_event_table:
                if data_table.event_name_field is None:
                    raise ValueError(
                        f"event_name_field is not set for multi event table {data_table.schema}.{data_table.table_name}"
                    )
                if (
                    columns_filter
                    and data_table.event_name_field.root_name not in columns_filter
                ):
                    columns_filter.append(data_table.event_name_field.root_name)

                if (
                    event_names
                    and len(event_names) == 1
                    and not self.indexing_context.data_scrambling
                ):
                    row_number = SA.literal(1)
                else:
                    row_number = SA.func.row_number().over(
                        partition_by=event_name_field,
                        order_by=(
                            self.adapter._get_random_function()
                            if self.indexing_context.data_scrambling
                            else SA.literal(True)
                        ),
                    )
                other_filters = other_filters & event_name_field.is_not(None)
                if event_names:
                    other_filters = other_filters & (event_name_field.in_(event_names))

                other_filters = other_filters & (
                    SA.cast(event_name_field, self.adapter._get_text_type()) != ""
                )

        cols = list(sa_table.columns.values())
        if columns_filter:
            cols = [i for i in cols if any(i.name == field for field in columns_filter)]

        raw_select = SAH.select(
            columns=[
                *cols,
                row_number.label("rn"),
            ],
            whereclause=timestamp_filter & other_filters,
            select_from=sa_table,
        )

        if (
            isinstance(data_table, M.EventDataTable)
            and not data_table.is_single_event_table
        ):
            raw_select_sub_query = raw_select.subquery()
            sampling_condition = (
                raw_select_sub_query.columns["rn"]
                <= self.indexing_context.min_property_sample_size
            )

            return SAH.select(
                columns=raw_select_sub_query.columns.values(),
                whereclause=sampling_condition,
            ).cte()
        elif isinstance(data_table, M.DimensionDataTable):
            return (
                raw_select.limit(self.indexing_context.min_dim_property_sample_size)
                .offset(offset)
                .cte()
            )
        else:
            return (
                raw_select.limit(self.indexing_context.min_property_sample_size)
                .offset(offset)
                .cte()
            )

    def _get_start_date(self) -> datetime:
        return (
            self.adapter.end_date
            - self.indexing_context.lookback_window.to_relative_delta()
        )

    @staticmethod
    def _merge_dicts(
        d1: Dict[str, Dict[str, Any]],
        d2: Dict[str, Dict[str, Any]],
    ) -> Dict[str, Dict[str, Any]]:
        """
        Merged two dicts, considering also Lists, Tuples, Sets and any scalar or string as values
        """

        def merge_value(
            v1: Any,
            v2: Any,
        ) -> Any:  # Should be refactored
            if v1 is None:
                return v2
            if v2 is None:
                return v1

            if isinstance(v1, list) and isinstance(v2, list):
                return v1 + v2
            elif isinstance(v1, tuple) and isinstance(v2, tuple):
                return v1 + v2
            elif isinstance(v1, set) and isinstance(v2, set):
                return v1 | v2
            elif isinstance(v1, dict) and isinstance(v2, dict):
                return Indexer._merge_dicts(v1, v2)
            raise ValueError(f"Cannot merge {type(v1)} and {type(v2)}")

        merged_dict: Dict[str, Dict[str, Any]] = d1
        for key, value in d2.items():
            merged_dict[key] = merge_value(d1.get(key, None), value)
        return merged_dict
