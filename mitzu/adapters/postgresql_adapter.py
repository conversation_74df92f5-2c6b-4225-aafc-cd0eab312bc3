from __future__ import annotations

from datetime import date, datetime
from typing import Any, Dict, Iterable, Optional, Union

import pandas as pd
import psycopg2
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu.adapters.helper import create_type_mapping
from mitzu.adapters.model import (
    FieldReference,
    fix_col_index,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import CacheKeyType
from mitzu.helper import STRING_SEPARATOR
from mitzu.logger import LOGGER

# https://www.postgresql.org/docs/16/datatype.html
# fmt: off
TYPE_NAME_PREFIX_MAPPING = create_type_mapping(
    booleans=["boolean", "bool"],
    numbers=[
        "bigint", "int", "bigserial", "serial", "bit", "double", "float",
        "integer", "smallint", "smallserial", "numeric", "decimal", "real",
    ],
    strings=["character", "text", "uuid", "varchar"],
    datetimes=["date", "interval", "time"],
    maps=["json", "jsonb"],
    structs=[],
    arrays=["array"],
    others=[
        "box", "bytea", "cidr", "circle", "inet", "line", "lseg",
        "macaddr", "money", "path", "pg_lsn", "pg_snapshot", "point",
        "polygon", "tsquery", "tsvector", "txid_snapshot", "xml",
        # support custom types
        "user-defined",
    ],
)
# fmt: on


class PostgresqlAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, psycopg2.errors.DivisionByZero):
            return GA.DivisionByZeroUserError(exc)

        if isinstance(exc.orig, psycopg2.errors.InvalidTextRepresentation):
            return GA.NotNumericFieldUserError(exc)

        orig_error = str(exc.orig)
        if "cannot cast" in orig_error and "to double precision" in orig_error:
            return GA.NotNumericFieldUserError(exc)

        return None

    def should_cast_cols_in_union(self) -> bool:
        return True

    def _parse_type_name(self, type_name: str) -> M.DataType:
        for type_prefix, data_type in TYPE_NAME_PREFIX_MAPPING.items():
            if type_name.lower().startswith(type_prefix) or type_name.lower().lstrip(
                "_"
            ).startswith(
                type_prefix
            ):  # array item type
                return data_type

        LOGGER.error(f"Unknown postgresql datatype: {type_name}")
        return M.DataType.STRING

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.to_timestamp(field)

    def _get_table_columns_df(
        self, schema_table_requests: Iterable[GA.SchemaTableRequest]
    ) -> pd.DataFrame:
        conditions = []
        for req in schema_table_requests:
            conditions.append(
                f"""
                (table_schema = '{req.schema}' AND table_name = '{req.table}')"""
            )

        if len(conditions) == 0:
            return pd.DataFrame(
                {
                    "table_schema": [],
                    "table_name": [],
                    "column_name": [],
                    "data_type": [],
                    "udt_name": [],
                }
            )

        # information schema is not loaded by sql alchemy, we need to create the query from scratch
        query = SA.text(
            f"""
            SELECT table_schema, table_name, column_name, data_type, udt_name
            FROM information_schema.columns
            WHERE {" OR ".join(conditions)}
            """
        )
        return self.execute_query(query)

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        result: Dict[str, M.DatabaseSchema] = {
            s.schema: M.DatabaseSchema({}) for s in schema_requests
        }
        (
            result,
            schema_tables_to_fetch,
        ) = self._get_all_table_columns_cache_key_and_object(
            schema_requests, ignore_cache
        )
        df = self._get_table_columns_df(schema_tables_to_fetch)

        for schema, table_name, field_name, d_type, udt_name in zip(
            df["table_schema"],
            df["table_name"],
            df["column_name"],
            df["data_type"],
            df["udt_name"],
        ):
            if table_name not in result[schema].tables:
                result[schema].tables[table_name] = M.DatabaseTable({}, {})

            field_type = self._parse_type_name(d_type)
            field = M.Field(field_name, type=field_type)
            if field:
                result[schema].tables[table_name].add_field(
                    field, udt_name if field_type == M.DataType.ARRAY else d_type
                )

        for schema_table_request in schema_tables_to_fetch:
            cache_key = self._cache.get_key(
                self._connection_object,
                CacheKeyType.TABLE_COLUMNS,
                schema=schema_table_request.schema,
                table_name=schema_table_request.table,
            )

            if (
                schema_table_request.table
                in result[schema_table_request.schema].tables.keys()
            ):
                self._cache_object(
                    cache_key,
                    result[schema_table_request.schema].tables[
                        schema_table_request.table
                    ],
                )

        return result

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        if type(dt) == date:
            return SA.literal(dt.isoformat())

        return super().correct_timestamp(dt)

    def get_field_reference(
        self, field: M.Field, sa_table: Union[SA.sql.TableClause, SEL.CTE]
    ) -> FieldReference:
        if field.parent is not None and field.parent.type == M.DataType.MAP:
            # The ->> operator casts every value to String from the JSONB or JSON type
            # However we need to cast the values back to their original types for correct comparison
            operator = "->>"
            if field.type == M.DataType.STRING:
                postgres_type = "::text"
            elif field.type == M.DataType.NUMBER:
                postgres_type = "::numeric"
            elif field.type == M.DataType.BOOL:
                postgres_type = "::boolean"
            else:
                raise ValueError(
                    f"Unsupported json data type for postgres: {field.type}"
                )
            return SA.literal_column(
                f"({sa_table.name}.{field.parent._get_name()} {operator} '{field.name}'){postgres_type}"  # type: ignore[union-attr]
            )
        return super().get_field_reference(field, sa_table)

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        return field_ref + SA.text(f"interval '{timewindow.value} {timewindow.period}'")

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return field_ref + (val_ref * SA.text(f"interval '1 {time_group}'"))

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return a / SA.func.nullif(b, 0)

    def _get_conv_aggregation(self, metric: M.Metric, cte: SEL.CTE, index: int) -> Any:
        t1 = cte.columns.get(fix_col_index(0, GA.CTE_DATETIME_COL))
        t2 = cte.columns.get(fix_col_index(index, GA.CTE_DATETIME_COL))
        if (
            metric.config.aggregation.type == M.AggType.PERCENTILE_TIME_TO_CONV
            and metric.config.aggregation.conv_param is not None
        ):
            diff = SA.func.extract("EPOCH", t2) - SA.func.extract("EPOCH", t1)
            return self._get_percentile_agg(diff, metric.config.aggregation.conv_param)
        if metric.config.aggregation.type != M.AggType.AVERAGE_TIME_TO_CONV:
            return super()._get_conv_aggregation(metric, cte, index)
        diff = SA.func.extract("EPOCH", t2) - SA.func.extract("EPOCH", t1)
        return SA.func.avg(diff)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.extract(period, end_dt - start_dt)

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return SA.func.percentile_cont(percentile / 100).within_group(col)

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix, collection_cte_value_cols)
        if metric.config.aggregation.type not in M.LIST_USER_AGGS:
            for index in range(1, len(metric.segments) + 1):
                df[f"{GA.AGG_VALUE_COL}_{index}"] = df[
                    f"{GA.AGG_VALUE_COL}_{index}"
                ].astype(float)
        return df

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        if metric.config.aggregation.type not in [
            M.AggType.RETURN_UNIQUE_USERS,
            M.AggType.RETURN_SUBSCRIBERS,
            M.AggType.RETURN_DROP_OFF_USERS,
        ]:
            df[GA.AGG_VALUE_COL] = df[GA.AGG_VALUE_COL].astype(float)
        return df

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> SA_F.Function:
        aggregation = SA.func.array_agg(SA.distinct(field_ref))
        if field.type == M.DataType.ARRAY:
            # psql cannot aggregate empty arrays
            aggregation = aggregation.filter(SA.func.array_length(field_ref, 1) > 0)  # type: ignore[assignment]
        if field.type == M.DataType.MAP:
            # psql cannot distinct json fields, just jsonb fields
            aggregation = SA.func.array_agg(field_ref)
        return SA.func.to_json(aggregation)

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.literal(value).op("=")(SA.func.any(field_ref))

    def _map_key_type_to_field(self, key_type: str) -> Optional[M.Field]:
        key, str_type = key_type.split(STRING_SEPARATOR)
        str_type = str_type.lower()

        if str_type == "string":
            data_type = M.DataType.STRING
        elif str_type == "number":
            data_type = M.DataType.NUMBER
        elif str_type == "boolean":
            data_type = M.DataType.BOOL
        else:
            return None
        return M.Field(key, data_type)

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        sf_type = self._parse_type_name(sa_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=sf_type)])
        return M.Field(
            name=name,
            type=M.DataType.ARRAY,
            sub_fields=sfs,
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        LOGGER.debug(f"Discovering map: {name}")

        if sa_type not in ["jsonb", "json"]:
            raise ValueError(f"Unsupported map type for Postgres adapter: {sa_type}")

        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _is_array_type_supported(self) -> bool:
        return True

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.extract("EPOCH", field_ref) % 2973
        return super().get_field_numeric_value(field_ref, field)
