from __future__ import annotations

from datetime import date, datetime
from typing import (
    Any,
    Dict,
    Iterable,
    List,
    Optional,
    Tuple,
    Union,
    cast,
)

import pandas as pd
import sqlalchemy as SA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from sqlalchemy.dialects import (  # type: ignore[attr-defined]
    registry,
)
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.model as M
import mitzu.vendor.pyathena.datatype as DA_T
import mitzu.vendor.pyathena.error as ATH_EXC
import mitzu.vendor.pyathena.sqlalchemy_athena as SAA
from mitzu.adapters.helper import (
    pdf_string_json_array_to_array,
)
from mitzu.adapters.model import (
    AggregatedField,
    ExecutableType,
    FieldReference,
    SelectType,
    fix_col_index,
    is_select_type,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.cache import CacheKeyType
from mitzu.helper import STRING_SEPARATOR

registry.register("athena", "mitzu.vendor.pyathena.sqlalchemy_athena", "AthenaDialect")


class AthenaAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, ATH_EXC.OperationalError):
            orig_error = str(exc.orig)
            if "INVALID_CAST_ARGUMENT" in orig_error or "TYPE_MISMATCH" in orig_error:
                return GA.NotNumericFieldUserError(exc)

            if (
                "INVALID_VIEW" in orig_error
                or "UnrecognizedClientException" in orig_error
            ):
                return GA.GenericInternalError(exc)

        return None

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
            query = str(
                query.compile(
                    compile_kwargs={"literal_binds": True},
                    dialect=SAA.AthenaDialect(),
                ),
            )
            query = query.replace(
                "%", "%%"
            )  # bugfix for pyathena, which has string formatting

        return super().execute_query(
            query=query, log_error_as_warning=log_error_as_warning
        )

    def _parse_type_name(self, type_name: str) -> M.DataType:
        return self.map_type(DA_T.parse_sqltype(type_name))

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.from_unixtime(field)

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        result: Dict[str, M.DatabaseSchema] = {
            s.schema: M.DatabaseSchema({}) for s in schema_requests
        }
        (
            result,
            schema_tables_to_fetch,
        ) = self._get_all_table_columns_cache_key_and_object(
            schema_requests, ignore_cache
        )

        df = self._get_table_columns_df(schema_tables_to_fetch)

        for schema, table_name, field_name, d_type in zip(
            df["table_schema"], df["table_name"], df["column_name"], df["data_type"]
        ):
            if table_name not in result[schema].tables:
                result[schema].tables[table_name] = M.DatabaseTable({}, {})
            field = self._sqlalchemy_column_to_field(
                DA_T.parse_sqltype(d_type),
                field_name=field_name,
                sa_table=None,
            )
            if field:
                result[schema].tables[table_name].add_field(field, d_type)

        for schema_table_request in schema_tables_to_fetch:
            cache_key = self._cache.get_key(
                self._connection_object,
                CacheKeyType.TABLE_COLUMNS,
                schema=schema_table_request.schema,
                table_name=schema_table_request.table,
            )

            if (
                schema_table_request.table
                in result[schema_table_request.schema].tables.keys()
            ):
                self._cache_object(
                    cache_key,
                    result[schema_table_request.schema].tables[
                        schema_table_request.table
                    ],
                )

        return result

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table=data_table,
            fields=fields,
            event_names=event_names,
            indexing_context=indexing_context,
        )
        return pdf_string_json_array_to_array(df)

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        timeformat = dt.strftime("%Y-%m-%d %H:%M:%S.%f")
        return SA.literal_column(f"timestamp '{timeformat}'")

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        if isinstance(sa_type, DA_T.MAP):
            return M.DataType.MAP
        if isinstance(sa_type, DA_T.STRUCT):
            return M.DataType.STRUCT
        if isinstance(sa_type, SA_T.ARRAY):
            return M.DataType.ARRAY
        return super().map_type(sa_type)

    def _get_struct_field_type(self, struct_type: DA_T.STRUCT, field_path: str) -> Any:
        paths = field_path.split(".")
        struct_fields = dict(self._get_struct_sub_types(struct_type))
        if len(paths) == 1:
            return struct_fields[paths[0]]

        return self._get_struct_field_type(struct_fields[paths[0]], ".".join(paths[1:]))

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> M.Field:
        array_type = DA_T.parse_sqltype(sa_type)

        if isinstance(array_type, DA_T.STRUCT):
            name_parts = name.split(".")
            parent_name = ".".join(name_parts[0:-1])
            field_name = name_parts[-1]
            array_type = self._get_struct_field_type(
                array_type, ".".join(name.split(".")[1:])
            )
            parent = M.Field(
                name=parent_name,
                type=M.DataType.STRUCT,
            )
        else:
            field_name = name
            parent = None

        if not isinstance(array_type, SA_T.ARRAY):
            raise ValueError(f"Cannot parse type as a array type: {sa_type}")

        item_type = self.map_type(array_type.item_type)
        sfs = tuple([M.Field(name=M.ARRAY_TYPE_SUBFIELD_NAME, type=item_type)])
        return M.Field(
            name=field_name,
            type=M.DataType.ARRAY,
            sub_fields=sfs,
            parent=parent,
        )

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        # example sa_type value: 'map(varchar(7), integer)'
        map_type = DA_T.parse_sqltype(sa_type)
        if not isinstance(map_type, DA_T.MAP):
            raise ValueError(f"Cannot parse type as a map type: {sa_type}")

        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        return SA.func.contains(field_ref, value)

    def _get_array_like_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        arr_str = SA.func.array_join(field_ref, STRING_SEPARATOR)
        if self.insight_settings and self.insight_settings.case_insensetive_filtering:
            value = value.lower()
            arr_str = SA.func.lower(arr_str)
        return arr_str.like(f"%{value}%")

    def _get_struct_type(self) -> TypeEngine:
        return DA_T.STRUCT  # type: ignore

    def _get_floating_type(self) -> TypeEngine:
        return DA_T.DOUBLE  # type: ignore

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        return SA.func.date_add(
            timewindow.period.name.lower(),
            timewindow.value,
            field_ref,
        )

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.date_add(time_group.name.lower(), val_ref, field_ref)

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            if metric._agg_param is None or 0 < metric._agg_param > 100:
                raise ValueError(
                    "Conversion percentile parameter must be between 0 and 100"
                )
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return self._get_percentile_agg(
                self.date_diff(t1, t2, "second"), metric._agg_param
            )
        if metric._agg_type == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = cte.columns[fix_col_index(0, GA.CTE_DATETIME_COL)]
            t2 = cte.columns[fix_col_index(index, GA.CTE_DATETIME_COL)]
            return SA.func.avg(self.date_diff(t1, t2, "second"))
        else:
            return super()._get_conv_aggregation(metric, cte, index)

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return a / SA.func.nullif(b, 0)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.date_diff(period, start_dt, end_dt)

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        return SA.func.approx_percentile(col, percentile / 100)

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:

        return AH.LeadIgnoreNulls(
            SA.case(
                (
                    identifier == step,
                    target_col,
                ),
                else_=None,
            )
        )

    def get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return None

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.to_unixtime(field_ref) % 2973
        if field.type == M.DataType.NUMBER:
            return super().get_field_numeric_value(field_ref, field)
        return SA.func.codepoint(SA_EXP.cast(field_ref, SA.VARCHAR(1))) * 31

    def _is_array_type_supported(self) -> bool:
        return True
