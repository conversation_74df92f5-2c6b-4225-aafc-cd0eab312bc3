from __future__ import annotations

import json
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union, cast

import pandas as pd
import pyodbc
import sqlalchemy as SA
import sqlalchemy.dialects.mssql.pyodbc as MSD
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu.adapters.helper import create_type_mapping
from mitzu.adapters.model import (
    ExecutableType,
    FieldReference,
    GroupByFieldReference,
    SelectType,
    is_select_type,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.logger import LOGGER

# https://learn.microsoft.com/en-us/fabric/data-warehouse/data-types
TYPE_NAME_PREFIX_MAPPING = create_type_mapping(
    booleans=[],
    numbers=["bit", "smallint", "int", "bigint", "decimal", "numeric", "float", "real"],
    strings=["char", "varchar"],
    datetimes=["date", "time", "datetime2"],
    maps=[],
    structs=[],
    arrays=[],
    others=["varbinary", "uniqueidentifier"],
)


class FabircAdapter(SQLAlchemyAdapter):  # pragma: no cover
    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, pyodbc.ProgrammingError):
            error_msg = str(exc.orig)
            if "Error converting data type" in error_msg and "to float." in error_msg:
                return GA.NotNumericFieldUserError(exc)

        if isinstance(exc.orig, pyodbc.DataError):
            error_msg = str(exc.orig)
            if (
                "Explicit conversion from data type" in error_msg
                and "to float is not allowed." in error_msg
            ):
                return GA.NotNumericFieldUserError(exc)

        return None

    def _get_connection_url(self, con: M.Connection) -> str:

        driver = "ODBC Driver 18 for SQL Server"
        return (
            f"mssql+pyodbc://{con.user_name}:{con.password}@{con.host}:{con.port}/{con.catalog}"
            f"?driver={driver}"
            "&authentication=ActiveDirectoryPassword"
            "&timeout=120"
            "&Encrypt=yes"
            "&TrustServerCertificate=no"
        )

    def _get_random_function(self) -> SA_F.Function:
        # TSQL RAND is not supported
        return SA.func.ABS(SA.func.CHECKSUM(SA.func.NEWID())).op("%")(1000) / 1000.0  # type: ignore[return-value]

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table,
            fields,
            event_names,
            indexing_context=indexing_context,
        )

        def parse_and_remove_duplicates(data: Any) -> Any:
            values = json.loads(data)
            if isinstance(values, list):
                return list(set(values))
            return values

        for field in df.columns:
            df[field] = df[field].apply(parse_and_remove_duplicates)

        return df

    def _parse_type_name(self, type_name: str) -> M.DataType:
        for type_prefix, data_type in TYPE_NAME_PREFIX_MAPPING.items():
            if type_name.lower().startswith(type_prefix) or type_name.lower().lstrip(
                "_"
            ).startswith(
                type_prefix
            ):  # array item type
                return data_type

        LOGGER.error(f"Unknown fabric datatype: {type_name}")
        return M.DataType.STRING

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        return SA.func.dateadd(
            SA.text("second"), field, SA.text("cast('1970-01-01' as datetime2(6))")
        )

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        return SA.func.JSON_ARRAYAGG(field_ref)

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> SA_F.Function:
        date_part = time_group.name
        if time_group == M.TimeGroup.WEEK:
            date_part = "iso_week"
        return SA.func.datetrunc(SA.text(date_part), field_ref)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.datediff(SA.literal_column(period), end_dt, start_dt)

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> FieldReference:
        return SA.func.dateadd(
            SA.text(timewindow.period.value), timewindow.value, field_ref
        )

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SA.func.datediff(
                SA.literal_column("second"), "1970-01-01 00:00:00", field_ref
            ).cast(SA.BIGINT)
        return super().get_field_numeric_value(field_ref, field)

    def concatenate_strings(self, *fields: FieldReference) -> FieldReference:
        return SA.func.concat(*fields)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> SA_F.Function:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.dateadd(
            SA.text(time_group.name.lower()),
            val_ref,
            field_ref,
        )

    def _column_index_support(self) -> bool:
        return False

    def get_group_by_expression(
        self, field_names: List[str], fields: List[FieldReference]
    ) -> List[GroupByFieldReference]:
        # ignore literal values from the group by columns
        return [field for field in fields if not isinstance(field, ELM.BindParameter)]  # type: ignore[return-value]

    def _get_distinct_on_column(self, field: FieldReference) -> FieldReference:
        # fabric does not support column-level distinct
        return field

    def _get_distinct_on_query(self, query: SEL.Select) -> SEL.Select:
        # fabric supports only query level distinc
        return query.distinct()

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        # compile with literal bind fails on datetimes in Fabric, so we need to compile them by hand
        return SA.literal_column(
            f"cast('{dt.isoformat()}' as datetime2(6))"
        )  # type: ignore[return-value]

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        if metric.config.aggregation.type not in [
            M.AggType.RETURN_UNIQUE_USERS,
            M.AggType.RETURN_SUBSCRIBERS,
        ]:
            df[GA.AGG_VALUE_COL] = df[GA.AGG_VALUE_COL].astype(float)
        return df

    def get_conversion_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> SelectType:
        query = super().get_conversion_select(
            metric, cte_prefix, collection_cte_value_cols
        )
        if metric.config.aggregation.type in [M.AggType.RETURN_UNIQUE_USERS]:
            if not isinstance(query, SEL.Select):
                raise ValueError(f"Query is not a select type {type(query)}")
            return self._get_distinct_on_query(query)
        return query

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix, collection_cte_value_cols)
        if metric.config.aggregation.type not in M.LIST_USER_AGGS:
            for index in range(1, len(metric.segments) + 1):
                df[f"{GA.AGG_VALUE_COL}_{index}"] = df[
                    f"{GA.AGG_VALUE_COL}_{index}"
                ].astype(float)
        return df

    def get_retention_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> SelectType:
        query = super().get_retention_select(
            metric, cte_prefix, collection_cte_value_cols
        )
        if metric.config.aggregation.type in [M.AggType.RETURN_UNIQUE_USERS]:
            if not isinstance(query, SEL.Select):
                raise ValueError(f"Query is not a select type {type(query)}")
            return self._get_distinct_on_query(query)
        return query

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_retention_df(metric, cte_prefix, collection_cte_value_cols)
        if metric.config.aggregation.type not in [
            M.AggType.RETURN_UNIQUE_USERS,
            M.AggType.RETURN_SUBSCRIBERS,
        ]:
            df[GA.AGG_VALUE_COL] = df[GA.AGG_VALUE_COL].astype(float)
        return df

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
            query = str(
                query.compile(
                    compile_kwargs={"literal_binds": True},
                    dialect=MSD.MSDialect_pyodbc(),
                )
            )

        return super().execute_query(query, log_error_as_warning)
