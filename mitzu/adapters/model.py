from __future__ import annotations

from dataclasses import dataclass, replace
from datetime import datetime
from typing import Dict, List, Optional, Union

import sqlalchemy as SA
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.model as M


def fix_col_index(index: int, col_name: str) -> str:
    return f"{col_name}_{index}"


class TooManyDataPointsError(ValueError):
    pass


class NoDataError(ValueError):
    pass


class MissingDimensionForeignKeyError(ValueError):
    def __init__(self, event_table_name: str):
        super().__init__(
            f"Foreign key id field is not configured for the event table {event_table_name}"
        )
        self.event_table_name = event_table_name


COLUMN_NAME_REPLACE_STR = "_cnrsptr_"
SPECIAL_CHARACTER_REPLACE_STR = "_backtick_"
COLUMN_NAME_INDEX_SEPARATOR = "_idxsptr_"
MAX_STRING_VALUE_LENGHT = 300
DEBUG_DF_TTL = 8 * 60 * 60

FieldReference = Union[
    SA.Column,
    ELM.Label,
    ELM.ColumnClause,  # eg. SA.literal_column(...)
    ELM.ColumnElement,  # eg. table.columns[...]
    ELM.BindParameter[TypeEngine[None]],  # eg. SA.literal(None)
]

GroupByFieldReference = Union[
    ELM.BindParameter[TypeEngine[int]],  # eg. SA.literal(12)
    ELM.TextClause,  # eg. SA.text(..)
    FieldReference,
]

AggregatedField = Union[
    SA_F.Function,
    ELM.ColumnElement,
]

FilterCondition = Union[
    ELM.BindParameter[TypeEngine[bool]],
    ELM.ColumnElement,
    ELM.BooleanClauseList,
]

ExecutableType = Union[SEL.Select, SEL.CompoundSelect, ELM.TextClause, str]
SelectType = Union[SEL.Select, SEL.CompoundSelect]


def is_select_type(query: ExecutableType) -> bool:
    return not (isinstance(query, str) or isinstance(query, ELM.TextClause))


SAMPLED_SOURCE_CTE_NAME = "sampled_source"

SIMPLE_TYPE_MAPPINGS = {
    SA_T.Numeric: M.DataType.NUMBER,
    SA_T.Float: M.DataType.NUMBER,
    SA_T.Integer: M.DataType.NUMBER,
    SA_T.Boolean: M.DataType.BOOL,
    SA_T.NullType: M.DataType.STRING,
    SA_T.DateTime: M.DataType.DATETIME,
    SA_T.TIMESTAMP: M.DataType.DATETIME,
    SA_T.DATETIME: M.DataType.DATETIME,
    SA_T.Date: M.DataType.DATETIME,
    SA_T.DATE: M.DataType.DATETIME,
    SA_T.Time: M.DataType.DATETIME,
    SA_T.String: M.DataType.STRING,
    SA_T.ARRAY: M.DataType.ARRAY,
    SA_T.JSON: M.DataType.MAP,
}


@dataclass(frozen=True)
class SegmentContext:
    start_date: datetime
    end_date: datetime
    is_break_down_by_subsegments: bool
    dimension_tables: Dict[str, SA.Table]


@dataclass(frozen=True)
class TableSegment:
    context: SegmentContext
    dimension_tables: Dict[str, SA.Table]
    extra_columns: Dict[str, FieldReference]
    events: List[M.EventDef]


@dataclass(frozen=True)
class SingleTableSegment(TableSegment):
    table_ref: SA.Table
    where_clause: FilterCondition
    event_data_table: M.EventDataTable
    segment_index: int
    subsegment_index: int
    custom_event_key: Optional[str]

    def with_subsegment_index(self, new_subsegment_index: int) -> SingleTableSegment:
        return replace(self, subsegment_index=new_subsegment_index)


@dataclass(frozen=True)
class MultiTableSegment(TableSegment):
    left: TableSegment
    right: TableSegment


@dataclass(frozen=True)
class DimensionTableRefs:
    sa_table: SA.Table
    dimension_data_table: M.DimensionDataTable
