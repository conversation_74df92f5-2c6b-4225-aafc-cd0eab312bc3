from __future__ import annotations

import json
from abc import ABC
from dataclasses import dataclass
from datetime import datetime
from enum import Enum, auto
from typing import Any, Dict, Iterable, List, Optional

import pandas as pd
from sqlalchemy.sql.selectable import Select

import mitzu.model as M
from mitzu.adapters.model import (
    ExecutableType,
    FieldReference,
)

# Final Select Columns
EVENT_NAME_ALIAS_COL = "_event_name"
DATETIME_COL = "_datetime"
RETENTION_INDEX = "_ret_index"
EVENT_INDEX_COL = "_event_index"
GROUP_COL = "_group"
AGG_VALUE_COL = "_agg_value"
USER_COUNT_COL = "_user_count"
FINAL_DIM_PRIMARY_KEY_COL = "___dim_id"

REV_DT = "_rev_dt"

# CTE Colmns
CTE_USER_ID_ALIAS_COL = "_cte_user_id"
CTE_DATETIME_COL = "_cte_datetime"
CTE_GROUP_COL = "_cte_group"
CTE_EVENT_PROPERTY_COL = "_cte_event_property"
CTE_ROW_NUMBER = "_cte_row_number"
CTE_SAMPLING_ROW_NUMBER = "_cte_sampling_row_number"
CTE_HOLDING_CONSTANT_COL = "_cte_holding_constant"
CTE_SEGMENT_IDENTIFIER_COL = "_cte_segment_identifier"
CTE_PARTITION_COL = "_cte_partition"

CTE_REV_PERIOD_START = "_cte_rev_period_start"
CTE_REV_PERIOD_END = "_cte_rev_period_end"
CTE_REV_AMOUNT = "_cte_rev_amount"

RETENTION_GROUP = "retention_group"
SUBSEGMENT_INDEX = "_subsegment_index"
MAX_ROW_LIMIT = 20000


class QueryState(Enum):
    RUNNING = auto()
    FAILED = auto()
    QUEUED = auto()
    FINISHED = auto()
    UNKNOWN = auto()
    CANCELLED = auto()


class QueryType(Enum):
    INDEXING = "indexing"
    INSIGHT = "insight"
    DASHBOARD = "dashboard"
    PUBLIC_DASHBOARD = "public_dashboard"
    QUERY_INFO = "query_info"
    COHORT = "cohort"
    CANCEL = "cancel"


@dataclass(frozen=True)
class DryRunResult:
    bytes_processed: int
    bytes_billed: int
    duration_seconds: Optional[float]


@dataclass(frozen=True)
class QueryInfo:
    query_id: str
    owner: str
    state: QueryState
    created_at: datetime
    source_url: str
    query_type: Optional[
        QueryType
    ]  # Optional just for the backward compatibility for a while
    statistics: Optional[Dict] = None

    def to_dict(self) -> dict:
        return {
            "query_id": self.query_id,
            "owner": self.owner,
            "state": self.state.name,
            "created_at": self.created_at,
            "source_url": self.source_url,
            "query_type": self.query_type.value if self.query_type else None,
            "statistics": self.statistics,
        }


@dataclass(frozen=True)
class ClusterInfo:
    mitzu_queries: Dict[str, QueryInfo]
    non_mitzu_queries: Dict[QueryState, int]


@dataclass(frozen=True)
class AdapterContext:  # pragma: no cover
    query_id: str
    user_id: str
    org_name: str
    email: str
    project_name: str
    url: str
    is_debug_enabled: bool
    query_type: QueryType

    def get_query_context_json(self) -> str:
        return json.dumps(
            {
                "user_id": self.user_id,
                "email": self.email,
                "project_name": self.project_name,
                "organisation": self.org_name,
                "source_url": self.url,
                "query_id": self.query_id,
                "created_at": datetime.now().isoformat(),
                "query_type": self.query_type.value,
            }
        )


@dataclass(frozen=True)
class SchemaTableRequest:  # pragma: no cover
    schema: str
    table: str

    @classmethod
    def tables_from_schema(
        cls, schema: str, table_names: Iterable[str]
    ) -> Iterable[SchemaTableRequest]:
        return [SchemaTableRequest(schema, table_name) for table_name in table_names]


class GenericDatasetAdapter(ABC):  # pragma: no cover
    def __init__(
        self,
        end_date: datetime,
        adapter_context: Optional[AdapterContext] = None,
    ):
        self.end_date = end_date
        self.adapter_context = adapter_context

    def execute_query(self, query: ExecutableType) -> pd.DataFrame:
        raise NotImplementedError()

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        """Returns physical columns including structs for a table"""
        raise NotImplementedError()

    def list_fields(
        self,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> List[M.Field]:
        """Returns all fields including structs and map keys for an Event Data Table or Dimension table
        It requires running SQL query for Map type discovery
        """
        raise NotImplementedError()

    def list_schemas(self) -> List[str]:
        raise NotImplementedError()

    def list_tables(self, schema: str) -> List[str]:
        raise NotImplementedError()

    def get_sql(self, metric: M.Metric) -> str:
        if metric.type == M.MetricType.CONVERSION:
            return self._get_conversion_sql(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        if metric.type == M.MetricType.SEGMENTATION:
            return self._get_segmentation_sql(metric, collection_cte_value_cols=None)
        if metric.type == M.MetricType.RETENTION:
            return self._get_retention_sql(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        if metric.type == M.MetricType.JOURNEY:
            return self._get_conversion_sql(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        raise ValueError(f"Unknown metric type: {metric.type}")

    def get_df(self, metric: M.Metric) -> pd.DataFrame:
        if metric.type == M.MetricType.CONVERSION:
            df = self._get_conversion_df(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        elif metric.type == M.MetricType.SEGMENTATION:
            df = self._get_segmentation_df(metric, collection_cte_value_cols=None)
        elif metric.type == M.MetricType.RETENTION:
            df = self._get_retention_df(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        elif metric.type == M.MetricType.JOURNEY:
            df = self._get_conversion_df(
                metric, cte_prefix=None, collection_cte_value_cols=None
            )
        else:
            raise ValueError(f"Unknown metric type: {metric.type}")

        return df

    def get_users_sql(
        self, metric: M.Metric, dim_field_defs: List[M.DimensionFieldDef]
    ) -> str:
        raise NotImplementedError()

    def get_users_df(
        self, metric: M.Metric, dim_field_defs: List[M.DimensionFieldDef]
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def get_single_dimensions_sql(
        self, dim_id: Any, dim_field_defs: List[M.DimensionFieldDef]
    ) -> str:
        raise NotImplementedError()

    def get_single_dimension_df(
        self, dim_id: Any, dim_field_defs: List[M.DimensionFieldDef]
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def search_dimensions_sql(
        self, dimensions_search_query: M.DimensionsSearchQuery
    ) -> str:
        raise NotImplementedError()

    def search_dimensions_df(
        self, dimensions_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def _get_conversion_sql(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        raise NotImplementedError()

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def _get_segmentation_sql(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        raise NotImplementedError()

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def _get_retention_sql(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        raise NotImplementedError()

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def test_connection(self) -> None:
        raise NotImplementedError()

    def cancel_query(self, query_id: str) -> None:
        raise NotImplementedError()

    def get_cluster_info(self) -> ClusterInfo:
        raise NotImplementedError()

    def is_cluster_info_supported(self) -> bool:
        return False

    def fix_segment_queries_for_union(
        self, segment_queries: List[Select]
    ) -> List[Select]:
        return segment_queries

    def count_dimension_sql(self, user_search_query: M.DimensionsSearchQuery) -> str:
        raise NotImplementedError()

    def count_dimension_collection(self, collection: M.DimensionCollection) -> int:
        raise NotImplementedError()

    def get_dimension_cohort_df(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        raise NotImplementedError()

    def dry_run_sql(self, query_str: str) -> Optional[DryRunResult]:
        raise NotImplementedError()

    def supports_dry_run(self) -> bool:
        raise NotImplementedError()


class MetricConfigUserError(Exception):
    """
    This is raised when the metric makes no sense and therefore
    the datawarehouse fails to query the data, eg. division by zero in a formula,
    calculating the average of varchars, etc...
    """


class DivisionByZeroUserError(MetricConfigUserError):
    pass


class NotNumericFieldUserError(MetricConfigUserError):
    pass


class QueryKilledError(Exception):
    pass


class GenericInternalError(Exception):
    pass


class DWHServerError(Exception):
    pass
