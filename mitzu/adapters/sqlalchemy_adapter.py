from __future__ import annotations

import json
import traceback
import urllib.parse as P
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from typing import (
    Any,
    Dict,
    Iterable,
    List,
    Optional,
    Sequence,
    SupportsInt,
    Tuple,
    Union,
    cast,
)

import pandas as pd
import sqlalchemy as SA
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL
import sqlalchemy.sql.sqltypes as SA_T
import sqlparse
from sqlalchemy.orm import aliased
from sqlalchemy.sql.type_api import TypeEngine

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.adapters.sa_helper as SAH
import mitzu.model as M
from mitzu import configs
from mitzu.adapters.model import (
    COLUMN_NAME_INDEX_SEPARATOR,
    COLUMN_NAME_REPLACE_STR,
    SAMPLED_SOURCE_CTE_NAME,
    SIMPLE_TYPE_MAPPINGS,
    SPECIAL_CHARACTER_REPLACE_STR,
    AggregatedField,
    ExecutableType,
    FieldReference,
    FilterCondition,
    GroupByFieldReference,
    NoDataError,
    SelectType,
    SingleTableSegment,
    TableSegment,
    fix_col_index,
    is_select_type,
)
from mitzu.adapters.query_builder.conversion import (
    ConversionQueryBuilder,
)
from mitzu.adapters.query_builder.retention import (
    RetentionQueryBuilder,
)
from mitzu.adapters.query_builder.search_dimensions import (
    SearchDimensionsQueryBuilder,
)
from mitzu.adapters.query_builder.segmentation import (
    SegmentationQueryBuilder,
)
from mitzu.adapters.query_builder.single_dimension import (
    SingleDimensionQueryBuilder,
)
from mitzu.cache import CacheKeyType, MitzuCache
from mitzu.helper import STRING_SEPARATOR
from mitzu.logger import LOGGER
from mitzu.model import DimensionFieldDef, Metric


class SQLAlchemyAdapterError(Exception):
    pass


class MissingTableField(Exception):
    def __init__(self, field_name: str) -> None:
        super().__init__(f"'{field_name}' field does not exist")


@dataclass
class SQLAlchemyAdapter(GA.GenericDatasetAdapter):
    def __init__(
        self,
        connection: M.Connection,
        cache: MitzuCache,
        end_date: datetime,
        insight_settings: M.InsightSettings,
        revenue_settings: Optional[M.RevenueSettings],
        adapter_context: Optional[GA.AdapterContext] = None,
        pseudocolumns: Optional[List[str]] = None,
    ):
        super().__init__(end_date, adapter_context)
        self._cache = cache
        self._connection_url = (
            self._get_connection_url(connection)
            if connection.url is None
            else connection.url
        )
        self._connection_object = connection
        self._connection_schema = connection.schema
        self._table_cache: Dict[str, SA.Table] = {}
        self._debug_mode = (
            adapter_context.is_debug_enabled if adapter_context is not None else False
        )
        self.revenue_settings = revenue_settings
        self.insight_settings = insight_settings
        self.pseudocolumns = pseudocolumns or []

    def get_event_name_field(
        self,
        ed_table: M.EventDataTable,
        sa_table: SA.Table,
    ) -> FieldReference:
        if ed_table.is_single_event_table:
            return SA.literal(ed_table.table_name)
        elif ed_table.event_name_field is not None:
            return self.get_field_reference(ed_table.event_name_field, sa_table)
        else:
            raise ValueError(
                f"event_name_field is not set for multi event table {ed_table.schema}.{ed_table.table_name}"
            )

    def _clear_table_cache(self) -> None:
        self._table_cache.clear()

    def _get_conversion_sql(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        return self._format_query(
            self.get_conversion_select(metric, cte_prefix, collection_cte_value_cols)
        )

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        res_df = self.execute_query(
            self.get_conversion_select(metric, cte_prefix, collection_cte_value_cols)
        )
        if metric.type == M.MetricType.CONVERSION and GA.GROUP_COL in res_df.columns:
            # GROUP_COL is missing if the aggregation is LIST_USERS
            res_df[GA.GROUP_COL] = res_df[GA.GROUP_COL].apply(AH.cleanup_group_by_col)
        return res_df

    def _get_segmentation_sql(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        return self._format_query(
            SegmentationQueryBuilder(self).get_query(metric, collection_cte_value_cols)
        )

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        return self.execute_query(
            SegmentationQueryBuilder(self).get_query(metric, collection_cte_value_cols)
        )

    def _get_retention_sql(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> str:
        return self._format_query(
            self.get_retention_select(metric, cte_prefix, collection_cte_value_cols)
        )

    def _format_query(self, raw_query: Any) -> str:
        if type(raw_query) != str:
            raw_query = str(
                raw_query.compile(
                    compile_kwargs={"literal_binds": True},
                    # dialect=self.get_engine().dialect,
                )
            )

        return sqlparse.format(raw_query, reindent=True, keyword_case="upper")

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        ret_df = self.execute_query(
            self.get_retention_select(metric, cte_prefix, collection_cte_value_cols)
        )

        col1 = fix_col_index(1, GA.AGG_VALUE_COL)
        col2 = fix_col_index(2, GA.AGG_VALUE_COL)
        if GA.GROUP_COL in ret_df.columns:
            # GROUP_COL is missing if the aggregation is LIST_USERS
            ret_df[GA.GROUP_COL] = ret_df[GA.GROUP_COL].apply(AH.cleanup_group_by_col)

        if col2 in ret_df.columns and col1 in ret_df.columns:
            ret_df[GA.AGG_VALUE_COL] = ret_df[col2]
            ret_df.drop(columns=[col1, col2], inplace=True)
        elif col1 in ret_df.columns:
            ret_df[GA.AGG_VALUE_COL] = ret_df[col1]
            ret_df.drop(columns=[col1], inplace=True)

        return ret_df

    def map_type(self, sa_type: TypeEngine) -> M.DataType:
        for sa_t, data_type in SIMPLE_TYPE_MAPPINGS.items():
            if isinstance(sa_type, sa_t) or sa_type == sa_t:
                return data_type

        try:
            sa_type_str = str(sa_type)
        except Exception:  # avoid errors like: Can't generate DDL for NullType();
            sa_type_str = "cannot convert sa_type to string"
        LOGGER.warning(f"Unknown type: {type(sa_type)}, {sa_type_str}")
        return M.DataType.STRING

    def _cache_object(self, key: str, obj: Any) -> None:
        self._cache.put(key, obj, expire=24 * 60 * 60)

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)

        engine = self.get_engine()
        try:
            with engine.connect() as connection:
                cursor_result = connection.execute(query)
                columns = pd.Index(cursor_result.keys())
                fetched = cursor_result.fetchall()
                if len(fetched) > 0:
                    pdf = pd.DataFrame(fetched)
                    pdf.columns = columns
                else:
                    pdf = pd.DataFrame(columns=columns)

                if self._debug_mode:
                    s3_key = AH.save_dataframe_for_debugging(
                        self._connection_object.project_id, pdf
                    )
                    LOGGER.bind(
                        call_stack="\n".join(traceback.format_stack()),
                        query=self._format_query(query),
                        s3_bucket=configs.DEBUG_DATAFRAME_S3_BUCKET,
                        s3_key=s3_key,
                        debug=True,  # for easier search in elastic
                        dataframe=pdf.head(50).to_string(),
                    ).info("adapter execute query")
                return pdf
        except Exception as exc:
            common_exc = self._generalize_warehouse_specific_error(exc)
            if common_exc:
                exc = common_exc

            if (
                log_error_as_warning
                or isinstance(exc, GA.MetricConfigUserError)
                or isinstance(exc, GA.QueryKilledError)
                or isinstance(exc, GA.GenericInternalError)
            ):
                LOGGER.opt(exception=exc).warning(
                    f"Failed Query:\n{self._format_query(query)}"
                )
            else:
                LOGGER.opt(exception=exc).exception(
                    f"Failed Query:\n{self._format_query(query)}"
                )
            raise exc
        finally:
            engine.dispose()

    def get_engine(self) -> SA.engine.Engine:
        return SA.create_engine(self._connection_url)

    def get_table_by_name(
        self,
        schema: str,
        table_name: str,
        auto_load: bool,
    ) -> SA.Table:
        full_name = f"{schema}.{table_name}"
        try:
            engine = self.get_engine()
        except Exception as e:
            raise SQLAlchemyAdapterError(f"Failed to connect to {full_name}") from e

        if full_name not in self._table_cache or (
            auto_load is True and len(self._table_cache[full_name].columns) == 0
        ):
            try:
                self._table_cache[full_name] = SA.Table(  # type: ignore[assignment]
                    table_name,
                    SA.MetaData(),
                    schema=schema,
                    autoload_with=engine if auto_load else None,
                ).alias(f"t{len(self._table_cache) + 1}")
            except Exception as e:
                raise SQLAlchemyAdapterError(
                    f"Access Denied to {full_name}. \nError message: {e}"
                ) from e
        return self._table_cache[full_name]

    def get_table(self, data_table: M.DataTable, auto_load: bool) -> SA.Table:
        return self.get_table_by_name(
            data_table.schema, data_table.table_name, auto_load=auto_load
        )

    def join_revenue_ctes(self, rev_dt_cte: SEL.CTE, segment_cte: SEL.CTE) -> SEL.Join:
        return rev_dt_cte.join(
            segment_cte,
            (
                rev_dt_cte.columns[GA.REV_DT]
                >= segment_cte.columns[GA.CTE_REV_PERIOD_START]
            )
            & (
                rev_dt_cte.columns[GA.REV_DT]
                <= segment_cte.columns[GA.CTE_REV_PERIOD_END]
            ),
            isouter=True,
        )

    def get_revenue_aggregation_in_segmentation(
        self, at: M.AggType, cte: SEL.CTE
    ) -> FieldReference:
        if at == M.AggType.TOTAL_MRR:
            return SA.func.sum(cte.columns[GA.CTE_REV_AMOUNT])
        elif at == M.AggType.COUNT_SUBSCRIBERS:
            return SA.func.count(cte.columns[GA.CTE_USER_ID_ALIAS_COL].distinct())
        else:
            raise ValueError(
                f"Aggregation type {at.name} is not supported for segmentation"
            )

    def get_field_reference(
        self,
        field: M.Field,
        sa_table: Union[SA.sql.TableClause, SEL.CTE],
    ) -> FieldReference:
        if isinstance(sa_table, SEL.CTE) and field.parent is None:
            if field.name not in sa_table.columns:
                raise MissingTableField(field.name)
            return sa_table.columns[field.name]

        if field.parent and field.parent.type == M.DataType.MAP:

            def _get_map_field_path(field: M.Field) -> str:
                # This section will support nested JSONs
                if field.parent is None:
                    return field._get_name()
                else:
                    return f"{_get_map_field_path(field.parent)}['{field.name}']"

            return SA.literal_column(
                f"{sa_table.name}.{_get_map_field_path(field)}"  # type: ignore[union-attr]
            )
        else:
            return SA.literal_column(f"{sa_table.name}.{field._get_name()}")  # type: ignore[union-attr]

    def _get_distinct_on_column(self, field: FieldReference) -> FieldReference:
        # some dwh does not support column-level distinct
        return field.distinct()

    def _get_distinct_on_query(self, query: SEL.Select) -> SEL.Select:
        # dwhs not support column-level distinct should use query level distinct
        return query

    def get_field_reference_for_dimension_search(
        self,
        field: M.Field,
        sa_table: Union[SA.Table, SEL.CTE],
    ) -> FieldReference:
        return self.get_field_reference(field, sa_table)

    def get_event_time_reference(
        self, edt: M.EventDataTable, sa_table: SA.sql.TableClause
    ) -> FieldReference:
        if edt.event_time_field_type is None:
            return self.get_field_reference(edt.event_time_field, sa_table)

        event_time_type = self._parse_type_name(edt.event_time_field_type)

        if event_time_type == M.DataType.NUMBER:
            return self.number_as_datetime(
                self.get_field_reference(edt.event_time_field, sa_table)
            )
        if event_time_type == M.DataType.DATETIME:
            return self.get_field_reference(edt.event_time_field, sa_table)

        LOGGER.warning(
            f"Unknown event time field type {edt.event_time_field_type}, trying as datetime"
        )
        return self.get_field_reference(edt.event_time_field, sa_table)

    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        return None

    def _get_struct_type(self) -> TypeEngine:
        raise NotImplementedError(
            "Complex types (struct, row) are not supported in this data warehouse type"
        )

    def _get_floating_type(self) -> TypeEngine:
        return SA_T.FLOAT  # type: ignore

    def _get_text_type(self) -> TypeEngine:
        return SA_T.VARCHAR  # type: ignore

    def _get_struct_sub_types(self, struct_val: Any) -> Any:
        return struct_val.attr_types

    def _parse_type_name(self, type_name: str) -> M.DataType:
        raise NotImplementedError("Type name parsing is not implemented")

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        raise NotImplementedError("Converting to datetime is not implemented")

    def missing_string_value(self) -> FieldReference:
        return SA.literal(None)

    def _parse_struct_type(
        self, sa_type: Any, name: str, path: str, sa_table: Optional[SA.Table]
    ) -> M.Field:
        real_struct_type = self._get_struct_type()
        if isinstance(sa_type, real_struct_type):  # type: ignore[arg-type]
            row: TypeEngine = sa_type
            sub_fields: List[M.Field] = []
            for n, st in self._get_struct_sub_types(row):
                next_path = f"{path}.{n}"
                sf = self._parse_struct_type(
                    sa_type=st,
                    name=n,
                    path=next_path,
                    sa_table=sa_table,
                )
                if sf.type == M.DataType.STRUCT and (
                    sf.sub_fields is None or len(sf.sub_fields) == 0
                ):
                    continue
                sub_fields.append(sf)
            return M.Field(
                name=name, type=M.DataType.STRUCT, sub_fields=tuple(sub_fields)
            )
        else:
            return M.Field(name=name, type=self.map_type(sa_type))

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        raise NotImplementedError(
            "Map types are not supported in this data warehouse type"
        )

    def _parse_array_type(
        self,
        sa_type: str,
        name: str,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,  # used in snowflake adapter
    ) -> M.Field:
        raise NotImplementedError(
            "Array types are not supported in this data warehouse type"
        )

    def list_schemas(self) -> List[str]:
        key = self._cache.get_key(self._connection_object, CacheKeyType.SCHEMAS)
        schemas = self._cache.get(key)
        if schemas is not None:
            return schemas

        engine = self.get_engine()
        insp = SA.inspect(engine)
        schemas = insp.get_schema_names()
        self._cache_object(key, schemas)
        return schemas

    def list_tables(self, schema: str) -> List[str]:
        cache_key = self._cache.get_key(
            self._connection_object, CacheKeyType.SCHEMA_TABLES, schema=schema
        )
        tables = self._cache.get(cache_key)
        if tables is not None:
            return tables

        engine = self.get_engine()
        insp = SA.inspect(engine)
        table_names = insp.get_table_names(schema=schema)
        try:
            view_names = insp.get_view_names(schema=schema)
        except Exception as exc:
            # Some DWHs don't support views (like databricks + glue views)
            LOGGER.opt(exception=exc).warning(f"Failed to list views {schema}")
            view_names = []
        tables = list(set(table_names + view_names))
        self._cache_object(cache_key, tables)
        return tables

    def _is_array_type_supported(self) -> bool:
        return False

    def _flatten_fields(self, fields: List[M.Field]) -> List[M.Field]:
        """Flattens the tree field structure to a list.
            All leaf-nodes in the tree will be in the result list

        Args:
            fields (List[M.Field]): Fields that might contain subfields

        Returns:
            List[M.Field]: All subfields (with correct parents)
        """
        res = []
        for f in fields:
            if f.type.is_complex():
                if f.type == M.DataType.ARRAY and self._is_array_type_supported():
                    res.append(f)
                elif f.sub_fields is not None and len(f.sub_fields) == 0:
                    res.append(f)
                elif f.sub_fields is not None:
                    res.extend(self._flatten_fields(list(f.sub_fields)))
            else:
                res.append(f)
        return res

    def list_fields(
        self,
        data_table: M.DataTable,
        indexing_context: M.IndexingContext,
    ) -> List[M.Field]:
        schema = data_table.schema
        table_name = data_table.table_name
        schema_request = GA.SchemaTableRequest(schema, table_name)
        database_schema = self.list_all_table_columns_from_schema(
            [schema_request], ignore_cache=True
        )
        if schema not in database_schema:
            raise ValueError(f"Schema '{schema}' not found")

        if table_name not in database_schema[schema].tables:
            raise ValueError(f"Table '{table_name}' not found")

        table_schema = database_schema[schema].tables[table_name]
        res = []
        ignored_field_names = [f._get_name() for f in data_table.ignored_fields]
        for field in table_schema.available_fields:
            field_name = field._get_name()
            try:
                if field_name in ignored_field_names:
                    continue
                if field.type == M.DataType.MAP:
                    orig_type = table_schema.get_orig_field_type(field_name)
                    if orig_type is None:
                        raise ValueError(
                            f"Original field type not found for {field_name}"
                        )
                    map_field = self._parse_map_type(
                        sa_type=orig_type,
                        name=field_name,
                    )
                    if map_field.sub_fields is None:
                        continue
                    res.append(map_field)
                elif field.type == M.DataType.ARRAY and self._is_array_type_supported():
                    orig_type = table_schema.get_orig_field_type(field_name)
                    if orig_type is None:
                        raise ValueError(
                            f"Original field type not found for {field_name}"
                        )
                    array_field = self._parse_array_type(
                        sa_type=orig_type,
                        name=field_name,
                        data_table=data_table,
                        indexing_context=indexing_context,
                    )
                    if (
                        array_field.sub_fields
                        and not array_field.sub_fields[0].type.is_complex()
                    ):
                        res.append(array_field)
                else:
                    res.append(field)
            except NoDataError as exc:
                LOGGER.opt(exception=exc).warning(
                    f"Couldn't list fields. {schema}.{table_name}.{field_name}: {field.type}"
                )
            except Exception as exc:
                # TODO: make sure the exception get's to the User on UI in a non-blocking way
                LOGGER.opt(exception=exc).exception(
                    f"Couldn't list fields. {schema}.{table_name}.{field_name}: {field.type}"
                )

        return self._flatten_fields(res)

    def _get_all_table_columns_cache_key_and_object(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool = False,
    ) -> Tuple[Dict[str, M.DatabaseSchema], List[GA.SchemaTableRequest]]:
        """
        Returns a tuple of
         - a dict of the requested schemas where the values are either defaulted or loaded from the cache
         - (schema_name, List[table_name]) dict of those schema-tables which were not found in the cache
        """
        result: Dict[str, M.DatabaseSchema] = {
            s.schema: M.DatabaseSchema({}) for s in schema_requests
        }

        schema_tables_to_fetch: List[GA.SchemaTableRequest] = []
        for schema_request in schema_requests:
            schema = schema_request.schema
            table_name = schema_request.table

            need_to_fetch = True
            if not ignore_cache:
                cache_key = self._cache.get_key(
                    self._connection_object,
                    CacheKeyType.TABLE_COLUMNS,
                    schema=schema,
                    table_name=table_name,
                )

                cached_data = self._cache.get(cache_key)
                if cached_data:
                    result[schema].tables[table_name] = cached_data
                    need_to_fetch = False

            if need_to_fetch:
                schema_tables_to_fetch.append(GA.SchemaTableRequest(schema, table_name))

        return result, schema_tables_to_fetch

    def _get_table_columns_df(
        self, schema_table_requests: Iterable[GA.SchemaTableRequest]
    ) -> pd.DataFrame:
        conditions = []
        for req in schema_table_requests:
            conditions.append(
                f"""
                (table_schema = '{req.schema}' AND table_name = '{req.table}')"""
            )

        if len(conditions) == 0:
            return pd.DataFrame(
                {
                    "table_schema": [],
                    "table_name": [],
                    "column_name": [],
                    "data_type": [],
                }
            )

        # information schema is not loaded by sql alchemy, we need to create the query from scratch
        query = SA.text(
            f"""
            SELECT table_schema, table_name, column_name, data_type
            FROM information_schema.columns
            WHERE {" OR ".join(conditions)}
            """
        )
        return self.execute_query(query)

    def list_all_table_columns_from_schema(
        self,
        schema_requests: Iterable[GA.SchemaTableRequest],
        ignore_cache: bool,
    ) -> Dict[str, M.DatabaseSchema]:
        """
        Returns all tables with all columns in a single call. The performance of this
        method is may not the best but it can be easily overridden to have a dwh specific
        method using the information_schema.columns table or something similar.
        """
        (
            result,
            schema_tables_to_fetch,
        ) = self._get_all_table_columns_cache_key_and_object(
            schema_requests, ignore_cache
        )

        self._clear_table_cache()
        for schema_table_request in schema_tables_to_fetch:
            schema = schema_table_request.schema
            table_name = schema_table_request.table
            try:
                # an SA.MetaData instance can live in the table cache preventing to
                # discover a second table as well therefore this needs to be cleared
                table_data = self._get_single_table_columns(schema, table_name)
                result[schema].tables[table_name] = table_data

                cache_key = self._cache.get_key(
                    self._connection_object,
                    CacheKeyType.TABLE_COLUMNS,
                    schema=schema,
                    table_name=table_name,
                )

                self._cache_object(cache_key, table_data)
            except Exception as exc:
                LOGGER.opt(exception=exc).warning(
                    f"Failed to list columns in table {table_name}"
                )

        return result

    def _sqlalchemy_column_to_field(
        self, sa_type: Any, field_name: str, sa_table: Optional[SA.Table]
    ) -> Optional[M.Field]:
        data_type = self.map_type(sa_type)
        if data_type == M.DataType.STRUCT:
            complex_field = self._parse_struct_type(
                sa_type=sa_type,
                name=field_name,
                path=field_name,
                sa_table=sa_table,
            )
            if complex_field.sub_fields is None or len(complex_field.sub_fields) == 0:
                return None
            return complex_field
        else:
            return M.Field(name=field_name, type=data_type)

    def _get_single_table_columns(
        self, schema: str, table_name: str
    ) -> M.DatabaseTable:
        cache_key = self._cache.get_key(
            self._connection_object,
            CacheKeyType.TABLE_COLUMNS,
            schema=schema,
            table_name=table_name,
        )
        table_schema = self._cache.get(cache_key)
        if table_schema is not None:
            return table_schema

        table = self.get_table_by_name(schema, table_name, auto_load=True)
        column_types = table.columns
        field_res: Dict[str, M.Field] = {}
        type_res: Dict[str, str] = {}
        for column_name, column_type in column_types.items():
            if "." in column_name:
                # Columns with periods are not supported
                continue
            sa_type = column_type.type
            field = self._sqlalchemy_column_to_field(sa_type, column_name, table)
            if field:
                field_res[field._get_name()] = field
                type_res[field._get_name()] = self._serialize_field_type_to_str(sa_type)
        table_schema = M.DatabaseTable(field_res, type_res)
        self._cache_object(cache_key, table_schema)
        return table_schema

    def _serialize_field_type_to_str(self, field_type: Any) -> str:
        # in databricsk the str(sa_type) is not verbose enough, use repr(sa_type)
        return repr(field_type)

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> FieldReference:
        return field_ref + SA.text(f"interval '{timewindow.value}' {timewindow.period}")

    def safe_divide(self, a: FieldReference, b: FieldReference) -> FieldReference:
        return SA.case((b != 0, a / b), else_=SA.literal_column("NULL"))

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> SA_F.Function:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SA.func.date_add(
            SA.text(f"'{time_group.name.lower()}'"),
            val_ref,
            field_ref,
        )

    def _get_connection_url(self, con: M.Connection) -> str:
        user_name = "" if con.user_name is None else P.quote(con.user_name)
        password = ""
        if con.auth_type == M.ConnectionAuthType.PASSWORD and con.password is not None:
            password = f":{P.quote(con.password)}"

        host_str = "" if con.host is None else str(con.host)
        if con.user_name is not None and con.host is not None:
            host_str = f"@{host_str}"
        port_str = "" if con.port is None else f":{str(con.port)}"
        schema_str = "" if con.schema is None else f"/{con.schema}"
        catalog_str = "" if con.catalog is None else f"/{con.catalog}"
        url_params_str = "" if con.url_params is None else con.url_params
        if not url_params_str.startswith("?"):
            url_params_str = "?" + url_params_str
        protocol = con.get_protocol().lower()
        res = f"{protocol}://{user_name}{password}{host_str}{port_str}{catalog_str}{schema_str}{url_params_str}"
        return res

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> SA_F.Function:
        return SA.func.cast(SA.func.array_agg(SA.distinct(field_ref)), SA.JSON)

    def _get_array_contains_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        raise NotImplementedError()

    def _get_array_like_condition(
        self, field_ref: FieldReference, value: Any
    ) -> Union[SA_F.Function, SA_EXP.ColumnElement]:
        arr_str = SA.func.array_to_string(field_ref, STRING_SEPARATOR)
        if self.insight_settings and self.insight_settings.case_insensetive_filtering:
            value = value.lower()
            arr_str = SA.func.lower(arr_str)
        return arr_str.like(f"%{value}%")

    def _get_null_discovered_value(self) -> FieldReference:
        return SA.literal(None)

    def _column_index_support(self) -> bool:
        return True

    def get_group_by_expression(
        self, field_names: List[str], fields: List[FieldReference]
    ) -> List[GroupByFieldReference]:
        if self._column_index_support():
            return [SA.literal(index) for index in range(1, len(field_names) + 1)]
        return [SA.text(fn) for fn in field_names]

    def _get_random_function(self) -> SA_F.Function:
        return SA.func.random()

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],  # used in redshift adapter
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        query = self._get_field_values_sql(
            dataset_cte, data_table, fields, indexing_context
        )
        df = self.execute_query(query)
        df = df.rename(
            # This is required for complext types, as aliasing with `.`
            # doesn't work. The `.` comes from the get _get_name()
            # This issue might appear elsewhere as well
            columns={
                k: k.replace(COLUMN_NAME_REPLACE_STR, ".")
                .replace(SPECIAL_CHARACTER_REPLACE_STR, "`")
                .split(COLUMN_NAME_INDEX_SEPARATOR)[0]
                for k in list(df.columns)
            }
        )

        if isinstance(data_table, M.EventDataTable):
            return df.set_index(GA.EVENT_NAME_ALIAS_COL)
        return df

    def _supports_array_agg_discovery(self) -> bool:
        return True

    def parse_array_values(self, values: List[Any]) -> List[Any]:
        raise NotImplementedError()

    def parse_map_values(self, values: List[Any]) -> List[Any]:
        raise NotImplementedError()

    def _get_field_values_sql(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        indexing_context: M.IndexingContext,
    ) -> SA_EXP.Select:
        cte = aliased(
            dataset_cte,
            alias=SAMPLED_SOURCE_CTE_NAME,
            name=SAMPLED_SOURCE_CTE_NAME,
        )

        if isinstance(data_table, M.EventDataTable):
            event_col = self.get_event_name_field(data_table, cte)
            event_fields = [
                event_col.label(GA.EVENT_NAME_ALIAS_COL),
            ]
            group_by = self.get_group_by_expression(
                [GA.EVENT_NAME_ALIAS_COL], [event_col]
            )
        elif isinstance(data_table, M.DimensionDataTable):
            event_fields = []
            group_by = None
        else:
            raise ValueError(f"Unkown data table: {data_table}")

        if not self._supports_array_agg_discovery():
            group_by = None

        field_columns = []
        for index, f in enumerate(fields):
            if f.sub_fields and f.type != M.DataType.ARRAY:
                continue

            field_label = (
                f._get_name()
                .replace(".", COLUMN_NAME_REPLACE_STR)
                .replace("`", SPECIAL_CHARACTER_REPLACE_STR)
            )

            if f._get_name() in self.pseudocolumns:
                field: FieldReference = SA.literal(None)
            else:
                field = self._get_distinct_array_agg_func(
                    self.get_field_reference(f, cte),
                    field=f,
                    compress_map_type=indexing_context.compress_map_type,
                )

            field_columns.extend(
                [
                    field.label(field_label + COLUMN_NAME_INDEX_SEPARATOR + str(index)),
                ]
            )

        query = SAH.select(
            group_by=group_by,
            columns=event_fields + field_columns,
            select_from=cte,
        )
        return query

    def get_collection_inclusion_condition(
        self,
        ref: FieldReference,
        agg_values_col: FieldReference,
    ) -> Union[ELM.ColumnElement, ELM.BooleanClauseList]:
        return SA.func.exists(
            self._get_distinct_on_query(
                SAH.select(columns=[agg_values_col], whereclause=agg_values_col == ref)
            ).scalar_subquery()
        )

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> SA_F.Function:
        return SA.func.date_trunc(time_group.name, field_ref)

    def get_modulo(self, field: FieldReference, modulus: float) -> FieldReference:
        return field % modulus

    def _get_date_trunc_string(
        self, time_group: M.TimeGroup, input_date: datetime
    ) -> datetime:
        if time_group == M.TimeGroup.YEAR:
            return input_date.replace(
                month=1, day=1, hour=0, minute=0, second=0, microsecond=0
            )
        elif time_group == M.TimeGroup.MONTH:
            return input_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif time_group == M.TimeGroup.WEEK:
            return (input_date - timedelta(days=input_date.weekday())).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        else:
            return input_date

    def get_field_reference_or_null(
        self, group_field: M.FieldDef, sub_query: TableSegment
    ) -> FieldReference:
        if not isinstance(sub_query, SingleTableSegment):
            raise ValueError("Cannot resolve field reference in multi table segment")
        if isinstance(group_field, M.EventFieldDef):
            for evt in sub_query.events:
                for f in evt.fields:
                    if f.field._get_name() == group_field.field._get_name():
                        return self.get_field_reference(
                            group_field.field, sub_query.table_ref
                        )
        elif isinstance(group_field, M.DimensionFieldDef):
            return self.get_field_reference(
                group_field.field,
                sub_query.dimension_tables[group_field.dimension_data_table.table_name],
            )
        elif isinstance(group_field, M.SubsegmentFieldDef):
            return SA.literal(
                f"{GA.SUBSEGMENT_INDEX}_{sub_query.segment_index}_{sub_query.subsegment_index}"
            )
        else:
            raise ValueError(f"Unexpected field type: {type(group_field)}")

        return self.missing_string_value()

    def default_group_by_col(self, metric_type: M.MetricType) -> FieldReference:
        return self.missing_string_value()

    def group_by_col_cast(self, col: FieldReference) -> FieldReference:
        return col.cast(SA.VARCHAR)

    def concatenate_string_fields(
        self, field_a: FieldReference, field_b: FieldReference
    ) -> FieldReference:
        return field_a + SA.literal(STRING_SEPARATOR) + field_b

    def concatenate_strings(self, *fields: FieldReference) -> FieldReference:
        if len(fields) == 2:
            return fields[0] + fields[1]
        elif len(fields) > 2:
            return fields[0] + self.concatenate_strings(*fields[1:])
        raise ValueError(f"unexpected amount of fields to concatenate: {fields}")

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        return SA.literal(dt)

    def date_diff(
        self, start_dt: FieldReference, end_dt: FieldReference, period: str
    ) -> FieldReference:
        return SA.func.date_diff(end_dt, start_dt, period)

    def get_date_partition_filter(
        self,
        edt: M.EventDataTable,
        table: SA.Table,
        start_dt: datetime,
        end_dt: datetime,
        time_group: Optional[M.TimeGroup],
    ) -> ELM.ColumnElement:
        if edt.date_partition_field is None:
            raise ValueError("Date partition field is not set")

        dt_part = self.get_field_reference(edt.date_partition_field, table)
        if edt.date_partition_field.type == M.DataType.STRING:
            start_date = start_dt
            end_date = end_dt
            if time_group is not None and time_group != M.TimeGroup.TOTAL:
                start_date = self._get_date_trunc_string(time_group, start_date)
            return (dt_part >= str(start_date.date())) & (
                dt_part <= str(end_date.date())
            )
        if edt.date_partition_field.type == M.DataType.DATETIME:
            _start_date = self.correct_timestamp(start_dt.date())
            _end_date = self.correct_timestamp(end_dt.date())
            if time_group is not None and time_group != M.TimeGroup.TOTAL:
                _start_date = self.get_date_trunc(time_group, _start_date)
            return (dt_part >= _start_date) & (dt_part <= _end_date)
        else:
            raise ValueError("Date partition field must be Date or Text type.")

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        at = metric.config.aggregation.type
        if at in (
            M.AggType.CONVERSION,
            M.AggType.COUNT_CONVERTED_USERS,
            M.AggType.RETENTION_RATE,
        ):
            if (
                isinstance(metric.config, M.ConversionMetricConfig)
                and metric.config.conv_orientation == M.ConversionOrientation.REVERSE
            ):
                compare_index = len(metric.segments) - 1
            else:
                compare_index = 0

            return self.safe_divide(
                SA.func.count(
                    cte.columns[
                        fix_col_index(index, GA.CTE_USER_ID_ALIAS_COL)
                    ].distinct()
                )
                * 100.0,
                SA.func.count(
                    cte.columns[
                        fix_col_index(compare_index, GA.CTE_USER_ID_ALIAS_COL)
                    ].distinct()
                ),
            )
        elif at == M.AggType.BY_EVENT_PROPERTY:
            return self.get_event_property_aggregation(
                metric.config.aggregation,
                cte.columns[fix_col_index(index, GA.CTE_EVENT_PROPERTY_COL)],
            )
        elif at == M.AggType.PERCENTILE_TIME_TO_CONV:
            raise NotImplementedError(
                "Percentile calculation is not supported in this data warehouse type"
            )
        elif at == M.AggType.RETURN_DROP_OFF_USERS:
            prev_col = cte.columns[fix_col_index(index - 1, GA.CTE_USER_ID_ALIAS_COL)]
            curr_col = cte.columns[fix_col_index(index, GA.CTE_USER_ID_ALIAS_COL)]
            return SA.case(
                (
                    SA.func.count(curr_col).over(partition_by=prev_col) == 0,
                    prev_col,
                ),
                else_=SA.null(),
            )
        elif at in M.LIST_USER_AGGS:
            return self._get_distinct_on_column(
                cte.columns[fix_col_index(index, GA.CTE_USER_ID_ALIAS_COL)]
            )
        else:
            raise ValueError(f"Aggregation type {at} is not supported for conversion")

    def _get_percentile_agg(
        self, col: SA_EXP.ColumnElement, percentile: int
    ) -> SA_EXP.ColumnElement:
        raise NotImplementedError(
            "Percentile calculation is not supported in this data warehouse type"
        )

    def get_event_property_aggregation(
        self,
        aggregation: M.AggregationConfig,
        col: ELM.ColumnElement,
    ) -> AggregatedField:
        ep_agg = aggregation.event_property_agg
        if ep_agg is None:
            raise ValueError("Event property aggregation details are not set")
        if ep_agg.agg_type == M.EventPropertyAggregationType.COUNT_DISTINCT:
            return SA.func.count(col.distinct())

        casted_event_property_col = SA.cast(col, self._get_floating_type())
        if ep_agg.agg_type == M.EventPropertyAggregationType.SUM:
            return SA.func.sum(casted_event_property_col)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.AVG:
            return SA.func.avg(casted_event_property_col)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.MIN:
            return SA.func.min(casted_event_property_col)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.MAX:
            return SA.func.max(casted_event_property_col)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.MEDIAN:
            return self._get_percentile_agg(casted_event_property_col, 50)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.P75:
            return self._get_percentile_agg(casted_event_property_col, 75)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.P90:
            return self._get_percentile_agg(casted_event_property_col, 90)
        elif ep_agg.agg_type == M.EventPropertyAggregationType.P95:
            return self._get_percentile_agg(casted_event_property_col, 95)
        raise ValueError(
            f"Event property aggregation type {ep_agg.agg_type} is not supported for segmentation"
        )

    # clickhouse does not support ... AND TRUE in join conditions, we need to avoid that
    def combine_filters(
        self, filters: Sequence[Optional[FilterCondition]]
    ) -> FilterCondition:
        combined_filter: Optional[FilterCondition] = None
        for f in filters:
            if f is None:
                continue
            if combined_filter is None:
                combined_filter = f
            else:
                combined_filter = combined_filter & f
        if combined_filter is None:
            return SA.literal(True)
        return combined_filter

    def get_first_event_conversion_window_frame(self) -> Optional[Tuple]:
        return (1, None)  # rows between current row + 1 to unbounded following

    def _get_first_event_conversion_window_function(
        self,
        identifier: SA_EXP.ColumnElement,
        step: int,
        target_col: SA_EXP.ColumnElement,
    ) -> SA_F.FunctionElement:
        return SA.func.min(
            (
                SA.case(
                    (
                        identifier == step,
                        target_col,
                    ),
                    else_=None,
                )
            )
        )

    def should_cast_cols_in_union(self) -> bool:
        return False

    def union(self, cte_a: SelectType, cte_b: SelectType) -> SelectType:
        return SA.union(cte_a, cte_b)

    def get_segmentation_select(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> SelectType:
        return SegmentationQueryBuilder(self).get_query(
            metric, collection_cte_value_cols
        )

    def get_conversion_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> SelectType:
        return ConversionQueryBuilder(self).get_query(
            metric, cte_prefix, collection_cte_value_cols
        )

    def get_retention_select(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> SelectType:
        return RetentionQueryBuilder(self).get_query(
            metric, cte_prefix, collection_cte_value_cols
        )

    def convert_to_datetime_if_needed(self, dt_col: FieldReference) -> FieldReference:
        return dt_col

    def test_connection(self) -> None:
        self.execute_query(
            SAH.select(columns=[SA.literal(True).label("test_connection")]),
            log_error_as_warning=True,
        )

    def cancel_query(self, query_id: str) -> None:
        pass

    def _get_users_select(
        self, metric: M.Metric, dim_field_defs: Dict[str, DimensionFieldDef]
    ) -> SelectType:
        if metric.type == M.MetricType.CONVERSION:
            if (
                metric.config.aggregation
                and metric.config.aggregation.type not in M.LIST_USER_AGGS
                and metric.config.aggregation.type != M.AggType.RETURN_DROP_OFF_USERS
            ):
                raise ValueError("Conversion metric must have unique users aggregation")

            cte = self.get_conversion_select(metric, cte_prefix=None).cte()
            event_user_id_col = cte.columns[GA.AGG_VALUE_COL + "_1"].label(
                GA.FINAL_DIM_PRIMARY_KEY_COL
            )
        elif metric.type == M.MetricType.SEGMENTATION:
            if len(metric.segments) != 1:
                raise ValueError("Segmentation metric must have exactly one segment")

            if (
                metric.segments[0].aggregation
                and metric.segments[0].aggregation.type not in M.LIST_USER_AGGS
            ):
                raise ValueError(
                    "Segmentation metric must have unique users aggregation"
                )

            cte = (
                SegmentationQueryBuilder(self)
                .get_query(metric, collection_cte_value_cols=None)
                .cte()
            )
            event_user_id_col = cte.columns[GA.AGG_VALUE_COL].label(
                GA.FINAL_DIM_PRIMARY_KEY_COL
            )

        elif metric.type == M.MetricType.RETENTION:
            if (
                metric.config.aggregation
                and metric.config.aggregation.type not in M.LIST_USER_AGGS
            ):
                raise ValueError("Retention metric must have unique users aggregation")

            cte = self.get_retention_select(metric, cte_prefix=None).cte()
            event_user_id_col = cte.columns[GA.AGG_VALUE_COL + "_1"].label(
                GA.FINAL_DIM_PRIMARY_KEY_COL
            )
        else:
            raise ValueError(f"Unknown metric type: {metric.type}")

        columns = [event_user_id_col]

        dim_tables: Dict[M.DimensionDataTable, SA.Table] = {}
        for label, dim_field in dim_field_defs.items():
            dim_table = dim_field.dimension_data_table
            sa_table = self.get_table_by_name(
                dim_table.schema, dim_table.table_name, auto_load=False
            )
            dim_tables[dim_field.dimension_data_table] = sa_table
            columns.append(
                self.get_field_reference(dim_field.field, sa_table).label(label)
            )

        select = SAH.select(
            columns=columns, select_from=cte, whereclause=event_user_id_col.is_not(None)
        )

        for dim_tbl, sa_tbl in dim_tables.items():
            dim_user_id_col = self.get_field_reference(
                dim_tbl.primary_key,
                sa_tbl,
            )
            select = select.join(
                sa_tbl,
                event_user_id_col == dim_user_id_col,
                isouter=True,
            )

            # We should be able to return more than 10000 but not too many so the server doesn't break.
        return self._get_distinct_on_query(select).limit(GA.MAX_ROW_LIMIT + 1)

    def get_users_sql(
        self, metric: Metric, dim_field_defs: List[DimensionFieldDef]
    ) -> str:
        dim_field_mapping = {
            AH.normalize_column_name(str(v.field_path)): v for v in dim_field_defs
        }
        return self._format_query(self._get_users_select(metric, dim_field_mapping))

    def normalize_field_path(self, field_path: M.FieldPath) -> str:
        return str(field_path)

    def get_users_df(
        self, metric: Metric, dim_field_defs: List[DimensionFieldDef]
    ) -> pd.DataFrame:
        dim_field_mapping = {
            AH.normalize_column_name(str(v.field_path)): v for v in dim_field_defs
        }
        res_df = self.execute_query(self._get_users_select(metric, dim_field_mapping))
        for c in res_df.columns:
            if c in dim_field_mapping:
                res_df[str(dim_field_mapping[c].field_path)] = res_df[c]
                res_df = res_df.drop(columns=[c])

        return res_df

    def _get_single_user_select(
        self, dim_id: Any, dim_field_defs: Dict[str, M.DimensionFieldDef]
    ) -> SelectType:
        return SingleDimensionQueryBuilder(self).get_query(dim_id, dim_field_defs)

    def get_single_dimensions_sql(
        self, dim_id: Any, dim_field_defs: List[M.DimensionFieldDef]
    ) -> str:
        dim_field_mapping = {
            AH.normalize_column_name(str(v.field_path)): v for v in dim_field_defs
        }
        return self._format_query(
            self._get_single_user_select(dim_id, dim_field_mapping)
        )

    def get_single_dimension_df(
        self, dim_id: Any, dim_field_defs: List[DimensionFieldDef]
    ) -> pd.DataFrame:
        dim_field_mapping = {
            AH.normalize_column_name(str(v.field_path)): v for v in dim_field_defs
        }
        single_dimension_df = self.execute_query(
            self._get_single_user_select(dim_id, dim_field_mapping)
        )
        for c in single_dimension_df.columns:
            if c in dim_field_mapping:
                single_dimension_df[
                    str(dim_field_mapping[c].field_path)
                ] = single_dimension_df[c]
                single_dimension_df = single_dimension_df.drop(columns=[c])

        dim_id_cols = [
            col
            for col in single_dimension_df.columns
            if GA.FINAL_DIM_PRIMARY_KEY_COL in col
        ]
        dim_id_col = single_dimension_df[f"{GA.FINAL_DIM_PRIMARY_KEY_COL}_0"]
        single_dimension_df = single_dimension_df.drop(columns=dim_id_cols)
        single_dimension_df[GA.FINAL_DIM_PRIMARY_KEY_COL] = dim_id_col
        return single_dimension_df

    def _get_search_dimension_select(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> SelectType:
        return SearchDimensionsQueryBuilder(self).get_query(user_search_query)

    def get_search_dimension_as_collection_select(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> SelectType:
        return SearchDimensionsQueryBuilder(self).get_cohort_query(user_search_query)

    def search_dimensions_sql(self, user_search_query: M.DimensionsSearchQuery) -> str:
        return self._format_query(self._get_search_dimension_select(user_search_query))

    def search_dimensions_df(
        self, dimension_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        single_dimension_df = self.execute_query(
            self._get_search_dimension_select(dimension_search_query)
        )
        dim_id_cols = [
            col
            for col in single_dimension_df.columns
            if GA.FINAL_DIM_PRIMARY_KEY_COL in col
        ]
        dim_id_col = single_dimension_df[f"{GA.FINAL_DIM_PRIMARY_KEY_COL}_0"]
        single_dimension_df = single_dimension_df.drop(columns=dim_id_cols)
        single_dimension_df[GA.FINAL_DIM_PRIMARY_KEY_COL] = dim_id_col
        return single_dimension_df

    def fix_types_for_filter(
        self, value: M.EVENT_FILTER_BASE_TYPE
    ) -> Union[M.EVENT_FILTER_BASE_TYPE, FieldReference]:
        if isinstance(value, datetime) or isinstance(value, date):
            return self.correct_timestamp(value)

        return value

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            raise ValueError("Datetime field cannot be used as a numeric value")

        if field.type == M.DataType.NUMBER:
            return SA.cast(field_ref, SA_T.INTEGER)

        return SA.func.ascii(field_ref) * 31

    def get_retention_alias(self) -> Optional[str]:
        return None

    def get_cluster_info_df(self) -> pd.DataFrame:
        raise NotImplementedError()

    def _parse_query_state_type(self, state_str: str) -> GA.QueryState:
        raise NotImplementedError()

    def get_cluster_info(self) -> GA.ClusterInfo:
        rows = self.get_cluster_info_df()
        mitzu_queries: Dict[str, GA.QueryInfo] = {}
        non_mitzu_queries: Dict[GA.QueryState, int] = {
            state: 0 for state in GA.QueryState
        }
        if rows.empty:
            return GA.ClusterInfo(mitzu_queries, non_mitzu_queries)
        for state_str, curr_query, statistics in zip(
            rows["state"], rows["query"], rows["statistics"]
        ):
            try:
                query_id: Optional[str] = None
                state = self._parse_query_state_type(state_str)

                meta_json_str: Optional[str] = None
                for line in curr_query.split("\n"):
                    if line.startswith("--"):
                        meta_json_str = line[2:]
                        break
                if meta_json_str is not None:
                    meta = json.loads(meta_json_str)
                    query_id = meta.get("query_id")
                    email = meta.get("email")
                    created_at = meta.get("created_at")
                    source_url = meta.get("source_url")
                    query_type = (
                        GA.QueryType(meta["query_type"])
                        if "query_type" in meta
                        else None
                    )

                    if query_id is not None:
                        mitzu_queries[query_id] = GA.QueryInfo(
                            query_id=query_id,
                            owner=email,
                            state=state,
                            created_at=datetime.fromisoformat(created_at),
                            source_url=source_url,
                            query_type=query_type,
                            statistics=statistics,
                        )

                elif (
                    self.adapter_context is None
                    or query_id != self.adapter_context.query_id
                ):
                    non_mitzu_queries[state] += 1
            except Exception as exc:
                LOGGER.opt(exception=exc).bind(query=curr_query).warning(
                    "failed to parse meta data from query"
                )
        return GA.ClusterInfo(mitzu_queries, non_mitzu_queries)

    def count_dimension_sql(self, user_search_query: M.DimensionsSearchQuery) -> str:
        search_query = (
            SearchDimensionsQueryBuilder(self)
            .get_cohort_query(user_search_query)
            .subquery()
        )
        count_query = SAH.select(
            columns=[
                SA.func.count(search_query.columns[GA.CTE_USER_ID_ALIAS_COL].distinct())
            ],
            select_from=search_query,
        )

        return self._format_query(count_query)

    def count_dimension_collection(self, collection: M.DimensionCollection) -> int:
        try:
            sql_query = self.count_dimension_sql(collection.search_query)

            result_df = self.execute_query(SA.text(sql_query))

            if not result_df.empty:
                count = result_df.iloc[0, 0]
                if isinstance(count, SupportsInt):
                    return int(count)
                else:
                    raise ValueError("Unexpected type for count")
            else:
                return 0
        except Exception as e:
            LOGGER.opt(exception=e).error("Failed to count dimension collection")
            raise SQLAlchemyAdapterError("Failed to count dimension collection") from e

    def get_dimension_cohort_df(
        self, user_search_query: M.DimensionsSearchQuery
    ) -> pd.DataFrame:
        try:
            search_query = (
                SearchDimensionsQueryBuilder(self)
                .get_cohort_query(user_search_query)
                .cte()
            )
            sql_query = self._get_distinct_on_query(
                SAH.select(
                    columns=[
                        self._get_distinct_on_column(
                            search_query.columns[GA.CTE_USER_ID_ALIAS_COL]
                        )
                    ],
                    select_from=search_query,
                )
            )

            result_df = self.execute_query(sql_query)

            return result_df
        except Exception as e:
            LOGGER.opt(exception=e).error("Failed to return dimension collection")
            raise SQLAlchemyAdapterError("Failed to return dimension collection") from e

    def dry_run_sql(self, query_str: str) -> Optional[GA.DryRunResult]:
        return None

    def supports_dry_run(self) -> bool:
        return False

    def sample_events(
        self,
        sa_table: SA.sql.TableClause,
        ed_table: M.EventDataTable,
        event_sampling: float,
        where_clause: FilterCondition,
    ) -> Tuple[SA.sql.TableClause, FilterCondition]:
        if event_sampling > 0:
            where_clause = where_clause & (
                self.get_modulo(
                    (
                        self.get_field_numeric_value(
                            self.get_field_reference(
                                ed_table.event_time_field, sa_table
                            ),
                            ed_table.event_time_field,
                        )
                    ),
                    100,
                )
                < int(event_sampling * 100)
            )
        return sa_table, where_clause
