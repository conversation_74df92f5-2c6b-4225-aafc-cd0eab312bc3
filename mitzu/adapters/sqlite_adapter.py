from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import sqlalchemy as SA
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.functions as SA_F
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu.adapters.helper import dataframe_str_to_datetime
from mitzu.adapters.model import (
    AggregatedField,
    FieldReference,
    fix_col_index,
)
from mitzu.adapters.sqlalchemy_adapter import (
    SQLAlchemyAdapter,
)
from mitzu.helper import STRING_SEPARATOR
from mitzu.logger import LOGGER


class SQLiteAdapter(SQLAlchemyAdapter):
    def _column_index_support(self) -> bool:
        return False

    def _get_connection_url(self, con: M.Connection) -> str:
        if not con.host:
            return "sqlite://?check_same_thread=False"
        else:
            return f"sqlite:///{con.host}.db?check_same_thread=False"

    def get_field_reference(
        self, field: M.Field, sa_table: Union[SA.sql.TableClause, SEL.CTE]
    ) -> FieldReference:
        if field.parent is not None and field.parent.type == M.DataType.MAP:
            return SA.literal_column(
                f"json_extract({sa_table.name}.{field.parent._get_name()}, '$.{field.name}')"  # type: ignore[union-attr]
            )
        return super().get_field_reference(field, sa_table)

    def _parse_map_type(
        self,
        sa_type: str,
        name: str,
    ) -> M.Field:
        if sa_type != "JSON()":
            raise ValueError(f"Cannot parse {sa_type} column as a map type")
        return M.Field(name=name, type=M.DataType.MAP, sub_fields=tuple())

    def _map_key_type_to_field(self, key_name: str, key_type: str) -> Optional[M.Field]:
        if key_type == "text":
            data_type = M.DataType.STRING
        elif key_type == "integer":
            data_type = M.DataType.NUMBER
        elif key_type == "true" or key_type == "false":
            data_type = M.DataType.BOOL
        elif key_type == "array":
            data_type = M.DataType.ARRAY
        elif key_type == "object":
            data_type = M.DataType.STRUCT
        elif key_type == "null":
            return None
        else:
            LOGGER.warning(f"Unknown data type: {key_type}")
            return None
        return M.Field(key_name, data_type)

    def get_date_trunc(
        self, time_group: M.TimeGroup, field_ref: Union[FieldReference, datetime, str]
    ) -> SA_F.Function:
        if time_group == M.TimeGroup.WEEK:
            return SA.func.datetime(SA.func.date(field_ref, "weekday 0", "-6 days"))
        if time_group == M.TimeGroup.SECOND:
            fmt = "%Y-%m-%dT%H:%M:%S"
        elif time_group == M.TimeGroup.MINUTE:
            fmt = "%Y-%m-%dT%H:%M:00"
        elif time_group == M.TimeGroup.HOUR:
            fmt = "%Y-%m-%dT%H:00:00"
        elif time_group == M.TimeGroup.DAY:
            fmt = "%Y-%m-%dT00:00:00"
        elif time_group == M.TimeGroup.MONTH:
            fmt = "%Y-%m-01T00:00:00"
        elif time_group == M.TimeGroup.QUARTER:
            raise NotImplementedError(
                "Timegroup Quarter is not supported for SQLite Adapter"
            )
        elif time_group == M.TimeGroup.YEAR:
            fmt = "%Y-01-01T00:00:00"
        else:
            raise ValueError(f"Unknown time group {time_group}")

        return SA.func.datetime(SA.func.strftime(fmt, field_ref))

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        df = super()._get_field_values_df(
            dataset_cte,
            data_table,
            fields,
            event_names,
            indexing_context=indexing_context,
        )
        for field in df.columns:

            df[field] = (
                df[field]
                .str.replace(f"{STRING_SEPARATOR}$", "", regex=True)
                .str.split(f"{STRING_SEPARATOR},")
            )

        return df

    def get_df(self, metric: M.Metric) -> pd.DataFrame:
        pdf = super().get_df(metric)
        return pdf

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        return SA.func.group_concat(SA.distinct(field_ref.concat(STRING_SEPARATOR)))

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        if timewindow.period == M.TimeGroup.WEEK:
            # SQL Lite doesn't have the week concept
            timewindow = M.TimeWindow(timewindow.value * 7, M.TimeGroup.DAY)
        return SA.func.datetime(
            field_ref,
            SA.text(f"'+{timewindow.value} {timewindow.period.name.lower()}'"),
        )

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if time_group == M.TimeGroup.WEEK:
            # SQL Lite doesn't have the week concept
            time_group = M.TimeGroup.DAY
            val_ref = val_ref * 7

        if adding != 0:
            val_ref = val_ref + (adding)
        if multiplier != 1:
            val_ref = val_ref * multiplier

        return SA.func.datetime(
            field_ref,
            SA.literal("+")
            + SA.cast(val_ref, SA.String)
            + f" {time_group.name.lower()}",
        )

    def _get_conv_aggregation(
        self, metric: M.Metric, cte: SEL.CTE, index: int
    ) -> AggregatedField:
        if metric.config.aggregation.type == M.AggType.PERCENTILE_TIME_TO_CONV:
            raise NotImplementedError(
                "Percentile calculation is not supported by SQLite Connections."
            )
        if metric.config.aggregation.type == M.AggType.AVERAGE_TIME_TO_CONV:
            t1 = cte.columns.get(fix_col_index(0, GA.CTE_DATETIME_COL))
            t2 = cte.columns.get(fix_col_index(index, GA.CTE_DATETIME_COL))
            diff = SA.func.round(
                (SA.func.julianday(t2) - SA.func.julianday(t1)) * 86400
            )
            return SA.func.avg(diff)
        else:
            return super()._get_conv_aggregation(metric, cte, index)

    def _get_conversion_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_conversion_df(metric, cte_prefix, collection_cte_value_cols)
        return dataframe_str_to_datetime(df, GA.DATETIME_COL)

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        return dataframe_str_to_datetime(df, GA.DATETIME_COL)

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_retention_df(metric, cte_prefix, collection_cte_value_cols)
        df = dataframe_str_to_datetime(df, GA.DATETIME_COL)
        return df

    def convert_to_datetime_if_needed(self, dt_col: FieldReference) -> Any:
        return SA.func.datetime(dt_col)

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.NUMBER:
            return super().get_field_numeric_value(field_ref, field)
        return SA.func.unicode(field_ref) * 31

    def _parse_type_name(self, type_name: str) -> M.DataType:
        if type_name == "DATETIME()":
            return M.DataType.DATETIME
        return M.DataType.STRING
