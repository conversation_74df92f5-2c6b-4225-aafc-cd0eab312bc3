from __future__ import annotations

from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union, cast

import pandas as pd
import psycopg2.errors as PE
import sqlalchemy as SQLA
import sqlalchemy.exc as SA_EXC
import sqlalchemy.sql.elements as ELM
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.selectable as SEL
from sqlalchemy import distinct, select
from sqlalchemy.orm import aliased

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
from mitzu.adapters.helper import string_array_to_array
from mitzu.adapters.model import (
    SAMPLED_SOURCE_CTE_NAME,
    ExecutableType,
    FieldReference,
    SelectType,
    is_select_type,
)
from mitzu.adapters.postgresql_adapter import (
    PostgresqlAdapter,
)

VALUES_COL_NAME = "values"


class RedshiftAdapter(PostgresqlAdapter):  # pragma: no cover
    def _generalize_warehouse_specific_error(
        self, exc: Exception
    ) -> Optional[Exception]:
        if not isinstance(exc, SA_EXC.DBAPIError):
            return None

        if isinstance(exc.orig, PE.InternalError):
            return GA.DWHServerError(exc)

        orig_error = str(exc.orig)
        if "Divide by zero" in orig_error:
            return GA.DivisionByZeroUserError(exc)

        if "Invalid digit" in orig_error or (
            "cannot cast type" in orig_error
            or "to double" in orig_error
            or "invalid input syntax for type double precision" in orig_error
        ):
            return GA.NotNumericFieldUserError(exc)

        return None

    def execute_query(
        self, query: ExecutableType, log_error_as_warning: bool = False
    ) -> pd.DataFrame:
        if is_select_type(query):
            query = cast(SelectType, query).limit(GA.MAX_ROW_LIMIT + 1)
            query = str(query.compile(compile_kwargs={"literal_binds": True}))
            query = query.replace(
                "%", "%%"
            )  # bugfix for redshift, which has string formatting
        return super().execute_query(
            query=query, log_error_as_warning=log_error_as_warning
        )

    def get_datetime_interval(
        self, field_ref: FieldReference, timewindow: M.TimeWindow
    ) -> Any:
        return SQLA.func.dateadd(str(timewindow.period), timewindow.value, field_ref)

    def get_dynamic_datetime_interval(
        self,
        field_ref: FieldReference,
        value_field_ref: FieldReference,
        time_group: M.TimeGroup,
        multiplier: int = 1,
        adding: int = 0,
    ) -> Any:
        val_ref = value_field_ref
        if multiplier != 1:
            val_ref = val_ref * multiplier
        if adding != 0:
            val_ref = val_ref + (adding)
        return SQLA.func.dateadd(str(time_group), val_ref, field_ref)

    def get_date_trunc(
        self,
        time_group: M.TimeGroup,
        field_ref: Union[FieldReference, datetime, str],
    ) -> Any:
        field_value = SQLA.cast(field_ref, SQLA.TIMESTAMP)  # type: ignore[type-var]
        return SQLA.func.date_trunc(time_group.name, field_value)

    def _get_conv_aggregation(self, metric: M.Metric, cte: SEL.CTE, index: int) -> Any:
        if metric._agg_type == M.AggType.PERCENTILE_TIME_TO_CONV:
            raise NotImplementedError(
                "Percentile calculation is not supported at the moment."
            )
        return super()._get_conv_aggregation(metric, cte, index)

    def number_as_datetime(self, field: FieldReference) -> FieldReference:
        # timestamp 'epoch' + t1.event_time_int * interval '1 second'
        return SQLA.literal_column("timestamp 'epoch'") + field * SQLA.literal_column(
            "interval '1 second'"
        )

    def correct_timestamp(self, dt: Union[datetime, date]) -> FieldReference:
        return SQLA.cast(super().correct_timestamp(dt), SQLA.TIMESTAMP)

    def _get_field_values_df(
        self,
        dataset_cte: SEL.CTE,
        data_table: M.DataTable,
        fields: List[M.Field],
        event_names: Optional[List[str]],
        indexing_context: M.IndexingContext,
    ) -> pd.DataFrame:
        # Redshift doesn't support ListAgg and ArrayAgg properly.
        # So the whole process needs to be rethought.
        # We discover with select distinct event_name, column_name_x from table.
        # We iterate through all columns in a loop. Also we use a window function to
        # pick only a sample of rows. We can't use group by at all for
        # discovering event field values.

        cte = aliased(
            dataset_cte,
            alias=SAMPLED_SOURCE_CTE_NAME,
            name=SAMPLED_SOURCE_CTE_NAME,
        )
        where = None
        if isinstance(data_table, M.EventDataTable):
            event_name_select_field = self.get_event_name_field(data_table, cte).label(
                GA.EVENT_NAME_ALIAS_COL
            )
            if not data_table.is_single_event_table and event_names is not None:
                where = event_name_select_field.in_(event_names)
        elif isinstance(data_table, M.DimensionDataTable):
            # let's temporarily create a 'dimension' fake event else pandas Dataframe groupby may filters out the values
            event_name_select_field = SQLA.literal("dimension").label(
                GA.EVENT_NAME_ALIAS_COL
            )
        else:
            raise ValueError(f"Unknown data table type: {data_table}")

        res_df: Optional[pd.DataFrame] = None
        for f in fields:
            query = select(
                columns=[
                    distinct(
                        event_name_select_field,
                    ),
                    self.get_field_reference(f, sa_table=cte).label(VALUES_COL_NAME),
                ],
                whereclause=where,
            )
            df = self.execute_query(query)

            # we cannot serialize types like pd.Timestamp types in the discovered project
            # and it may causes other issues, so it's better to convert them to string
            # right here and our codebase is already prepared to recognise them as timestamps
            def fix_data_types(val: Any) -> Any:
                if isinstance(val, pd.Timestamp):
                    return str(val)
                if val.__class__.__name__ in ["NoneType", "NaTType"]:
                    return None
                return val

            # need a newer pandas version to resolve this linter issue
            df = df.applymap(fix_data_types)  # type: ignore[operator]
            groupped = (
                df.groupby(df[GA.EVENT_NAME_ALIAS_COL])[VALUES_COL_NAME]
                .apply(lambda val: list(val))
                .reset_index()
            )
            groupped = groupped.rename(columns={VALUES_COL_NAME: f._get_name()})
            if res_df is None:
                res_df = groupped
            else:
                res_df = res_df.merge(
                    groupped,
                    left_on=GA.EVENT_NAME_ALIAS_COL,
                    right_on=GA.EVENT_NAME_ALIAS_COL,
                )
        if res_df is None:
            return pd.DataFrame()
        return res_df.set_index(GA.EVENT_NAME_ALIAS_COL)

    def _get_retention_df(
        self,
        metric: M.Metric,
        cte_prefix: Optional[str],
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        res_df = super()._get_retention_df(
            metric, cte_prefix, collection_cte_value_cols
        )
        if metric.config.aggregation.type not in [
            M.AggType.RETURN_UNIQUE_USERS,
            M.AggType.RETURN_DROP_OFF_USERS,
            M.AggType.RETURN_SUBSCRIBERS,
        ]:
            res_df[GA.AGG_VALUE_COL] = res_df[GA.AGG_VALUE_COL].astype(float)
        return res_df

    def _get_distinct_array_agg_func(
        self, field_ref: FieldReference, field: M.Field, compress_map_type: bool
    ) -> Any:
        return SQLA.func.SPLIT_TO_ARRAY(
            SQLA.func.listagg(SQLA.distinct(field_ref), ","), ","
        )

    def _get_segmentation_df(
        self,
        metric: M.Metric,
        collection_cte_value_cols: Optional[Dict[str, FieldReference]] = None,
    ) -> pd.DataFrame:
        df = super()._get_segmentation_df(metric, collection_cte_value_cols)
        if (
            metric.config is not None
            and metric.config.aggregation is not None
            and metric.config.aggregation.type in M.LIST_USER_AGGS
        ):
            return df
        return string_array_to_array(df)

    def missing_string_value(self) -> FieldReference:
        return SQLA.cast(SQLA.literal(None), SQLA.VARCHAR)

    def get_field_numeric_value(
        self, field_ref: FieldReference, field: M.Field
    ) -> SA_EXP.ColumnElement:
        if field.type == M.DataType.DATETIME:
            return SQLA.func.extract("EPOCH", field_ref) % 2973
        return super().get_field_numeric_value(field_ref, field)

    def get_collection_inclusion_condition(
        self,
        ref: FieldReference,
        agg_values_col: FieldReference,
    ) -> Union[ELM.ColumnElement, ELM.BooleanClauseList]:
        return ref.in_(
            SQLA.select(
                columns=[agg_values_col], whereclause=agg_values_col.is_not(None)
            )
        )
