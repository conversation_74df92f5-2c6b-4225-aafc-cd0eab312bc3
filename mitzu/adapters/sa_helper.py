from dataclasses import dataclass
from typing import Any, List, Optional, Union

import sqlalchemy as SA
import sqlalchemy.sql.expression as SA_EXP
import sqlalchemy.sql.selectable as SEL

import mitzu.adapters.model as AM


@dataclass(frozen=True)
class Join:
    table: SEL.CTE
    condition: SA_EXP.ColumnElement
    isouter: bool


def select(
    columns: List[Any],
    whereclause: Optional[AM.FilterCondition] = None,
    group_by: Optional[List[AM.GroupByFieldReference]] = None,
    select_from: Optional[
        Union[SA.sql.TableClause, SEL.CTE, SEL.Join, SA_EXP.Subquery]
    ] = None,
    joins: Optional[List[Join]] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
) -> SA_EXP.Select:
    """
    sqlalchemy 2.0 has a new query api which does not support Nones (like .groupby(None))
    but at many places we have optional parts and this helper helps us not writing the
    `is not None` conditions everywhere
    """
    query = SA.select(*columns)
    if select_from is not None:
        query = query.select_from(select_from)

    if whereclause is not None:
        query = query.where(whereclause)

    if group_by is not None:
        query = query.group_by(*group_by)

    if limit is not None:
        query = query.limit(limit)

    if offset is not None:
        query = query.offset(offset)

    if joins is not None:
        for join in joins:
            query = query.join(join.table, join.condition, isouter=join.isouter)
    return query
