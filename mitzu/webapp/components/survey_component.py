from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, <PERSON>ple

import dash_mantine_components as dmc
from dash import callback, html, no_update
from dash.dependencies import ALL, Input, Output, State
from dash_bootstrap_components import ModalBody, ModalFooter
from dash_iconify import DashIconify

import mitzu.webapp.model as WM
from mitzu.logger import LOGGER
from mitzu.webapp.dependencies import Dependencies
from mitzu.webapp.model import SurveySubmission, SurveyType
from mitzu.webapp.pages.constants import (
    NOTIFICATIONS_CONTAINER,
    PROJECT_HOME_LOCATION,
    SURVEY_INPUT,
)
from mitzu.webapp.pages.uikit import primary_button


@dataclass
class SurveyQuestion:
    id: str
    label: str
    options: Optional[List[str]] = None
    placeholder: Optional[str] = None


user_survey = [
    SurveyQuestion(
        id="analytics-software",
        label="Are you currently using any analytics software?",
        options=["Yes", "No"],
        placeholder="Select Yes or No",
    ),
    SurveyQuestion(
        id="primary-use-case",
        label="What is your primary use case for Mitzu?",
        options=[
            "Product analytics",
            "Marketing analytics",
            "Revenue analytics",
            "Sales analytics",
            "Dashboards and reporting",
        ],
        placeholder="Select your primary use case",
    ),
]


organization_survey = [
    SurveyQuestion(
        id="company-size-question",
        label="What is the size of your company?",
        options=[
            "1-10",
            "11-50",
            "51-200",
            "201-500",
            "501-2000",
            "2000+",
        ],
        placeholder="Select company size",
    ),
    SurveyQuestion(
        id="warehouse-setup-help",
        label="Do you need help with setting up your data warehouse?",
        options=["Yes", "No"],
        placeholder="Select an option",
    ),
    SurveyQuestion(
        id="referral-source",
        label="Where did you hear about Mitzu?",
        options=[
            "Linkedin",
            "Referral",
            "Blogs",
            "Search engine",
            "Review platform",
            "Community platform",
        ],
        placeholder="Select the source",
    ),
]

SUBMIT_SURVEY_BUTTON = "submit_survey_button"
SURVEY_MODAL = "survey-modal"


def generate_survey_elements(
    survey_questions: List[SurveyQuestion], survey_type: str
) -> List[dmc.Select]:
    """
    Generates the dynamic survey elements for the given list of questions.
    """
    elements = []
    for idx, question in enumerate(survey_questions):
        elements.append(
            dmc.Select(
                label=question.label,
                id={
                    "type": SURVEY_INPUT,
                    "index": f"{survey_type}-{idx}",
                },
                data=question.options,
                required=True,
                placeholder=question.placeholder,
                className="mb-3",
                size="sm",
            )
        )
    return elements


def show_survey_modal() -> dmc.Container:
    dependencies = Dependencies.get()
    storage = dependencies.storage
    if not dependencies.user:
        LOGGER.warning("Trying to access survey modal without a user")
        return dmc.Container()

    membership_id = dependencies.user.membership_id
    org_id = dependencies.user.org_id

    user_survey_submitted = storage.survey_submission_for_user(
        membership_id=membership_id,
        org_id=org_id,
    )
    organization_survey_submitted = storage.survey_submission_for_org(org_id)

    children = []

    if user_survey_submitted is None:
        children.extend(generate_survey_elements(user_survey, survey_type="user"))

    if organization_survey_submitted is None:
        children.extend(
            generate_survey_elements(organization_survey, survey_type="organization")
        )

    return dmc.Container(
        [
            dmc.Modal(
                id=SURVEY_MODAL,
                title=dmc.Text(
                    "Welcome to Mitzu",
                    size="lg",
                    weight=600,
                    className="text-center text-dark",
                ),
                children=[
                    ModalBody(
                        dmc.Text(
                            children=[
                                "Before we get started, please complete this short survey. ",
                                "It will help us fine-tune your experience and provide you with onboarding material.",
                            ],
                            size="md",
                            className="text-muted mb-3",
                        ),
                    ),
                    html.Div(children=children),
                    ModalFooter(
                        dmc.Group(
                            position="right",
                            children=[
                                primary_button(
                                    "Submit",
                                    id=SUBMIT_SURVEY_BUTTON,
                                    icon=DashIconify(icon="mdi:send"),
                                    disabled=True,
                                )
                            ],
                        )
                    ),
                ],
                size="lg",
                centered=True,
                opened=False,
                overlayOpacity=0.2,
                transition="fade",
                withCloseButton=False,
                closeOnClickOutside=False,
            ),
        ]
    )


@callback(
    Output(SURVEY_MODAL, "opened", allow_duplicate=True),
    Output(NOTIFICATIONS_CONTAINER, "children"),
    Input(SUBMIT_SURVEY_BUTTON, "n_clicks"),
    [State({"type": SURVEY_INPUT, "index": ALL}, "value")],
    prevent_initial_call=True,
)
def handle_submit_survey(
    n_clicks: int, values: List[Optional[str]]
) -> Tuple[bool, dmc.Notification]:
    try:
        dependencies = Dependencies.get()
        storage = dependencies.storage
        dependencies.tracking_service
        if dependencies.user is None:
            raise ValueError("User is not logged in.")
        membership_id = dependencies.user.membership_id
        org_id = dependencies.user.org_id

        user_survey_submitted = storage.survey_submission_for_user(
            membership_id=membership_id,
            org_id=org_id,
        )
        organization_survey_submitted = storage.survey_submission_for_org(org_id)

        user_survey_length = len(user_survey)
        user_answers = (
            values[:user_survey_length] if user_survey_submitted is None else []
        )
        org_answers = (
            values[user_survey_length:] if organization_survey_submitted is None else []
        )

        if user_survey_submitted is None and all(user_answers):
            user_survey_submission = SurveySubmission(
                membership_id=membership_id,
                org_id=org_id,
                answers={
                    q.label: answer for q, answer in zip(user_survey, user_answers)
                },
                survey_type=SurveyType.USER_SURVEY,
                submitted_at=datetime.now(),
            )
            storage.save_survey_submission(submission=user_survey_submission)
            dependencies.tracking_service.track_survey_submitted(
                submission=user_survey_submission
            )
        if organization_survey_submitted is None and all(org_answers):
            org_survey_submission = SurveySubmission(
                membership_id=membership_id,
                org_id=org_id,
                answers={
                    q.label: answer
                    for q, answer in zip(organization_survey, org_answers)
                },
                survey_type=SurveyType.ORGANIZATION_SURVEY,
                submitted_at=datetime.now(),
            )
            dependencies.tracking_service.track_survey_submitted(
                submission=org_survey_submission
            )
            storage.save_survey_submission(submission=org_survey_submission)

        return False, dmc.Notification(
            id="survey-success",
            title="Thank you!",
            message="Your answers have been submitted!",
            action="show",
        )

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to submit survey")
        return no_update, dmc.Notification(
            id="survey-error",
            title="Couldn't submit your answers",
            message="Something went wrong, if the issue persist please contact support.",
            action="show",
        )


@callback(
    Output(SUBMIT_SURVEY_BUTTON, "disabled"),
    [Input({"type": SURVEY_INPUT, "index": ALL}, "value")],
    prevent_initial_call=True,
)
def update_submit_button_state(values: List[Optional[str]]) -> bool:
    """Disables the submit button if any of the survey questions are unanswered."""

    return not all(values)


@callback(
    Output(SURVEY_MODAL, "opened"),
    Input(PROJECT_HOME_LOCATION, "pathname"),
)
def toggle_survey_modal(pathname: str) -> bool:
    try:
        dependencies = Dependencies.get()
        storage = dependencies.storage

        if dependencies.user is None:
            raise ValueError("User is not logged in")
        if (
            dependencies.user.first_login_at is not None
            and dependencies.user.first_login_at
            < datetime(2024, 10, 2)  # release date of the surveys
        ) or (
            dependencies.user.role in [WM.Role.SUPER_ADMIN_READ_ONLY, WM.Role.READ_ONLY]
        ):
            return False

        membership_id = dependencies.user.membership_id
        org_id = dependencies.user.org_id

        user_survey_submitted = storage.survey_submission_for_user(
            membership_id=membership_id,
            org_id=org_id,
        )
        organization_survey_submitted = storage.survey_submission_for_org(org_id)

        return user_survey_submitted is None or organization_survey_submitted is None

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to check survey submission status")
        return no_update
