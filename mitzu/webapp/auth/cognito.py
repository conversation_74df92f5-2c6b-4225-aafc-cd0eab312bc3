from __future__ import annotations

import os
from typing import Any, Dict, List, Optional

import mitzu.webapp.pages.paths as P
from mitzu import configs
from mitzu.webapp.auth.model import OAuthConfig, OAuthType


class Cognito:
    @classmethod
    def get_config(
        cls,
        pool_id: Optional[str] = None,
        region: Optional[str] = None,
        domain: Optional[str] = None,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        jwt_algo: Optional[List[str]] = None,
        identity_provider_id: Optional[str] = None,
    ) -> OAuthConfig:
        """
        Creates a new OAuthConfig instance using the Cognito specific settings.

        :param pool_id: ID of the AWS Cognito User Pool (something like ``<aws region>_xxxxxxxxx``),
            if it's not set then the ``COGNITO_POOL_ID`` environmental variable will be used
        :param region: AWS region of the AWS Cognito User Pool (eg. eu-west-1),
            if it's not set then the ``COGNITO_REGION`` environmental variable will be used
        :param domain: AWS Cognito domain set for the App integrations
            (``xxxxx`` from the ``https://xxxxx.auth.eu-west-1.amazoncognito.com``),
            if it's not set the the ``COGNITO_DOMAIN`` environmental variable will be used
        :param client_id: Client ID of the configured App,
            if it's not set then the ``COGNITO_CLIENT_ID`` environmental variable will be used
        :param client_secret: Client secret of the configured App,
            if it's not set then the ``COGNITO_CLIENT_SECRET`` environmental variable will be used
        :param jwt_algo: List of accepted JWT token signing algorithms, by default it's ``["RS256"]``
            it can be overwritted with the ``COGNITO_JWT_ALGORITHMS`` environmental variable
        """
        pool_id = cls.fallback_to_env_var(pool_id, "COGNITO_POOL_ID")
        region = cls.fallback_to_env_var(region, "COGNITO_REGION")
        domain = cls.fallback_to_env_var(domain, "COGNITO_DOMAIN")
        client_id = cls.fallback_to_env_var(client_id, "COGNITO_CLIENT_ID")
        client_secret = cls.fallback_to_env_var(client_secret, "COGNITO_CLIENT_SECRET")
        if jwt_algo is None:
            jwt_algo = ["RS256"]

        jwt_algo_config = cls.fallback_to_env_var(
            ",".join(jwt_algo), "COGNITO_JWT_ALGORITHMS"
        )
        jwt_algorithms = []
        if jwt_algo_config:
            jwt_algorithms = jwt_algo_config.split(",")

        if client_id is None:
            raise ValueError("Client ID should be set")

        if client_secret is None:
            raise ValueError("Client secret should be set")

        return OAuthConfig(
            client_id=client_id,
            client_secret=client_secret,
            jwks_url=f"https://cognito-idp.{region}.amazonaws.com/{pool_id}/.well-known/jwks.json",
            sign_in_base_url=f"https://{domain}/oauth2/authorize",
            sign_in_params={
                "client_id": client_id,
                "response_type": "code",
                "scope": "email+openid",
            },
            sign_out_url=f"https://{domain}/logout?client_id={client_id}&logout_uri={configs.HOME_URL}{P.UNAUTHORIZED_URL}",
            token_url=f"https://{domain}/oauth2/token",
            jwt_algorithms=jwt_algorithms,
            type=OAuthType.COGNITO,
            identity_provider_id=identity_provider_id,
        )

    @classmethod
    def fallback_to_env_var(cls, value: Optional[str], env_var: str) -> Optional[str]:
        if value is not None:
            return value
        if env_var == "COGNITO_CLIENT_ID":
            return configs.secrets.cognito_client_id
        if env_var == "COGNITO_CLIENT_SECRET":
            return configs.secrets.cognito_client_secret
        return os.getenv(env_var)

    @classmethod
    def sign_in_url_with_google(cls, oauth_config: OAuthConfig, auth_state: str) -> str:
        return (
            f"https://signin.mitzu.io/oauth2/authorize?identity_provider=Google&redirect_uri={configs.HOME_URL}"
            f"{P.OAUTH_CODE_URL}&response_type=CODE&client_id={oauth_config.client_id}&state={auth_state}&scope=email%20openid"
        )

    @classmethod
    def create_from_dict(
        cls,
        config: Dict[str, Any],
        secret: str,
        identity_provider_id: Optional[str],
    ) -> OAuthConfig:
        jwt_algo = config.get("jwt_algo")
        if jwt_algo is not None and not isinstance(jwt_algo, list):
            jwt_algo = None
        return cls.get_config(
            pool_id=config["pool_id"],
            region=config["region"],
            domain=config["domain"],
            client_id=config["client_id"],
            client_secret=secret,
            jwt_algo=jwt_algo,
            identity_provider_id=identity_provider_id,
        )
