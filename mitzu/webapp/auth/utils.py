from typing import Optional
from urllib import parse

from mitzu.webapp.auth.model import UnauthorizedReason
from mitzu.webapp.pages.paths import UNAUTHORIZED_URL


def get_query_param(request_url: str, param_name: str) -> Optional[str]:
    parse_result = parse.urlparse(request_url)
    params = parse.parse_qs(parse_result.query)
    code_ls = params.get(param_name)
    if code_ls is not None:
        return code_ls[0]
    return None


def get_unauthorized_url(error: UnauthorizedReason) -> str:
    return UNAUTHORIZED_URL + "?errcode=" + error.value
