import base64
from dataclasses import dataclass

import requests

import mitzu.webapp.pages.paths as P
from mitzu import configs
from mitzu.cache import Mitzu<PERSON><PERSON>
from mitzu.webapp.auth.model import (
    OAuthConfig,
    OAuthType,
    TokenValidator,
)


class OAuthError(Exception):
    pass


@dataclass(frozen=True)
class OAuthCodeChecker:
    token_validator: TokenValidator
    oauth_config: OAuthConfig
    cache: MitzuCache

    def get_user_email(self, code: str) -> str:
        identity_token = self._exchange_code_to_identity_token(code)
        return self._exchange_identity_token_to_user_email(identity_token)

    def _exchange_code_to_identity_token(self, auth_code: str) -> str:
        payload = {
            "grant_type": "authorization_code",
            "client_id": self.oauth_config.client_id,
            "code": auth_code,
            "redirect_uri": f"{configs.HOME_URL}{P.OAUTH_CODE_URL}",
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        if self.oauth_config.type in [OAuthType.COGNITO, OAuthType.GOOGLE]:
            message = bytes(
                f"{self.oauth_config.client_id}:{self.oauth_config.client_secret}",
                "utf-8",
            )
            secret_hash = base64.b64encode(message).decode()
            headers["Authorization"] = f"Basic {secret_hash}"
        elif self.oauth_config.type == OAuthType.OIDC:
            payload["client_secret"] = self.oauth_config.client_secret
        else:
            raise ValueError(f"Unknown oauth type: {self.oauth_config.type}")

        resp = requests.post(self.oauth_config.token_url, data=payload, headers=headers)

        if resp.status_code != 200:
            raise OAuthError(
                f"Unexpected response: {resp.status_code}, {resp.content.decode('utf-8')}"
            )

        return resp.json()["id_token"]

    def _exchange_identity_token_to_user_email(self, token: str) -> str:
        try:
            decoded_token = self.token_validator.validate_token(token)
            user_email = decoded_token.get("email")
            if user_email is None:
                raise OAuthError("Email field is missing from the identity token")

            return user_email
        except Exception as exc:
            raise OAuthError(f"Failed to validate foreign token: {str(exc)}") from exc
