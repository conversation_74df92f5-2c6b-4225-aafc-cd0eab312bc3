from __future__ import annotations

import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, cast
from urllib import parse

import flask
import jwt
from dash import ctx

import mitzu.configs as configs
import mitzu.webapp.auth.model as AM
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.service.org_manager as OM
from mitzu.cache import MitzuCache
from mitzu.logger import LOGGER
from mitzu.webapp.auth.auth_config_manager import (
    AuthConfigManager,
)
from mitzu.webapp.auth.auth_state_cache import (
    AuthStateCache,
)
from mitzu.webapp.auth.cognito import Cognito
from mitzu.webapp.auth.dash_request_parser import (
    get_dash_update_component_payload,
    is_dash_update_component_whitelisted,
)
from mitzu.webapp.auth.google import GoogleOAuth
from mitzu.webapp.auth.model import (
    DEFAULT_IDP_ID,
    JWT_ALGORITHM,
    LAST_ORG_ID_COOKIE_NAME,
    LAST_WORKSPACE_ID_COOKIE_NAME,
    REDIRECT_COOKIE_NAME,
    TOKEN_COOKIE_NAME,
    JWTTokenValidator,
    OAuthConfig,
    RequestContext,
    ResponseType,
)
from mitzu.webapp.auth.oauth_code_checker import (
    OAuthCodeChecker,
)
from mitzu.webapp.auth.response_generator import (
    ResponseGenerator,
)
from mitzu.webapp.auth.utils import get_query_param
from mitzu.webapp.service.path_parser import PathParser

CONFIG_KEY = "cloud_authorizer"
DASH_UPDATE_COMPONENT_PATH = "/_dash-update-component"

API_KEY_HEADER = "x-api-key"


@dataclass(frozen=True)
class CloudAuthorizer:
    """
    Authorizes a request
     - with SSO
     - semantically validates the token
     - delegates the authorization to an OrgAuthorizer instance if the token
       contains an org_id
    """

    default_oauth: OAuthConfig
    _org_manager: OM.OrgManager
    _cache: MitzuCache
    _auth_state_cache: AuthStateCache
    _response_generator: ResponseGenerator = field(
        default_factory=lambda: ResponseGenerator()
    )

    @property
    def _authorized_url_prefixes(self) -> List[str]:
        return [
            P.UNAUTHORIZED_URL,
            P.HEALTHCHECK_PATH,
            P.UI_ERROR_PATH,
            P.API_ROOT,
            "/assets/",
            "/_dash-component-suites/",
            "/_dash-layout",
            "/_dash-dependencies",
        ]

    @property
    def _ignore_token_refresh_prefixes(self) -> List[str]:
        return [
            P.UNAUTHORIZED_URL,
            P.SIGN_OUT_URL,
            P.HEALTHCHECK_PATH,
            P.UI_ERROR_PATH,
            "/assets/",
            "/_dash-component-suites/",
        ]

    def get_home_url(self, request: flask.Request) -> str:
        home_url = request.url_root

        if "localhost" in home_url:
            # trailing slashes can cause issues with SSO login
            return home_url.strip("/")

        url = parse.urlparse(home_url)
        return f"https://{url.hostname}"

    def get_fixed_request_url(
        self, request: flask.Request, domain: Optional[str] = None
    ) -> str:
        if domain is None:
            domain = self.get_home_url(request)
        path = request.path
        query_string = request.query_string.decode("utf-8")
        if query_string:
            query_string = f"?{query_string}"

        url = f"{domain}{path}{query_string}"
        return url

    def get_auth_token(self, request: flask.Request) -> Optional[str]:
        cookie_token = request.cookies.get(TOKEN_COOKIE_NAME)
        if cookie_token is not None:
            return cookie_token

        # because of securirty reasons we cannot share the auth cookies between
        # the Miro iframes/fetch calls, the only method found so far to share a
        # session is to pass the auth token as a query parameter
        url = PathParser(request.full_path)
        return url.get_query_value("access_token")

    # tested with browser tests
    def get_last_org_id(
        self, request: flask.Request
    ) -> Optional[str]:  # pragma: no cover
        return request.cookies.get(LAST_ORG_ID_COOKIE_NAME)

    @classmethod
    def create(
        cls,
        org_manager: OM.OrgManager,
        cache: MitzuCache,
        auth_config: Optional[AuthConfigManager] = None,
    ) -> CloudAuthorizer:
        if auth_config is not None:
            default_config = auth_config.default_oauth
        else:
            auth_backend = configs.AUTH_BACKEND

            if auth_backend == "cognito":
                default_config = Cognito.get_config()
            elif auth_backend == "google":
                default_config = GoogleOAuth.get_config()
            else:
                raise ValueError(f"Unknown auth backend: {auth_backend}")

        authorizer = CloudAuthorizer(
            default_oauth=default_config,
            _org_manager=org_manager,
            _cache=cache,
            _auth_state_cache=AuthStateCache(cache),
        )
        return authorizer

    @classmethod
    def get(
        cls,
    ) -> CloudAuthorizer:
        return cast(CloudAuthorizer, flask.current_app.config.get(CONFIG_KEY))

    def _get_token_claims_for_login_identity(
        self, login_identity: AM.LoginIdentity
    ) -> Dict[str, Any]:
        user = self._org_manager.get_user_by_login_identity_with_default_org(
            login_identity
        )
        if user is None:
            return {
                "sub": login_identity.email,
                "idp": login_identity.idp_id
                if login_identity.idp_id is not None
                else DEFAULT_IDP_ID,
            }
        self._org_manager.track_login(user)
        return self.__get_token_for_user(user)

    def __get_token_for_user(self, user: WM.User) -> Dict[str, Any]:
        return {
            "sub": user.id,
            "org": user.org_id,
        }

    def authorize_request(self, request: flask.Request) -> Optional[ResponseType]:
        """
        Performs authorization on the current flask requests
        Returns None if the request is authorized or returns a response object
        containing a redirection or other HTTP response
        """
        self.get_home_url(request)
        auth_config_manager = AuthConfigManager.create(
            self.default_oauth,
            self._org_manager._cloud_storage,
            self._cache,
        )
        oauth_config = auth_config_manager.default_oauth

        # starting the enterprise login is authorized only when the method is correct
        if request.path == P.ENTERPRISE_LOGIN and request.method == "POST":
            return None

        # do not block google crawlers
        if request.path == P.ROBOTS_TXT:
            return None

        # do not block stripe webhook
        if request.path == P.STRIPE_WEBHOOK:
            return None

        # redirects the user to the Google SSO of the Cognito
        if request.path == P.REDIRECT_TO_GOOGLE_LOGIN_URL:
            auth_state = self._auth_state_cache.initiate_login_flow(DEFAULT_IDP_ID)
            url = Cognito.sign_in_url_with_google(oauth_config, auth_state)
            resp = self._redirect(url)
            return resp

        # redirects the user to the configured SSO sign in url
        if P.path_template_matches(P.REDIRECT_TO_LOGIN_URL, request.path):
            if P.path_template_matches(P.REDIRECT_TO_LOGIN_WITH_IDP_URL, request.path):
                idp_id = P.get_path_value(
                    P.REDIRECT_TO_LOGIN_WITH_IDP_URL, request.path, P.IDP_ID
                )
                oauth_config = auth_config_manager.get_oauth_config_by_idp_id(idp_id)
            else:
                idp_id = oauth_config.identity_provider_id or DEFAULT_IDP_ID

            auth_state = self._auth_state_cache.initiate_login_flow(idp_id)
            resp = self._redirect(oauth_config.sign_in_url(state=auth_state))
            return resp

        # clears the cookies when sign out if signed in, the user needs to be navigated
        # to this URL with dcc.Location(..., refresh=True), else it will be a dash api call
        if request.path == P.SIGN_OUT_URL:
            response = self._get_unauthenticated_response(oauth_config)
            self.clear_cookie(response, LAST_ORG_ID_COOKIE_NAME)
            return response

        # URLs eg. healthcheck, assets are served without any authorization
        for prefix in self._authorized_url_prefixes:
            if request.path.startswith(prefix):
                return None

        # allow loading the dash app for publicly shared dashboard urls
        if P.path_template_matches(P.SHARED_DASHBOARD_PATH, request.path):
            return None

        # allow loading the dash app for publicly shared dashboard urls
        if P.path_template_matches(P.SHARED_INSIGHT_PATH, request.path):
            return None

        # all callbacks are authorized but some of them are whitelisted based
        # on their request body
        if request.path.startswith(DASH_UPDATE_COMPONENT_PATH):
            dash_request_body = get_dash_update_component_payload(request)
            # authorize whitelisted dash update component calls
            if dash_request_body is not None and is_dash_update_component_whitelisted(
                dash_request_body,
            ):
                return None

        # authentication performed on the SSO side, the SSO provider
        # redirected the user to the mitzu with an oauth code
        # this code need to be validated with the SSO server and if
        # it is good then the mitzu session can be started by issuing
        # a new auth token
        if request.path == P.OAUTH_CODE_URL:
            return self._check_oauth_code(auth_config_manager, request)

        auth_token = self.get_auth_token(request)
        if auth_token is None:  # unauthorized
            auth_token = self.get_anonymous_user_token_if_configured()
            if auth_token is None:
                return self._get_unauthenticated_response(
                    oauth_config, self.get_fixed_request_url(request)
                )

        # after this point the user is authorized but maybe not part of any orgs
        try:
            claims = self.parse_token(auth_token)
            if claims is None:
                LOGGER.bind(auth_token=auth_token).warning(
                    "failed to parse token claims"
                )
                return self._get_unauthenticated_response(oauth_config)
            sub = claims["sub"]
            idp_id = cast(str, claims.get("idp"))
            login_identity = AM.LoginIdentity.create_from_token_subject(sub, idp_id)
            org_id = claims.get("org", None)

        except Exception as exc:
            LOGGER.opt(exception=exc).warning("failed to parse auth token")
            return self._get_unauthenticated_response(oauth_config)

        # the user is changing an organisation, after double checking the org membership
        # a new auth token is set with a different org_id, the user needs to be navigated
        # to this URL with dcc.Location(..., refresh=True), else it will be a dash api call
        if P.path_template_matches(P.CHANGE_ORGANISATION, request.path):
            org_id = P.get_path_value(
                P.CHANGE_ORGANISATION, request.path, P.ORG_ID_PATH_PATH
            )
            user = self._org_manager.get_user_by_login_identity_within_org(
                login_identity, org_id
            )
            if user is None:
                return self._get_unauthenticated_response(oauth_config)

            token = self._generate_new_token_with_claims(
                {"sub": user.id, "org": org_id}
            )
            redirection = request.args.get("redirect_to")
            response = self._redirect(redirection or P.HOME_PATH)
            self.set_cookie(response, TOKEN_COOKIE_NAME, token)
            self.set_cookie(response, LAST_ORG_ID_COOKIE_NAME, org_id)
            self.clear_cookie(response, LAST_WORKSPACE_ID_COOKIE_NAME)
            return response

        # the user is not member of any org, he needs to be redirected to the create org page
        # when he is not already there
        if org_id is None:
            if not request.path.startswith(P.CREATE_ORGANISATION):
                return self._redirect(P.CREATE_ORGANISATION)
            else:
                return None

        # just for safety if the user is part of any organisations then he can't open the
        # intro or create org pages to avoid duplicated orgs and other anomalies
        if request.path.startswith(P.INTRO_PAGE) or request.path.startswith(
            P.CREATE_ORGANISATION
        ):
            return self._redirect(P.HOME_PATH)

        # check the user membership in the org
        user = self._org_manager.get_user_by_login_identity_within_org(
            login_identity, org_id
        )
        if user is None:
            return self._get_unauthenticated_response(oauth_config)

        # successfully authenticated
        return None

    def _check_oauth_code(
        self, auth_config_manager: AuthConfigManager, request: flask.Request
    ) -> Optional[ResponseType]:

        request_url = self.get_fixed_request_url(request)
        oauth_config = auth_config_manager.default_oauth

        try:
            code = get_query_param(request_url, "code")
            if code is None:
                raise ValueError("code is missing from the request")

            state = get_query_param(request_url, "state")
            if state is None:
                raise ValueError("state is missing from the request")

            LOGGER.warning(f"Redirected with code={code}, state={state}")

            idp_id_from_auth_state = self._auth_state_cache.get_auth_state(state)
            if idp_id_from_auth_state is None:
                raise ValueError(f"Auth attempt with invalid state, state={state}")

            oauth_config = auth_config_manager.get_oauth_config_by_idp_id(
                idp_id_from_auth_state
            )
            code_checker = OAuthCodeChecker(
                JWTTokenValidator.create_from_oauth_config(oauth_config),
                oauth_config,
                self._cache,
            )
            # validate the oauth code with the remote server

            user_email = code_checker.get_user_email(code)

            # redirect the user to the home url or to the redirect cookie value
            fallback_redirect_url = configs.HOME_URL
            redirect_url = flask.request.cookies.get(
                REDIRECT_COOKIE_NAME, fallback_redirect_url
            )
            if not self._response_generator.is_redirection_url_valid(redirect_url):
                redirect_url = fallback_redirect_url

            # create new auth tokens
            login_identity = AM.LoginIdentity.from_email(
                user_email, idp_id=oauth_config.identity_provider_id
            )
            token_claims = self._get_token_claims_for_login_identity(login_identity)
            token = self._generate_new_token_with_claims(token_claims)

            # assemble the successful auth response
            resp = self._redirect(redirect_url)
            self.set_cookie(resp, TOKEN_COOKIE_NAME, token)
            self.clear_cookie(resp, REDIRECT_COOKIE_NAME)
            return resp
        except Exception as exc:
            LOGGER.opt(exception=exc).warning("Failed to authenticate")
            return self._get_unauthenticated_response(oauth_config)

    def refresh_auth_token(
        self, request: flask.Request, response: ResponseType
    ) -> ResponseType:
        request = flask.request

        # tokens are not refreshed for assets
        for prefix in self._ignore_token_refresh_prefixes:
            if request.path.startswith(prefix):
                return response

        # auth token already set on this path, it should not be refreshed
        # eg. it's set because of changing the current org
        if P.path_template_matches(P.CHANGE_ORGANISATION, request.path):
            return response

        auth_token = self.get_auth_token(flask.request)

        # nothing to refresh
        if auth_token is None:
            return response

        try:
            claims = self.parse_token(auth_token)
            if claims is None:  # this should never happen
                raise ValueError("Failed to parse auth token claims")

            sub = claims.get("sub", None)
            idp_id = claims.get("idp", None)
            login_identity = AM.LoginIdentity.create_from_token_subject(sub, idp_id)
            org_id = claims.get("org", None)
            issued_at = claims.get("iat", 0)

            if org_id is None:
                # org_id can be None when:
                #  - user is not part of any orgs
                #  - user was put into an org in this request
                #
                # If the user was just put into an org then it's auth token
                # need to be refreshed to contain the actual org id

                if login_identity.user_id_or_none is None:
                    login_identity.user_id_or_none = (
                        self._org_manager.get_user_id_by_login_identity(login_identity)
                    )

                user = self._org_manager.get_user_by_login_identity_with_default_org(
                    login_identity
                )
                if user:
                    self._org_manager.track_login(user)
                    self.set_cookie(response, LAST_ORG_ID_COOKIE_NAME, user.org_id)
            else:
                user = self._org_manager.get_user_by_login_identity_within_org(
                    login_identity, org_id
                )
                self.set_cookie(response, LAST_ORG_ID_COOKIE_NAME, org_id)

            # user is not part of any orgs, just return the current token
            if user is None:
                return response

            # if the token already has an org_id and it's grand new then we don't need
            # to refresh it for every requests
            if (
                org_id is not None
                and issued_at >= int(time.time()) - configs.AUTH_TOKEN_REFRESH_TIMEOUT
            ):
                cookie_token = flask.request.cookies.get(TOKEN_COOKIE_NAME)
                if cookie_token is None:
                    self.set_cookie(response, TOKEN_COOKIE_NAME, auth_token)
                return response

            # update the token claims with the latest user role
            token = self._generate_new_token_with_claims(
                {"sub": user.id, "org": user.org_id}
            )

            # set the new token and return
            self.set_cookie(response, TOKEN_COOKIE_NAME, token)
            return response

        except Exception as exc:
            LOGGER.warning(f"Failed to parse auth token when refreshing it: {str(exc)}")
            # before request call redirected the request already to login
            return response

    def _get_unauthenticated_response(
        self, auth_config: AM.OAuthConfig, redirect_url: Optional[str] = None
    ) -> ResponseType:
        return self._response_generator.get_unauthenticated_response(
            auth_config, redirect_url
        )

    def _redirect(self, location: str) -> ResponseType:
        return self._response_generator.get_redirection(location)

    def _generate_new_token_with_claims(
        self,
        claims: Dict[str, Any],
    ) -> str:
        now = int(time.time())
        claims["iat"] = now - 10
        claims["exp"] = now + configs.AUTH_SESSION_TIMEOUT
        claims["iss"] = "mitzu"
        return jwt.encode(claims, key=configs.AUTH_JWT_SECRET, algorithm=JWT_ALGORITHM)

    def parse_token(self, token: str) -> Optional[Dict]:
        try:
            return jwt.decode(
                token, configs.AUTH_JWT_SECRET, algorithms=[JWT_ALGORITHM]
            )
        except Exception:
            # it should be logged but if the token is invalid then the logger will try to parse it again causing an
            # infinite recursion
            # LOGGER.warning(f"Failed to parse token: {str(exc)}")
            return None

    # browser tests provide a way better coverage on this than unit tests
    def get_request_context(self) -> RequestContext:  # pragma: no cover
        """
        This should not log any errors since it will cause an infinite recursion with the current
        logger implementation which tries to extend the current logging context with the request context
        """
        auth_token = self.get_auth_token(flask.request)
        last_org_id = self.get_last_org_id(flask.request)
        eventn_id = flask.request.cookies.get("__eventn_id")
        ga_id = flask.request.cookies.get("_ga")
        user_agent = flask.request.headers.get("User-Agent")
        referer = flask.request.referrer

        if auth_token is None:
            auth_token = self.get_anonymous_user_token_if_configured()
            if auth_token is None:
                return RequestContext(
                    user_id=None,
                    org_id=None,
                    eventn_id=eventn_id,
                    user_agent=user_agent,
                    ga_id=ga_id,
                    idp_id=None,
                    last_org_id=last_org_id,
                    referer=referer,
                )
        token_claims = self.parse_token(auth_token)
        if token_claims is None:
            return RequestContext(
                user_id=None,
                org_id=None,
                eventn_id=eventn_id,
                user_agent=user_agent,
                ga_id=ga_id,
                idp_id=None,
                last_org_id=last_org_id,
                referer=referer,
            )
        return RequestContext(
            user_id=token_claims.get("sub"),
            org_id=token_claims.get("org"),
            eventn_id=eventn_id,
            user_agent=user_agent,
            ga_id=ga_id,
            idp_id=token_claims.get("idp"),
            last_org_id=last_org_id,
            referer=referer,
        )

    def get_anonymous_user_token_if_configured(self) -> Optional[str]:
        if configs.AUTO_LOGIN_EMAIL is None or configs.AUTO_LOGIN_ORG is None:
            return None

        org_id = configs.AUTO_LOGIN_ORG
        login_identity = AM.LoginIdentity.from_email(
            configs.AUTO_LOGIN_EMAIL, idp_id=None
        )
        # FIXME: we may don't need to lookup the userid
        user_id = self._org_manager.get_user_id_by_login_identity(login_identity)
        if user_id is None:
            return None
        login_identity = AM.LoginIdentity.from_user_id(user_id)
        user = self._org_manager.get_user_by_login_identity_within_org(
            login_identity, org_id
        )
        if user is None:
            return None
        return self._generate_new_token_with_claims({"sub": user.id, "org": org_id})

    def get_last_workspace_id(self) -> Optional[str]:
        return flask.request.cookies.get(LAST_WORKSPACE_ID_COOKIE_NAME)

    def set_last_workspace_id(self, workspace_id: str) -> None:
        self.set_cookie(ctx.response, LAST_WORKSPACE_ID_COOKIE_NAME, workspace_id)

    def set_cookie(
        self,
        response: ResponseType,
        cookie_name: str,
        value: str,
        expires: Optional[int] = None,
    ) -> None:
        response.set_cookie(
            cookie_name,
            value,
            path="/",
            httponly=True,
            expires=expires,
            secure=configs.ENVIRONMENT != "local",
            samesite="Lax",
        )

    def clear_cookie(self, response: ResponseType, cookie_name: str) -> None:
        self.set_cookie(response, cookie_name, "", expires=0)
