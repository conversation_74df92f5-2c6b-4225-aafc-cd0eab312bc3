from dataclasses import dataclass
from typing import List, Optional
from urllib import parse

import flask

import mitzu.webapp.pages.paths as P
from mitzu import __version__, configs
from mitzu.logger import LOGGER
from mitzu.webapp.auth.model import (
    MITZU_VERSION_COOKIE_NAME,
    REDIRECT_COOKIE_NAME,
    TOKEN_COOKIE_NAME,
    OAuthConfig,
    ResponseType,
)


@dataclass
class ResponseGenerator:
    @property
    def _invalid_redirection_url_prefixes(self) -> List[str]:
        return [
            "/assets/",
            "/_dash",
            "/_reload-hash",
            "/auth",
            P.HEALTHCHECK_PATH,
            P.CREATE_ORGANISATION,
            P.CHANGE_ORGANISATION,
            P.UI_ERROR_PATH,
            "/robots.txt",
        ]

    def is_redirection_url_valid(self, redirect_url: str) -> bool:
        url = parse.urlparse(redirect_url)
        if url.path == "":
            return False

        for prefix in self._invalid_redirection_url_prefixes:
            if url.path.startswith(prefix):
                return False
        return True

    def get_unauthenticated_response(
        self, oauth_config: OAuthConfig, redirect_url: Optional[str] = None
    ) -> ResponseType:
        LOGGER.debug(f"Unauthenticated response, redirect_url={redirect_url}")
        resp = self.get_redirection(oauth_config.sign_out_url)
        self.clear_cookie(resp, TOKEN_COOKIE_NAME)
        if redirect_url and self.is_redirection_url_valid(redirect_url):
            self.set_cookie(resp, REDIRECT_COOKIE_NAME, redirect_url)
        return resp

    def get_redirection(self, location: str) -> ResponseType:
        LOGGER.debug(f"Redirect response, location={location}")
        resp = flask.redirect(code=307, location=location)
        resp.headers["Pragma"] = "no-cache"
        resp.headers["Expires"] = "0"
        resp.headers["Cache-Control"] = "public, max-age=0"
        return resp

    def set_cookie(
        self,
        response: ResponseType,
        cookie_name: str,
        value: str,
        expires: Optional[int] = None,
    ) -> None:
        response.set_cookie(
            cookie_name,
            value,
            path="/",
            httponly=True,
            expires=expires,
            secure=configs.ENVIRONMENT != "local",
            samesite="Lax",
        )

    def set_miro_auth_cookie(
        self,
        response: ResponseType,
        cookie_name: str,
        value: str,
        expires: Optional[int] = None,
    ) -> None:
        domain = None
        if configs.ENVIRONMENT != "local":
            domain = ".mitzu.io"
        response.set_cookie(
            cookie_name,
            value,
            path="/",
            httponly=False,
            expires=expires,
            secure=True,
            samesite="None",
            domain=domain,
        )

    def clear_cookie(self, response: ResponseType, cookie_name: str) -> None:
        self.set_cookie(response, cookie_name, "", expires=0)

    def set_version_cookie_when_needed(
        self, request_path: str, response: ResponseType
    ) -> None:
        if response.status_code != 200:
            return

        for prefix in [
            "/assets",
            "/_dash",
        ]:
            if request_path.startswith(prefix):
                return

        self.set_cookie(response, MITZU_VERSION_COOKIE_NAME, __version__)
