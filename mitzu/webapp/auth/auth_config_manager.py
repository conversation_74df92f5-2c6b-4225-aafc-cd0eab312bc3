from __future__ import annotations

from dataclasses import dataclass
from typing import Dict

import mitzu.webapp.storage.cloud_storage as CS
from mitzu import __version__
from mitzu.cache import MitzuCache
from mitzu.logger import LOGGER
from mitzu.webapp.auth.model import (
    DEFAULT_IDP_ID,
    OAuthConfig,
)

AUTH_CONFIG_CACHE_KEY = f"{__version__}_auth_configs"
AUTH_CONFIG_EXPIRATION = 60 * 60 * 24


@dataclass(frozen=True)
class AuthConfigManager:
    default_oauth: OAuthConfig
    oauth_configs: Dict[str, OAuthConfig]
    idp_id_by_org_id: Dict[str, str]

    @classmethod
    def create(
        self,
        default_oauth: OAuthConfig,
        cloud_storage: CS.CloudStorage,
        cache: MitzuCache,
    ) -> AuthConfigManager:
        oauth_configs = cache.get(AUTH_CONFIG_CACHE_KEY)
        if oauth_configs is None:
            LOGGER.info("loading auth configs from the database ")
            oauth_configs = cloud_storage.get_oauth_configs()
            cache.put(
                AUTH_CONFIG_CACHE_KEY, oauth_configs, expire=AUTH_CONFIG_EXPIRATION
            )

        oauth_configs_by_idp_id = {
            config.identity_provider_id: config for config in oauth_configs.values()
        }
        idp_id_by_org_id = {
            org_id: config.identity_provider_id
            for org_id, config in oauth_configs.items()
        }

        return AuthConfigManager(
            default_oauth=default_oauth,
            oauth_configs=oauth_configs_by_idp_id,
            idp_id_by_org_id=idp_id_by_org_id,
        )

    def get_oauth_config_by_idp_id(self, idp_id: str) -> OAuthConfig:
        return self.oauth_configs.get(idp_id, self.default_oauth)

    def get_idp_for_org_id(self, org_id: str) -> str:
        return self.idp_id_by_org_id.get(org_id, DEFAULT_IDP_ID)

    @classmethod
    def clear_cached_config(self, cache: MitzuCache) -> None:
        cache.clear(AUTH_CONFIG_CACHE_KEY)
