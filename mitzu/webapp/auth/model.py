from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional

import jwt
import werkzeug

import mitzu.webapp.pages.paths as P
from mitzu import configs

TOKEN_COOKIE_NAME = "auth-token"
REDIRECT_COOKIE_NAME = "redirect-to"
LAST_WORKSPACE_ID_COOKIE_NAME = "last-workspace-id"
LAST_ORG_ID_COOKIE_NAME = "last-org-id"
DEFAULT_IDP_ID = "mitzu-global"
MITZU_VERSION_COOKIE_NAME = "mitzu-version"

ResponseType = werkzeug.wrappers.response.Response


class OAuthType(Enum):
    COGNITO = "cognito"
    GOOGLE = "google"
    OIDC = "oidc"


class UnauthorizedReason(Enum):
    SESSION_EXPIRED = "session-expired"


@dataclass(frozen=True)
class OAuthConfig:
    """
    Contains the minimal configuration for an OAuth backend.

    :param client_id: Client ID, used for validing the JWT token claim and for fetching the identity token
    :param client_secret: Client secret, used for fetching the identity toke
    :param jwks_url: URL to fetch the JSON Web Key Set (JWKS)
    :param sign_in_base_url: URL to where the user is redirected at the beginning of the sign in flow
    :param sign_in_params: URL params to pass when signing in
    :param token_url: URL where the tokens can be fetched during the sign in flow
    :param jwt_algorithms: List of supported signing algorithms for the JWT tokens
    """

    client_id: str
    client_secret: str
    jwks_url: str
    sign_in_base_url: str
    sign_in_params: Dict[str, str]
    sign_out_url: str
    token_url: str
    jwt_algorithms: List[str]
    type: OAuthType
    identity_provider_id: Optional[str]

    def sign_in_url(self, state: str) -> str:
        params = self.sign_in_params
        params["redirect_uri"] = f"{configs.HOME_URL}{P.OAUTH_CODE_URL}"
        params["state"] = state
        return f"{self.sign_in_base_url}?{'&'.join(f'{key}={value}' for key, value in params.items())}"


JWT_ALGORITHM = "HS256"


@dataclass
class RequestContext:
    user_id: Optional[str]
    org_id: Optional[str]
    eventn_id: Optional[str]
    user_agent: Optional[str]
    ga_id: Optional[str]
    idp_id: Optional[str]
    last_org_id: Optional[str]
    referer: Optional[str]


class TokenValidator(ABC):  # pragma: no cover
    @abstractmethod
    def validate_token(self, token: str) -> Dict[str, Any]:
        pass


class JWTTokenValidator(TokenValidator):
    def __init__(self, jwks_url: str, algorithms: List[str], audience: str):
        self._jwks_client = jwt.PyJWKClient(jwks_url)
        self._algorithms = algorithms
        self._audience = audience

    def validate_token(self, token: str) -> Dict[str, Any]:
        signing_key = self._jwks_client.get_signing_key_from_jwt(token)
        return jwt.decode(
            token,
            signing_key.key,
            algorithms=self._algorithms,
            audience=self._audience,
        )

    @staticmethod
    def create_from_oauth_config(oauth_config: OAuthConfig) -> JWTTokenValidator:
        return JWTTokenValidator(
            oauth_config.jwks_url,
            oauth_config.jwt_algorithms,
            oauth_config.client_id,
        )

    def __eq__(self, value: object) -> bool:
        if not isinstance(value, JWTTokenValidator):
            return False

        return (
            self._jwks_client.uri == value._jwks_client.uri
            and self._algorithms == value._algorithms
            and self._audience == value._audience
        )


@dataclass
class LoginIdentity:
    user_id_or_none: Optional[str]
    email_or_none: Optional[str]
    idp_id: Optional[str]

    @property
    def user_id(self) -> str:
        if self.user_id_or_none is None:
            raise ValueError("User id is not set")
        return self.user_id_or_none

    @property
    def email(self) -> str:
        if self.email_or_none is None:
            raise ValueError("Email is not set")
        return self.email_or_none

    @classmethod
    def create_from_token_subject(
        cls, sub: str, idp_id: Optional[str]
    ) -> LoginIdentity:
        return LoginIdentity(
            user_id_or_none=sub if "@" not in sub else None,
            email_or_none=sub if "@" in sub else None,
            idp_id=idp_id if idp_id != DEFAULT_IDP_ID else None,
        )

    @classmethod
    def from_user_id(cls, user_id: str) -> LoginIdentity:
        return LoginIdentity(
            user_id_or_none=user_id,
            email_or_none=None,
            idp_id=None,  # user id is unique on its own
        )

    @classmethod
    def from_email(cls, email: str, idp_id: Optional[str]) -> LoginIdentity:
        return LoginIdentity(
            user_id_or_none=None,
            email_or_none=email,
            idp_id=idp_id,
        )
