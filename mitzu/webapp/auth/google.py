from __future__ import annotations

import os
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import mitzu.webapp.pages.paths as P
from mitzu import configs
from mitzu.webapp.auth.model import OAuthConfig, OAuthType


@dataclass(frozen=True)
class GoogleOAuth:
    @classmethod
    def get_config(
        cls,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        project_id: Optional[str] = None,
        jwt_algo: Optional[List[str]] = None,
        identity_provider_id: Optional[str] = None,
    ) -> OAuthConfig:
        """
        Creates a new OAuthConfig instance using the Cognito specific settings.

        :param client_id: Client ID of the configured App,
            if it's not set then the ``GOOGLE_CLIENT_ID`` environmental variable will be used
        :param client_secret: Client secret of the configured App,
            if it's not set then the ``GOOGLE_CLIENT_SECRET`` environmental variable will be used
        :param project_id: ID of the Google Project
            if it's not set then the ``GOOGLE_PROJECT_ID`` environmental variable will be used
        :param jwt_algo: List of accepted JWT token signing algorithms, by default it's ``["RS256"]``
            it can be overwritted with the ``COGNITO_JWT_ALGORITHMS`` environmental variable
        """
        client_id = cls.fallback_to_env_var(client_id, "GOOGLE_CLIENT_ID")
        project_id = cls.fallback_to_env_var(project_id, "GOOGLE_PROJECT_ID")
        if jwt_algo is None:
            jwt_algo = ["RS256"]
        jwt_algorithms = cls.fallback_to_env_var(
            ",".join(jwt_algo), "GOOGLE_JWT_ALGORITHMS"
        ).split(",")

        return OAuthConfig(
            client_id=client_id,
            client_secret=cls.fallback_to_env_var(
                client_secret, "GOOGLE_CLIENT_SECRET"
            ),
            jwks_url="https://www.googleapis.com/oauth2/v3/certs",
            sign_in_base_url="https://accounts.google.com/o/oauth2/auth",
            sign_in_params={
                "prompt": "select_account",
                "client_id": client_id,
                "response_type": "code",
                "scope": "email+openid",
                "access_type": "offline",
            },
            sign_out_url=configs.HOME_URL + P.UNAUTHORIZED_URL,
            token_url="https://oauth2.googleapis.com/token",
            jwt_algorithms=jwt_algorithms,
            type=OAuthType.GOOGLE,
            identity_provider_id=identity_provider_id,
        )

    @classmethod
    def fallback_to_env_var(cls, value: Optional[str], env_var: str) -> str:
        if value is not None:
            return value
        val = os.getenv(env_var)
        if val is None:
            raise ValueError(f"{env_var} should be set")
        return val

    @classmethod
    def create_from_dict(
        cls,
        config: Dict[str, Any],
        secret: str,
        identity_provider_id: Optional[str],
    ) -> OAuthConfig:
        jwt_algo = config.get("jwt_algo")
        if jwt_algo is not None and not isinstance(jwt_algo, list):
            jwt_algo = None
        return cls.get_config(
            client_id=config["client_id"],
            client_secret=secret,
            project_id=config["project_id"],
            jwt_algo=jwt_algo,
            identity_provider_id=identity_provider_id,
        )
