from dataclasses import dataclass
from typing import Optional
from uuid import uuid4

from mitzu.cache import MitzuCache

AUTH_STATE_EXPIRATION = 60 * 60


@dataclass(frozen=True)
class AuthStateCache:
    _cache: Mitzu<PERSON>ache

    def initiate_login_flow(self, idp_id: str) -> str:
        auth_state = str(uuid4())[-12:]

        self._cache.put(
            self._get_auth_state_cache_key(auth_state),
            idp_id,
            expire=AUTH_STATE_EXPIRATION,
        )
        return auth_state

    def get_auth_state(self, state: str) -> Optional[str]:
        cache_key = self._get_auth_state_cache_key(state)
        value = self._cache.get(cache_key)
        if self._cache.get(cache_key) is None:
            return None
        self._cache.clear(cache_key)
        return value

    def _get_auth_state_cache_key(self, state: str) -> str:
        return f"auth_state_{state}"
