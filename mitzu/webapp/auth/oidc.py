from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, Optional

import mitzu.webapp.pages.paths as P
from mitzu import configs
from mitzu.webapp.auth.model import OAuthConfig, OAuthType


@dataclass(frozen=True)
class OIDCOAuth:
    @classmethod
    def create_from_dict(
        cls,
        config: Dict[str, str],
        secret: str,
        identity_provider_id: Optional[str],
    ) -> OAuthConfig:
        jwt_algorithms = ["RS256"]

        client_id = config["client_id"]
        return OAuthConfig(
            client_id=client_id,
            client_secret=secret,
            jwks_url=config["jwks_uri"],
            sign_in_base_url=config["authorize_endpoint"],
            sign_in_params={
                "client_id": client_id,
                "response_type": "code",
                "scope": "email+openid",
            },
            sign_out_url=configs.HOME_URL + P.UNAUTHORIZED_URL,
            token_url=config["token_endpoint"],
            jwt_algorithms=jwt_algorithms,
            type=OAuthType.OIDC,
            identity_provider_id=identity_provider_id,
        )
