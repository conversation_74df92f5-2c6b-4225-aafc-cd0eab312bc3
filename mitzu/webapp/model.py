from __future__ import annotations

import json
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, replace
from datetime import datetime, timedelta
from enum import Enum
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Set,
    Tuple,
    TypeAlias,
    Union,
    cast,
)
from uuid import uuid4

import mitzu.model as M
import mitzu.webapp.serialization as SE
from mitzu import __version__
from mitzu.helper import create_unique_id

FORMULA_SEGMENT_INDEX = -1


@dataclass(frozen=True)
class UserEventData:
    count: int
    project_id: str
    score: float = 0.0

    def serialize(self) -> dict:
        return {
            "score": self.score,
            "count": self.count,
            "project_id": self.project_id,
        }

    @staticmethod
    def parse(data: dict) -> "UserEventData":
        return UserEventData(
            score=data["score"],
            count=data["count"],
            project_id=data["project_id"],
        )


ScoringOutput: TypeAlias = Dict[str, Dict[str, UserEventData]]


class CohortException(Exception):
    pass


class SubscriptionExpiredException(Exception):
    pass


class DashboardItemType(str, Enum):
    METRIC = "metric"
    TEXT = "text"
    HORIZONTAL_DIVIDER = "horizontal_divider"


class PurchasedPackageType(str, Enum):
    STARTER = "starter"
    GROWTH = "growth"
    ENTERPRISE = "enterprise"


class SharedObjectType(str, Enum):
    DASHBOARD = "dashboard"
    SAVED_METRIC = "saved_metric"


class Shareable(ABC):
    @abstractmethod
    def is_shared(self) -> bool:
        pass

    @abstractmethod
    def get_id(self) -> str:
        pass


@dataclass(frozen=True)
class Organisation:
    org_id: str
    name: str
    created_at: datetime
    is_trial: bool
    subscription_expiration_date: Optional[datetime]
    subscription_cancel_date: Optional[datetime]
    stripe_customer_id: Optional[str]
    purchased_package_type: PurchasedPackageType


@dataclass(frozen=True)
class SavedMetricInfo:
    id: str
    name: str
    description: Optional[str]
    image_base64: str
    small_base64: str
    created_at: datetime
    last_updated_at: datetime
    owner: MembershipID
    project_id: str
    shared: bool


@dataclass(frozen=True, init=False)
class SavedMetric(M.Identifiable, Shareable):
    """
    SavedMetric class to store a group of a Metric and a SimpleChart

    :param chart: simple chart
    :param small_base64: image in base64 string format to represent the metric as thumbnail
    :param image_base64: larger image in base64 string format to represent the metric
    :param project: the project the metric can be queried on
    :param metric: metric
    :param saved_at: the time of creation
    """

    id: str
    name: str
    description: str
    image_base64: str
    small_base64: str
    created_at: datetime
    last_updated_at: datetime
    owner: MembershipID
    metric_json: str
    project_id: str
    shared: bool

    def __init__(
        self,
        name: str,
        description: Optional[str],
        image_base64: str,
        small_base64: str,
        owner: MembershipID,
        project_id: str,
        metric_json: Optional[str] = None,
        webapp_metric: Optional[WebappMetric] = None,
        created_at: Optional[datetime] = None,
        id: Optional[str] = None,
        last_updated_at: Optional[datetime] = None,
        shared: bool = False,
    ):
        if created_at is None:
            created_at = datetime.now()

        if metric_json is None:
            if webapp_metric is not None:
                metric_json = SE.to_compressed_string(webapp_metric)
            else:
                raise ValueError(
                    "Either metric or metric_json needs to be defined as an argument"
                )

        if id is None:
            id = create_unique_id()

        if last_updated_at is None:
            last_updated_at = datetime.now()

        object.__setattr__(self, "name", name)
        object.__setattr__(self, "description", description if description else "")
        object.__setattr__(self, "image_base64", image_base64)
        object.__setattr__(self, "small_base64", small_base64)
        object.__setattr__(self, "created_at", created_at)
        object.__setattr__(self, "last_updated_at", last_updated_at)
        object.__setattr__(self, "metric_json", metric_json)
        object.__setattr__(self, "owner", owner)
        object.__setattr__(self, "id", id)
        object.__setattr__(self, "project_id", project_id)
        object.__setattr__(self, "shared", shared)

    def metric(self) -> WebappMetric:
        res = SE.from_compressed_string(self.metric_json)
        if res.title is None:
            res = replace(
                res,
                title=self.name,
                description=(
                    self.description
                    if self.description is not None and self.description.strip() != ""
                    else None
                ),
            )
        return res

    def get_id(self) -> str:
        return self.id

    def extract_saved_metric_info(self) -> SavedMetricInfo:
        return SavedMetricInfo(
            id=self.id,
            name=self.name,
            description=self.description,
            image_base64=self.image_base64,
            small_base64=self.small_base64,
            created_at=self.created_at,
            last_updated_at=self.last_updated_at,
            owner=self.owner,
            project_id=self.project_id,
            shared=self.shared,
        )

    def is_shared(self) -> bool:
        return self.shared


@dataclass(frozen=True)
class DashboardItem:
    x: int
    y: int
    width: int
    height: int
    id: str

    def get_card_type(self) -> DashboardItemType:
        raise NotImplementedError()


@dataclass(frozen=True)
class DashboardMetricItem(DashboardItem):
    saved_metric: SavedMetric

    def get_card_type(self) -> DashboardItemType:
        return DashboardItemType.METRIC


@dataclass(frozen=True)
class DashboardTextItem(DashboardItem):
    text: str

    def get_card_type(self) -> DashboardItemType:
        return DashboardItemType.TEXT


@dataclass(frozen=True)
class DashboardHorizontalDividerItem(DashboardItem):
    text: str

    def get_card_type(self) -> DashboardItemType:
        return DashboardItemType.HORIZONTAL_DIVIDER


@dataclass(frozen=True)
class Dashboard(Shareable):
    """
    Contains all details of a Dashboard.

    param name: the name of the dashboard
    param project_id: the of the project
    param id: the id of the dashboard
    param dashboard_metric: list of dashboard metrics
    param created_at: the time of creation of the dashboard
    param owner: the name of the owner
    """

    name: str
    project_id: str
    owner: MembershipID
    shared: bool
    show_annotations: bool
    auto_refresh_time_window: M.TimeWindow | None = M.TimeWindow(1, M.TimeGroup.DAY)
    description: Optional[str] = None
    id: str = field(default_factory=lambda: str(uuid4())[-12:])
    dashboard_metrics: List[DashboardItem] = field(default_factory=list)
    created_at: datetime = field(default_factory=lambda: datetime.now())
    last_updated_at: datetime = field(default_factory=lambda: datetime.now())
    use_consistent_coloring: bool = False
    default_filter: Optional[DashboardFilter] = None

    def update(self, dashboard_metrics: List[DashboardItem]) -> Dashboard:
        return replace(
            self,
            last_updated_at=datetime.now(),
            dashboard_metrics=dashboard_metrics,
        )

    def refreshed(self) -> Dashboard:
        return replace(
            self,
            last_updated_at=datetime.now(),
        )

    def is_shared(self) -> bool:
        return self.shared

    def get_id(self) -> str:
        return self.id

    def get_all_event_name_paths(self) -> Set[M.EventNamePath]:
        result: Set[M.EventNamePath] = set()
        for dashboard_item in self.dashboard_metrics:
            if isinstance(dashboard_item, DashboardMetricItem):
                for segment in dashboard_item.saved_metric.metric().segments:
                    result.update(segment.get_event_name_paths())

        return result

    def is_stale(self) -> bool:
        if self.auto_refresh_time_window is None:
            return False
        last_refresh = self.last_updated_at
        next_expected_refresh = (
            last_refresh + self.auto_refresh_time_window.to_relative_delta()
        )

        if datetime.now() > next_expected_refresh:
            return True
        return False


@dataclass(frozen=True)
class DashboardFilter:
    filters: List[WebappEventFilter]
    time_config: Optional[TimeWindowChooserConfig]

    @classmethod
    def empty(cls) -> DashboardFilter:
        return DashboardFilter(
            [],
            None,
        )

    @property
    def is_empty(self) -> bool:
        return len(self.filters) == 0 and (
            self.time_config is None or self.time_config.is_empty
        )


@dataclass(frozen=True)
class TimeWindowChooserConfig:
    start_dt: Optional[datetime]
    end_dt: Optional[datetime]
    time_group: Optional[M.TimeGroup]
    lookback_days: Optional[M.TimeWindow]
    calendar_date_range: Optional[CalendarDateRange]
    since_date: Optional[datetime]

    @classmethod
    def create_from_webapp_metric_config(
        cls, metric_config: WebappMetricConfig
    ) -> TimeWindowChooserConfig:
        return metric_config.time_window_chooser_config

    @property
    def is_empty(self) -> bool:
        return (
            self.start_dt is None
            and self.end_dt is None
            and self.time_group is None
            and self.lookback_days is None
            and self.calendar_date_range is None
            and self.since_date is None
        )


class Role(Enum):
    ADMIN = "admin"
    MEMBER = "member"
    READ_ONLY = "read-only"  # used only in the public app
    SUPER_ADMIN_READ_ONLY = "super-admin-read-only"


# in python 3.12 it could be a typedef if linter supports it
@dataclass(frozen=True)
class MembershipID:
    id: str


@dataclass
class User:
    """
    Container class for describing a user
    """

    email: str
    org_id: str
    invited_at: datetime
    membership_id: MembershipID = field(
        default_factory=lambda: MembershipID(create_unique_id())
    )
    id: str = field(default_factory=create_unique_id)
    role: Role = Role.MEMBER
    is_org_owner: bool = False
    is_sso_user: bool = False
    log_performance: bool = False
    first_login_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    can_create_second_org: bool = False

    def __hash__(self) -> int:
        return hash(self.id)


@dataclass(frozen=True)
class OrgMembership:
    org_id: str
    name: str
    org_created_at: datetime
    role: Role
    org_is_trial: bool
    org_subscription_expires_at: Optional[datetime]


@dataclass(frozen=True)
class ProjectInfo:
    """
    Contains the minimal set of details of a project.

    Use this instance to quickly render UI elements without querying the event data tables or discovered fields.
    """

    id: str
    name: str
    description: Optional[str]
    is_sample: bool
    is_default: bool


@dataclass(frozen=True)
class EventTableMeta:
    entities: List[M.Entity]


@dataclass(frozen=True)
class EventTableDiscoveryInfo:
    events: List[str]


@dataclass(frozen=True)
class EntityMeta:
    entity: M.Entity
    display_name: str
    icon: str

    def __lt__(self, other: EntityMeta) -> bool:
        if self.entity == M.USER_ENTITY:
            return True
        if other.entity == M.USER_ENTITY:
            return False
        return self.display_name < other.display_name


@dataclass(frozen=True)
class EventMeta:
    """
    Defines extra meta data for an event like alias or description

    :param source_table: name of table where the event name can be found
    :param event_name: name of the event
    :param event_id: id of the discovered event
    :param display_name: display name of the event
    :param description: description of the event
    """

    source_table: str
    event_name: str
    event_id: str
    display_name: str
    description: Optional[str] = None
    is_hidden: bool = False

    @property
    def event_name_path(self) -> M.EventNamePath:
        return M.EventNamePath(
            source_table=self.source_table,
            event_name=self.event_name,
        )

    def copy_with_updates(
        self, display_name: str, description: Optional[str], is_hidden: bool
    ) -> EventMeta:
        return EventMeta(
            source_table=self.source_table,
            event_name=self.event_name,
            event_id=self.event_id,
            display_name=display_name,
            description=description,
            is_hidden=is_hidden,
        )


@dataclass(frozen=True)
class EventCatalogRecord:
    """
    This record represent an event row on the catalog page
    """

    event_id: str
    event_source_table: str
    event_name: str
    event_display_name: str
    event_description: Optional[str]
    event_last_discovered: Optional[datetime]
    is_hidden: bool

    @property
    def event_name_path(self) -> M.EventNamePath:
        return M.EventNamePath(self.event_source_table, self.event_name)


@dataclass(frozen=True)
class PropertyCatalogRecord:
    """
    This record represent an event property row on the catalog page
    """

    property_path: str
    property_name: str
    property_display_name: str
    property_description: Optional[str]
    events: List[EventCatalogRecord]
    is_hidden: bool


@dataclass(frozen=True)
class DimensionPropertyCatalogRecord:
    """
    This record represent an event property row on the catalog page
    """

    dimension_property_id: str
    table_name: str
    entity: M.Entity
    field_name: str
    display_name: str
    description: Optional[str]
    last_discovered: Optional[datetime]
    is_hidden: bool


@dataclass(frozen=True)
class TooltipCustomJson:
    segment_index: int
    break_down: Optional[List[str]] = None
    event_label: Optional[str] = None
    event_value: Optional[str] = None
    title_label: Optional[str] = None
    title_value: Optional[str] = None
    subtitle: Optional[str] = None
    version: Optional[int] = None
    normalized_percentage: Optional[float] = None
    unique_field_label: Optional[str] = None
    retention_index: Optional[int] = None
    is_comparison: Optional[bool] = None
    direction: Optional[str] = None
    is_drop_off: Optional[bool] = None

    @classmethod
    def from_json(cls, json_str: str) -> TooltipCustomJson:
        data = json.loads(json_str)

        return TooltipCustomJson.from_dict(data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> TooltipCustomJson:
        return TooltipCustomJson(
            segment_index=data["si"],
            break_down=data.get("bd"),
            event_label=data.get("el"),
            event_value=data.get("ev"),
            title_label=data.get("tl"),
            title_value=data.get("tv"),
            subtitle=data.get("st"),
            version=data.get("v"),
            normalized_percentage=data.get("np"),
            unique_field_label=data.get("ufl"),
            retention_index=data.get("ri"),
            is_comparison=data.get("ic"),
            direction=data.get("di"),
            is_drop_off=data.get("ido"),
        )

    def to_dict(self) -> Dict[str, Any]:
        data = {
            "si": self.segment_index,
            "bd": self.break_down or None,
            "el": self.event_label or None,
            "ev": self.event_value or None,
            "tl": self.title_label or None,
            "tv": self.title_value or None,
            "st": self.subtitle or None,
            "v": self.version,
            "np": self.normalized_percentage or None,
            "ufl": self.unique_field_label or None,
            "ri": self.retention_index,
            "ic": self.is_comparison,
            "di": self.direction or None,
            "ido": self.is_drop_off,
        }

        # Remove None keys
        return {k: v for k, v in data.items() if v is not None}

    def to_json(self) -> str:
        return json.dumps(self.to_dict())


@dataclass(frozen=True)
class CohortReference:
    id: str


@dataclass(frozen=True)
class CohortInfo:
    id: str
    name: str
    created_at: datetime
    last_updated_at: datetime
    owner: MembershipID
    type: M.CohortType
    metric_json: str
    cohort_size: Optional[int]
    entity: M.Entity
    description: Optional[str] = None

    def __str__(self) -> str:
        return self.name

    def refresh_cohort(
        self, last_updated_at: datetime, cohort_size: Optional[int]
    ) -> CohortInfo:
        return CohortInfo(
            name=self.name,
            id=self.id,
            created_at=self.created_at,
            last_updated_at=last_updated_at,
            metric_json=self.metric_json,
            owner=self.owner,
            type=self.type,
            entity=self.entity,
            description=self.description,
            cohort_size=cohort_size,
        )

    def used_cohorts(self) -> Set[CohortReference]:
        if self.type == M.CohortType.DYNAMIC:
            webapp_metric = SE.webapp_metric_from_dict(json.loads(self.metric_json))
            return webapp_metric.used_cohorts()
        return set()


@dataclass(frozen=True)
class WebappEventFilter:
    left: M.FieldPath
    operator: M.Operator
    right: Optional[Any] = None


@dataclass(frozen=True)
class WebappEventPropertyAggregation:
    field_def: Optional[M.FieldPath]
    agg_type: M.EventPropertyAggregationType

    @classmethod
    def from_model(
        cls, model: M.EventPropertyAggregation
    ) -> WebappEventPropertyAggregation:
        return WebappEventPropertyAggregation(
            field_def=model.field_def.field_path,
            agg_type=model.agg_type,
        )


@dataclass(frozen=True)
class WebappAggregationConfig:
    type: Optional[M.AggType]
    conv_param: Optional[Any] = None
    event_property_agg: Optional[WebappEventPropertyAggregation] = None

    @classmethod
    def from_model(cls, model: M.AggregationConfig) -> WebappAggregationConfig:
        return WebappAggregationConfig(
            type=model.type,
            conv_param=model.conv_param,
            event_property_agg=(
                WebappEventPropertyAggregation.from_model(model.event_property_agg)
                if model.event_property_agg
                else None
            ),
        )


@dataclass(frozen=True)
class WebappSegment:
    group_by: List[M.FieldPath]
    aggregation: Optional[WebappAggregationConfig]
    filter: Optional[M.SegmentFilter]
    title: Optional[str]
    expanded_custom_event_key: Optional[str]

    def aggregate(self, aggregation: WebappAggregationConfig) -> WebappSegment:
        return replace(self, aggregation=aggregation)

    def clear_top_level_configs(self) -> WebappSegment:
        return replace(self, group_by=[], aggregation=None, title=None)

    def get_event_name_paths(self) -> Set[M.EventNamePath]:
        raise NotImplementedError()

    def get_event_filters(self) -> List[WebappEventFilter]:
        raise NotImplementedError()

    def get_events_in_order(self) -> List[M.EventNamePath]:
        raise NotImplementedError()


@dataclass(frozen=True)
class WebappSimpleSegment(WebappSegment):
    event: M.EventNamePath
    event_filter: Optional[WebappEventFilter]

    @classmethod
    def create(
        cls,
        event: M.EventNamePath,
        event_filter: Optional[WebappEventFilter],
        group_by: Optional[List[M.FieldPath]] = None,
    ) -> WebappSimpleSegment:
        return WebappSimpleSegment(
            group_by=group_by or [],
            aggregation=None,
            filter=None,
            title=None,
            event=event,
            event_filter=event_filter,
            expanded_custom_event_key=None,
        )

    def get_event_name_paths(self) -> Set[M.EventNamePath]:
        return set([self.event])

    def get_events_in_order(self) -> List[M.EventNamePath]:
        return [self.event]

    def get_event_filters(self) -> List[WebappEventFilter]:
        if self.event_filter is not None:
            return [self.event_filter]
        return []


@dataclass(frozen=True)
class WebappComplexSegment(WebappSegment):
    left: WebappSegment
    right: WebappSegment
    operator: M.BinaryOperator

    @classmethod
    def create(
        cls, left: WebappSegment, operator: M.BinaryOperator, right: WebappSegment
    ) -> WebappComplexSegment:
        return WebappComplexSegment(
            group_by=[],
            aggregation=None,
            filter=None,
            title=None,
            left=left,
            operator=operator,
            right=right,
            expanded_custom_event_key=None,
        )

    def get_event_name_paths(self) -> Set[M.EventNamePath]:
        return self.left.get_event_name_paths().union(self.right.get_event_name_paths())

    def get_events_in_order(self) -> List[M.EventNamePath]:
        if self.operator == M.BinaryOperator.OR:
            return self.left.get_events_in_order() + self.right.get_events_in_order()
        return self.left.get_events_in_order()

    def get_event_filters(self) -> List[WebappEventFilter]:
        return self.left.get_event_filters() + self.right.get_event_filters()


class CalendarDateRange(Enum):
    TODAY = "today"
    YESTERDAY = "yesterday"
    THIS_WEEK = "this_week"
    LAST_WEEK = "last_week"
    THIS_MONTH = "this_month"
    LAST_MONTH = "last_month"
    THIS_YEAR = "this_year"
    LAST_YEAR = "last_year"

    def to_string(self) -> str:
        if self == CalendarDateRange.TODAY:
            return "Today"
        if self == CalendarDateRange.YESTERDAY:
            return "Yesterday"
        if self == CalendarDateRange.THIS_WEEK:
            return "This week"
        if self == CalendarDateRange.LAST_WEEK:
            return "Last week"
        if self == CalendarDateRange.THIS_MONTH:
            return "This month"
        if self == CalendarDateRange.LAST_MONTH:
            return "Last month"
        if self == CalendarDateRange.THIS_YEAR:
            return "This year"
        if self == CalendarDateRange.LAST_YEAR:
            return "Last year"
        raise ValueError(f"Unknown calendar date range: {self}")

    @staticmethod
    def get_start_of_week(date: datetime) -> datetime:
        """Returns the start of the week (e.g., Monday) for the given date."""
        return date - timedelta(days=date.weekday())

    @staticmethod
    def get_start_of_month(date: datetime) -> datetime:
        """Returns the start of the month for the given date."""
        return datetime(date.year, date.month, 1)

    def to_approx_days(self) -> int:
        if self == CalendarDateRange.TODAY:
            return 1
        if self == CalendarDateRange.YESTERDAY:
            return 1
        if self == CalendarDateRange.THIS_WEEK:
            return 7
        if self == CalendarDateRange.LAST_WEEK:
            return 7
        if self == CalendarDateRange.THIS_MONTH:
            return 31
        if self == CalendarDateRange.LAST_MONTH:
            return 31
        if self == CalendarDateRange.THIS_YEAR:
            return 365
        if self == CalendarDateRange.LAST_YEAR:
            return 365
        raise ValueError(f"Unsupported calendar date range: {self}")

    def to_start_and_end_date(self, now: datetime) -> Tuple[datetime, datetime]:
        if self == CalendarDateRange.TODAY:
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif self == CalendarDateRange.YESTERDAY:
            start_date = (now - timedelta(days=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif self == CalendarDateRange.THIS_WEEK:
            start_date = self.get_start_of_week(now).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = now
        elif self == CalendarDateRange.LAST_WEEK:
            start_date = self.get_start_of_week(now - timedelta(weeks=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = self.get_start_of_week(now).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        elif self == CalendarDateRange.THIS_MONTH:
            start_date = self.get_start_of_month(now).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = now
        elif self == CalendarDateRange.LAST_MONTH:
            start_date = self.get_start_of_month(
                self.get_start_of_month(now) - timedelta(days=1)
            ).replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = self.get_start_of_month(now).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        elif self == CalendarDateRange.THIS_YEAR:
            start_date = datetime(now.year, 1, 1).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = now
        elif self == CalendarDateRange.LAST_YEAR:
            start_date = datetime(now.year - 1, 1, 1).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end_date = datetime(now.year, 1, 1).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        else:
            raise ValueError(f"Unknown CalendarDateRange {self}")
        # hack: This should be done in a better way than to subtract 1 day here
        return start_date, end_date - timedelta(days=1)


class InvalidYRangeConfig(Exception):
    pass


@dataclass(frozen=True)
class YRangeConfig:
    min: Optional[float]
    max: Optional[float]

    @staticmethod
    def parse(value: Dict[str, Optional[float]]) -> YRangeConfig:
        return YRangeConfig(
            min=value.get("min"),
            max=value.get("max"),
        )

    def __post_init__(self) -> None:
        if self.min is not None and self.max is not None and self.min > self.max:
            raise InvalidYRangeConfig('"min" value cannot be greater than "max" value.')
        if self.min is not None and self.max is None:
            raise InvalidYRangeConfig('cannot set "min" value without "max" value')

    def serialize(self) -> Dict[str, Optional[float]]:
        return {"min": self.min, "max": self.max}

    def __str__(self) -> str:
        return f"{self.min}, {self.max}"


class ViewMode(Enum):
    EDIT = "edit"
    CHART = "chart"
    TABLE = "table"


@dataclass(frozen=True)
class WebappMetricConfig:
    time_window_chooser_config: TimeWindowChooserConfig

    max_group_count: int
    chart_type: Optional[M.SimpleChartType]
    chart_post_processing: Optional[M.ChartPostProcessing]
    use_consistent_coloring: bool
    resolution: M.Resolution
    event_sampling: Optional[float]

    # used only in conversion metrics
    conv_window: Optional[M.TimeWindow]
    conv_window_type: Optional[M.ConversionWindowType]
    conv_attribution: Optional[M.ConversionAttribution]
    custom_holding_constants: List[M.EventFieldPath]
    conv_orientation: Optional[M.ConversionOrientation]

    # used only in retention metrics
    retention_indices: Optional[List[int]]
    retention_window: Optional[M.TimeWindow]
    retention_attribution_type: Optional[M.RetentionAttributionType]

    aggregation: WebappAggregationConfig
    formula: Optional[str]
    unique_field: Union[M.Entity, M.EventFieldPath]

    comparison_options: Optional[M.ComparisonOptions]
    cut_off_incomplete_data: bool
    y_range_config: Optional[YRangeConfig]
    show_other_groups: bool

    view_mode: ViewMode

    def get_retention_indices(self, default_end_date: datetime) -> List[int]:
        if self.time_window_chooser_config.start_dt is not None:
            start_dt = self.time_window_chooser_config.start_dt
        elif self.time_window_chooser_config.lookback_days is not None:
            start_dt = (
                default_end_date
                - self.time_window_chooser_config.lookback_days.to_relative_delta()
            )
        elif self.time_window_chooser_config.calendar_date_range is not None:
            (
                start_dt,
                _,
            ) = self.time_window_chooser_config.calendar_date_range.to_start_and_end_date(
                now=default_end_date
            )
        elif (
            self.time_window_chooser_config.start_dt is None
            and self.time_window_chooser_config.end_dt is not None
        ):
            # Missing start date, assuming user wants to look at the one day
            start_dt = self.time_window_chooser_config.end_dt - timedelta(days=1)
        elif self.time_window_chooser_config.since_date is not None:
            start_dt = self.time_window_chooser_config.since_date

        else:
            raise ValueError("Failed to determine start date")

        return self._get_retention_indices(
            start_dt,
            default_end_date,
            self.retention_window
            if self.retention_window is not None
            else M.TimeWindow(1, M.TimeGroup.WEEK),
        )

    def _get_retention_indices(
        self, start_dt: datetime, end_dt: datetime, time_window: M.TimeWindow
    ) -> List[int]:
        if time_window.period == M.TimeGroup.TOTAL:
            return [1]
        indices: List[int] = []
        curr_dt = start_dt
        index = 0
        while curr_dt <= end_dt:
            indices.append(index)
            index += time_window.value
            curr_dt = curr_dt + time_window.to_relative_delta()
        return indices

    def with_conv_window(self, conv_window: M.TimeWindow) -> WebappMetricConfig:
        return replace(self, conv_window=conv_window)

    def with_retention_window(
        self, retention_window: M.TimeWindow
    ) -> WebappMetricConfig:
        return replace(self, retention_window=retention_window)

    @classmethod
    def from_model(cls, model: M.MetricConfig) -> WebappMetricConfig:
        if isinstance(model.unique_field, M.FieldDef):
            unique_field_path: Union[
                M.Entity, M.EventFieldPath
            ] = model.unique_field.field_path
        else:
            unique_field_path = model.unique_field

        retention_indices: Optional[List[int]] = None
        retention_window: Optional[M.TimeWindow] = None
        retention_attribution_type: Optional[M.RetentionAttributionType] = None
        cut_off_incomplete_data = False
        if isinstance(model, M.RetentionMetricConfig):
            retention_indices = model.retention_indices
            retention_window = model.time_window
            retention_attribution_type = model.retention_attribution_type
            cut_off_incomplete_data = model.cut_off_incomplete_data

        conv_window: Optional[M.TimeWindow] = None
        conv_window_type: Optional[M.ConversionWindowType] = None
        conv_attribution: Optional[M.ConversionAttribution] = None
        conv_orientation: Optional[M.ConversionOrientation] = None
        if isinstance(model, M.ConversionMetricConfig):
            conv_window = model.time_window
            conv_window_type = model.conv_window_type
            conv_orientation = model.conv_orientation

        custom_holding_constants: List[M.EventFieldPath] = []
        if isinstance(model, M.ConversionRetentionMetricConfig):
            custom_holding_constants = [
                field.field_path for field in model.custom_holding_constants
            ]
            conv_attribution = model.conv_attribution

        formula: Optional[str] = None
        if isinstance(model, M.SegmentationMetricConfig):
            formula = model.formula
        time_window_chooser_config = TimeWindowChooserConfig(
            start_dt=model.start_dt,
            end_dt=model.end_dt,
            lookback_days=model.lookback_days,
            calendar_date_range=None,
            since_date=None,
            time_group=model.time_group,
        )
        return WebappMetricConfig(
            time_window_chooser_config=time_window_chooser_config,
            max_group_count=model.max_group_count,
            chart_type=model.chart_type,
            chart_post_processing=model.chart_post_processing,
            use_consistent_coloring=False,
            resolution=model.resolution,
            event_sampling=model.event_sampling,
            conv_window=conv_window,
            conv_window_type=conv_window_type,
            conv_orientation=conv_orientation,
            conv_attribution=conv_attribution,
            custom_holding_constants=custom_holding_constants,
            aggregation=(
                WebappAggregationConfig.from_model(model.aggregation)
                if model.aggregation is not None
                else None
            ),
            formula=formula,
            unique_field=unique_field_path,
            comparison_options=model.comparison_options,
            y_range_config=None,
            show_other_groups=False,
            # retention specific
            retention_indices=retention_indices,
            retention_window=retention_window,
            retention_attribution_type=retention_attribution_type,
            cut_off_incomplete_data=cut_off_incomplete_data,
            view_mode=ViewMode.EDIT,
        )

    @classmethod
    def get_default(cls) -> WebappMetricConfig:
        return WebappMetricConfig.from_model(M.MetricConfig())

    def calculate_end_dt(self, default_end_dt: datetime) -> datetime:
        if self.time_window_chooser_config.end_dt is None:
            return default_end_dt
        else:
            return self.time_window_chooser_config.end_dt

    def calculate_start_dt(self, default_end_dt: datetime) -> datetime:
        if self.time_window_chooser_config.start_dt is not None:
            return self.time_window_chooser_config.start_dt
        elif self.time_window_chooser_config.lookback_days is not None:
            return (
                self.calculate_end_dt(default_end_dt)
                - self.time_window_chooser_config.lookback_days.to_relative_delta()
            )
        elif self.time_window_chooser_config.calendar_date_range is not None:
            return self.time_window_chooser_config.calendar_date_range.to_start_and_end_date(
                now=default_end_dt
            )[
                0
            ]
        elif self.time_window_chooser_config.since_date is not None:
            return self.time_window_chooser_config.since_date
        raise ValueError("start_dt is not set")


@dataclass(frozen=True)
class WebappMetric:
    metric_type: M.MetricType
    config: WebappMetricConfig
    segments: List[WebappSegment]
    title: Optional[str] = None
    description: Optional[str] = None
    global_filters: List[WebappEventFilter] = field(default_factory=lambda: [])
    label_mapping: Dict[str, str] = field(default_factory=lambda: {})

    def has_segments(self) -> bool:
        return len(self.segments) > 0

    def has_enough_segments(self) -> bool:
        return (
            self.metric_type not in [M.MetricType.RETENTION, M.MetricType.JOURNEY]
            and self.has_segments()
            or len(self.segments) > 1
        )

    @property
    def dimension_profile_config(self) -> Optional[WebappEventFilter]:
        if len(self.global_filters) == 1 and isinstance(
            self.global_filters[0].left, M.DimensionIDFieldPath
        ):
            return self.global_filters[0]
        return None

    @classmethod
    def create_empty_webapp_metric(cls, metric_type: M.MetricType) -> WebappMetric:
        if metric_type == M.MetricType.SEGMENTATION:
            core_config: M.MetricConfig = M.SegmentationMetricConfig(
                aggregation=M.AggregationConfig(type=M.AggType.COUNT_UNIQUE_USERS)
            )
        elif metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
            core_config = M.ConversionMetricConfig(
                aggregation=M.AggregationConfig(type=M.AggType.CONVERSION)
            )
        elif metric_type == M.MetricType.RETENTION:
            core_config = M.RetentionMetricConfig(
                aggregation=M.AggregationConfig(type=M.AggType.RETENTION_RATE),
                time_group=M.TimeGroup.DAY,
                retention_indices=[1, 2],
            )
        else:
            raise ValueError(f"Unexpected metric type {metric_type}")
        return WebappMetric(
            metric_type=metric_type,
            config=WebappMetricConfig.from_model(core_config),
            segments=[],
            title=None,
            description=None,
        )

    def copy(self) -> WebappMetric:
        return WebappMetric(
            metric_type=self.metric_type,
            config=self.config,
            segments=self.segments.copy(),
            title=self.title,
            description=self.description,
            global_filters=self.global_filters,
        )

    def used_cohorts(self) -> Set[CohortReference]:
        result: Set[CohortReference] = set()
        for segment in self.segments:
            for event_filter in segment.get_event_filters():
                if isinstance(event_filter.left, M.CollectionFieldPath) and isinstance(
                    event_filter.right, CohortReference
                ):
                    result.add(event_filter.right)
        return result

    def has_group_by(self) -> bool:
        for segment in self.segments:
            if segment.group_by:
                return True
        return False


@dataclass(frozen=True)
class WebappDimensionsSearchQuery:
    entity: M.Entity
    search_text: Optional[str]
    filters: List[WebappEventFilter]
    selected_columns: List[M.DimensionFieldPath]

    def is_empty(self) -> bool:
        valid_filters = [
            filter
            for filter in self.filters
            if filter.operator.is_unary_operator()
            or (filter.right is not None and filter.right != [])
        ]
        return (self.search_text is None or self.search_text == "") and len(
            valid_filters
        ) == 0

    def used_fields(self) -> List[M.DimensionFieldPath]:
        res = self.selected_columns
        for filter in self.filters:
            if filter.left not in res:
                res.append(cast(M.DimensionFieldPath, filter.left))

        return res


class SurveyType(str, Enum):
    USER_SURVEY = "user_survey"
    ORGANIZATION_SURVEY = "organization_survey"


@dataclass
class SurveySubmission:
    membership_id: MembershipID
    answers: Dict[str, Any]
    survey_type: SurveyType
    submitted_at: datetime
    org_id: str


@dataclass(frozen=True)
class EventScores:
    scores: ScoringOutput
    id: str = field(default_factory=create_unique_id)
    created_at: datetime = field(default_factory=lambda: datetime.now())
    mitzu_version: str = __version__


@dataclass(frozen=True)
class CustomEvent:
    id: str
    display_name: str
    description: Optional[str]
    segment: WebappSegment
    owner: MembershipID
    created_at: datetime
    last_updated_at: datetime


class CollectionReferenceSource(Enum):
    COLLECTION = "collection"
    SAVED_INSIGHT = "saved_insight"


@dataclass(frozen=True)
class CollectionReference:
    collection_id: str
    source_type: CollectionReferenceSource
    source_id: str
    source_name: str
