._dash-loading,
._dash-loading::before,
._dash-loading::after {
  color: transparent;
  content: "";
  display: block;
  width: 15px;
  height: 15px;
  position: absolute;
  background-color: black;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  opacity: 0.8;
}

/* First dot using the main element */
._dash-loading {
  animation: dotFade 1.2s infinite cubic-bezier(0.445, 0.05, 0.55, 0.95);
}

/* Second dot */
._dash-loading::before {
  transform: translateX(-20px);
  animation: dotFade 1.2s infinite cubic-bezier(0.445, 0.05, 0.55, 0.95);
  animation-delay: 0.4s;
  transform: translate(-200%, -50%);
}
/* 
/* Third dot */
._dash-loading::after {
  transform: translateX(20px);
  animation: dotFade 1.2s infinite cubic-bezier(0.445, 0.05, 0.55, 0.95);
  animation-delay: 0.8s;
  transform: translate(100%, -50%);
}

@keyframes dotFade {
  0%,
  100% {
    opacity: 0.8;
  }

  50% {
    opacity: 0.2;
  }
}
