if (!window.dash_clientside) {
    window.dash_clientside = {};
}

document.addEventListener('mousemove', function (event) {
    // For sankey
    window.mousePageX = event.pageX;
    window.mousePageY = event.pageY;
    setDashComponentValue('mouse_position', event.pageX + ',' + event.pageY);
});

document.addEventListener('keydown', function (event) {
    if ((event.metaKey || event.ctrlKey) && event.key === 'Enter') {
        const runButton = document.getElementById('graph_run_button');
        if (runButton && runButton.offsetParent !== null) {
            runButton.click();
        }

        const lookupRunButton = document.getElementById('dim_run_button');
        if (lookupRunButton && lookupRunButton.offsetParent !== null) {
            lookupRunButton.click();
        }
    }
});

const add_insights_card_id = 'add_insight_card_id';

const create_tooltip_chart = (sub_plot_data, title) => {
    if (sub_plot_data.length < 2) {
        return undefined;
    }
    let x_data = Array.from(
        {
            length: sub_plot_data.length,
        },
        (_, i) => i + 1
    );
    return {
        data: [
            {
                x: x_data,
                y: sub_plot_data,
                text: sub_plot_data,
                type: 'bar',
                hoverinfo: 'none',
                marker: {
                    color: '#4c6ef5', // Trace setting: marker color
                },
            },
        ],
        layout: {
            margin: {
                l: 0,
                r: 0,
                t: 0,
                b: 0,
            }, // Layout setting: margin
            xaxis: {
                title: '',
                showticklabels: true,
            },
            yaxis: {
                title: title,
                showticklabels: false,
                automargin: true,
            },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)', // Layout setting: plot background color
            autosize: true,
            font: {
                size: 10, // Layout setting: font size
            },
        },
    };
};

const create_from_previous_label = (value) => {
    // FIXME: 0.01 is not green
    if (value.charAt(0) !== '-' && value.charAt(0) !== '0') {
        value = '+' + value;
    }
    let color = '';
    if (value.charAt(0) === '-') {
        color = 'text-danger';
    } else if (value.charAt(0) === '+') {
        color = 'text-success';
    }
    return [
        {
            type: 'Span',
            namespace: 'dash_html_components',
            props: {
                children: value,
                className: color,
            },
        },
        ' from previous',
    ];
};

const get_tooltip_triggered_id = () => {
    const ctx = window.dash_clientside.callback_context;

    if (!ctx || ctx.triggered.length == 0) {
        return null;
    }

    const first_triggered = ctx.triggered[0].prop_id;
    try {
        return JSON.parse(first_triggered.replace('.hoverData', ''));
    } catch (exc) {
        return null;
    }
};

window.dash_clientside.clientside = {
    init_dashboard_drag_and_drop: function (id) {
        var el = document.getElementById(id);
        var sortable = Sortable.create(el, {
            dataIdAttr: 'id',
            onEnd: function (evt) {
                // delay update by 300 ms so animation can happen
                setTimeout(() => {
                    order = sortable.toArray().filter((id) => id != add_insights_card_id);
                    order.push(add_insights_card_id);
                    setDashComponentChildren('dashboards-order-input', order);
                    sortable.sort(order, true);
                }, 300);
            },
            animation: 200,
            swap: false,
            handle: '.drag-handler',
            swapClass: 'dashboard-chart-highlight',
            delay: 0,
            delayOnTouchOnly: true,
            filter: '.not-draggable',
        });

        return window.dash_clientside.no_update;
    },

    set_explore_title: function (value) {
        if (value) {
            document.title = value;
        } else {
            document.title = 'Mitzu - Explore';
        }
    },
    set_dashboard_title: function (value) {
        if (value) {
            document.title = value;
        } else {
            document.title = 'Mitzu - Dashboard';
        }
    },
    clear_search_input: function (clicks) {
        return '';
    },
    set_event_dropdown_options: function (
        current_options,
        search_value,
        event_options,
        selected_option,
        current_classname
    ) {
        var updated_options = [];

        if (current_options !== null) {
            updated_options = [...current_options];
        }
        if (current_options === null || current_options.length != event_options.length) {
            updated_options = [...event_options];
        }
        var selected_item = null;
        if (selected_option) {
            var is_selected_valid = false;
            for (var i = 0; i < updated_options.length; i++) {
                if (event_options[i].value == selected_option) {
                    is_selected_valid = true;
                    selected_item = event_options[i];
                    break;
                }
            }

            if (!is_selected_valid) {
                for (var i = 0; i < current_options.length; i++) {
                    if (current_options[i].value == selected_option) {
                        selected_item = {
                            label: current_options[i].label,
                            value: selected_option,
                            search: current_options[i].search,
                        };
                        updated_options.push(selected_item);
                        break;
                    }
                }
                current_classname += ' invalid-input-value';
            }
        }

        updated_options = handle_event_search_limits(
            search_value,
            updated_options,
            selected_option,
            selected_item,
            100
        );

        return [updated_options, current_classname];
    },

    set_custom_event_property_value: function (selected_values, search_input, data, operator) {
        const ctx = window.dash_clientside.callback_context;

        let is_on_dashboard =
            ctx.triggered.length == 1 &&
            ctx.triggered[0]['prop_id'].indexOf('dashboard-filter-value-type') != -1;
        let is_on_insights_page =
            ctx.triggered.length == 1 &&
            ctx.triggered[0]['prop_id'].indexOf('global-filter-value-type') != -1;

        // This filter is needed so we wouldn't keep the cohort not found item
        data = data.filter((item) => !item.value.startsWith('_custom_prefix_cohort_not_found_'));

        if (search_input == undefined || search_input === '') {
            return data;
        }
        let exists = data.some((item) => item.label === search_input);
        if (!exists) {
            // This filter is needed so we wouldn't push every pressed key to the data
            data = data.filter(
                (item) =>
                    !(
                        item.value.startsWith('_custom_prefix_') ||
                        item.value.startsWith('_custom_lookup_option_')
                    ) || selected_values.includes(item.value)
            );
            if (operator === 'in_cohort' || operator === 'not_in_cohort') {
                data.push({
                    label: 'Cohort not found: ' + search_input,
                    value: '_custom_prefix_cohort_not_found_' + search_input,
                    disabled: true,
                });
            } else {
                data.unshift({
                    label: search_input,
                    value: '_custom_prefix_' + search_input,
                });
                if (!is_on_dashboard && !is_on_insights_page) {
                    data.push({
                        label: '⛁ search for "' + search_input + '"',
                        value: '_custom_lookup_option_' + search_input,
                    });
                }
            }
        } else {
            // the item exists we move it to the top
            data.unshift(
                data.splice(
                    data.findIndex((item) => item.label === search_input),
                    1
                )[0]
            );
        }
        return data;
    },

    show_tooltip: function (hoverData) {
        let output = dict_output(
            'show',
            'bbox',
            'date',
            'event',
            'title',
            'number',
            'normalized_value',
            'plot_graph',
            'plot_class',
            'subtitle_value',
            'subtitle_class',
            'click_for_actions_class',
            'direction',
            'normalized_value_class',
            'number_class'
        );

        const click_for_actions_class =
            document.location.href.indexOf('tab=usage') === -1 ? 'fw-normal mb-2' : 'd-none';
        const normalized_value_class = 'fw-bold d-inline-block me-1';
        const number_class = 'fw-bold d-inline-block me-1';

        const no_update = output({
            show: false,
            bbox: null,
            date: undefined,
            event: undefined,
            title: undefined,
            number: undefined,
            normalized_value: undefined,
            plot_graph: undefined,
            plot_class: 'd-none',
            subtitle_value: undefined,
            subtitle_class: 'd-none',
            click_for_actions_class: click_for_actions_class,
            direction: 'left',
            normalized_value_class: normalized_value_class,
            number_class: number_class,
        });
        if (!hoverData || hoverData.length === 0) {
            return no_update;
        }

        const triggered_id = get_tooltip_triggered_id();
        const is_shared = triggered_id !== null && triggered_id.shared == 'True';

        const hoverData_value = hoverData.find((value) => value !== undefined && value !== null);

        if (!hoverData_value) {
            return no_update;
        }

        let pointData = hoverData_value.points[0];

        if (!pointData) {
            return no_update;
        }

        let bbox = pointData.bbox ? pointData.bbox : null;
        if (!bbox) {
            bbox = {
                x0: window.mousePageX,
                x1: window.mousePageX,
                y0: window.mousePageY,
                y1: window.mousePageY,
            };
        }
        let custom_json = null;
        let custom_data = pointData.customdata;

        if (!custom_data) {
            return no_update;
        }

        let is_heatmap = false;
        let show_plot = false;
        let sub_plot_data = [];
        let figure = undefined;
        let plot_container_class = '';

        try {
            let parsed = JSON.parse(custom_data);
            if (parsed && typeof parsed === 'object') {
                if (parsed.type === 'sankey_node') {
                    if (parsed.group == '##placeholder##') {
                        return no_update;
                    }
                    return output({
                        show: true,
                        bbox: bbox,
                        date: parsed.step + '. step',
                        event: parsed.group,
                        title: parsed.analyze_by,
                        number: parsed.user_count,
                        normalized_value: undefined,
                        plot_graph: undefined,
                        plot_class: 'd-none',
                        subtitle_value: undefined,
                        subtitle_class: 'd-none',
                        click_for_actions_class: click_for_actions_class,
                        direction: 'left',
                        normalized_value_class: normalized_value_class,
                        number_class: number_class,
                    });
                } else if (parsed.type === 'sankey_link') {
                    if (parsed.source_group == '##placeholder##') {
                        return no_update;
                    }
                    return output({
                        show: true,
                        bbox: bbox,
                        date:
                            parsed.source_step + '. step → ' + (parsed.source_step + 1) + '. step',
                        event: parsed.source_group + ' → ' + parsed.target_group,
                        title: 'Conversion rate',
                        number: parsed.user_count,
                        normalized_value: undefined,
                        plot_graph: create_tooltip_chart(
                            [parsed.source_user_count, parsed.target_user_count],
                            parsed.analyze_by
                        ),
                        plot_class: '',
                        subtitle_value:
                            ((parsed.target_user_count / parsed.source_user_count) * 100).toFixed(
                                2
                            ) + '%',
                        subtitle_class: 'fw-bold',
                        click_for_actions_class: 'd-none',
                        direction: 'left',
                        normalized_value_class: 'd-none',
                        number_class: 'd-none',
                    });
                }
            }
        } catch (e) {}

        for (let i = 0; i < custom_data.length; i++) {
            try {
                let parsed = JSON.parse(custom_data[i]);
                if (parsed && typeof parsed === 'object') {
                    custom_json = parsed;
                    is_heatmap = false;
                    show_plot = i == 2;
                    break;
                }
            } catch (e) {}
            is_heatmap = true;
        }

        // Version is hardcoded so we wouldn't show wrong tooltip on dashboards
        if (custom_json && (custom_json.v === undefined || custom_json.v !== 1)) {
            return no_update;
        }

        if (is_heatmap === true) {
            try {
                let parsed_custom_data = JSON.parse(custom_data);
                let custom_data_json = JSON.parse(parsed_custom_data[2]);
                let unique_field_label = custom_data_json.ufl;
                figure = create_tooltip_chart(parsed_custom_data.slice(3), unique_field_label);
                if (figure === undefined) {
                    plot_container_class = 'd-none';
                }

                let normalized_percentage = custom_data_json.np;

                if (normalized_percentage === 100) {
                    normalized_percentage = undefined;
                }
                if (normalized_percentage != undefined && normalized_percentage != null) {
                    normalized_percentage = '(' + normalized_percentage + '%)';
                }
                return output({
                    show: true,
                    bbox: bbox,
                    date: custom_data_json.el,
                    event: custom_data_json.ev,
                    title: custom_data_json.tl,
                    number: custom_data_json.tv,
                    normalized_value: normalized_percentage,
                    plot_graph: figure,
                    plot_class: plot_container_class,
                    subtitle_value: undefined,
                    subtitle_class: 'd-none',
                    click_for_actions_class: is_shared ? 'd-none' : click_for_actions_class,
                    direction: 'left',
                    normalized_value_class: normalized_value_class,
                    number_class: number_class,
                });
            } catch (e) {
                return no_update;
            }
        }

        if (show_plot === true) {
            sub_plot_data = custom_data.slice(3);

            let unique_field_label = custom_json.ufl;
            figure = create_tooltip_chart(sub_plot_data, unique_field_label);

            if (figure === undefined) {
                plot_container_class = 'd-none';
            }
        } else {
            figure = undefined;
            plot_container_class = 'd-none';
        }

        let normalized_percentage = custom_json.np;
        let subtitle = custom_json.st;

        subtitle = typeof subtitle === 'undefined' || subtitle === '' ? '' : subtitle;
        let has_subtitle = !(typeof subtitle === 'undefined' || subtitle === '');

        if (has_subtitle) {
            subtitle = create_from_previous_label(subtitle);
        }

        if (normalized_percentage === 100) {
            normalized_percentage = undefined;
        }
        if (normalized_percentage != undefined && normalized_percentage != null) {
            normalized_percentage = '(' + normalized_percentage + '%)';
        }

        return output({
            show: true,
            bbox: bbox,
            date: custom_json.el,
            event: custom_json.ev,
            title: custom_json.tl,
            number: custom_json.tv,
            normalized_value: normalized_percentage,
            plot_graph: figure,
            plot_class: plot_container_class,
            subtitle_value: subtitle,
            subtitle_class: has_subtitle ? '' : 'd-none',
            click_for_actions_class: is_shared ? 'd-none' : click_for_actions_class,
            direction: custom_json.di ? custom_json.di : 'left',
            normalized_value_class: normalized_value_class,
            number_class: number_class,
        });
    },

    delete_dashboard_modal: function (
        delete_dashboard_clicks,
        modal_close_clicks,
        delete_dashboard_accept_clicks
    ) {
        const ctx = window.dash_clientside.callback_context;

        if (
            ctx.triggered.length === 1 &&
            ctx.triggered[0].prop_id === 'delete_dashboard_button.n_clicks'
        ) {
            return true;
        }

        return false;
    },

    duplicate_dashboard_modal: function (duplicate_clicks, close_clicks, current_dashboard_name) {
        let output = dict_output('is_open', 'dashboard_name', 'error');

        const ctx = window.dash_clientside.callback_context;

        if (
            ctx.triggered.length === 1 &&
            ctx.triggered[0].prop_id === 'duplicate_dashboard_button.n_clicks'
        ) {
            return output({
                is_open: true,
                dashboard_name: current_dashboard_name + ' (copy)',
                error: '',
            });
        }

        return output({
            is_open: false,
            dashboard_name: window.dash_clientside.no_update,
            error: window.dash_clientside.no_update,
        });
    },
    refresh_frequency_modal: function (
        refresh_frequency_clicks,
        close_clicks,
        accept_clicks,
        auto_refresh_indicator_clicks
    ) {
        const ctx = window.dash_clientside.callback_context;
        if (
            ctx.triggered.length === 1 &&
            (ctx.triggered[0].prop_id === 'dashboard_auto_refresh.n_clicks' ||
                ctx.triggered[0].prop_id === 'auto_refresh_settings_indicator.n_clicks')
        ) {
            return true;
        }
        return false;
    },
    toggle_auto_refresh: function (checked) {
        const ctx = window.dash_clientside.callback_context;
        if (
            ctx.triggered.length === 1 &&
            ctx.triggered[0].prop_id === 'enable_auto_refresh_checkbox.checked' &&
            checked === true
        ) {
            return 'd-inline-block';
        }
        return 'd-none';
    },
    delete_dashboard_modal: function (
        delete_dashboard_clicks,
        modal_close_clicks,
        delete_dashboard_accept_clicks
    ) {
        const ctx = window.dash_clientside.callback_context;

        if (
            ctx.triggered.length === 1 &&
            ctx.triggered[0].prop_id === 'delete_dashboard_button.n_clicks'
        ) {
            return true;
        }

        return false;
    },
    snapshot_dashboard_modal: function (snapshot_clicks, close_clicks, current_dashboard_name) {
        let output = dict_output('is_open', 'dashboard_name', 'error');

        const ctx = window.dash_clientside.callback_context;

        if (
            ctx.triggered.length === 1 &&
            ctx.triggered[0].prop_id === 'create_snapshot_button.n_clicks'
        ) {
            return output({
                is_open: true,
                dashboard_name: current_dashboard_name + ' (snapshot)',
                error: '',
            });
        }

        return output({
            is_open: false,
            dashboard_name: window.dash_clientside.no_update,
            error: window.dash_clientside.no_update,
        });
    },
    toggle_graph_annotations: function (is_visible) {
        const plots = document.getElementsByClassName('dash-graph');
        for (var pi = 0; pi < plots.length; pi++) {
            const plot = plots[pi];
            const plot_parent = plot.parentNode; // sometimes dash fails to update the dcc.Graph classnames

            if (plot_parent.getAttribute('id') === 'sub_plot_container') {
                continue; // it's the chart in the tooltip
            }
            const is_heatmap =
                plot_parent.getAttribute('class').indexOf('chart-type-heatmap') !== -1;

            /* texts */
            const annotations = plot.getElementsByClassName('annotation');
            for (var i = 0; i < annotations.length; i++) {
                annotations[i].style['display'] = is_visible || is_heatmap ? 'inline' : 'none';
            }

            if (is_heatmap) {
                continue;
            }

            /* vertical lines */
            const share_groups = plot.getElementsByClassName('shape-group');

            for (var i = 0; i < share_groups.length; i++) {
                const share_group = share_groups[i];
                if (!share_group.hasChildNodes()) {
                    continue;
                }
                if (share_group.children[0].style['strokeDasharray'] != '') {
                    share_group.style['display'] = is_visible ? 'inline' : 'none';
                }
            }
        }
    },
    manage_add_tables_modal: function (add_tables, close) {
        let output = dict_output(
            'is_open',
            'error',
            'schema_data',
            'schema_value',
            'schema_placeholder',
            'user_id',
            'event_time',
            'event_name',
            'date_partition',
            'primary_key',
            'user_id_data',
            'event_time_data',
            'event_name_data',
            'date_partition_data',
            'primary_key_data',
            'fetch_columns_progress_info'
        );
        const ctx = dash_clientside.callback_context;
        if (!ctx.triggered.length) {
            return output({
                is_open: dash_clientside.no_update,
                error: dash_clientside.no_update,
                schema_data: dash_clientside.no_update,
                schema_value: dash_clientside.no_update,
                schema_placeholder: dash_clientside.no_update,
                user_id: dash_clientside.no_update,
                event_time: dash_clientside.no_update,
                event_name: dash_clientside.no_update,
                date_partition: dash_clientside.no_update,
                primary_key: dash_clientside.no_update,
                user_id_data: dash_clientside.no_update,
                event_time_data: dash_clientside.no_update,
                event_name_data: dash_clientside.no_update,
                date_partition_data: dash_clientside.no_update,
                primary_key_data: dash_clientside.no_update,
                fetch_columns_progress_info: dash_clientside.no_update,
            });
        }

        const triggered = ctx.triggered[0].prop_id;

        if (triggered === 'add_tables_button.n_clicks') {
            return output({
                is_open: true,
                error: [],
                schema_data: [],
                schema_value: null,
                schema_placeholder: 'Select schema',
                user_id: '',
                event_time: '',
                event_name: '',
                date_partition: '',
                primary_key: '',
                user_id_data: [],
                event_time_data: [],
                event_name_data: [],
                date_partition_data: [],
                primary_key_data: [],
                fetch_columns_progress_info: '',
            });
        }

        return output({
            is_open: false,
            error: dash_clientside.no_update,
            schema_data: dash_clientside.no_update,
            schema_value: dash_clientside.no_update,
            schema_placeholder: dash_clientside.no_update,
            user_id: dash_clientside.no_update,
            event_time: dash_clientside.no_update,
            event_name: dash_clientside.no_update,
            date_partition: dash_clientside.no_update,
            primary_key: dash_clientside.no_update,
            user_id_data: dash_clientside.no_update,
            event_time_data: dash_clientside.no_update,
            event_name_data: dash_clientside.no_update,
            date_partition_data: dash_clientside.no_update,
            primary_key_data: dash_clientside.no_update,
            fetch_columns_progress_info: dash_clientside.no_update,
        });
    },

    update_label_mapping: function (
        new_value,
        unused_param,
        current_mappings_str,
        all_groups_arr,
        reverse_mapping_json_arr
    ) {
        const ctx = window.dash_clientside.callback_context;
        if (
            all_groups_arr.length == 0 ||
            reverse_mapping_json_arr.length == 0 ||
            ctx.triggered.length != 1
        ) {
            // no values are present in the ALL callback match
            return window.dash_clientside.no_update;
        }
        const all_groups = all_groups_arr[0];
        const reverse_mapping_json = reverse_mapping_json_arr[0];
        const reverse_mapping = JSON.parse(reverse_mapping_json);
        var mapping = JSON.parse(current_mappings_str);
        const used_labels = ['<n/a>', ''].concat(JSON.parse(all_groups), Object.values(mapping));

        const triggered_id = JSON.parse(ctx.triggered[0]['prop_id'].replace('.value', ''));
        const new_label = ctx.inputs[ctx.triggered[0]['prop_id']];
        if (used_labels.includes(new_label)) {
            return window.dash_clientside.no_update;
        }
        let mapping_key;
        if (reverse_mapping.hasOwnProperty(triggered_id['index'])) {
            mapping_key = reverse_mapping[triggered_id['index']];
        } else {
            mapping_key = triggered_id['index'];
        }

        mapping[mapping_key] = new_label;

        var new_mapping = { ...mapping };
        /* avoid the mappings like a->b b->c, it's a->c */
        Object.entries(mapping).forEach(([key, value]) => {
            Object.entries(mapping).forEach(([key_2, value_2]) => {
                if (value == key_2) {
                    delete new_mapping[key_2];
                    new_mapping = { ...new_mapping, [key]: value_2 };
                }
            });
        });
        return JSON.stringify(new_mapping);
    },
    show_raw_text: function (n_clicks, data, text_area_value) {
        if (!n_clicks) {
            return [
                window.dash_clientside.no_update,
                window.dash_clientside.no_update,
                window.dash_clientside.no_update,
            ];
        }
        if (!data) {
            return [{ display: 'none' }, { display: 'block' }, window.dash_clientside.no_update];
        } else {
            return [
                {
                    'max-height': '400px',
                    'overflow-y': 'auto',
                    padding: '10px',
                    'white-space': 'pre-wrap',
                    'font-size': '18px',
                },
                { display: 'none' },
                text_area_value,
            ];
        }
    },

    add_value_when_searching: function (search_input, data, selected_values) {
        if (search_input == undefined || search_input === '') {
            return data;
        }
        let exists = data.some((item) => item.label === search_input);
        if (!exists) {
            // This filter is needed so we wouldn't push every pressed key to the data
            data = data.filter(
                (item) =>
                    !item.value.startsWith('_custom_prefix_') ||
                    (selected_values != null && selected_values.includes(item.value))
            );
            data.unshift({
                label: search_input,
                value: '_custom_prefix_' + search_input,
            });
        } else {
            // the item exists we move it to the top
            data.unshift(
                data.splice(
                    data.findIndex((item) => item.label === search_input),
                    1
                )[0]
            );
        }
        return data;
    },

    update_selected_tables_count_text: function (checkbox_store, state_str) {
        const edts = JSON.parse(state_str);
        const total_tables = edts.length + ' table' + (edts.length > 1 ? 's' : '');
        const selectedCount = Object.values(checkbox_store || {}).filter(Boolean).length;
        if (selectedCount > 0) {
            return (
                selectedCount +
                ' table' +
                (selectedCount === 1 ? '' : 's') +
                ' selected from ' +
                total_tables
            );
        } else {
            return 'Total ' + total_tables;
        }
    },

    update_edt_table_checkboxes: function (
        select_all_checked,
        edt_row_checked_list,
        edt_row_id_list,
        store_data,
        state_str
    ) {
        const triggered = dash_clientside.callback_context.triggered.map((t) => t.prop_id)[0];
        const output = dict_output('store', 'checkboxes', 'select_all_checked');

        const edts = JSON.parse(state_str);
        var is_all_selected = true;
        if (triggered === 'edt_table_all_checkbox.checked') {
            const newState = {};
            for (let i = 0; i < edts.length; i++) {
                newState[edts[i].id] = edts[i].matches_on_filter && select_all_checked;
                if (edts[i].matches_on_filter) {
                    is_all_selected = is_all_selected && select_all_checked;
                }
            }
            var checkbox_values = [];
            for (let i = 0; i < edt_row_id_list.length; i++) {
                checkbox_values.push(newState[edt_row_id_list[i].id]);
            }
            return output({
                store: newState,
                checkboxes: checkbox_values,
                select_all_checked: is_all_selected,
            });
        } else {
            const newState = { ...store_data };
            for (let i = 0; i < edt_row_id_list.length; i++) {
                newState[edt_row_id_list[i].id] = edt_row_checked_list[i];
            }
            return output({
                store: newState,
                checkboxes: edt_row_checked_list,
                select_all_checked: select_all_checked,
            });
        }
    },
};

function handle_event_search_limits(
    search_value,
    updated_options,
    selected_option,
    selected_item,
    limit
) {
    //  We need to limit the number of options to 100 because otherwise the react UI breaks
    if (!updated_options) {
        return updated_options;
    }
    var total_length = updated_options.length;

    if (search_value) {
        // Removing values based on search
        search_value = search_value.toLowerCase();
        updated_options = updated_options.filter(
            (word) =>
                word.search.toLowerCase().indexOf(search_value) >= 0 &&
                word.value !== 'ONLY_SHOWING'
        );
    }

    // Limiting the number of options
    if (total_length > limit) {
        updated_options = updated_options.slice(0, limit);
        if (selected_option !== null) {
            // Adding back the selected item
            if (!updated_options.some((item) => item.value === selected_option)) {
                updated_options.push(selected_item);
            }
        }
        // Adding the "Showing only 50 events" option
        updated_options.push({
            value: 'ONLY_SHOWING',
            label:
                'Showing only ' + updated_options.length + ' our of ' + total_length + ' events...',
            disabled: true,
            search: search_value,
        });
    }
    return updated_options;
}

window.dash_clientside.clientside.handle_event_search_limits = handle_event_search_limits;

function displaySegmentPanelMenu(target) {
    // Set menu display to block
    document.documentElement.style.setProperty('--menu-target-display', 'block');

    // Calculate and set the top position of the menu
    const fixTopHeight = 175;
    const topPosition =
        target.parentElement.parentElement.getBoundingClientRect().top +
        window.scrollY -
        fixTopHeight;
    document.documentElement.style.setProperty('--menu-target-top', `${topPosition}px`);
}

function setupEventHoverListeners() {
    document.querySelectorAll('.event_name_dropdown .VirtualizedSelectOption').forEach((option) => {
        // Check if the listener was already added to avoid duplicate listeners
        if (option.dataset.hoverListenerAdded) return;
        option.dataset.hoverListenerAdded = 'true';

        option.addEventListener('mouseenter', (event) => {
            const target = event.target;
            if (target.className.indexOf('VirtualizedSelectFocusedOption') === -1) return;

            const field_type = target.children[0]?.children[1]?.textContent || '';
            setDashComponentChildren('segment_panel_menu_state', field_type);
            displaySegmentPanelMenu(target);
        });
    });
}

function setupPropertyHoverListeners() {
    document
        .querySelectorAll(
            '.property_name_dropdown .VirtualizedSelectOption, .complex_segment_group_by .VirtualizedSelectOption, .global-filter-property-type .VirtualizedSelectOption'
        )
        .forEach((option) => {
            // Check if the listener was already added to avoid duplicate listeners
            if (option.dataset.hoverListenerAdded) return;
            option.dataset.hoverListenerAdded = 'true';

            option.addEventListener('mouseenter', (event) => {
                const target = event.target;
                if (target.className.indexOf('VirtualizedSelectFocusedOption') === -1) return;

                const field_type = target.children[0]?.children[1]?.textContent || '';
                if (field_type === '') {
                    document.documentElement.style.setProperty('--menu-target-display', 'none');
                    return;
                }
                setDashComponentChildren('segment_panel_menu_state', field_type);
                displaySegmentPanelMenu(target);
            });
        });
}

function setupMultiSelectEnterListeners() {
    document.querySelectorAll('.mantine-MultiSelect-searchInput').forEach((input) => {
        // Check if the listener was already added to avoid duplicate listeners
        if (input.dataset.enterListenerAdded) return;
        input.dataset.enterListenerAdded = 'true';

        input.addEventListener('keyup', (event) => {
            if (event.key == 'Enter') {
                first_dd_option = document.querySelector('.mantine-MultiSelect-item');

                var reactkey = '';
                var props = Object.keys(first_dd_option);
                for (var i = 0; i < props.length; i++) {
                    if (props[i].startsWith('__reactInternal')) {
                        reactkey = props[i];
                        break;
                    }
                }
                // compatible with mantine v5.4.0, dmc v0.11.1
                first_dd_option[reactkey].return.pendingProps.onMouseEnter();
                first_dd_option[reactkey].return.pendingProps.onMouseDown(new Event('mousedown'));
            }
        });
    });
}

function setupLegendRenamingEventListeners() {
    document.querySelectorAll('.legend-container').forEach((badge) => {
        if (badge.enterListenerAdded) return;
        badge.enterListenerAdded = 'true';
        const input = badge.querySelector('.mantine-TextInput-input');
        /* it's null when the metric is broken */
        if (input === null) {
            return;
        }

        const inputHandler = function (e) {
            if (e.target.value == '') {
                /* the dash callback does not updage the mapping, just restore the original value */
                e.target.value = e.target.placeholder;
            }
        };

        input.addEventListener('focusout', inputHandler);
    });
}

// works only with the basic dash components
function setDashComponentChildren(component_id, children) {
    var component = document.getElementById(component_id);
    var reactkey = '';
    var props = Object.keys(component);
    for (var i = 0; i < props.length; i++) {
        if (props[i].startsWith('__reactInternal')) {
            reactkey = props[i];
            break;
        }
    }
    component[reactkey].return.pendingProps.setProps({
        children: children,
    });
}

// works only with the basic dash components
function setDashComponentValue(component_id, value) {
    var component = document.getElementById(component_id);
    var reactkey = '';
    if (component === null) {
        return;
    }
    var props = Object.keys(component);
    for (var i = 0; i < props.length; i++) {
        if (props[i].startsWith('__reactInternal')) {
            reactkey = props[i];
            break;
        }
    }
    component[reactkey].return.pendingProps.setProps({
        value: value,
    });
}

document.addEventListener('click', function (event) {
    const segment_panel = document.getElementById('segment_panel_menu_description');
    if (segment_panel === null) {
        return;
    }
    const segment_parent = segment_panel.parentElement;
    if (segment_parent === null) {
        return;
    }
    const overlay =
        segment_parent.parentElement; /* FixMe: add extra parentElement to not close it on edit link click */
    const isClickInside = overlay != null && overlay.contains(event.target);
    if (!isClickInside) {
        document.documentElement.style.setProperty('--menu-target-display', 'none');
    }
});

const observerCallback = (mutationsList, observer) => {
    for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
            if (document.querySelector('.VirtualizedSelectOption')) {
                setupEventHoverListeners();
                setupPropertyHoverListeners();
            } else if (document.querySelector('.mantine-MultiSelect-searchInput')) {
                setupMultiSelectEnterListeners();
            }
            if (document.querySelector('.legend-container')) {
                setupLegendRenamingEventListeners();
            }
        }
    }
};

const observer = new MutationObserver(observerCallback);
const config = {
    childList: true,
    subtree: true,
};
const targetNode = document.body;
observer.observe(targetNode, config);
setupEventHoverListeners();
setupPropertyHoverListeners();
setupMultiSelectEnterListeners();
setupLegendRenamingEventListeners();

var version_mismatch_modal_open = false;
const error_handler = () => {
    if (version_mismatch_modal_open) {
        return;
    }

    state = window.store.getState();
    errors = state.error.backEnd.concat(state.error.frontEnd);
    if (errors.length == 0) {
        return;
    }
    const current_ui_version = document.getElementById('backend_version_at_page_load').value;

    error = errors[0];
    payload = {
        type: error.type,
        message: error.error.message,
        pathname: location.pathname,
        ui_version: current_ui_version,
    };

    version_mismatch_modal_open = true;
    fetch('/ui-error', {
        method: 'POST',
        headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
        },
        follow: 'manual',
        body: JSON.stringify(payload),
    })
        .then(async (response) => {
            if (response.status === 200) {
                const data = await response.json();
                setDashComponentValue('backend_version', data['backend_version']);
                setDashComponentValue('backend_version_last_checked', Date.now());
            } else {
                setDashComponentValue('backend_version', 'unauthorized');
                setDashComponentValue('backend_version_last_checked', Date.now());
            }
        })
        .catch(async (reason) => {
            setDashComponentValue('backend_version', 'unauthorized');
            setDashComponentValue('backend_version_last_checked', Date.now());
        });
};

window.dict_output = (...args) => {
    const arg_order = args;
    const ret = (outputs) => {
        var output_list = [];

        for (var i = 0; i < arg_order.length; i++) {
            if (!outputs.hasOwnProperty(arg_order[i])) {
                throw new Error(
                    arg_order[i] + ' is missing from the outputs ' + JSON.stringify(outputs)
                );
            }
            output_list.push(outputs[arg_order[i]]);
        }

        return output_list;
    };

    return ret;
};
