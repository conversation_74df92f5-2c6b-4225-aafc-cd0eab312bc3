.filter-container .mantine-Chip-label {
    padding-left: 24px;
}

.filter-container .mantine-Chip-label[data-checked] {
    padding-left: 0px;
}

.filter-container .mantine-Chip-iconWrapper {
    vertical-align: baseline;
}

.filter-container .filter-chip:hover {
    background-color: #ececec;
}

.browse-result:hover {
    background-color: #ececec88 !important;
}

.home-action:hover {
    background-color: #ececec !important;
}

.resource-button div :nth-child(2) {
    width: 100%;
}

.resource-button:hover {
    background-color: #ececec !important;
}

.search-bar .Select-control {
    border: 1px solid transparent;
}

.search-bar:hover .Select-control {
    border: 1px solid;
    outline: none;
    border-color: #555159;
    box-shadow: 0 0 10px #555159;
}

.search-bar input {
    color: #e1dfe4;
    font-family: var(--def-font) !important;
    letter-spacing: 0.25px;
}

.search-bar .Select-arrow {
    display: none;
}
.search-bar .Select-placeholder {
    color: #e1dfe4;
}
.annotation-datepicker tbody tr td {
    /* dmc.Datepicker bugfix inside a table*/
    padding: 0px;
}

.home-quick-actions .mantine-Accordion-chevron {
    margin-right: 1px;
}

.home-tooltip-container {
    max-width: 450px;
}

.home-tooltip-img-container {
    width: 200px;
}

.home-tooltip-title-container {
    width: 200px;
}

.dashboard-preview-slot {
    width: 100%;
    height: 73px;
}

.empty-dashboard-preview-slot {
    width: 100%;
    height: 73px;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
    background-image: linear-gradient(to bottom right, #ffffff, #f5f5f5);
}

.dashboard-preview-grid {
    height: 168px;
    width: 200px;
}

.uikit-tab-list .mantine-Tabs-tab[data-active],
.uikit-tab-list .mantine-Tabs-tab[data-active]:hover {
    border-color: black;
    background-color: #ececec;
}

.nav-button:not(#search_bar):not(#search_bar *):hover {
    background-color: #555159;
}
