:root {
    --component-height: 31px;
    --mdb-body-font-size-large: 18px;
    --mdb-body-font-size: 12px;
    --mdb-body-font-size-small: 8px;
    --expore-content-min-height: 450px;
    --def-font: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji';
}

.complex_segment {
    margin-bottom: 5px;
    border-left: 8px solid #9843e5 !important;
    border-right: 2px solid #ececec !important;
    border-top: 2px solid #ececec !important;
    border-bottom: 2px solid #ececec !important;
}

.graph_container {
    min-height: var(--expore-content-min-height);
}

.spinner-border {
    height: 10px;
    width: 10px;
    border-width: 1px;
    padding: 0px;
}

.complex_segment {
    margin-bottom: 5px;
    padding-bottom: 2px;
    font-family: var(--def-font) !important;
    letter-spacing: 0.25px;
}

.complex_segment .mantine-12zgr9 {
    font-size: var(--mdb-body-font-size) !important;
}

.complex_segment .mantine-1e8dll4,
.complex_segment .mantine-zmh4e,
.complex_segment .mantine-1ajyf09,
.complex_segment .mantine-4z4z7q,
.complex_segment .mantine-1i9vch3,
.complex_segment .mantine-qew57g {
    /* All selects in complex segment */
    border: 0px;
    background: transparent;
    font-size: var(--mdb-body-font-size) !important;
    font-weight: var(--mdb-body-font-weight);
    line-height: var(--mdb-body-font-weight);
    font: var(--mdb-font-roboto);
}

.complex_segment .mantine-qew57g,
.complex_segment .mantine-1e8dll4 {
    padding-right: 5px;
    padding-left: 5px;
}

.complex_segment .mantine-4z4z7q {
    font-weight: bold;
    color: #454e56;
}

.complex_segment .aggregation-container {
    background-color: #5b7bf710;

    font: bold;
    border-radius: 6px;
}

.property_value_input .mantine-fqghar {
    font-size: var(--mdb-body-font-size) !important;
    font-weight: var(--mdb-body-font-weight);
    font: var(--mdb-font-roboto);
    background: rgba(39, 128, 227, 0.2);
    overflow-wrap: anywhere;
    height: auto !important;
}

.loading-value-input {
    background: linear-gradient(90deg, #eee0, rgba(39, 128, 227, 0.1), #eee0);
    animation: leftToRight 1s infinite reverse;
    background-size: 200%;
    border-radius: 6px;
}

@keyframes leftToRight {
    0% {
        background-position: -100% 0;
    }

    100% {
        background-position: 100% 0;
    }
}

.property_value_input .mantine-rsiguj {
    white-space: normal !important;
}

.complex_segment_group_by .mantine-fqghar {
    background-color: #6ebbaa2a;
}

.event_name_dropdown .Select-control,
.event_name_dropdown,
.event_name_dropdown .Select,
.event_name_dropdown .Select-placeholder,
.event_name_dropdown .Select-value {
    background: transparent;
}

.event_name_dropdown .Select-value-label {
    color: #7950f2 !important;
}

.event_name_dropdown .Select-placeholder {
    font-weight: normal !important;
}

.complex_segment .Select-value-label div {
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 0px;
}

.complex_segment .Select-value {
    padding-right: 20px !important;
}

.complex_segment .mantine-uquezq,
.complex_segment .mantine-bl0c8b {
    /* Caret of every select  */
    display: none;
}

.property_name_dropdown .mantine-rynikm {
    /* Property name dropdown popup position from screen left */
    left: 10px !important;
}

.property_name_dropdown:hover .Select-control {
    background-color: transparent;
}

.property_name_dropdown .Select-value .bi-dot {
    display: none;
}

.event_segment {
    border-radius: 6px;
    margin-left: 8px;
    margin-right: 7px;
    margin-bottom: 7px;
}

.complex_segment .mantine-1e8dll4 {
    /* Simple segment value dropdown */
    background: transparent;
    border: none;
    padding-left: 9px;
}

.dash-table-container
    .dash-spreadsheet-container
    .dash-spreadsheet-inner
    th.dash-filter
    input.dash-filter--case--sensitive {
    border-color: var(--mdb-primary) !important;
    color: var(--mdb-primary) !important;
}

.dash-table-container
    .dash-spreadsheet-container
    .dash-spreadsheet-inner
    th:hover
    [class^='dash-filter--']:not(.disabled) {
    border-color: var(--mdb-primary) !important;
    color: var(--mdb-primary) !important;
}

#explore_results_table .dash-spreadsheet-inner {
    overflow-x: auto;
}

.dash-table-container .export {
    /* .btn .btn-info merged to make Datatable export button look like DMC button */
    text-transform: uppercase;
    vertical-align: bottom;
    border: 0;
    border-radius: 6px;
    margin-bottom: 6px;
    height: 32px;
    display: inline-block;
    padding: 0.375rem 1.5rem;
    font-size: 12px;
    font-weight: 600;
    line-height: 1.5;
    color: white;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: #4c6ef5;
    border-radius: 6px;
    margin-bottom: 16px;
}

.dash-table-container .dash-spreadsheet-container {
    overflow-x: auto;
    font-size: 12px;
}

.prop_description textarea {
    background: rgba(200, 200, 200, 0.1);
    border: 0px;
    border-radius: 6px;
}

.prop_description textarea:hover,
.prop_display_name input:hover {
    background-color: #5b7bf720;
}

.prop_display_name input {
    background: rgba(200, 200, 200, 0.1);
    border: 0px;
    border-radius: 6px;
    font-weight: bold;
}

.event_segment .Select-control {
    border: 0px;
    margin-bottom: 1px;
}

.event_segment .Select-value {
    background-color: #7950f21a;
    font-weight: bold;
    outline: none;
    box-shadow: none;
}

.property_name_dropdown .Select-value {
    background-color: transparent !important;
}

.event_segment .Select-arrow-zone {
    display: none;
}

.event_segment .Select-placeholder {
    color: var(--mdb-dark);
}

.event_segment .event_name_dropdown .Select-placeholder:hover {
    background-color: #7950f21a;
}

.event_segment .property_name_dropdown .Select-placeholder:hover {
    background-color: #5b7bf710;
}

.event_segment .property_name_dropdown.has-value:hover {
    background-color: #5b7bf710;
}

.complex_segment .mantine-MultiSelect-values:hover {
    background-color: #5b7bf710;
}

.event_segment .mantine-Select-wrapper:hover {
    background-color: #5b7bf710;
}

.metric_segments input::placeholder {
    color: var(--mdb-dark);
}

/* Filter value search color */
.complex_segment input.mantine-MultiSelect-searchInputEmpty,
.complex_segment input.mantine-MultiSelect-searchInput {
    color: #808080;
}

.complex_segment input.mantine-MultiSelect-searchInputEmpty::placeholder,
.complex_segment input.mantine-MultiSelect-searchInput::placeholder {
    color: var(--mdb-secondary);
}

.metric_segments input:focus::placeholder {
    opacity: 0.3;
}

#metrics_config_container input::placeholder {
    color: var(--mdb-dark);
}

#metrics_config_container input:focus::placeholder {
    opacity: 0.3;
}

.initial-segment .Select-placeholder {
    color: #7950f2 !important;
    background-color: #7950f21a;
    font-weight: bold !important;
}

.dropdown-menu-end {
    right: 0px !important;
    left: inherit;
}

.mantine-y1lrl8 {
    font-weight: normal !important;
    color: var(--mdb-secondary);
}

.mantine-6ntg99::placeholder {
    color: var(--mdb-dark) !important;
}

#graph_hover_tooltip,
#graph_click_tooltip {
    cursor: pointer !important;
    animation: fadeIn 0.3s;
}

.dcc-tooltip-bounding-box {
    cursor: pointer !important;
}

.dcc-tooltip-bounding-box .hover::before {
    cursor: pointer !important;
    display: none;
    border-color: transparent var(--tooltip-color) transparent transparent !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 0.95;
    }
}

.dcc-tooltip-bounding-box span[data-dash-is-loading='true'] {
    display: none;
}

.mantine-DateRangePicker-wrapper input,
.mantine-DateRangePicker-wrapper input:focus {
    height: 32px;
    border-radius: 6px;
    border-color: rgb(241, 243, 245);
    border-width: 2px;
}

.time-window-chooser-date-range-selected input,
.time-window-chooser-date-range-selected input:focus {
    background-color: rgb(241, 243, 245);
    border-color: rgb(112, 72, 232);
}

.mantine-1khhbm2,
.mantine-1wvsdi7,
.mantine-14909xf {
    /* Segmented control padding is 4px */
    padding-top: 2px;
    padding-bottom: 2px;
    padding-left: 4px;
    padding-right: 4px;
    background: transparent;
    border: 1px solid #e7eaedff;
}

.mantine-1khhbm2 span,
.mantine-1wvsdi7 span,
.mantine-14909xf span {
    /* Fixing segmented control selected value */
    margin-top: 2px;
}

.mantine-DateRangePicker-wrapper input,
.mantine-DatePicker-wrapper input {
    border: 1px solid #e7eaedff;
}

.mantine-DateRangePicker-wrapper input::placeholder {
    color: var(--mdb-dark);
}

#formula_input:placeholder-shown:not(:focus) {
    border: none;
}

#formula_input:focus {
    cursor: auto !important;
}

.mantine-InputWrapper-description {
    display: none;
    padding-left: 10px;
}

.mantine-1m3pqry:focus-within .mantine-InputWrapper-description {
    display: block;
}

.title-input input::placeholder {
    font-weight: bold !important;
}

.title-input input {
    font-weight: bold !important;
}

.name-input {
    padding-left: 5px;
}

.first-event-filter .mantine-Chip-label {
    padding-left: 0px;
}

/* prevents resizing the save metric buttons when loading */
.mantine-Button-leftIcon {
    display: inline-block;
}

.fade-violet .mantine-SegmentedControl-active {
    background-color: #5b7bf71a;
}

.fade-violet .mantine-SegmentedControl-labelActive {
    color: rgba(10, 10, 10);
    /* font-weight: bold; */
}

.text-start .mantine-Button-inner {
    justify-content: start;
}

.mantine-Input-icon {
    color: rgba(20, 20, 20);
    background-color: transparent;
    border-radius: 6px;
}

.mantine-Button-root:disabled {
    background-color: transparent;
}

.VirtualSelectGrid::parent {
    border: none;
}

/* Fixing DCC Dropdown to look like DMC Select */

.Select-control {
    box-shadow: none !important;
}

.Select-menu-outer {
    background: rgb(255, 255, 255);
    border: 1px solid rgb(233, 236, 239) !important;
    box-shadow:
        rgba(0, 0, 0, 0.05) 0px 1px 3px,
        rgba(0, 0, 0, 0.05) 0px 10px 15px -5px,
        rgba(0, 0, 0, 0.04) 0px 7px 7px -5px !important;
    border-radius: 2px;
    min-width: 400px !important;
}

.Select-menu {
    margin-top: -2px;
}

.VirtualSelectGrid {
    padding: 4px;
    min-width: 400px !important;
}

[id^='metric-type-choice-tab'][data-active='true'] {
    background-color: #ffffff !important;
}
[id^='metric-type-choice-tab'][aria-selected='false'] {
    color: #6e6873;
}
[id^='metric-type-choice-tab'] {
    font-weight: 500;
    font-size: 12px;
}
#metric-type-choice-tab-segmentation[data-active='true'] {
    border-left: none;
}

.VirtualizedSelectOption {
    text-align: left;
    cursor: pointer;
    color: rgb(0, 0, 0);
    border-radius: 2px;
    font-weight: 400;
    list-style: 1.55;
}

.VirtualizedSelectFocusedOption.VirtualizedSelectSelectedOption {
    background-color: rgb(28, 126, 214);
}

.VirtualizedSelectSelectedOption {
    font-weight: 400;
    background-color: rgb(34, 139, 230);
    color: white;
}

.VirtualizedSelectSelectedOption .text-secondary {
    color: rgb(220, 220, 220) !important;
}

.VirtualizedSelectFocusedOption {
    background-color: rgb(241, 243, 245);
}

.mantine-Spoiler-control {
    font-size: 14px;
    margin-top: 5px;
}

.cell-table td {
    max-width: 400px;
    text-overflow: ellipsis;
}

.legend-container {
    text-overflow: ellipsis;
    max-width: 300px;
    overflow: ellipsis;
    align-items: start;
}

.legend-badge:hover {
    text-overflow: visible !important;
    overflow: visible;
    cursor: default;
}

.legend-badge {
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 300px;
    font-weight: 600;
}

.legend-badge .mantine-TextInput-root {
    display: inline-block;
    width: 100%;
}

.legend-badge .mantine-TextInput-input {
    background: transparent;
    border: none;
    text-transform: uppercase;
    text-overflow: ellipsis;
    max-width: 280px;
    text-align: center;
    font-size: 10px;
    font-family: var(--def-font);
    font-weight: 600;
    line-height: 16px;
    color: white;
}

.legend-badge .mantine-TextInput-input:focus,
.legend-badge .mantine-TextInput-input:hover {
    background-color: rgb(255, 255, 255, 0.1);
    border-radius: 4px;
}

.mantine-Textarea-input {
    appearance: unset;
}

.mantine-InputWrapper-root,
.mantine-SegmentedControl-root,
.mantine-Input-icon {
    z-index: 0;
}

.dash-dropdown .Select-menu-outer {
    /* Fixing dcc.Dropdown issue with a single option */
    min-height: 38px;
}

body::-webkit-scrollbar {
    width: 0.4em;
}

body::-webkit-scrollbar-thumb {
    background-color: #f4f4f4;
    border-radius: 8px;
}

/* multi select layout fixes to support descriptions */
.dash-dropdown .Select--multi .Select-value-label {
    color: rgb(73, 80, 87);
    font-size: 12px;
}

.dash-dropdown .Select--multi .Select-multi-value-wrapper .property-description {
    display: none;
}

.dash-dropdown .Select--multi .Select-multi-value-wrapper .Select-value-icon {
    border: none;
}

.dash-dropdown .Select-multi-value-wrapper .Select-value {
    border: none;
}

.dash-dropdown .Select-control {
    border: none;
}

.complex_segment_group_by .Select-multi-value-wrapper .Select-value {
    background-color: #6ebbaa2a;
    border-radius: 2px;
}

.complex_segment_group_by .Select-multi-value-wrapper .Select-value-icon {
    color: rgb(73, 80, 87);
}

.complex_segment_group_by .Select-multi-value-wrapper .Select-placeholder {
    color: var(--mdb-dark);
}

.complex_segment_group_by .Select-multi-value-wrapper .Select-value:hover,
.complex_segment_group_by .Select-multi-value-wrapper .Select-placeholder:hover {
    background-color: #5b7bf710;
}

.complex_segment_body .mantine-Select-wrapper:hover {
    border-radius: 2px;
}

.complex_segment_group_by .Select-arrow-zone {
    display: none;
}

.complex_segment_group_by .Select-value .bi-dot {
    display: none;
}

/* event property aggregation dropdown */
.event-prop-agg .prop-agg-input-container:first-child {
    width: 120px;
    display: inline-block;
    margin-right: 4px;
}

.event-prop-agg .prop-agg-input-container:last-child {
    width: calc(100% - 124px);
    display: inline-block;
}

.event-prop-agg label {
    display: block;
    color: var(--mdb-secondary);
}

.event-prop-agg .Select-control,
.event-prop-agg .Select.is-focused:not(.is-open) > .Select-control,
.event-prop-agg .Select.is-open > .Select-control,
.event-prop-agg .Select-control .Select-multi-value-wrapper .Select-value {
    background-color: transparent;
    line-height: 28px;
    height: 28px;
}

.event-prop-agg .Select-input {
    height: 28px;
}

.event-prop-agg .Select-arrow-zone {
    display: none;
}

.event-prop-agg .Select-placeholder {
    color: var(--mdb-secondary);
    line-height: 28px;
    padding-top: 1px;
}

.event-prop-agg .Select-clear-zone {
    line-height: 10px;
    padding-top: 1px;
}

.event-prop-agg .Select-value {
    padding-top: 1px;
}

.event-prop-agg.in-config-panel .Select-control {
    border: solid 1px rgb(206, 212, 218);
}

.event-prop-agg.in-segment .Select-placeholder {
    color: var(--mdb-dark);
}

/* advanced options */
.advanced-options label {
    color: var(--mdb-secondary);
}

.advanced-options .Select-control {
    background-color: transparent;
    border: solid 1px rgb(206, 212, 218);
    line-height: 28px;
    height: 28px;
}

.advanced-options .Select-input {
    height: 28px;
}

.advanced-options .Select-placeholder {
    color: var(--mdb-secondary);
    line-height: 28px;
    padding-top: 1px;
}

.advanced-options .Select-multi-value-wrapper .Select-value-icon {
    color: rgb(73, 80, 87);
}

.advanced-options .Select--single .Select-control .Select-multi-value-wrapper .Select-value {
    background-color: transparent;
    line-height: 28px;
    height: 28px;
}

.advanced-options .Select--multi .Select-control .Select-multi-value-wrapper .Select-value {
    background-color: rgb(241, 243, 245);
    border-radius: 2px;
}

.nav-button {
    background-color: transparent;
}

.nav-button:hover {
    background-color: #ececec;
}

.overflow-visible .modal-content {
    overflow: visible;
}

button.segment-hover:hover,
.segment-hover:hover .mantine-TextInput-input:hover {
    background-color: #5b7bf710 !important;
    cursor: pointer;
}

.mantine-Input-icon {
    background-color: transparent;
}

.mantine-Menu-dropdown.pull-left {
    left: 10px !important;
}

.resolution_dd .mantine-Select-wrapper:hover input {
    background-color: #5b7bf710;
}

.mapboxgl-canvas {
    border-radius: 6px;
}

.adt-container {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
}

.adt-container .mantine-Chip-label {
    border: 0;
    margin-left: 10px;
    width: 90%;
}

.adt-container .mantine-Chip-label[data-checked][data-variant='outline'] {
    border: 0;
    margin-left: 0;
}

.adt-container .mantine-Chip-checkIcon {
    color: black;
}

.graph_hover_tooltip div {
    text-overflow: ellipsis;
    overflow: hidden;
}

.dark-muted input:disabled {
    opacity: 1;
}

.menu-target-overlay {
    display: var(--menu-target-display, none);
    position: absolute;
    left: calc(100% + 5px);
    top: var(--menu-target-top, 0px);
    z-index: 3000;
}

#segment_panel_menu *:disabled {
    background-color: unset;
    color: unset;
    opacity: unset;
    cursor: unset;
    font-size: 12px;
}

#segment_panel_menu_link {
    color: gray;
    text-decoration: underline;
    font-size: 11px;
}

#dimension_table_id p {
    text-decoration: underline;
    margin-bottom: 5px;
}

.invalid-input-value .input-error-msg {
    color: rgb(237, 58, 58);
    margin-left: 10px;
}

.invalid-input-value .Select-value,
.invalid-input-value .mantine-Select-input {
    background-color: rgba(237, 58, 58, 0.1);
}

.invalid-input-value .Select-value .Select-value-label,
.invalid-input-value .mantine-Select-label,
span.invalid-input-value {
    color: rgb(237, 58, 58) !important;
}

.not-clickable-button {
    pointer-events: none;
}

#title_analyze_uniques .mantine-Button-inner {
    color: var(--mdb-dark);
}

.overflow-wrap-anywhere {
    overflow-wrap: anywhere;
}

.no-cursor-change input:disabled,
.no-cursor-change input:disabled:hover {
    cursor: unset !important;
    background: transparent !important;
}

.home-panel {
    height: calc(100vh - 60px);
}

.mantine-Spoiler-control {
    color: var(--mdb-dark);
}

.explore-page-date-picker .mantine-DateRangePicker-dropdown,
.explore-page-date-picker .mantine-DatePicker-dropdown {
    left: -1px !important;
    width: calc(100% + 2px) !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-radius: 0 0 6px 6px;
}

/* date picker calendar should fit into the dropdown menu */
.datetime-picker-dropdown {
    width: 582px !important;
}

/* width: fit-content prevents the inputs to scale */
.datetime-picker-dropdown .mantine-1avyp1d {
    width: unset !important;
}

.mantine-Card-root {
    overflow: visible;
}

#unique_field_choice {
    font-size: 14px;
}

#unique_field_choice .Select-control {
    border: 1px solid #ccc;
    border-radius: 6px;
}

.rtl-text {
    direction: rtl;
    text-align: left;
}

#graph_top_container .run-button-overlay,
#graph_top_container .cancel-button-overlay {
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    top: 0px;
    position: absolute;
}

/* these are only to avoid annotation glitches */
.shape-group:has(path[style*='stroke-dasharray: 9px']) {
    display: none;
}

.annotation {
    display: none;
}

/* dmc chip in menu glitch fix */
.mantine-Menu-item .mantine-Chip-label {
    height: 46px;
    line-height: 46px;
    min-width: 150px;
}

.mantine-Menu-item:has(.mantine-Chip-label) {
    padding: 0px 10px;
}

/* Circle indicator */
.Select-menu-outer .VirtualizedSelectOption:has(.highlighted-dropdown-option)::after {
    content: ''; /* Placeholder for the circle */
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%; /* Makes it a circle */
    position: absolute;
    right: 10px; /* Position the circle on the right side of the option */
}

.tooltip-info-text::before {
    content: ''; /* Placeholder for the circle */
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 10px;
    border-radius: 50%; /* Makes it a circle */
    margin-bottom: -2px;
}

/* Adjust circle color based on opacity class */
.Select-menu-outer .VirtualizedSelectOption:has(.highlighted-opacity-1)::after,
.tooltip-info-text.highlighted-opacity-1::before {
    background-color: rgba(39, 128, 227, 1);
}

.Select-menu-outer .VirtualizedSelectOption:has(.highlighted-opacity-2)::after,
.tooltip-info-text.highlighted-opacity-2::before {
    background-color: rgba(39, 128, 227, 0.5);
}

.Select-menu-outer .VirtualizedSelectOption:has(.highlighted-opacity-3)::after,
.tooltip-info-text.highlighted-opacity-3::before {
    background-color: rgba(39, 128, 227, 0.2);
}

#share_insight_modal .mantine-Prism-copy {
    background-color: white;
    top: -28px;
}
#share_insight_modal .mantine-Tooltip-tooltip {
    padding: 3px;
}
.black-placeholder input::placeholder {
    color: black !important;
    opacity: 1 !important;
}

.other-groups-checkbox {
    width: 10px;
    margin-top: -2px;
}

.js-plotly-plot .js-line {
    pointer-events: stroke;
}

.js-plotly-plot .scatterlayer:has(.js-line:hover) .js-line,
.js-plotly-plot .pielayer:has(.surface:hover) .surface,
.js-plotly-plot .scatterlayer:has(.js-line:hover) .points,
.js-plotly-plot .scatterlayer:has(.js-line:hover) .fills {
    opacity: 0.3 !important;
    transition: 0.3s;
}

.js-plotly-plot .scatterlayer .scatter .js-line:hover,
.js-plotly-plot .pielayer .trace .surface:hover {
    stroke-width: 2.3px !important;
    opacity: 1 !important;
    transition: 0.3s;
}
