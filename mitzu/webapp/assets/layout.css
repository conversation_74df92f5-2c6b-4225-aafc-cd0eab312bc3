.mitzu-left-panel {
    background: linear-gradient(to right, #fb4b6e, #f57480);
}

@media (max-width: 575px) {
    .sm-bg-melon {
        background: linear-gradient(to right, #fb4b6e, #f57480);
    }

    .mobile-padding {
        padding: 10px 0 10px 0;
    }
}

@media (min-width: 576px) {
    .card-width {
        width: 370px;
    }

    .intro-card-opacity {
        opacity: 0.9;
        cursor: pointer;
        transition: opacity 0.3s;
    }

    .intro-card-opacity:hover {
        opacity: 1;
    }
}

@media (min-width: 768px) and (min-height: 576px) {
    .skip-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}

@media (min-width: 1083px) {
    .card-size-100 {
        height: 100%;
    }
}

.mantine-Stepper-stepIcon {
    border-radius: 6px;
}

.mantine-Stepper-stepIcon[data-progress] {
    border-color: black;
}

.mantine-Stepper-stepIcon[data-completed],
.mantine-Stepper-verticalSeparatorActive {
    background-color: black;
    border-color: black;
}

.mantine-Radio-label img {
    user-drag: none;
    -webkit-user-drag: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

#show_sql_modal .mantine-Tooltip-tooltip {
    padding: 3px;
}
