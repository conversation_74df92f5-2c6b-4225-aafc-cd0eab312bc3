(function(h, o, t, j, a, r) {
    if (document.cookie.indexOf("mitzu_employee=") >= 0) {
        console.log("Mitzu employee cookie is set, skipping loading hotjar lib");
        return;
    }
    h.hj =
        h.hj ||
        function() {
            (h.hj.q = h.hj.q || []).push(arguments);
        };

    if (window.location.href.startsWith("https://pub.mitzu.io/")) {
        h._hjSettings = {
            hjid: 3826619,
            hjsv: 6
        };
    } else {
        h._hjSettings = {
            hjid: 3663913,
            hjsv: 6
        };
    }
    a = o.getElementsByTagName("head")[0];
    r = o.createElement("script");
    r.async = 1;
    r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
    a.appendChild(r);
})(window, document, "https://static.hotjar.com/c/hotjar-", ".js?sv=");