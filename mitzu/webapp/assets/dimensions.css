.cell-table tr:hover {
    background-color: rgba(34, 139, 230, 0.1) !important;
    cursor: pointer !important;
}

.cell-table tr:hover td {
    background-color: transparent !important;
    cursor: pointer !important;
}

/* Dimensions page data */

#dim_data_container .run-button-overlay,
#dim_data_container .cancel-button-overlay {
    align-items: center;
    justify-content: center;
    width: 100%;
    top: 20px;
    position: absolute;
}

#dim_table_container .dash-spreadsheet-inner {
    overflow-x: auto;
}

#edt_data_table th {
    text-wrap: nowrap !important;
    text-align: left !important;
    overflow-x: ellipsis !important;
}
