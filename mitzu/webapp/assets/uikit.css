.uikit-checkbox-black .form-check-input[type='checkbox'] {
    border: solid 1px rgb(206, 212, 218);
}

.uikit-checkbox-black .form-check-input[type='checkbox']:checked {
    background-color: black;
    border: solid 2px black;
}

.uikit-tooltip {
    max-width: 300px;
    min-width: 250px;
}

.uikit-row {
    align-items: flex-start;
}

.uikit-row-label {
    min-width: 300px;
}

.uikit-row-content {
    flex-grow: 1;
    min-width: 180px;
}

.uikit-divider-line {
    margin: 0px;
}

.uikit-card-with-help {
    max-width: 80vw;
}

.uikit-card-with-help-card {
    min-width: 700px;
    flex-grow: 1;
}

.uikit-card-with-help-help {
    max-width: 200px;
}

.uikit-card-with-help-link {
    padding-left: 0px;
}

.uikit-input {
    max-width: 400px;
}

.uikit-file-upload {
    width: 100%;
    height: 60px;
    line-height: 60px;
    border-width: 1px;
    border-style: dashed;
    border-radius: 6px;
    text-align: center;
    margin: 10px 0px;
}

.uikit-video-card-play-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.uikit-modal-header-no-content {
    border: 0;
}

.uikit-modal-footer-no-content {
    border: 0;
}

.uikit-modal .uikit-row-label {
    min-width: 150px;
}

.uikit-modal .uikit-row-content {
    max-width: 220px;
}

.uikit-modal.no-content .modal-header,
.uikit-modal.no-content .modal-footer {
    border: 0px;
}

.uikit-modal.no-content .modal-body {
    display: none;
}

/* the padding is not transparent */
.uikit-modal .mantine-Tooltip-tooltip {
    padding: 0px;
}

.dmc-dcc-dropdown .Select-multi-value-wrapper,
.dmc-dcc-dropdown .Select-control,
.dmc-dcc-dropdown .Select-multi-value-wrapper span {
    height: 28px;
    line-height: 28px;
}

.dmc-dcc-dropdown .Select {
    height: 30px;
}

.uikit-flex-zindex-fix {
    z-index: 50000;
}

.uikit-image-preview-component {
    width: 100%;
    height: 100%;
    border: 1px solid #ddd;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
}

.auxialry-button {
    color: #495057 !important;
}

.auxialry-button:disabled {
    color: #adb5bd !important;
}

.auxialry-danger-button {
    color: #dc3545 !important;
}

.auxialry-danger-button:disabled {
    color: #f5c6cb !important;
}

.icon-button[disabled] {
    pointer-events: none;
    opacity: 0.5;
}

/* property filter row */

.uikit-property-filter-container {
    border: 1px var(--mdb-border-style) var(--mdb-border-color) !important;
    border-radius: var(--mdb-border-radius);
    padding: 0.25rem;
}

.uikit-property-filter-row .uikit-property-filter-row-property {
    /* Making dcc dropdowns look like dmc selects */
    font-size: 12px;
    font-family: var(--def-font);
}

.uikit-property-filter-row .uikit-property-filter-row-property .Select-value-label {
    font-size: 12px;
    line-height: 30px;
}

.uikit-property-filter-row .uikit-property-filter-row-property .Select-control,
.uikit-property-filter-row .uikit-property-filter-row-property .Select-input {
    height: 30px;
}

.uikit-property-filter-row .uikit-property-filter-row-property .Select-placeholder {
    color: var(--mdb-dark);
}

.uikit-property-filter-row .uikit-property-filter-row-property .Select-value {
    padding-right: 15px !important;
}

.uikit-property-filter-row .mantine-MultiSelect-value {
    background-color: rgba(39, 128, 227, 0.2);
}

.uikit-property-filter-row .mantine-Input-rightSection,
.uikit-property-filter-row .Select-arrow-zone {
    /* Hides right carret for dmc.Select and Multiselect */
    display: None;
}

.uikit-property-filter-row .mantine-Select-input,
.uikit-property-filter-row .mantine-MultiSelect-input,
.uikit-property-filter-row .Select-control {
    /* Removes borders from dmc.Select */
    border: 0px;
    padding-left: 4px;
    padding-right: 4px;
    cursor: default;
    background: transparent;
}

.uikit-property-filter-row .mantine-Select-input:hover,
.uikit-property-filter-row .mantine-MultiSelect-input:hover,
.uikit-property-filter-row .Select-control:hover {
    /* Blue hover color for dmc.Select and multiselect */
    background-color: #5b7bf710;
}

.uikit-property-filter-row .mantine-InputWrapper-root {
    /* Making sure dcc and dmc dropdowns work together correctly */
    z-index: auto;
}

.uikit-property-filter-row input.mantine-MultiSelect-searchInputEmpty,
.uikit-property-filter-row input.mantine-MultiSelect-searchInput {
    color: #808080;
}

.uikit-property-filter-row input.mantine-MultiSelect-searchInputEmpty::placeholder,
.uikit-property-filter-row input.mantine-MultiSelect-searchInput::placeholder {
    color: var(--mdb-secondary);
}


@media (max-width: 768px) {
    .mantine-MultiSelect-value {
      max-width: 120px;
      
    }
    .uikit-property-filter-row-property {
      min-width: 120px;
    }
  }
  
@media (min-width: 769px) {
    .mantine-MultiSelect-value {
      max-width: 200px;
    }
    .uikit-property-filter-row-property {
      min-width: 300px;
    }
  }


.uikit-property-filter-row-operator{
    min-width: 80px;
}

