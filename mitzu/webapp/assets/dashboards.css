.gu-mirror {
    position: fixed !important;
    margin: 0 !important;
    z-index: 9999 !important;
    opacity: 0.8;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=80)';
    filter: alpha(opacity=80);
}
.gu-hide {
    display: none !important;
}
.gu-unselectable {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}
.gu-transit {
    opacity: 0.2;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)';
    filter: alpha(opacity=20);
}

.drag-handler {
    cursor: move;
}

.drag-handler:hover {
    background-color: #ececec;
}

.loaded-dashboard .skeleton {
    visibility: hidden !important;
}

.loading-dashboard .graph {
    opacity: 0.5;
}

.loading-dashboard .skeleton,
.loaded-dashboard .graph {
    visibility: visible;
}

.loading-dashboard .dashboard-card-spinner {
    display: inline-block;
}

.loaded-dashboard .dashboard-card-spinner {
    display: none;
}

.saved-metric-link:hover {
    text-decoration: underline;
}

.card-description {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.cohort-upload-container {
    width: 100%;
    height: 60px;
    line-height: 60px;
    border-width: 1px;
    border-style: dashed;
    border-radius: 6px;
    text-align: center;
}

.dashboard-chart-highlight div {
    background: rgb(224, 237, 255);
}

#dashboard_name_input:invalid {
    border-radius: 6px;
}

#dashboard_refresh,
#dashboard_refresh_cancel {
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
}

#share_modal .mantine-Prism-copy,
#share_insight_modal .mantine-Prism-copy {
    background-color: white;
    top: -28px;
}

.graph:has(.text-box) {
    overflow: hidden;
}

.text-box textarea {
    border: 0;
}

.dashboard-divider input {
    border: 0;
    text-align: center;
}

.read-only {
    pointer-events: none;
    background-color: inherit;
    color: inherit;
    cursor: default;
}

div[id='metric_card_stack']:has(input[type='text'])
    *:not(#metric_card_dropdown_menu, #metric_card_dropdown_menu *) {
    background-color: #f7f7f7;
}

#share_dashboard_modal .mantine-Tooltip-tooltip {
    padding: 3px;
}
#share_dashboard_modal .mantine-Prism-copy {
    background-color: white;
    top: -28px;
}

#dashboard_name_input{
    height: 28px;
}

#dashboard_desc_input{
    height: 28px;
}