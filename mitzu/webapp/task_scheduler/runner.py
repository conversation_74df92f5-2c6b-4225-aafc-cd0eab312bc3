import mitzu.adapters.generic_adapter as GA
import mitzu.webapp.storage.session_manager as SM
import mitzu.webapp.task_scheduler.model as TM
from mitzu.cache import Mitzu<PERSON>ache
from mitzu.project_discovery import DiscoveryResult
from mitzu.webapp.service.catalog_service import (
    CatalogService,
)
from mitzu.webapp.service.events_service import (
    REPORT_CALLBACK,
    EventsService,
)
from mitzu.webapp.service.tracking_service import (
    TrackingService,
)
from mitzu.webapp.storage.project_discovery_storage import (
    ProjectDiscoveryStorage,
)


class TaskRunner:
    def __init__(self, session_manager: SM.SessionManager, cache: MitzuCache) -> None:
        self._session_manager = session_manager
        self._cache = cache

    def run_edt_indexing_task(
        self,
        task: TM.EDTIndexingTask,
        report_pgoress_callback: REPORT_CALLBACK,
    ) -> DiscoveryResult:
        project_storage = self._get_discovery_storage_for_task(task.source)
        catalog_service = CatalogService(project_storage, self._cache)
        tracking_service = TrackingService(task.source.tracking_context)
        events_service = EventsService(
            project_storage, self._cache, tracking_service, task.source.user_role
        )

        project = project_storage.get_project()
        adapter_context = GA.AdapterContext(
            query_id=task.task_id,
            user_id=task.source.user_id,
            org_name=task.source.org_name,
            email=task.source.user_email,
            project_name=project.project_name,
            url=task.source.url,
            is_debug_enabled=False,
            query_type=GA.QueryType.INDEXING,
        )
        edts = [
            edt
            for edt in project.event_data_tables
            if edt.id in task.event_data_table_ids
        ]

        return events_service.discover_event_data_tables(
            project=project,
            event_data_tables=edts,
            catalog_service=catalog_service,
            report_progress_callback=report_pgoress_callback,
            remove_orphan_fields=task.remove_orphan_fields,
            remove_orphan_events=task.remove_orphan_events,
            adapter_context=adapter_context,
            indexing_context=project.get_default_indexing_context(),
        )

    def _get_discovery_storage_for_task(
        self, task_source: TM.TaskSource
    ) -> ProjectDiscoveryStorage:
        return ProjectDiscoveryStorage(
            task_source.org_id,
            task_source.project_id,
            self._session_manager,
        )
