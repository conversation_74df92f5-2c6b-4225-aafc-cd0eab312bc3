import mitzu.adapters.generic_adapter as GA
import mitzu.webapp.storage.session_manager as SM
import mitzu.webapp.task_scheduler.model as TM
from mitzu.cache import Mitzu<PERSON>ache
from mitzu.logger import LOGGER
from mitzu.project_discovery import DiscoveryResult
from mitzu.webapp.service.catalog_service import (
    CatalogService,
)
from mitzu.webapp.service.events_service import (
    REPORT_CALLBACK,
    EventsService,
)
from mitzu.webapp.service.tracking_service import (
    TrackingService,
)
from mitzu.webapp.storage.project_discovery_storage import (
    ProjectDiscoveryStorage,
)
from mitzu.webapp.storage.task_storage import TaskStorage


class TaskRunner:
    def __init__(self, session_manager: SM.SessionManager, cache: MitzuCache) -> None:
        self._session_manager = session_manager
        self._cache = cache

    def run_task(
        self, task_id: str, report_pgoress_callback: REPORT_CALLBACK
    ) -> DiscoveryResult:
        task_storage = TaskStorage(task_id, self._session_manager)
        try:
            task = task_storage.get_task()
            if isinstance(task, TM.EDTIndexingTask):
                result = self._run_edt_indexing_task(task, report_pgoress_callback)
                task_storage.mark_task_finished(
                    ",".join(
                        [str(exc) for exc in result.errors.values()]
                    )  # FIXME: single edt per task
                    if len(result.errors) > 0
                    else None
                )
                return result
            raise ValueError(f"Unknown task type: {task}")
        except Exception as exc:
            LOGGER.opt(exception=exc).bind(task_id=task_id).error(
                "failed to process task"
            )
            task_storage.mark_task_finished(str(exc))
            raise exc

    def _run_edt_indexing_task(
        self,
        task: TM.EDTIndexingTask,
        report_pgoress_callback: REPORT_CALLBACK,
    ) -> DiscoveryResult:
        project_storage = self._get_discovery_storage_for_task(task.source)
        catalog_service = CatalogService(project_storage, self._cache)
        tracking_service = TrackingService(task.source.tracking_context)
        events_service = EventsService(
            project_storage, self._cache, tracking_service, task.source.user_role
        )

        project = project_storage.get_project()
        adapter_context = GA.AdapterContext(
            query_id=task.task_id,
            user_id=task.source.user_id,
            org_name=task.source.org_name,
            email=task.source.user_email,
            project_name=project.project_name,
            url=task.source.url,
            is_debug_enabled=False,
            query_type=GA.QueryType.INDEXING,
        )
        edts = [
            edt
            for edt in project.event_data_tables
            if edt.id == task.event_data_table_id
        ]
        if len(edts) == 1:
            edt = edts[0]
        else:
            raise ValueError(
                f"Event data table not found with id: {task.event_data_table_id}"
            )

        return events_service.discover_event_data_table(
            project=project,
            event_data_table=edt,
            catalog_service=catalog_service,
            report_progress_callback=report_pgoress_callback,
            remove_orphan_fields=task.remove_orphan_fields,
            remove_orphan_events=task.remove_orphan_events,
            adapter_context=adapter_context,
            indexing_context=project.get_default_indexing_context(),
        )

    def _get_discovery_storage_for_task(
        self, task_source: TM.TaskSource
    ) -> ProjectDiscoveryStorage:
        return ProjectDiscoveryStorage(
            task_source.org_id,
            task_source.project_id,
            self._session_manager,
        )
