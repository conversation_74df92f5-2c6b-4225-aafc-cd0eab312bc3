from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional

import mitzu.webapp.model as WM
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)


class TaskState(Enum):
    QUEUED = "queued"
    RUNNING = "running"
    FINISHED = "finished"


@dataclass(frozen=True)
class TaskSource:
    org_id: str
    project_id: str

    # needed to create the adapter context
    user_id: str
    user_email: str
    user_role: WM.Role
    org_name: str
    url: str

    tracking_context: TrackingContext


@dataclass(frozen=True)
class Task:
    task_id: str
    source: TaskSource
    state: TaskState
    created_at: datetime
    error: Optional[str]


@dataclass(frozen=True)
class EDTIndexingTask(Task):
    event_data_table_id: str
    event_data_table_hash: str
    remove_orphan_fields: bool
    remove_orphan_events: bool
