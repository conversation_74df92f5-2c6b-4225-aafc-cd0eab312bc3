from dataclasses import dataclass
from typing import List

import mitzu.webapp.model as WM
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
)


@dataclass(frozen=True)
class TaskSource:
    org_id: str
    project_id: str

    # needed to create the adapter context
    user_id: str
    user_email: str
    user_role: WM.Role
    org_name: str
    url: str

    tracking_context: TrackingContext


@dataclass(frozen=True)
class Task:
    task_id: str
    source: TaskSource


@dataclass(frozen=True)
class EDTIndexingTask(Task):
    event_data_table_ids: List[str]
    remove_orphan_fields: bool
    remove_orphan_events: bool
