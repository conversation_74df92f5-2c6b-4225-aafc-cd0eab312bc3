<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    h1, h2 {
      page-break-after: avoid;
      break-after: avoid;
      margin-bottom: 0.5em;
    }
    body {
      font-family: sans-serif;
    }
    .dashboard-row {
      display: block;
    }
    .dashboard-card {
      width: 100%;
      max-width: 800px;
      margin: 10px auto;
      padding: 12px;
      border: 1px solid #ccc;
      border-radius: 6px;
      break-inside: avoid;
    }

    .dashboard-card h3 {
      margin-top: 0;
      font-size: 1.2em;
      font-weight: normal;
    }
    .dashboard-divider {
      text-align: center;
      background-color: #f7f7f7;
      padding: 16px;
      border-radius: 6px;
    }
    .divider-text {
      font-size: 1.2em;
      font-weight: normal;
      margin: 0;
    }
    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h2>{{ dashboard.name }}</h2>
  <div class="dashboard-row">
    {% for card in cards %}
    {% if card.card_type == "divider" %}
      <div class="dashboard-card dashboard-divider">
        <p class="divider-text">{{ card.text }}</p>
      </div>
    {% elif card.card_type == "text" %}
      <div class="dashboard-card">
        <p>{{ card.text }}</p>
      </div>
    {% elif card.card_type == "chart" %}
      <div class="dashboard-card">
          <h3>{{ card.name }}</h3>
          <img src="data:image/png;base64,{{ card.chart_b64 }}" width="100%">
          <p>{{ card.description }}</p>
      </div>
    {% endif %}
  {% endfor %}

  </div>
</body>
</html>
