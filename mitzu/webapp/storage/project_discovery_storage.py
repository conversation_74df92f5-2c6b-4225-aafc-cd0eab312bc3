import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import sqlalchemy as SA

import mitzu.helper as H
import mitzu.model as M
import mitzu.webapp.storage.storage_model as SM
from mitzu.webapp.storage.project_storage import (
    ProjectScopedStorage,
)
from mitzu.webapp.storage.session_manager import (
    SessionManager,
)


class ProjectDiscoveryStorage(ProjectScopedStorage):
    def __init__(
        self,
        org_id: str,
        project_id: str,
        session_manager: SessionManager,
    ) -> None:
        super().__init__(org_id, project_id, session_manager, read_only=False)

    def mark_event_property_as_discovered(self, event_property_id: str) -> None:
        with self._new_db_session() as session:
            event_property = (
                session.query(SM.EventPropertyStorageRecord)
                .filter_by(event_property_id=event_property_id)
                .join(
                    SM.EventDefStorageRecord,
                    SM.EventDefStorageRecord.event_id
                    == SM.EventPropertyStorageRecord.event_id,
                )
                .join(
                    SM.EventDataTableStorageRecord,
                    SM.EventDataTableStorageRecord.event_data_table_id
                    == SM.EventDefStorageRecord.event_data_table_id,
                )
                .join(
                    SM.ProjectStorageRecord,
                    SM.ProjectStorageRecord.project_id
                    == SM.EventDataTableStorageRecord.project_id,
                )
                .filter_by(org_id=self._org_id)
                .filter(SM.ProjectStorageRecord.project_id == self._project_id)
                .first()
            )
            if event_property is None:
                raise ValueError(
                    f"Event property not found with id={event_property_id}"
                )
            event_property.last_discovered = datetime.now()
            session.commit()

    def set_event_field_definition(
        self, event_field_def: M.EventFieldDef, event_def: M.EventDef
    ) -> None:
        with self._new_db_session() as session:
            rec: Optional[SM.EventPropertyStorageRecord] = (
                session.query(SM.EventPropertyStorageRecord)
                .filter(
                    (
                        SM.EventPropertyStorageRecord.field_path
                        == str(event_field_def.field_path.field_name)
                    )
                    & (
                        SM.EventPropertyStorageRecord.event_id
                        == str(event_def.get_id())
                    )
                )
                .first()
            )
            if rec is not None:
                rec.update(event_field_def)
            else:
                rec = SM.EventPropertyStorageRecord.from_model_instance(
                    event_def.get_id(), event_field_def
                )
                session.add(rec)
            session.commit()

    def mark_dimension_property_as_discovered(self, dimension_property_id: str) -> None:
        with self._new_db_session() as session:
            dimension_property: Optional[SM.DimensionPropertyStorageRecord] = (
                session.query(SM.DimensionPropertyStorageRecord)
                .filter_by(dimension_property_id=dimension_property_id)
                .join(
                    SM.DimensionDataTableStorageRecord,
                    SM.DimensionDataTableStorageRecord.dimension_data_table_id
                    == SM.DimensionPropertyStorageRecord.dimension_data_table_id,
                )
                .join(
                    SM.ProjectStorageRecord,
                    SM.ProjectStorageRecord.project_id
                    == SM.DimensionDataTableStorageRecord.project_id,
                )
                .filter_by(org_id=self._org_id)
                .filter(SM.ProjectStorageRecord.project_id == self._project_id)
                .first()
            )
            if dimension_property is None:
                raise ValueError(
                    f"Dimension property not found with id={dimension_property_id}"
                )
            dimension_property.last_discovered = datetime.now()
            session.commit()

    def set_dimension_field_definition(
        self, dimension_field_def: M.DimensionFieldDef
    ) -> None:
        with self._new_db_session() as session:
            rec: Optional[SM.DimensionPropertyStorageRecord] = (
                session.query(SM.DimensionPropertyStorageRecord)
                .filter(
                    (
                        SM.DimensionPropertyStorageRecord.dimension_property_id
                        == dimension_field_def.get_id()
                    )
                )
                .first()
            )
            if rec is not None:
                rec.update(dimension_field_def)
            else:
                rec = SM.DimensionPropertyStorageRecord.from_model_instance(
                    dimension_field_def
                )
                session.add(rec)
            session.commit()

    def update_event_data_table_original_field_types(
        self,
        event_data_table_id: str,
        event_time_field: M.Field,
        event_time_field_type: str,
        date_partition_field: Optional[M.Field],
        date_partition_field_type: Optional[str],
        event_name_field: Optional[M.Field],
        foreign_keys: Dict[M.Entity, M.Field],
    ) -> M.EventDataTable:
        with self._new_db_session() as session:
            event_data_table: Optional[SM.EventDataTableStorageRecord] = (
                self._query_within_project(session, SM.EventDataTableStorageRecord)
                .filter_by(event_data_table_id=event_data_table_id)
                .first()
            )
            if event_data_table is None:
                raise ValueError(
                    f"Event data table not found with id={event_data_table_id}"
                )
            event_data_table.event_time_field = json.dumps(
                SM.serialize_field(event_time_field)
            )
            event_data_table.event_time_field_type = event_time_field_type
            event_data_table.date_partition_field = (
                json.dumps(SM.serialize_field(date_partition_field))
                if date_partition_field
                else None
            )
            event_data_table.date_partition_field_type = date_partition_field_type
            event_data_table.user_id_field = json.dumps(
                SM.serialize_field(foreign_keys[M.USER_ENTITY])
            )
            event_data_table.event_name_field = (
                json.dumps(SM.serialize_field(event_name_field))
                if event_name_field is not None
                else None
            )
            event_data_table.foreign_keys = json.dumps(
                {
                    entity.name: SM.serialize_field(field)
                    for entity, field in foreign_keys.items()
                }
            )
            session.commit()
            return event_data_table.as_model_instance()

    def bulk_upsert_event_defs(
        self,
        event_defs: List[M.EventDef],
        remove_orphan_fields: bool,
        chunk_size: int = 300,
    ) -> None:
        event_def_chunks = H.divide_chunks(event_defs, chunk_size)
        for chunk in event_def_chunks:
            with self._new_db_session() as session:
                events: Dict[Tuple[str, str], M.EventDef] = {}
                filters = []
                for stored_event in chunk:
                    filters.append(
                        (
                            (
                                SM.EventDefStorageRecord.event_data_table_id
                                == stored_event.event_data_table.get_id()
                            )
                            & (
                                SM.EventDefStorageRecord.event_name
                                == stored_event.event_name
                            )
                        )
                    )

                    events[
                        (
                            stored_event.event_data_table.get_id(),
                            stored_event.event_name,
                        )
                    ] = stored_event

                records = (
                    session.query(SM.EventDefStorageRecord)
                    .filter(SA.or_(*filters))
                    .all()
                )

                # update the already stored events
                for stored_records in records:
                    event_key = (
                        stored_records.event_data_table_id,
                        stored_records.event_name,
                    )
                    event = events[event_key]
                    if event is not None:
                        stored_records.update(event, remove_orphan_fields, session)
                        del events[event_key]

                # the remaining events needs to be added
                for event in events.values():
                    rec = SM.EventDefStorageRecord.from_model_instance(
                        event.event_data_table.get_id(), event
                    )
                    session.add(rec)
                session.commit()

    def bulk_remove_event_defs(
        self, event_defs: List[M.EventDef], chunk_size: int = 300
    ) -> None:
        event_def_chunks = H.divide_chunks(event_defs, chunk_size)
        for chunk in event_def_chunks:
            with self._new_db_session() as session:
                filters = []

                for event_def in chunk:
                    filters.append(
                        (SM.EventDefStorageRecord.event_id == event_def.get_id())
                    )

                records = (
                    session.query(SM.EventDefStorageRecord)
                    .filter(SA.or_(*filters))
                    .all()
                )
                for rec in records:
                    session.delete(rec)
                session.commit()

    def set_indexing_task_for_edt(self, task_id: str, event_data_table_id: str) -> None:
        with self._new_db_session() as session:
            event_data_table = (
                self._query_within_project(session, SM.EventDataTableStorageRecord)
                .filter_by(event_data_table_id=event_data_table_id)
                .first()
            )
            if event_data_table is None:
                raise ValueError(
                    f"Event data table not found with id={event_data_table_id}"
                )

            event_data_table.last_index_task_id = task_id
            session.commit()
