from typing import Optional

import mitzu.webapp.storage.storage_model as SM
import mitzu.webapp.task_scheduler.model as TSM
from mitzu.webapp.storage.session_manager import (
    SessionManager,
    Storage,
)


class TaskNotFoundError(Exception):
    def __init__(self) -> None:
        super().__init__("Task not found")


class TaskStorage(Storage):
    def __init__(
        self,
        task_id: str,
        session_manager: SessionManager,
    ) -> None:
        self._session_manager = session_manager
        self._task_id = task_id

    def get_task(self) -> TSM.Task:
        with self._session_manager._new_db_session() as session:
            record = (
                session.query(SM.TaskStorageRecord)
                .filter_by(task_id=self._task_id)
                .one_or_none()
            )

            if record is None:
                raise TaskNotFoundError()
            return record.as_model_instance()

    def mark_task_finished(self, error: Optional[str]) -> None:
        with self._session_manager._new_db_session() as session:
            record: Optional[SM.TaskStorageRecord] = (
                session.query(SM.TaskStorageRecord)
                .filter_by(task_id=self._task_id)
                .one_or_none()
            )
            if record is not None:
                record.state = TSM.TaskState.FINISHED.value
                record.error = error
                session.commit()
