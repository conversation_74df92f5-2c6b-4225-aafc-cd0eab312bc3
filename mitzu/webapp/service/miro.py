from typing import Dict

import requests

import mitzu.webapp.pages.paths as P
import mitzu.webapp.storage.project_config_storage as PCS
from mitzu import configs
from mitzu.cache import Mitz<PERSON><PERSON>ache
from mitzu.helper import create_unique_id
from mitzu.logger import LOGGER
from mitzu.webapp.integrations.miro import MiroIntegration

REDIRECT_URI = configs.HOME_URL + P.ADD_MIRO_TEAM_API
MIRO_TOKEN_CACHE_EXPIRATION = 6 * 4 * 7 * 60 * 60
MIRO_TOKEN_CACHE_PREFIX = "miro_token_details_"


class MiroService:
    def __init__(self, project_storage: PCS.ProjectConfigStorage) -> None:
        self._storage = project_storage

    @classmethod
    def add_miro_team_url(cls, project_id: str) -> str:
        return (
            f"https://miro.com/oauth/authorize?response_type=code&client_id={configs.MIRO_CLIENT_ID}"
            f"&redirect_uri={REDIRECT_URI}&state={project_id}"
        )

    @classmethod
    def exchange_auth_code_to_token(
        cls, auth_code: str, redirect_uri: str
    ) -> Dict[str, str]:
        payload = {
            "grant_type": "authorization_code",
            "client_id": configs.MIRO_CLIENT_ID,
            "code": auth_code,
            "redirect_uri": redirect_uri,
            "client_secret": configs.MIRO_CLIENT_SECRET,
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        url = "https://api.miro.com/v1/oauth/token"
        resp = requests.post(url, data=payload, headers=headers)

        return resp.json()

    @classmethod
    def get_token_info(cls, access_token: str) -> Dict[str, Dict[str, str]]:
        url = "https://api.miro.com/v1/oauth-token"
        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {access_token}",
        }

        resp = requests.get(url, headers=headers)
        return resp.json()

    def add_team_to_project_by_auth_code(self, auth_code: str) -> None:
        try:
            tokens = self.exchange_auth_code_to_token(auth_code, REDIRECT_URI)
            resp_json = self.get_token_info(tokens["access_token"])

            miro_integration = MiroIntegration(
                self._storage._project_id,
                miro_org_id=resp_json["organization"]["id"],
                miro_org_name=resp_json["organization"]["name"],
                miro_team_id=resp_json["team"]["id"],
                miro_team_name=resp_json["team"]["name"],
            )
            self._storage.upsert_miro_integration(miro_integration)
        except Exception as exc:
            LOGGER.opt(exception=exc).error("failed to add miro team by auth code")

    @classmethod
    def store_miro_token_details(
        cls, cache: MitzuCache, token_details: Dict[str, Dict[str, str]]
    ) -> str:
        key = MIRO_TOKEN_CACHE_PREFIX + create_unique_id()
        cache.put(key, token_details, expire=MIRO_TOKEN_CACHE_EXPIRATION)
        return key
