from typing import List, Optional, Set

import mitzu.model as M
from mitzu.webapp.service.input_generator.base_generator import (
    BaseGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    Dropdown,
    DropdownOption,
)


class DasboardFilterPropertyGenerator(BaseGenerator):
    def create_dropdown(
        self,
        selected: Optional[M.FieldPath],
        placeholder: str,
        event_name_paths: Set[M.EventNamePath],
    ) -> Dropdown:
        is_valid = True
        fields = self.get_property_options_for_dashboard_filter(
            event_name_paths=event_name_paths,
            include=selected,
            search=None,
        )

        all_dim_fields = self.metric_context.get_all_dimension_fields()

        is_valid = all([field.is_valid for field in fields])

        if (
            isinstance(selected, M.DimensionField<PERSON>ath)
            and selected not in all_dim_fields
        ):
            is_valid = False

        return self._create_property_dropdown(
            options=fields,
            placeholder=placeholder,
            selected=str(selected) if selected else None,
            is_visible=True,
            is_valid=is_valid,
            multi=False,
        )

    def get_property_options_for_dashboard_filter(
        self,
        event_name_paths: Set[M.EventNamePath],
        include: Optional[<PERSON><PERSON>Field<PERSON>ath],
        search: Optional[str],
    ) -> List[DropdownOption]:
        event_name_path_list = self._resolve_custom_events_if_any(
            list(event_name_paths)
        )

        options, _ = self._get_property_options_with_search_and_limit(
            event_name_paths=event_name_path_list,
            include=[include] if include else [],
            search=search,
            with_cohort=True,
            with_entities=list(self._get_common_entities(event_name_path_list)),
            with_subsegment_breakdown=False,
            with_array_types=True,
            with_only_common_event_fields=True,
        )
        return options
