from __future__ import annotations

from typing import Any, List, Optional, Set

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.storage.project_storage as PS
from mitzu.webapp.service.input_generator.base_generator import (
    BaseGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    CUSTOM_LOOKUP_PROP_VALUE,
    Dropdown,
    DropdownOption,
)
from mitzu.webapp.service.input_generator.utils import (
    generate_value_input_placeholder,
    get_input_value_options,
    value_to_string,
)


class PropertyValueGenerator(BaseGenerator):
    def __init__(
        self,
        project_id: str,
        metric_context: MC.MetricContext,
        project_storage: PS.ProjectScopedStorage,  # needed for listing the cohorts and later getting the enums
    ):
        super().__init__(metric_context)
        self.project_id = project_id
        self.project_storage = project_storage

    def create_dropdown(
        self, event_filter: Optional[WM.WebappEventFilter], allow_reindex: bool
    ) -> Dropdown:
        options: List[DropdownOption] = []
        if event_filter is None:
            placeholder = "..."
            comp_value: Any = None
            multi = False
            is_visible = False
        elif not self.metric_context.is_field_path_valid(event_filter.left):
            placeholder = "..."
            comp_value = event_filter.right
            multi = False
            options = [
                DropdownOption(
                    label=str(comp_value),
                    value=str(comp_value),
                    search=str(comp_value),
                )
            ]
            is_visible = not event_filter.operator.is_unary_operator()
        else:
            multi = event_filter.operator.supports_multiple_operands()
            value = event_filter.right
            is_visible = not event_filter.operator.is_unary_operator()

            if event_filter.operator in (
                M.Operator.IN_COHORT,
                M.Operator.NOT_IN_COHORT,
            ):
                if not isinstance(event_filter.left, M.CollectionFieldPath):
                    raise ValueError(
                        f"Event filter path should be a collection field path {event_filter.left}"
                    )
                cohorts = [
                    c
                    for c in self.project_storage.list_cohorts()
                    if c.entity == event_filter.left.entity
                ]
                options = [
                    DropdownOption(
                        label=c.name,
                        value=c.id,
                        search=c.name,
                    )
                    for c in cohorts
                ]
                placeholder = (", ".join([str(e) for e in cohorts]))[:40] + "..."
                options.sort(key=lambda v: v.label)
                multi = False
                comp_value = [value.id if value else None]
            else:
                path = event_filter.left
                field_meta = self.metric_context.get_property_meta(path)
                enums: Set[Any] = set()
                if field_meta.last_discovered:
                    if isinstance(field_meta, MC.EventPropertyMeta):
                        enums = self.project_storage.get_event_property_enums(
                            field_meta.event_property_id
                        )
                    elif isinstance(field_meta, MC.DimensionPropertyMeta):
                        enums = self.project_storage.get_dimension_property_enums(
                            field_meta.dimension_property_id
                        )
                    else:
                        raise ValueError(f"Unknown field meta {field_meta}")

                options = [
                    DropdownOption(
                        label=o["label"],
                        value=o["value"],
                        search=o["label"],
                    )
                    for o in get_input_value_options(value, enums, allow_reindex)
                ]
                if not (
                    len(options) == 0
                    or (
                        len(options) == 1
                        and options[0].value == CUSTOM_LOOKUP_PROP_VALUE
                    )
                ):
                    placeholder = generate_value_input_placeholder(
                        [o.label for o in options]
                    )
                elif field_meta.last_discovered is not None:
                    placeholder = "Enter a value ..."
                else:
                    placeholder = "Click to re-index values ..."

                comp_value = value
                if multi:
                    if value is not None and type(value) in (list, tuple):
                        comp_value = [value_to_string(v) for v in value]
                    if value is None:
                        comp_value = []
                else:
                    comp_value = [value_to_string(comp_value) if comp_value else None]

        return Dropdown(
            options=options,
            selected=comp_value,
            placeholder=placeholder,
            is_visible=is_visible,
            is_valid=True,
            multi=multi,
        )
