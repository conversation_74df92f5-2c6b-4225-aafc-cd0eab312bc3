from dataclasses import dataclass
from typing import Optional

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.webapp.pages.explore.constants import (
    OPERATOR_MAPPING,
)
from mitzu.webapp.service.input_generator.model import (
    Dropdown,
    DropdownOption,
)


@dataclass(init=False)
class PropertyOperatorGenerator:
    def __init__(
        self,
        metric_context: MC.MetricContext,
    ):
        self.metric_context = metric_context

    def create_dropdown(
        self,
        filter: Optional[WM.WebappEventFilter],
    ) -> Dropdown:
        is_visible = False
        available_operators = []
        default_operator = M.Operator.ANY_OF
        if filter is not None and self.metric_context.is_field_path_valid(filter.left):
            is_visible = True
            # backward compatibility, filter.left could be anything
            if filter.operator in (M.Operator.IN_COHORT, M.Operator.NOT_IN_COHORT):
                available_operators = [
                    M.Operator.IN_COHORT,
                    M.Operator.NOT_IN_COHORT,
                ]
            else:
                data_type = self.metric_context.get_property_meta(
                    filter.left
                ).property_type
                if data_type == M.DataType.BOOL:
                    available_operators = [
                        M.Operator.ANY_OF,
                        M.Operator.NONE_OF,
                        M.Operator.IS_NOT_NULL,
                        M.Operator.IS_NULL,
                    ]
                elif data_type == M.DataType.NUMBER:
                    available_operators = [
                        M.Operator.ANY_OF,
                        M.Operator.NONE_OF,
                        M.Operator.GT,
                        M.Operator.GT_EQ,
                        M.Operator.LT,
                        M.Operator.LT_EQ,
                        M.Operator.IS_NOT_NULL,
                        M.Operator.IS_NULL,
                    ]
                elif data_type == M.DataType.ARRAY:
                    available_operators = [
                        M.Operator.ARRAY_CONTAINS,
                        M.Operator.ARRAY_NOT_CONTAINS,
                        M.Operator.ARRAY_LIKE,
                        M.Operator.ARRAY_NOT_LIKE,
                        M.Operator.IS_NOT_NULL,
                        M.Operator.IS_NULL,
                    ]
                    default_operator = M.Operator.ARRAY_CONTAINS
                else:
                    available_operators = [
                        k
                        for k in OPERATOR_MAPPING.keys()
                        if k
                        not in [
                            M.Operator.IN_COHORT,
                            M.Operator.NOT_IN_COHORT,
                            M.Operator.ARRAY_CONTAINS,
                            M.Operator.ARRAY_NOT_CONTAINS,
                            M.Operator.ARRAY_LIKE,
                            M.Operator.ARRAY_NOT_LIKE,
                        ]
                    ]
        elif filter is not None:
            is_visible = True
            available_operators = [filter.operator]

        return Dropdown(
            options=[
                DropdownOption(
                    label=OPERATOR_MAPPING[x],
                    value=x.value,
                    search=OPERATOR_MAPPING[x],
                )
                for x in available_operators
            ],
            selected=(
                default_operator.value
                if filter is None or filter.operator not in available_operators
                else filter.operator.value
            ),
            is_valid=True,
            is_visible=is_visible,
            placeholder="",
            multi=False,
        )
