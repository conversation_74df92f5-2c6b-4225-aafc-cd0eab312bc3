from __future__ import annotations

from typing import Dict, List, Optional, Set, Tuple, Union

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.storage.project_storage as PS
from mitzu.webapp.pages.explore.constants import (
    NUMBER_OF_PROPERTIES_TO_LIST,
)
from mitzu.webapp.service.input_generator.base_generator import (
    BaseGenerator,
)
from mitzu.webapp.service.input_generator.dashboard_filter_property_generator import (
    DasboardFilterPropertyGenerator,
)
from mitzu.webapp.service.input_generator.event_generator import (
    EventGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    Dropdown,
    DropdownOption,
    DropdownOptionIcon,
    DropdownOptionIconType,
)
from mitzu.webapp.service.input_generator.property_operator_generator import (
    PropertyOperatorGenerator,
)
from mitzu.webapp.service.input_generator.property_value_generator import (
    PropertyValueGenerator,
)


class InputGenerator(BaseGenerator):
    def __init__(
        self,
        project_id: str,
        metric_context: MC.MetricContext,
        project_storage: PS.ProjectScopedStorage,  # needed for listing the cohorts and later getting the enums
    ):
        super().__init__(metric_context)
        self.project_id = project_id
        self.project_storage = project_storage

    def create_event_dropdown(
        self,
        selected: Optional[M.EventNamePath],
        placeholder: Optional[str],
        is_visible: bool,
        analyze_by_field: Union[M.EventFieldPath, M.Entity],
        relevance_scores: Dict[str, WM.UserEventData],
    ) -> Dropdown:
        return EventGenerator(
            self.metric_context, relevance_scores
        ).create_event_dropdown(selected, placeholder, is_visible, analyze_by_field)

    def get_event_dropdown_options(
        self, relevance_scores: Dict[str, WM.UserEventData]
    ) -> List[DropdownOption]:
        return EventGenerator(
            self.metric_context, relevance_scores
        ).get_event_dropdown_options()

    def create_property_dropdown_for_segment_filter(
        self,
        event_name_path: Optional[M.EventNamePath],
        placeholder: str,
        selected: Optional[M.FieldPath],
        with_cohort: bool,
        with_dimensions: bool,
    ) -> Dropdown:
        options = []
        is_valid = True
        if (
            event_name_path is not None
            and event_name_path in self.metric_context.get_all_event_names()
        ):
            options = self.get_property_options_for_event(
                event_name_path,
                include=[selected] if selected is not None else [],
                search=None,
                with_cohort=with_cohort,
                with_dimensions=with_dimensions,
            )

            if selected is not None and not isinstance(selected, M.CollectionFieldPath):
                is_selected_value_present = False
                for option in options:
                    if (
                        option.value is None or option.value == ""
                    ):  # eg. Showing 5 out of 6 properties ...
                        continue

                    field_path = M.FieldPath.parse(option.value)
                    if field_path == selected:
                        is_selected_value_present = True
                        break

                if not is_selected_value_present:
                    options.append(
                        self._create_missing_field_dropdown_option(
                            self.metric_context.get_default_display_name(selected),
                            str(selected),
                            is_property=True,
                        )
                    )
                    is_valid = False

                if isinstance(selected, M.DimensionFieldPath):
                    dim_meta = self.metric_context.get_dimension_meta(selected)
                    table_meta = self.metric_context.get_table_meta(event_name_path)

                    is_valid = dim_meta.entity in table_meta.entities

        elif selected is not None:
            options = [
                self._create_missing_field_dropdown_option(
                    self.metric_context.get_default_display_name(selected),
                    str(selected),
                    is_property=True,
                )
            ]
            is_valid = False
        return self._create_property_dropdown(
            options=options,
            placeholder=placeholder,
            selected=str(selected) if selected is not None else None,
            is_visible=True,
            is_valid=is_valid,
            multi=False,
        )

    def create_property_dropdown_for_dimension_search_filter(
        self,
        entity: M.Entity,
        placeholder: str,
        selected: Optional[M.FieldPath],
        search: Optional[str],
    ) -> Dropdown:
        is_valid = True
        options = self._get_property_options_with_search_and_limit(
            event_name_paths=[],
            include=[selected] if selected is not None else [],
            search=search,
            with_cohort=False,
            with_entities=[entity],
            with_subsegment_breakdown=False,
            with_array_types=False,
            with_only_common_event_fields=False,
        )[0]

        if selected is not None and not isinstance(selected, M.CollectionFieldPath):
            is_selected_value_present = False
            for option in options:
                if (
                    option.value is None or option.value == ""
                ):  # eg. Showing 5 out of 6 properties ...
                    continue

                if option.value == search:
                    continue  # it cannot be selected

                field_path = M.FieldPath.parse(option.value)
                if field_path == selected:
                    is_selected_value_present = True
                    break

            if not is_selected_value_present:
                options.append(
                    self._create_missing_field_dropdown_option(
                        self.metric_context.get_default_display_name(selected),
                        str(selected),
                        is_property=True,
                    )
                )
                is_valid = False

        elif selected is not None:
            options = [
                self._create_missing_field_dropdown_option(
                    self.metric_context.get_default_display_name(selected),
                    str(selected),
                    is_property=True,
                )
            ]
            is_valid = False

        return self._create_property_dropdown(
            options=options,
            placeholder=placeholder,
            selected=str(selected) if selected is not None else None,
            is_visible=True,
            is_valid=is_valid,
            multi=False,
        )

    def get_property_options_for_segment_break_down(
        self,
        event_name_paths: Set[M.EventNamePath],
        include: List[M.FieldPath],
        search: Optional[str],
        with_subsegment_breakdown: bool,
    ) -> Tuple[List[DropdownOption], List[str]]:
        events = self._resolve_custom_events_if_any(list(event_name_paths))
        return self._get_property_options_with_search_and_limit(
            events,
            include=include,
            search=search,
            with_cohort=False,
            with_entities=self._get_available_entities(events),
            with_subsegment_breakdown=with_subsegment_breakdown,
            with_array_types=False,
            with_only_common_event_fields=False,
        )

    def get_property_options_for_list_events_modal(
        self,
        event_name_paths: List[M.EventNamePath],
        include: List[M.FieldPath],
        search: Optional[str],
    ) -> List[DropdownOption]:
        return self._get_property_options_with_search_and_limit(
            event_name_paths,
            include=include,
            search=search,
            with_cohort=False,
            with_entities=self._get_available_entities(event_name_paths),
            with_subsegment_breakdown=False,
            with_array_types=False,
            with_only_common_event_fields=False,
        )[0]

    def get_property_options_for_dimensions_modal(
        self,
        entity: M.Entity,
        include: List[M.FieldPath],
        search: Optional[str],
    ) -> List[DropdownOption]:
        return self._get_property_options_with_search_and_limit(
            event_name_paths=[],
            include=include,
            search=search,
            with_cohort=False,
            with_entities=[entity],
            with_subsegment_breakdown=False,
            with_array_types=False,
            with_only_common_event_fields=False,
        )[0]

    def create_property_dropdown_for_segment_break_down(
        self,
        event_name_paths: Set[M.EventNamePath],
        selected: List[M.FieldPath],
        with_subsegment_breakdown: bool,
    ) -> Dropdown:
        is_valid = True
        fields, value = self.get_property_options_for_segment_break_down(
            event_name_paths,
            include=selected,
            search=None,
            with_subsegment_breakdown=with_subsegment_breakdown,
        )

        all_dim_fields = self.metric_context.get_all_dimension_fields()

        is_valid = all([field.is_valid for field in fields])
        for field_path in selected:
            if (
                isinstance(field_path, M.DimensionFieldPath)
                and field_path not in all_dim_fields
            ):
                is_valid = False

            if not is_valid:
                break

        return self._create_property_dropdown(
            options=fields,
            placeholder="+ Break downs",
            selected=value,
            is_visible=True,
            is_valid=is_valid,
            multi=True,
        )

    def create_property_dropdown_for_global_filter(
        self,
        selected: Optional[M.FieldPath],
        placeholder: str,
        event_name_paths: Set[M.EventNamePath],
    ) -> Dropdown:
        return DasboardFilterPropertyGenerator(self.metric_context).create_dropdown(
            selected, placeholder, event_name_paths
        )

    def get_property_options_for_dashboard_filter(
        self,
        event_name_paths: Set[M.EventNamePath],
        include: Optional[M.FieldPath],
        search: Optional[str],
    ) -> List[DropdownOption]:
        return DasboardFilterPropertyGenerator(
            self.metric_context
        ).get_property_options_for_dashboard_filter(event_name_paths, include, search)

    def create_property_dropdown_for_custom_holding_constants(
        self,
        event_name_paths: List[M.EventNamePath],
        selected_field_paths: List[M.EventFieldPath],
        visible: bool,
    ) -> Dropdown:
        event_name_paths = self._resolve_custom_events_if_any(event_name_paths)
        return self._create_property_dropdown_with_merged_fields(
            event_name_paths=event_name_paths,
            selected_field_paths=selected_field_paths,
            visible=visible,
            multi=True,
        )

    def _create_property_dropdown_with_merged_fields(
        self,
        event_name_paths: List[M.EventNamePath],
        selected_field_paths: List[M.EventFieldPath],
        visible: bool,
        multi: bool,
    ) -> Dropdown:
        common_fields = {}
        is_valid = True
        if len(event_name_paths) > 0 and visible:
            common_fields = {
                field_path.field_name: self._get_field_def_as_dropdown_option(
                    field_path
                )
                for field_path in self.metric_context.get_event_fields(
                    event_name_paths[0]
                )
            }

            for event_name_path in event_name_paths[1:]:
                event_fields = self.metric_context.get_event_fields(event_name_path)
                available_field_names = [
                    field_path.field_name for field_path in event_fields
                ]
                for field_name in list(common_fields.keys()):
                    if field_name not in available_field_names:
                        del common_fields[field_name]

        fields = sorted(common_fields.values(), key=lambda o: o.search)
        selected_values = []
        for field_path in selected_field_paths:
            field_added = False
            for field in fields:
                if (
                    M.EventFieldPath.parse(field.value).field_name
                    == field_path.field_name
                ):
                    selected_values.append(field.value)
                    field_added = True
                    break

            if not field_added and visible:
                missing_field = self._create_missing_field_dropdown_option(
                    self.metric_context.get_default_display_name(field_path),
                    str(field_path),
                    is_property=True,
                )
                fields.append(missing_field)
                selected_values.append(missing_field.value)
                is_valid = False

        if multi:
            selected_value: Union[None, List, str] = selected_values
        else:
            selected_value = selected_values[0] if len(selected_values) > 0 else None

        return self._create_property_dropdown(
            options=fields,
            placeholder="Select property",
            selected=selected_value,
            is_visible=visible,
            is_valid=is_valid,
            multi=multi,
        )

    def _get_unique_field_default_options(
        self,
    ) -> List[DropdownOption]:
        res = []
        for entity_meta in sorted(
            [
                self.metric_context.get_entity_meta(entity)
                for entity in self.metric_context.entities
            ]
        ):
            res.append(
                DropdownOption(
                    label=entity_meta.display_name,
                    value=entity_meta.entity.name,
                    search=entity_meta.entity.name + " " + entity_meta.display_name,
                    icon=DropdownOptionIcon(
                        DropdownOptionIconType.ENTITY, entity_meta.icon
                    ),
                )
            )
        return res

    def create_property_dropdown_for_unique_field_selector(
        self,
        event_name_paths: List[M.EventNamePath],
        selected_field_path: Union[None, M.Entity, M.EventFieldPath],
    ) -> Dropdown:
        is_valid = False
        selected_value = None
        event_name_paths = self._resolve_custom_events_if_any(event_name_paths)
        options = self.get_property_options_for_unique_field_selector(
            event_name_paths, selected_field_path, search=None
        )
        if isinstance(selected_field_path, M.EventFieldPath):
            for option in options:
                try:
                    field_path = M.FieldPath.parse(option.value)
                    if (
                        option.is_valid
                        and field_path.field_name == selected_field_path.field_name
                    ):
                        selected_value = option.value
                        is_valid = True
                        break
                except Exception:
                    pass

            if not is_valid:
                selected_value = str(selected_field_path)

        elif isinstance(selected_field_path, M.Entity):
            selected_value = selected_field_path.name
            is_valid = True
        elif selected_field_path is None:
            is_valid = True
        else:
            raise ValueError(f"Unknown selected value: {selected_field_path}")

        return Dropdown(
            options=options,
            selected=selected_value,
            is_valid=is_valid,
            is_visible=True,
            placeholder="Select property",
            multi=False,
        )

    def get_property_options_for_unique_field_selector(
        self,
        event_name_paths: List[M.EventNamePath],
        selected_field_path: Union[None, M.Entity, M.EventFieldPath],
        search: Optional[str],
    ) -> List[DropdownOption]:
        selected_event_field_paths: List[M.FieldPath] = (
            [selected_field_path]
            if isinstance(selected_field_path, M.EventFieldPath)
            else []
        )

        common_fields: Dict[str, DropdownOption] = {}
        common_field_counter: Dict[str, int] = {}
        default_options = self._get_unique_field_default_options()
        if search is not None or selected_field_path is not None:
            for event_name_path in event_name_paths:
                field_options, _ = self._get_property_options_with_search_and_limit(
                    [event_name_path],
                    selected_event_field_paths,
                    search,
                    with_cohort=False,
                    with_entities=[],
                    with_subsegment_breakdown=False,
                    with_array_types=False,
                    with_only_common_event_fields=False,
                )
                for field_option in field_options:
                    if field_option.disabled:  # type for more placeholder
                        continue

                    field_path = M.FieldPath.parse(field_option.value)
                    if field_path.field_name not in common_fields.keys():
                        common_fields[field_path.field_name] = field_option

                    if field_path.field_name not in common_field_counter.keys():
                        common_field_counter[field_path.field_name] = 0
                    common_field_counter[field_path.field_name] += 1

        result = default_options

        total_hits = 0
        shown = 0
        if search is not None:
            for opt in result:
                if search.lower() in opt.search.lower():
                    total_hits += 1
                    shown += 1

        selected_value_str = ""
        if isinstance(selected_field_path, M.Entity):
            selected_value_str = str(selected_field_path.name)
        elif isinstance(selected_field_path, M.EventFieldPath):
            selected_value_str = str(selected_field_path)

        for field in sorted(
            list(common_fields.values()),
            key=lambda x: x.label not in selected_value_str,
        ):
            should_be_returned = True
            try:
                should_be_returned = common_field_counter[
                    M.FieldPath.parse(field.value).field_name
                ] == len(event_name_paths)
            except Exception:
                pass
            if should_be_returned:
                if search is None or search.lower() in field.search.lower():
                    total_hits += 1

                if len(result) < NUMBER_OF_PROPERTIES_TO_LIST:
                    result.append(field)
                    if search is None or search.lower() in field.search.lower():
                        shown += 1

        if search is not None:
            result.append(
                self._create_show_more_dropdown_option(
                    "properties",
                    shown=shown,
                    total=total_hits,
                    search_term=search,
                )
            )
        else:
            result.append(
                DropdownOption(
                    label="Start typing for more...",
                    search="",
                    value="",
                    disabled=True,
                )
            )

        return result

    def create_property_dropdown_for_property_aggregation(
        self,
        event_name_paths: List[M.EventNamePath],
        selected_field_path: Optional[M.FieldPath],
        visible: bool,
    ) -> Dropdown:
        selected_value = None
        is_valid = True
        event_name_paths = self._resolve_custom_events_if_any(event_name_paths)
        available_entities = self._get_available_entities(event_name_paths)
        if len(event_name_paths) > 0 and visible:
            all_fields: Dict[str, DropdownOption] = {}
            for event_name_path in event_name_paths:
                if event_name_path not in self.metric_context.get_all_event_names():
                    continue

                for field_path in self.metric_context.get_event_fields(event_name_path):
                    key = field_path.field_name
                    if key not in all_fields.keys():
                        all_fields[key] = self._get_field_def_as_dropdown_option(
                            field_path
                        )

            fields = self.sort_options(
                list(all_fields.values())
                + self._get_dimension_field_dropdown_options(
                    include=[selected_field_path]
                    if selected_field_path is not None
                    else [],
                    search=None,  # as of now dynamic searching is not supported
                    include_entities=available_entities,
                )[0]
            )

            if selected_field_path is not None:
                for field in fields:
                    if (
                        M.FieldPath.parse(field.value).field_name
                        == selected_field_path.field_name
                    ):
                        selected_value = field.value
                        break

                if selected_value is None:
                    fields.append(
                        self._create_missing_field_dropdown_option(
                            self.metric_context.get_default_display_name(
                                selected_field_path
                            ),
                            str(selected_field_path),
                            is_property=True,
                        )
                    )
                    selected_value = str(selected_field_path)
                    is_valid = False
        else:
            fields = []

        if isinstance(selected_field_path, M.DimensionFieldPath):
            dim_meta = self.metric_context.get_dimension_meta(selected_field_path)
            is_valid = dim_meta.entity in available_entities

        return self._create_property_dropdown(
            options=fields,
            placeholder="Select a property",
            selected=selected_value,
            is_visible=visible,
            is_valid=is_valid,
            multi=False,
        )

    def create_property_operator_dropdown(
        self,
        filter: Optional[WM.WebappEventFilter],
    ) -> Dropdown:
        return PropertyOperatorGenerator(self.metric_context).create_dropdown(filter)

    def create_property_value_input(
        self, event_filter: Optional[WM.WebappEventFilter], allow_reindex: bool
    ) -> Dropdown:
        return PropertyValueGenerator(
            self.project_id, self.metric_context, self.project_storage
        ).create_dropdown(event_filter, allow_reindex)

    def get_property_options_for_event(
        self,
        event_name_path: M.EventNamePath,
        include: List[M.FieldPath],
        search: Optional[str],
        with_cohort: bool,
        with_dimensions: bool,
    ) -> List[DropdownOption]:
        events = self._resolve_custom_events_if_any([event_name_path])
        return self._get_property_options_with_search_and_limit(
            event_name_paths=events,
            include=include,
            search=search,
            with_cohort=with_cohort,
            with_entities=self._get_available_entities(events)
            if with_dimensions
            else [],
            with_subsegment_breakdown=False,
            with_array_types=True,
            with_only_common_event_fields=False,
        )[0]
