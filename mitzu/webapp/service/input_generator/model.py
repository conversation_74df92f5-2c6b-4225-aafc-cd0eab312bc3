from __future__ import annotations

from dataclasses import dataclass
from enum import Enum, auto
from typing import List, Optional, Union

REINDEX_LABEL = "↻ re-index"
CUSTOM_LOOKUP_PROP_VALUE = "_custom_lookup_option_"
HIGHLIGHTED_DROPDOWN_OPTION_CLASS_NAME = "highlighted-dropdown-option"


class DropdownOptionIconType(Enum):
    EVENT_PROPERTY = auto()
    ENTITY = auto()
    COHORT = auto()
    SUBSEGMENT_BREAK_DOWN = auto()
    REVENUE_ICON = auto()


@dataclass(frozen=True)
class DropdownOptionIcon:
    type: DropdownOptionIconType
    custom_value: Optional[str] = None


class DropdownOptionType(Enum):
    EVENT = "event"
    EVENT_PROPERTY = "event_property"
    DIMENSION_PROPERTY = "dimension_property"


@dataclass(frozen=True)
class FieldDisplayAssets:
    display_name: str
    description: Optional[str]
    icon: DropdownOptionIcon


@dataclass(frozen=True)
class DropdownOption:
    label: str
    value: str
    search: str
    is_valid: bool = True
    disabled: bool = False
    description: Optional[str] = None
    icon: Optional[DropdownOptionIcon] = None
    field_type: Optional[DropdownOptionType] = None
    is_property: bool = True
    class_name: Optional[str] = None
    tooltip_info: Optional[str] = None

    @classmethod
    def create(cls, value: str) -> DropdownOption:
        return DropdownOption(
            label=value,
            value=value,
            search=value,
        )


@dataclass(frozen=True)
class Dropdown:
    options: List[DropdownOption]
    selected: Union[None, str, List[str]]
    is_valid: bool
    is_visible: bool
    placeholder: Optional[str]
    multi: bool

    @classmethod
    def empty(cls) -> Dropdown:
        return Dropdown(
            options=[],
            selected=None,
            is_valid=False,
            is_visible=False,
            placeholder=None,
            multi=False,
        )
