from typing import Dict, List, Optional, Union

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.webapp.service.input_generator.base_generator import (
    BaseGenerator,
)
from mitzu.webapp.service.input_generator.model import (
    HIGHLIGHTED_DROPDOWN_OPTION_CLASS_NAME,
    Dropdown,
    DropdownOption,
    DropdownOptionIcon,
    DropdownOptionIconType,
    DropdownOptionType,
)


class EventGenerator(BaseGenerator):
    def __init__(
        self,
        metric_context: MC.MetricContext,
        relevance_scores: Dict[str, WM.UserEventData],
    ) -> None:
        self.metric_context = metric_context
        self.relevance_scores = relevance_scores

    def create_event_dropdown(
        self,
        selected: Optional[M.EventNamePath],
        placeholder: Optional[str],
        is_visible: bool,
        analyze_by_field: Union[M.EventFieldPath, M.Entity],
    ) -> Dropdown:
        is_valid = True
        options = []
        rev_settings = self.metric_context.revenue_settings

        if selected is not None:
            event_meta = self.metric_context.get_event_meta_or_default_values(selected)

            if selected in self.metric_context.get_all_event_names():
                custom_icon = (
                    DropdownOptionIcon(DropdownOptionIconType.REVENUE_ICON)
                    if rev_settings and rev_settings.revenue_event == selected
                    else None
                )
                options = [
                    self._create_event_name_dropdown_option(
                        event_meta=event_meta,
                        event_path=selected,
                        custom_icon=custom_icon,
                    )
                ]
                table_meta = self.metric_context.get_table_meta(selected)

                if isinstance(analyze_by_field, M.EventFieldPath):
                    event_has_field = False
                    if isinstance(selected, M.CustomEventNamePath):
                        custom_events = self._resolve_custom_events_if_any([selected])
                        _, field_path_dict, _ = self._get_intersection_of_event_fields(
                            custom_events,
                            [analyze_by_field],
                            search=None,
                            with_array_types=False,
                        )
                        available_field_paths: List[M.EventFieldPath] = list(
                            field_path_dict.values()  # type: ignore[arg-type]
                        )
                    else:
                        available_field_paths = self.metric_context.get_event_fields(
                            selected
                        )
                    for field_path in available_field_paths:
                        if field_path.field_name == analyze_by_field.field_name:
                            event_has_field = True
                            break
                    is_valid = event_has_field
                else:
                    if isinstance(selected, M.CustomEventNamePath):
                        custom_events = self._resolve_custom_events_if_any([selected])
                        available_entities = self._get_common_entities(custom_events)
                    else:
                        available_entities = table_meta.entities
                    is_valid = analyze_by_field in available_entities

                if self.metric_context.dimension_profile_config is not None:
                    dpc = self.metric_context.dimension_profile_config
                    if (
                        isinstance(dpc.left, M.DimensionIDFieldPath)
                        and dpc.left.entity not in table_meta.entities
                    ):
                        is_valid = False

            else:
                options.append(
                    self._create_missing_field_dropdown_option(
                        event_meta.display_name,
                        str(selected),
                        is_property=False,
                    )
                )
                is_valid = False

        if not is_visible:
            is_visible = False

        return Dropdown(
            options=options,
            selected=str(selected) if selected else None,
            is_valid=is_valid,
            is_visible=is_visible,
            placeholder=placeholder,
            multi=False,
        )

    def get_event_dropdown_options(self) -> List[DropdownOption]:
        options: List[DropdownOption] = []
        event_names = self.metric_context.get_all_event_names()
        rev_settings = self.metric_context.revenue_settings

        def get_opacity_class(score: float) -> str:
            if score >= 0.60:
                return "highlighted-opacity-1"
            elif score >= 0.10:
                return "highlighted-opacity-2"
            else:
                return "highlighted-opacity-3"

        for event_path in event_names:
            event_meta = self.metric_context.get_event_meta_or_default_values(
                event_path
            )
            custom_icon = (
                DropdownOptionIcon(DropdownOptionIconType.REVENUE_ICON)
                if rev_settings and rev_settings.revenue_event == event_path
                else None
            )
            event_score_data = self.relevance_scores.get(
                str(event_path),
            )

            relevance_score = event_score_data.score if event_score_data else 0.0
            class_name = (
                " ".join(
                    [
                        HIGHLIGHTED_DROPDOWN_OPTION_CLASS_NAME,
                        get_opacity_class(
                            relevance_score,
                        ),
                    ]
                ).strip()
                if relevance_score > 0
                else None
            )
            tooltip_info = None
            if event_score_data and event_score_data.count:
                tooltip_info = f'Used {str(event_score_data.count)+ " times" if event_score_data.count > 1 else "once"} recently'
            options.append(
                self._create_event_name_dropdown_option(
                    event_path,
                    event_meta,
                    custom_icon=custom_icon,
                    class_name=class_name,
                    tooltip_info=tooltip_info,
                )
            )

        options.sort(
            key=lambda v: (
                -self.relevance_scores[v.value].score
                if v.value in self.relevance_scores
                and self.relevance_scores[v.value].score
                else 0,
                v.icon is None,
                v.search,
            ),
        )
        return options

    def _create_event_name_dropdown_option(
        self,
        event_path: M.EventNamePath,
        event_meta: WM.EventMeta,
        custom_icon: Optional[DropdownOptionIcon] = None,
        class_name: Optional[str] = None,
        tooltip_info: Optional[str] = None,
    ) -> DropdownOption:
        return DropdownOption(
            label=event_meta.display_name,
            description=event_meta.description,
            search=str(event_meta.display_name)
            + (f" {event_meta.description}" if event_meta.description else ""),
            value=str(event_path),
            is_property=False,
            icon=custom_icon,
            field_type=DropdownOptionType.EVENT,
            class_name=class_name,
            tooltip_info=tooltip_info,
        )
