from typing import Dict, List, Optional, Set, Tuple, Union

import mitzu.model as M
import mitzu.webapp.metric_context as MC
from mitzu import configs
from mitzu.webapp.pages.explore.constants import (
    NUMBER_OF_PROPERTIES_TO_LIST,
)
from mitzu.webapp.service.input_generator.model import (
    Dropdown,
    DropdownOption,
    DropdownOptionIcon,
    DropdownOptionIconType,
    DropdownOptionType,
)
from mitzu.webapp.service.input_generator.utils import (
    get_field_display_assets,
    merge_options_with_limit,
)


class BaseGenerator:
    def __init__(self, metric_context: MC.MetricContext) -> None:
        self.metric_context = metric_context

    def _create_missing_field_dropdown_option(
        self, label: str, value: str, is_property: bool
    ) -> DropdownOption:
        return DropdownOption(
            label=label,
            search=value,
            value=value,
            is_property=is_property,
        )

    def _get_property_options_with_search_and_limit(
        self,
        event_name_paths: List[M.EventNamePath],
        include: List[M.FieldPath],
        search: Optional[str],
        with_cohort: bool,
        with_entities: List[M.Entity],
        with_subsegment_breakdown: bool,
        with_array_types: bool,
        with_only_common_event_fields: bool,
    ) -> Tuple[List[DropdownOption], List[str]]:
        event_property_options: List[DropdownOption] = []
        if with_cohort:
            entities: Set[M.Entity] = set([M.USER_ENTITY])
            if configs.COLLECTIONS_ENABLED:
                for event_name_path in event_name_paths:
                    table_meta = self.metric_context.get_table_meta(event_name_path)
                    entities.update(table_meta.entities)

            for entity in entities:
                em = self.metric_context.get_entity_meta(entity)

                if entity == M.USER_ENTITY:
                    label = "Cohort"
                    icon = DropdownOptionIcon(DropdownOptionIconType.COHORT)
                else:
                    label = em.display_name + " collection"
                    icon = DropdownOptionIcon(DropdownOptionIconType.ENTITY, em.icon)
                cohort_option = DropdownOption(
                    label=label,
                    value=str(M.CollectionFieldPath(em.entity)),
                    search=label,
                    icon=icon,
                )
                if self._property_dropdown_matches_on_search(
                    cohort_option, include, search
                ):
                    event_property_options.append(cohort_option)

        if with_subsegment_breakdown:
            event_property_options.append(
                self._create_subsegment_break_down_property_dropdown()
            )

        total_event_field_count = len(event_property_options)
        value: List = []
        invalid_fields: List[M.FieldPath] = []
        if with_only_common_event_fields:
            (
                event_field_union,
                field_name_mapping,
                total_count,
            ) = self._get_intersection_of_event_fields(
                event_name_paths, include, search, with_array_types
            )
        else:
            (
                event_field_union,
                field_name_mapping,
                total_count,
            ) = self._get_union_of_event_fields(
                event_name_paths, include, search, with_array_types
            )
        total_event_field_count += total_count
        event_property_options = (event_property_options + event_field_union)[
            0:NUMBER_OF_PROPERTIES_TO_LIST
        ]

        for included_field_path in include:
            if isinstance(included_field_path, M.EventFieldPath):
                if included_field_path.field_name in field_name_mapping:
                    value.append(
                        str(field_name_mapping[included_field_path.field_name])
                    )
                else:
                    invalid_fields.append(included_field_path)
                    value.append(str(included_field_path))  # type: ignore[arg-type]
            elif isinstance(included_field_path, M.DimensionFieldPath):
                value.append(str(included_field_path))
                if (
                    included_field_path
                    not in self.metric_context.get_all_dimension_fields()
                ):
                    invalid_fields.append(included_field_path)  # type: ignore[arg-type]
            elif with_cohort and isinstance(included_field_path, M.CollectionFieldPath):
                value.append(str(included_field_path))
            elif included_field_path == M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH:
                value.append(str(included_field_path))
            else:
                raise ValueError(
                    f"Unknown field path: {included_field_path} {type(included_field_path)}"
                )

        if with_entities:
            (
                dimension_field_options,
                total_dim_field_count,
            ) = self._get_dimension_field_dropdown_options(
                include, search, with_entities
            )
        else:
            dimension_field_options = []
            total_dim_field_count = 0

        options = self.sort_options(
            merge_options_with_limit(
                NUMBER_OF_PROPERTIES_TO_LIST,
                event_property_options,
                dimension_field_options,
            )
        )

        if (
            total_event_field_count + total_dim_field_count
            > NUMBER_OF_PROPERTIES_TO_LIST
        ):
            options = [
                self._create_show_more_dropdown_option(
                    "properties",
                    shown=NUMBER_OF_PROPERTIES_TO_LIST,
                    total=total_event_field_count + total_dim_field_count,
                    search_term=search,
                )
            ] + options

        for field_path in invalid_fields:
            display_name = self.metric_context.get_default_display_name(field_path)

            options.append(
                self._create_missing_field_in_multip_dropdown_option(
                    display_name, str(field_path)
                )
            )

        return options, value

    def _get_union_of_event_fields(
        self,
        event_name_paths: List[M.EventNamePath],
        include: List[M.FieldPath],
        search: Optional[str],
        with_array_types: bool,
    ) -> Tuple[List[DropdownOption], Dict[str, M.FieldPath], int]:
        event_property_options: List[DropdownOption] = []
        field_name_mapping: Dict[str, M.FieldPath] = {}
        total_count = 0
        for event_name_path in event_name_paths:
            if event_name_path not in self.metric_context.get_all_event_names():
                continue

            for event_field_path in sorted(
                self.metric_context.get_event_fields(event_name_path),
                key=lambda f: f not in include,
            ):
                # NOTE: in case of overlapping event names from different source tables
                # only one of them will be listed here and later the SQL generation will
                # try to add the _cte_group column to the query based on the event name

                if event_field_path.field_name in field_name_mapping.keys():
                    continue

                if (
                    not with_array_types
                    and self.metric_context.get_property_meta(
                        event_field_path
                    ).property_type
                    == M.DataType.ARRAY
                ):
                    continue

                dropdown_option = self._get_field_def_as_dropdown_option(
                    event_field_path
                )
                if self._property_dropdown_matches_on_search(
                    dropdown_option, include, search
                ):
                    total_count += 1
                    if len(event_property_options) <= NUMBER_OF_PROPERTIES_TO_LIST:
                        event_property_options.append(dropdown_option)
                        field_name_mapping[
                            event_field_path.field_name
                        ] = event_field_path

        return event_property_options, field_name_mapping, total_count

    def _get_intersection_of_event_fields(
        self,
        event_name_paths: List[M.EventNamePath],
        include: List[M.FieldPath],
        search: Optional[str],
        with_array_types: bool,
    ) -> Tuple[List[DropdownOption], Dict[str, M.FieldPath], int]:
        event_property_options: Dict[M.FieldPath, DropdownOption] = {}
        first_event = True
        for event_name_path in event_name_paths:
            if event_name_path not in self.metric_context.get_all_event_names():
                continue

            available_fields: Dict[M.FieldPath, DropdownOption] = {}
            for event_field_path in sorted(
                self.metric_context.get_event_fields(event_name_path),
                key=lambda f: f not in include,
            ):
                if (
                    not with_array_types
                    and self.metric_context.get_property_meta(
                        event_field_path
                    ).property_type
                    == M.DataType.ARRAY
                ):
                    continue

                dropdown_option = self._get_field_def_as_dropdown_option(
                    event_field_path
                )
                if self._property_dropdown_matches_on_search(
                    dropdown_option, include, search
                ):
                    available_fields[event_field_path] = dropdown_option

            if first_event is True:
                event_property_options = available_fields
            else:
                available_field_names = [
                    fp.field_name for fp in available_fields.keys()
                ]
                for field_path in list(event_property_options.keys()):
                    field_name = field_path.field_name
                    if field_name not in available_field_names:
                        del event_property_options[field_path]

            first_event = False

        include_field_names = [fp.field_name for fp in include]
        common_event_properties = sorted(
            event_property_options.values(),
            key=lambda opt: M.FieldPath.parse(opt.value).field_name
            not in include_field_names,
        )

        return (
            common_event_properties[0:NUMBER_OF_PROPERTIES_TO_LIST],
            {fp.field_name: fp for fp in event_property_options.keys()},
            len(event_property_options),
        )

    def _property_dropdown_matches_on_search(
        self,
        property_meta: DropdownOption,
        include: List[M.FieldPath],
        search: Optional[str],
    ) -> bool:
        return (
            search is None
            or search.lower() in property_meta.search.lower()
            or M.FieldPath.parse(property_meta.value) in include
        )

    def _create_subsegment_break_down_property_dropdown(self) -> DropdownOption:
        return DropdownOption(
            label="Subsegment",
            value=str(M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH),
            search="subsegment",
            icon=DropdownOptionIcon(DropdownOptionIconType.SUBSEGMENT_BREAK_DOWN),
        )

    def _get_field_def_as_dropdown_option(
        self, field_path: M.FieldPath
    ) -> DropdownOption:
        assets = get_field_display_assets(self.metric_context, field_path)

        if isinstance(field_path, M.EventFieldPath):
            field_type = DropdownOptionType.EVENT_PROPERTY
        elif isinstance(field_path, M.DimensionFieldPath):
            field_type = DropdownOptionType.DIMENSION_PROPERTY
        else:
            raise ValueError(f"Unknown field path type: {field_path}")

        return DropdownOption(
            label=assets.display_name,
            value=str(field_path),
            search=(
                assets.display_name
                + (f" {assets.description}" if assets.description is not None else "")
            ),
            description=assets.description,
            icon=assets.icon,
            field_type=field_type,
        )

    def _get_dimension_field_dropdown_options(
        self,
        include: List[M.FieldPath],
        search: Optional[str],
        include_entities: List[M.Entity],
    ) -> Tuple[List[DropdownOption], int]:
        total_field_count = 0

        options_by_entity: Dict[M.Entity, List[DropdownOption]] = {}
        unavailable_entities: List[M.Entity] = []

        for field_path in sorted(
            self.metric_context.get_all_dimension_fields(),
            key=lambda f: f not in include,
        ):
            dim_meta = self.metric_context.get_dimension_meta(field_path)
            if dim_meta.entity not in include_entities and field_path not in include:
                continue

            dropdown_option = self._get_field_def_as_dropdown_option(field_path)
            if self._property_dropdown_matches_on_search(
                dropdown_option, include, search
            ):
                total_field_count += 1
                if dim_meta.entity in options_by_entity.keys():
                    if (
                        len(options_by_entity[dim_meta.entity])
                        <= NUMBER_OF_PROPERTIES_TO_LIST
                    ):
                        options_by_entity[dim_meta.entity].append(dropdown_option)
                else:  # field is in the include list but not in the available entities
                    options_by_entity[dim_meta.entity] = [dropdown_option]

        return (
            merge_options_with_limit(
                NUMBER_OF_PROPERTIES_TO_LIST,
                *[
                    options_by_entity[ent]
                    for ent in self.metric_context.entities + unavailable_entities
                    if ent in options_by_entity.keys()
                ],  # make it deterministic
            ),
            total_field_count,
        )

    def sort_options(self, options: List[DropdownOption]) -> List[DropdownOption]:
        # the default order for bools is False -> True
        def order_func(o: DropdownOption) -> Tuple[Union[bool, str], ...]:
            keys: List[Union[bool, str]] = [
                not o.value.startswith(
                    M.COLLECTION_FIELD_PATH_PREFIX + ":"
                ),  # show cohorts/collections first
                o.value
                != str(
                    M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH
                ),  # show subsegment break down as second
                o.icon
                != DropdownOptionIcon(
                    DropdownOptionIconType.EVENT_PROPERTY
                ),  # then events properties
            ]
            for entity in self.metric_context.entities:
                entity_meta = self.metric_context.get_entity_meta(entity)
                keys.append(
                    o.icon
                    != DropdownOptionIcon(
                        DropdownOptionIconType.ENTITY, entity_meta.icon
                    )
                )
            keys.append(o.search)  # order the rest alphabetically
            return tuple(keys)

        return sorted(options, key=order_func)

    def _create_show_more_dropdown_option(
        self, record_type: str, shown: int, total: int, search_term: Optional[str]
    ) -> DropdownOption:
        return DropdownOption(
            label=f"Showing {shown} out of {total} {record_type}. Type for more",
            search=search_term
            if search_term
            else "",  # dash UI cannot handle None values, fallback to empty string
            value=search_term
            if search_term
            else "",  # set it to the search term so the UI won't filter it out
            disabled=True,
        )

    def _create_missing_field_in_multip_dropdown_option(
        self, label: str, value: str
    ) -> DropdownOption:
        return DropdownOption(
            label=label,
            search=value,
            value=value,
            is_valid=False,
        )

    def _create_property_dropdown(
        self,
        options: List[DropdownOption],
        placeholder: str,
        selected: Union[None, str, List[str]],
        is_visible: bool,
        is_valid: bool,
        multi: bool = False,
    ) -> Dropdown:
        return Dropdown(
            options=options,
            selected=selected,
            placeholder=placeholder,
            is_visible=is_visible,
            is_valid=is_valid,
            multi=multi,
        )

    def _get_available_entities(
        self, event_name_paths: List[M.EventNamePath]
    ) -> List[M.Entity]:
        entities: Set[M.Entity] = set()
        for event_name_path in event_name_paths:
            entities.update(
                self.metric_context.get_table_meta(event_name_path).entities
            )
        # keep the order original order of the entities else IG may become undeterministic
        return [entity for entity in self.metric_context.entities if entity in entities]

    def _get_common_entities(
        self, event_name_paths: List[M.EventNamePath]
    ) -> Set[M.Entity]:
        if len(event_name_paths) == 0:
            return set()

        entities: Set[M.Entity] = set(
            self.metric_context.get_table_meta(event_name_paths[0]).entities
        )
        for event_name_path in event_name_paths[1:]:
            available_entities = self.metric_context.get_table_meta(
                event_name_path
            ).entities
            entities.intersection_update(available_entities)
        # keep the order original order of the entities else IG may become undeterministic
        return {entity for entity in self.metric_context.entities if entity in entities}

    def _resolve_custom_events_if_any(
        self, selected_events: List[M.EventNamePath]
    ) -> List[M.EventNamePath]:
        result = self.metric_context.resolve_custom_events_in_list(selected_events)
        return sorted(result, key=lambda e: str(e))  # sorting makes test deterministic
