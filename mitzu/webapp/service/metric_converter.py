import json
from dataclasses import dataclass, replace
from datetime import datetime
from typing import Any, Dict, Iterable, List, Optional, Set

from dateutil import parser

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.serialization as SE
import mitzu.webapp.storage.project_storage as PS
from mitzu.adapters.model import (
    MissingDimensionForeignKeyError,
)
from mitzu.webapp.pages.explore.constants import (
    RETENTION_INDICES_ALL_GROUPS,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)


class NotEnoughSegmentsError(Exception):
    pass


class InvalidWebappMetricException(ValueError):
    pass


class TooManyBreakDownsInConversion(ValueError):
    pass


class InvalidRevenueCombinedEventException(ValueError):
    pass


class RevenueIsNotConfigured(ValueError):
    pass


@dataclass(frozen=True)
class MetricConverter:
    defs_for_metric: M.DefinitionsForMetric
    project_storage: PS.ProjectScopedStorage
    metric_updater: MetricUpdater

    def convert_metric(self, webapp_metric: WM.WebappMetric) -> M.Metric:
        segments = []
        for webapp_segment in webapp_metric.segments:
            for global_filter in webapp_metric.global_filters:
                webapp_segment = self.metric_updater.add_or_update_segment_filter(
                    webapp_segment,
                    global_filter.left,
                    global_filter.operator,
                    global_filter.right,
                )
            segments.append(self.convert_segment(webapp_segment))

        if (
            webapp_metric.metric_type in [M.MetricType.RETENTION, M.MetricType.JOURNEY]
            and len(segments) == 1
        ):
            raise NotEnoughSegmentsError()

        if (
            webapp_metric.metric_type == M.MetricType.CONVERSION
            and len([seg for seg in segments if len(seg.group_by) > 0]) > 1
        ):
            raise TooManyBreakDownsInConversion()

        config = self.convert_metric_config(
            webapp_metric.config, webapp_metric.metric_type
        )
        if isinstance(config, M.RetentionMetricConfig):
            consider_first_event_as_retained = (
                self.defs_for_metric.project.insight_settings.consider_first_event_as_retained
            )
            config = replace(
                config,
                consider_first_event_as_retained=consider_first_event_as_retained,
            )

        expected_custom_holding_fields = set()
        if isinstance(config, M.ConversionRetentionMetricConfig) and len(
            config.custom_holding_constants
        ):
            expected_custom_holding_fields = {
                field.field._get_name() for field in config.custom_holding_constants
            }

        if len(expected_custom_holding_fields) > 0 or config.unique_field is not None:

            for segment in segments:
                available_field_names: Set[str] = set()
                for event_path in segment.get_event_name_paths():
                    fields = self.defs_for_metric.get_event_def(event_path).fields
                    available_field_names.update(
                        {field.field._get_name() for field in fields}
                    )

                if len(
                    expected_custom_holding_fields
                ) > 0 and not expected_custom_holding_fields.issubset(
                    available_field_names
                ):
                    raise InvalidWebappMetricException(
                        "A selected custom holding constant field is not present in all steps"
                    )

                if (
                    isinstance(config.unique_field, M.FieldDef)
                    and config.unique_field.field._get_name()
                    not in available_field_names
                ):
                    raise InvalidWebappMetricException(
                        "The selected unique field is not present in all "
                        + (
                            "segments"
                            if webapp_metric.metric_type == M.MetricType.SEGMENTATION
                            else "steps"
                        )
                    )

                if isinstance(config.unique_field, M.Entity):
                    required_tables = {
                        enp.source_table for enp in segment.get_event_name_paths()
                    }

                    for edt in self.defs_for_metric.project.event_data_tables:
                        if edt.table_name not in required_tables:
                            continue
                        if config.unique_field not in edt.foreign_keys:
                            raise InvalidWebappMetricException(
                                f"Identifier for the selected analyze uniques by entity "
                                f"is not configured for the event table {edt.table_name}"
                            )
        return M.Metric(
            type=webapp_metric.metric_type,
            segments=segments,
            config=config,
            title=webapp_metric.title,
            description=webapp_metric.description,
        )

    def convert_segment(self, segment: WM.WebappSegment) -> M.Segment:
        events = segment.get_event_name_paths()

        if any([isinstance(event, M.CustomEventNamePath) for event in events]):
            segment = self.metric_updater.resolve_custom_events(segment, "")
            events = segment.get_event_name_paths()

        revenue_event = (
            self.defs_for_metric.project.revenue_settings.revenue_event
            if self.defs_for_metric.project.revenue_settings is not None
            else None
        )
        if (
            revenue_event is not None
            and revenue_event in events
            and set(events) != set([revenue_event])
        ):
            raise InvalidRevenueCombinedEventException()

        if (
            segment.aggregation is not None
            and segment.aggregation.type
            in [
                M.AggType.TOTAL_MRR,
                M.AggType.COUNT_SUBSCRIBERS,
                M.AggType.RETURN_SUBSCRIBERS,
            ]
            and self.defs_for_metric.project.revenue_settings is None
        ):
            raise RevenueIsNotConfigured()

        if isinstance(segment, WM.WebappSimpleSegment):
            return self._convert_simple_segment(segment)
        elif isinstance(segment, WM.WebappComplexSegment):
            return self._convert_complex_segment(segment)
        else:
            raise ValueError(f"unexpected segment type: {segment}")

    def _get_group_by_fields(
        self,
        selected_events: Iterable[M.EventNamePath],
        group_by: List[M.FieldPath],
    ) -> List[M.FieldDef]:
        result: List[M.FieldDef] = []
        available_fields: Dict[str, M.FieldDef] = {}
        for event_path in selected_events:
            event = self.defs_for_metric.get_event_def(event_path)
            available_fields.update(
                {field.field._get_name(): field for field in event.fields}
            )

        for dim_def in self.defs_for_metric.get_all_dimension_fields():
            available_fields[str(dim_def.field_path)] = dim_def

        for field_path in group_by:
            if isinstance(field_path, M.EventFieldPath):
                # NOTE: clear the previously selected break down fields
                # when the event of that field is not selected anymore
                gp_field: Optional[M.FieldDef] = available_fields.get(
                    field_path.field_name
                )
            elif isinstance(field_path, M.DimensionFieldPath):
                gp_field = self.defs_for_metric.get_dimension_field_def(field_path)
            elif field_path == M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH:
                gp_field = M.SubsegmentFieldDef()
            elif field_path == M.EVENT_TIME_FIELD_PATH:
                for event_path in selected_events:
                    event = self.defs_for_metric.get_event_def(event_path)
                    event_time_field_path = M.EventFieldPath(
                        event.event_data_table.table_name,
                        event.event_name,
                        event.event_data_table.event_time_field._get_name(),
                    )
                    result.append(
                        self.defs_for_metric.get_field_def(event_time_field_path)
                    )
                continue

            else:
                raise ValueError(f"Unexpected field path type: {type(field_path)}")

            if gp_field is None:
                is_event_discovered = False
                if isinstance(field_path, M.EventFieldPath):
                    event_opt = self.defs_for_metric.event_definitions.get(
                        field_path.event_name_path
                    )
                    if event_opt and len(event_opt.fields) > 0:
                        is_event_discovered = True
                raise M.MissingFieldException(field_path, is_event_discovered)

            result.append(gp_field)

        return result

    def _convert_simple_segment(
        self, segment: WM.WebappSimpleSegment
    ) -> M.SimpleSegment:
        return M.SimpleSegment(
            event=self.defs_for_metric.get_event_def(segment.event),
            event_filter=(
                self.convert_event_filter(segment.event_filter, segment.event)
                if segment.event_filter is not None
                else None
            ),
            filter=segment.filter,
            aggregation=(
                self.convert_aggregation_config(segment.aggregation)
                if segment.aggregation is not None
                else None
            ),
            group_by=self._get_group_by_fields(
                segment.get_event_name_paths(), segment.group_by
            ),
        )

    def _convert_complex_segment(
        self, segment: WM.WebappComplexSegment
    ) -> M.ComplexSegment:
        return M.ComplexSegment(
            left=self.convert_segment(segment.left),
            right=self.convert_segment(segment.right),
            operator=segment.operator,
            filter=segment.filter,
            aggregation=(
                self.convert_aggregation_config(segment.aggregation)
                if segment.aggregation is not None
                else None
            ),
            group_by=self._get_group_by_fields(
                segment.get_event_name_paths(), segment.group_by
            ),
            expanded_custom_event_key=segment.expanded_custom_event_key,
        )

    def convert_metric_config(
        self, config: WM.WebappMetricConfig, metric_type: M.MetricType
    ) -> M.MetricConfig:
        start_dt: Optional[datetime] = None
        end_dt: Optional[datetime] = None
        if config.time_window_chooser_config.calendar_date_range is not None:
            (
                start_dt,
                end_dt,
            ) = config.time_window_chooser_config.calendar_date_range.to_start_and_end_date(
                now=self.defs_for_metric.project.get_default_end_dt()
            )
        elif config.time_window_chooser_config.since_date is not None:
            start_dt = config.time_window_chooser_config.since_date
            end_dt = self.defs_for_metric.project.get_default_end_dt()
        else:
            start_dt = config.time_window_chooser_config.start_dt
            end_dt = config.time_window_chooser_config.end_dt

        if metric_type == M.MetricType.SEGMENTATION:
            return M.SegmentationMetricConfig(
                start_dt=start_dt,
                event_sampling=config.event_sampling,
                end_dt=end_dt,
                lookback_days=config.time_window_chooser_config.lookback_days,
                time_group=config.time_window_chooser_config.time_group,
                max_group_count=config.max_group_count,
                chart_type=config.chart_type,
                chart_post_processing=config.chart_post_processing,
                resolution=config.resolution,
                aggregation=(
                    self.convert_aggregation_config(config.aggregation)
                    if config.aggregation is not None
                    else None
                ),
                formula=config.formula,
                unique_field=self.defs_for_metric.get_event_field_def(
                    config.unique_field
                )
                if isinstance(config.unique_field, M.EventFieldPath)
                else config.unique_field,
                comparison_options=config.comparison_options,
            )

        elif metric_type in [
            M.MetricType.CONVERSION,
            M.MetricType.JOURNEY,
        ]:
            # apply the default values
            conv_window = config.conv_window or M.TimeWindow(1, M.TimeGroup.DAY)
            conv_window_type = (
                config.conv_window_type or M.ConversionWindowType.ENTIRE_FUNNEL
            )
            conv_attribution = (
                config.conv_attribution or M.ConversionAttribution.FIRST_EVENT
            )
            conv_orientation = (
                config.conv_orientation or M.ConversionOrientation.FORWARD
            )

            return M.ConversionMetricConfig(
                start_dt=start_dt,
                end_dt=end_dt,
                lookback_days=config.time_window_chooser_config.lookback_days,
                time_group=config.time_window_chooser_config.time_group,
                max_group_count=config.max_group_count,
                chart_type=config.chart_type,
                chart_post_processing=config.chart_post_processing,
                resolution=config.resolution,
                event_sampling=config.event_sampling,
                time_window=conv_window,
                conv_window_type=conv_window_type,
                conv_attribution=conv_attribution,
                conv_orientation=conv_orientation,
                custom_holding_constants=[
                    self.defs_for_metric.get_event_field_def(chc_path)
                    for chc_path in config.custom_holding_constants
                ],
                aggregation=(
                    self.convert_aggregation_config(config.aggregation)
                    if config.aggregation is not None
                    else None
                ),
                unique_field=self.defs_for_metric.get_event_field_def(
                    config.unique_field
                )
                if isinstance(config.unique_field, M.EventFieldPath)
                else config.unique_field,
                comparison_options=config.comparison_options,
            )

        elif metric_type == M.MetricType.RETENTION:
            if config.retention_indices == [RETENTION_INDICES_ALL_GROUPS]:
                retention_indices = config.get_retention_indices(
                    self.defs_for_metric.project.get_default_end_dt()
                )
            elif isinstance(config.retention_indices, list):
                retention_indices = config.retention_indices
            else:
                retention_indices = []

            retention_window = config.retention_window or M.TimeWindow(
                1, M.TimeGroup.WEEK
            )
            retention_attribution_type = (
                config.retention_attribution_type
                or M.RetentionAttributionType.RETURN_ON
            )

            conv_attribution = (
                config.conv_attribution or M.ConversionAttribution.FIRST_EVENT
            )
            return M.RetentionMetricConfig(
                start_dt=start_dt,
                end_dt=end_dt,
                lookback_days=config.time_window_chooser_config.lookback_days,
                time_group=config.time_window_chooser_config.time_group,
                max_group_count=config.max_group_count,
                chart_type=config.chart_type,
                chart_post_processing=config.chart_post_processing,
                resolution=config.resolution,
                event_sampling=config.event_sampling,
                custom_holding_constants=[
                    self.defs_for_metric.get_event_field_def(chc_path)
                    for chc_path in config.custom_holding_constants
                ],
                retention_indices=retention_indices,
                time_window=retention_window,
                retention_attribution_type=retention_attribution_type,
                aggregation=(
                    self.convert_aggregation_config(config.aggregation)
                    if config.aggregation is not None
                    else None
                ),
                unique_field=self.defs_for_metric.get_event_field_def(
                    config.unique_field
                )
                if isinstance(config.unique_field, M.EventFieldPath)
                else config.unique_field,
                comparison_options=config.comparison_options,
                cut_off_incomplete_data=config.cut_off_incomplete_data,
                conv_attribution=conv_attribution,
            )
        else:
            raise ValueError(f"Unknown metric type {metric_type}")

    def convert_aggregation_config(
        self, agg_config: WM.WebappAggregationConfig
    ) -> M.AggregationConfig:
        return M.AggregationConfig(
            type=agg_config.type,
            conv_param=agg_config.conv_param,
            event_property_agg=(
                self._convert_event_property_aggregation(agg_config.event_property_agg)
                if agg_config.event_property_agg is not None
                else None
            ),
        )

    def _convert_event_property_aggregation(
        self, agg: WM.WebappEventPropertyAggregation
    ) -> M.EventPropertyAggregation:
        if agg.field_def is None:
            raise InvalidWebappMetricException("Select a property for aggregation")

        return M.EventPropertyAggregation(
            field_def=self.defs_for_metric.get_field_def(agg.field_def),
            agg_type=agg.agg_type,
        )

    def _convert_value_to_type(self, val: Any, data_type: M.DataType) -> Optional[Any]:
        try:
            return (
                MetricConverter._convert_strs_to_correct_type(data_type, val)
                if isinstance(val, str)
                else val
            )
        except Exception as exc:
            raise InvalidWebappMetricException(
                f"The value '{val}' can not be converted to {data_type.name.lower()} type"
            ) from exc

    def _collect_filter_values(
        self, value: Any, data_type: M.DataType
    ) -> Optional[List[Any]]:
        if value is None:
            return None
        if type(value) in (list, tuple):
            return [self._convert_value_to_type(v, data_type) for v in value]
        else:
            return self._convert_value_to_type(value, data_type)

    def convert_event_filter(
        self,
        filter: WM.WebappEventFilter,
        event_name_path: Optional[M.EventNamePath],
    ) -> M.EventFilter:
        if isinstance(filter.left, M.CollectionFieldPath):
            if event_name_path is None:
                raise ValueError(
                    "Cohort filter cannot be converted without an event name path"
                )
            event_def = self.defs_for_metric.get_event_def(event_name_path)
            if not isinstance(filter.right, WM.CohortReference):
                raise InvalidWebappMetricException(
                    "Cohort is not set for cohort filter"
                )
            edt = event_def.event_data_table
            cohort = self.project_storage.get_cohort(filter.right.id)
            entity_field = edt.foreign_keys.get(cohort.entity)
            if entity_field is None:
                raise InvalidWebappMetricException(
                    f"Identifier ({cohort.entity}) for the selected cohort is not "
                    f"configured for the event table {edt.table_name}"
                )

            field_def: M.FieldDef = event_def.get_field_def(entity_field._get_name())

            if cohort.type == M.CohortType.IMPORTED:

                filter_value: Any = M.StaticCollection(
                    id=cohort.id,
                    values=self.project_storage.get_static_collection_entity_id_list(
                        cohort.id
                    ),
                )
            elif cohort.type == M.CohortType.DYNAMIC:
                cohort_metric = self.convert_metric(
                    SE.webapp_metric_from_dict(json.loads(cohort.metric_json))
                )

                filter_value = M.DynamicCollection(
                    id=cohort.id,
                    metric=self._fix_metric_aggs_for_cohort_filter(cohort_metric),
                )
            elif cohort.type == M.CohortType.DIMENSION:
                search_query = self.convert_dimension_search_query(
                    SE.search_query_from_dict(json.loads(cohort.metric_json))
                )

                filter_value = M.DimensionCollection(
                    id=cohort.id,
                    search_query=search_query,
                )
            else:
                raise ValueError(f"Unknown cohort type: {cohort.type}")

        elif isinstance(filter.left, M.DimensionIDFieldPath):
            if event_name_path is None:
                raise ValueError(
                    "Event name path is required to resolve dimension id field path"
                )
            event_def = self.defs_for_metric.get_event_def(event_name_path)
            edt = event_def.event_data_table
            id_field = edt.foreign_keys.get(filter.left.entity)

            if id_field is None:
                raise MissingDimensionForeignKeyError(event_table_name=edt.table_name)

            field_def = M.EventFieldDef.create_empty_field_def_from_field(
                id_field, event_name_path
            )
            filter_value = filter.right

        else:
            field_def = self.defs_for_metric.get_field_def(filter.left)
            filter_value = self._collect_filter_values(
                filter.right, field_def.field.type
            )

        return M.EventFilter(
            left=field_def, operator=filter.operator, right=filter_value
        )

    def _fix_metric_aggs_for_cohort_filter(self, cohort_metric: M.Metric) -> M.Metric:
        if cohort_metric.type == M.MetricType.SEGMENTATION:
            for seg in cohort_metric.segments:
                if seg.aggregation is None:
                    # Shouldn't happen
                    raise ValueError("Segment aggregation is missing")

                elif seg.aggregation.type in (
                    M.AggType.COUNT_SUBSCRIBERS,
                    M.AggType.RETURN_SUBSCRIBERS,
                    M.AggType.TOTAL_MRR,
                ):
                    seg.aggregation.type = M.AggType.RETURN_SUBSCRIBERS
                else:
                    seg.aggregation.type = M.AggType.RETURN_UNIQUE_USERS
        else:
            cohort_metric.config.aggregation = M.AggregationConfig(
                M.AggType.RETURN_UNIQUE_USERS
            )
        return cohort_metric

    @classmethod
    def _convert_strs_to_correct_type(
        cls, data_type: M.DataType, string_value: str
    ) -> Any:
        if data_type == M.DataType.BOOL:
            lower_string = string_value.lower()
            if lower_string not in ["true", "false"]:
                raise ValueError(f"Cannot parse '{string_value}' as boolean")
            return lower_string == "true"
        if data_type == M.DataType.NUMBER:
            return int(string_value) if string_value.isdigit() else float(string_value)
        if data_type == M.DataType.STRING:
            return string_value
        if data_type == M.DataType.ARRAY:
            return string_value
        if data_type != M.DataType.DATETIME:
            raise Exception(f"Unsupported parsing for type: {data_type.name}.")
        try:
            return parser.parse(string_value)
        except parser.ParserError as exc:
            raise ValueError(f"Cannot parse '{string_value}' as datetime") from exc

    def convert_dimension_search_query(
        self, search_query: WM.WebappDimensionsSearchQuery
    ) -> M.DimensionsSearchQuery:
        dim_fields = [
            d
            for d in self.defs_for_metric.get_all_dimension_fields()
            if d.dimension_data_table.entity == search_query.entity
        ]

        tokens = search_query.search_text.split() if search_query.search_text else []

        fields_for_search_and_select = []
        if len(tokens) > 0:
            fields_for_search_and_select = dim_fields
        else:
            fields_for_search_and_select = [
                self.defs_for_metric.get_dimension_field_def(path)
                for path in search_query.used_fields()
            ]

        return M.DimensionsSearchQuery(
            search_tokens=tokens,
            search_field_defs=fields_for_search_and_select,
            select_field_defs=fields_for_search_and_select,
            filters=[
                self.convert_event_filter(filter, event_name_path=None)
                for filter in search_query.filters
            ],
            limit=500,
        )
