import base64
from datetime import datetime

from lxml import etree

from mitzu.logger import LOGGER

SVG_IMAGE_HEADER = "data:image/svg+xml;base64,"


def update_svg_for_miro(base64_encoded_image: str, last_updated: datetime) -> str:
    if not base64_encoded_image.startswith(SVG_IMAGE_HEADER):
        return base64_encoded_image
    try:
        header_length = len(SVG_IMAGE_HEADER)
        svg_src = base64.b64decode(base64_encoded_image[header_length:])
        root = etree.XML(svg_src)
        root.insert(0, etree.XML('<rect width="100%" height="100%" fill="white"/>'))
        viewbox = (
            root.attrib["viewBox"].decode("utf-8")
            if isinstance(root.attrib["viewBox"], bytes)
            else root.attrib["viewBox"]
        )
        height = int(viewbox.split(" ")[3])
        root.insert(
            1,
            etree.XML(
                """<style>
.small {
    font: lighter 11px sans-serif;
    fill: black;
}
</style>"""
            ),
        )
        root.insert(
            2,
            etree.XML(
                f'<text x="4" y="{height - 4}" class="small">Chart generated at: {last_updated.strftime("%Y-%m-%d %H:%M:%S%Z")} UTC</text>'
            ),
        )

        return SVG_IMAGE_HEADER + base64.b64encode(etree.tostring(root)).decode("utf-8")
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update svg image for miro")
        return base64_encoded_image


def get_error_image(message: str) -> str:
    svg_content = f"""<svg width="300" height="300" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="100%" height="100%" fill="white"/>
<style>
.error {{
    font: bold 11px sans-serif;
    fill: red;
}}
</style>
<text text-anchor="middle" x="50%" y="50%" class="error">{message}</text>
</svg>"""
    return SVG_IMAGE_HEADER + base64.b64encode(svg_content.encode("utf-8")).decode(
        "utf-8"
    )
