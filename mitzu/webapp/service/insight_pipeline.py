from __future__ import annotations

from typing import Optional

import pandas as pd

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.plot as PLT
import mitzu.webapp.model as WM
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.adapter_factory as AF
import mitzu.webapp.storage.project_storage as PS
from mitzu import configs
from mitzu.cache import MitzuCache
from mitzu.visualization.common import SimpleChart
from mitzu.visualization.visualizer import Visualizer
from mitzu.webapp.metric_context import MetricContext
from mitzu.webapp.service.adapter_service import (
    MetricOrigin,
    get_metric_result_df,
)
from mitzu.webapp.service.catalog_service import (
    CatalogService,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.service.tracking_service import (
    TrackingContext,
    TrackingService,
)


class InsightPipeline:
    def __init__(
        self,
        webapp_metric: WM.WebappMetric,
        user_role: WM.Role,
        source_url: str,
        metric_origin: MetricOrigin,
        project_storage: PS.ProjectScopedStorage,
        cache: MitzuCache,
        tracking_context: TrackingContext,
        adapter_context: GA.AdapterContext,
    ) -> None:
        self.webapp_metric = webapp_metric
        self.project_storage = project_storage
        self.project = project_storage.get_project()
        self.cache = cache
        self.catalog_service = CatalogService(self.project_storage, self.cache)
        self.tracking_service = TrackingService(tracking_context)
        self.tracking_service.set_project(self.project)
        self.adapter_context = adapter_context
        self.user_role = user_role
        self.source_url = source_url
        self.metric_origin = metric_origin
        self.adapter = AF.create_adapter_from_project(
            self.project,
            self.cache,
            self.adapter_context,
            user_role=user_role,
        )

        self._metric_context: Optional[MetricContext] = None
        self._metric_updater: Optional[MetricUpdater] = None
        self._metric_converter: Optional[MetricConverter] = None
        self._result_df: Optional[pd.DataFrame] = None
        self._metric: Optional[M.Metric] = None
        self._simple_chart: Optional[SimpleChart] = None

    def get_metric_context(self) -> MetricContext:
        if self._metric_context is None:
            self._metric_context = self.catalog_service.get_metric_context(
                self.webapp_metric
            )
        return self._metric_context

    def get_metric_updater(self) -> MetricUpdater:
        if self._metric_updater is None:
            self._metric_updater = MetricUpdater(
                self.get_metric_context(), self.project.insight_settings
            )
        return self._metric_updater

    def get_metric_converter(self) -> MetricConverter:
        if self._metric_converter is None:
            metric_defs = self.project_storage.get_definitions_for_metric(
                self.webapp_metric
            )
            self._metric_converter = MetricConverter(
                metric_defs, self.project_storage, self.get_metric_updater()
            )
        return self._metric_converter

    def get_metric(self) -> M.Metric:
        if self._metric is None:
            metric_converter = self.get_metric_converter()
            self._metric = metric_converter.convert_metric(self.webapp_metric)

        return self._metric

    def get_result_df(self) -> pd.DataFrame:
        if self._result_df is None:

            hash_key = SE.get_metric_cache_key(self.webapp_metric, self.project)
            result_df, _ = get_metric_result_df(
                adapter=self.adapter,
                hash_key=hash_key,
                metric=self.get_metric(),
                webapp_metric=self.webapp_metric,
                mitzu_cache=self.cache,
                metric_origin=self.metric_origin,
                tracking_service=self.tracking_service,
                href=self.source_url,
            )
            self._result_df = result_df

        return self._result_df

    def get_simple_chart(
        self,
    ) -> SimpleChart:
        if self._simple_chart is None:
            visualizer = Visualizer.create_visualizer(
                self.get_result_df(),
                self.webapp_metric,
                self.project,
                self.get_metric_context(),
                self.get_metric(),
            )
            self._simple_chart = visualizer.generate_chart()
        return self._simple_chart

    def get_base64_encoded_image(self) -> str:
        # We don't render annotations for stored insights
        fig, _ = PLT.plot_chart(
            self.get_simple_chart(),
            self.get_metric(),
            self.get_metric_context(),
            annotations=[],
            webapp_metric=self.webapp_metric,
            default_end_dt=self.project.get_default_end_dt(),
        )

        return PLT.figure_to_base64_image(
            fig, 0.5, kaleid_configs=configs.get_kaleido_configs()
        )
