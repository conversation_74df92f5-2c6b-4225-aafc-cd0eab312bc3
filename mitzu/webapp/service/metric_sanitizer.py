from dataclasses import dataclass, replace
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union, cast

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.constants as CONST
from mitzu.formula import validate_formula_string


def is_subsegment_break_down_available(segment: WM.WebappSegment) -> bool:
    if isinstance(segment, WM.WebappSimpleSegment):
        return False
    elif isinstance(segment, WM.WebappComplexSegment):
        return segment.operator == M.BinaryOperator.OR
    else:
        raise ValueError(f"Unexpected webapp segment: {segment}")


def get_available_agg_types(
    metric_type: M.MetricType,
    is_revenue_segment: bool = False,
) -> List[Tuple[M.AggType, Optional[int]]]:
    if metric_type == M.MetricType.CONVERSION:
        res: List[Tuple[M.AggType, Optional[int]]] = [
            (M.AggType.CONVERSION, None),
            (M.AggType.COUNT_CONVERTED_USERS, None),
            (M.AggType.BY_EVENT_PROPERTY, None),
            (M.AggType.AVERAGE_TIME_TO_CONV, None),
        ]
        res.extend(
            [
                (M.AggType.PERCENTILE_TIME_TO_CONV, val)
                for val in CONST.SUPPORTED_PERCENTILES
            ]
        )
        return res
    elif metric_type == M.MetricType.RETENTION:
        return [
            (M.AggType.RETENTION_RATE, None),
            (M.AggType.BY_EVENT_PROPERTY, None),
        ]
    elif metric_type == M.MetricType.SEGMENTATION:
        res = [
            (M.AggType.COUNT_UNIQUE_USERS, None),
            (M.AggType.COUNT_EVENTS, None),
            (M.AggType.BY_EVENT_PROPERTY, None),
            (M.AggType.FREQUENCY, None),
        ]
        if is_revenue_segment:
            res.extend(
                [
                    (M.AggType.TOTAL_MRR, None),
                    (M.AggType.COUNT_SUBSCRIBERS, None),
                ]
            )
        return res
    elif metric_type == M.MetricType.JOURNEY:
        return [(M.AggType.CONVERSION, None)]

    raise ValueError(f"Unknown metric type: {metric_type}")


@dataclass(frozen=True)
class MetricSanitizer:
    metric_context: MC.MetricContext

    def sanitize_metric_on_event_change(
        self, webapp_metric: WM.WebappMetric
    ) -> WM.WebappMetric:
        new_segments = []
        available_field_paths: Dict[str, M.FieldPath] = {}

        for segment in webapp_metric.segments:
            selected_events = segment.get_event_name_paths()
            segment_field_paths: Dict[str, M.FieldPath] = {}

            if is_subsegment_break_down_available(segment):
                segment_field_paths[
                    M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH.field_name
                ] = M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH

            for event_path in selected_events:
                segment_field_paths.update(
                    {
                        field_path.field_name: field_path
                        for field_path in self.metric_context.get_event_fields(
                            event_path
                        )
                    }
                )
            available_field_paths.update(segment_field_paths)

            if segment.aggregation is not None:
                segment = replace(
                    segment,
                    aggregation=self._fix_fields_in_aggregation(
                        segment_field_paths, segment.aggregation
                    ),
                )

            segment = replace(
                segment,
                group_by=self._fix_group_by_fields(
                    segment_field_paths, segment.group_by
                ),
            )

            new_segments.append(segment)

        if webapp_metric.metric_type != M.MetricType.SEGMENTATION:
            metric_config = replace(
                webapp_metric.config,
                aggregation=self._fix_fields_in_aggregation(
                    available_field_paths, webapp_metric.config.aggregation
                ),
            )
        else:
            metric_config = webapp_metric.config

        if isinstance(webapp_metric.config.unique_field, M.EventFieldPath):
            metric_config = replace(
                metric_config,
                unique_field=self._fix_unique_field_property(
                    available_field_paths, webapp_metric.config.unique_field
                ),
            )

        if webapp_metric.config.formula is not None:
            try:
                validate_formula_string(
                    webapp_metric.config.formula, len(webapp_metric.segments)
                )
            except Exception:
                metric_config = replace(metric_config, formula=None)

        return replace(
            webapp_metric,
            segments=new_segments,
            config=metric_config,
        )

    def _fix_fields_in_aggregation(
        self,
        available_field_paths: Dict[str, M.FieldPath],
        aggregation: WM.WebappAggregationConfig,
    ) -> WM.WebappAggregationConfig:
        agg_type = aggregation.type
        agg_param = aggregation.conv_param

        if agg_type == M.AggType.BY_EVENT_PROPERTY:
            if aggregation.event_property_agg is None:
                raise ValueError("Event property aggregation is not configured")

            ep_agg = aggregation.event_property_agg.agg_type
            field_path = aggregation.event_property_agg.field_def

            if isinstance(field_path, M.DimensionFieldPath) or field_path is None:
                return aggregation

            if field_path.field_name in available_field_paths.keys():
                agg_field_path = available_field_paths[field_path.field_name]
            else:
                agg_field_path = None

            event_property_agg = WM.WebappEventPropertyAggregation(
                field_def=agg_field_path,
                agg_type=ep_agg,
            )
            return WM.WebappAggregationConfig(
                agg_type,
                conv_param=agg_param,
                event_property_agg=event_property_agg,
            )
        else:
            return aggregation

    def _fix_group_by_fields(
        self,
        available_field_paths: Dict[str, M.FieldPath],
        group_by_fields: List[M.FieldPath],
    ) -> List[M.FieldPath]:
        result: List[M.FieldPath] = []
        for field in group_by_fields:
            if isinstance(field, M.DimensionFieldPath):
                result.append(field)
                continue

            if field.field_name in available_field_paths.keys():
                result.append(available_field_paths[field.field_name])

        return result

    def _fix_unique_field_property(
        self,
        available_field_paths: Dict[str, M.FieldPath],
        selected_unique_field: M.EventFieldPath,
    ) -> Union[M.Entity, M.EventFieldPath]:
        # Fallback to the user_id
        result: Union[M.Entity, M.EventFieldPath] = M.USER_ENTITY
        if not isinstance(selected_unique_field, M.EventFieldPath):
            raise ValueError("Selected unique field is not an EventFieldPath.")
        if selected_unique_field.field_name in available_field_paths.keys():
            result = cast(
                M.EventFieldPath,
                available_field_paths[selected_unique_field.field_name],
            )
        return result

    def enforce_defaults_on_retention_metrics(
        self, webapp_metric: WM.WebappMetric, insight_settings: M.InsightSettings
    ) -> WM.WebappMetric:
        if webapp_metric.metric_type != M.MetricType.RETENTION:
            return webapp_metric

        segments = webapp_metric.segments
        if len(segments) > 2:
            segments = segments[0:2]
        if (
            len(segments) > 0
            and webapp_metric.config.chart_type == M.SimpleChartType.HEATMAP
        ):
            segments[0].group_by.clear()

        if len(webapp_metric.segments) == 0:
            webapp_metric = replace(
                webapp_metric,
                config=replace(
                    webapp_metric.config,
                    cut_off_incomplete_data=insight_settings.cut_off_incomplete_data,
                ),
            )

        return replace(webapp_metric, segments=segments)

    def enforce_defaults_on_journey_metrics(
        self, webapp_metric: WM.WebappMetric
    ) -> WM.WebappMetric:
        if webapp_metric.metric_type != M.MetricType.JOURNEY:
            return webapp_metric

        config = replace(
            webapp_metric.config,
            time_group=M.TimeGroup.TOTAL,
            conv_window=M.TimeWindow(1, M.TimeGroup.DAY)
            if webapp_metric.config.conv_window is None
            else webapp_metric.config.conv_window,
            conv_attribution=M.ConversionAttribution.FIRST_EVENT,
            aggregation=WM.WebappAggregationConfig(
                type=M.AggType.CONVERSION,
                conv_param=None,
                event_property_agg=None,
            ),
            chart_type=M.SimpleChartType.SANKEY,
            use_consistent_coloring=False,  # this is not supported
        )

        new_segments = []

        for index, segment in enumerate(webapp_metric.segments):
            segment_filter = (
                M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW if index == 0 else None
            )
            new_segments.append(replace(segment, filter=segment_filter))

        return replace(
            webapp_metric,
            config=config,
            segments=new_segments,
        )

    def restore_default_aggregation_type(
        self, webapp_metric: WM.WebappMetric
    ) -> WM.WebappMetric:
        segments = webapp_metric.segments

        available_agg_types = get_available_agg_types(webapp_metric.metric_type, False)

        main_agg_type, main_agg_param = (
            webapp_metric.config.aggregation.type,
            webapp_metric.config.aggregation.conv_param,
        )
        if (main_agg_type, main_agg_param) not in available_agg_types:
            main_agg_type, main_agg_param = available_agg_types[0]

        segments = webapp_metric.segments
        if webapp_metric.metric_type == M.MetricType.SEGMENTATION:
            new_segments = []
            for segment in segments:
                if segment.aggregation is None:
                    # partially created segment
                    new_segments.append(segment)
                    continue

                segment_agg, segment_agg_param = (
                    segment.aggregation.type,
                    segment.aggregation.conv_param,
                )

                seg_available = get_available_agg_types(
                    webapp_metric.metric_type,
                    is_revenue_segment=self.metric_context.revenue_settings is not None
                    and self.metric_context.revenue_settings.revenue_event
                    in segment.get_event_name_paths(),
                )
                if (segment_agg, segment_agg_param) not in seg_available:
                    segment_agg, segment_agg_param = seg_available[0]

                new_segments.append(
                    replace(
                        segment,
                        aggregation=replace(
                            segment.aggregation,
                            type=segment_agg,
                            conv_param=segment_agg_param,
                        ),
                    )
                )
            segments = new_segments

        return replace(
            webapp_metric,
            config=replace(
                webapp_metric.config,
                aggregation=replace(
                    webapp_metric.config.aggregation,
                    type=main_agg_type,
                    conv_param=main_agg_param,
                ),
            ),
            segments=segments,
        )

    def reset_time_window_interval(
        self, webapp_metric: WM.WebappMetric
    ) -> WM.WebappMetric:

        if (
            webapp_metric.metric_type == M.MetricType.RETENTION
            and webapp_metric.config.time_group == M.TimeGroup.TOTAL
        ):
            period = (
                webapp_metric.config.retention_window.period
                if webapp_metric.config.retention_window is not None
                else M.TimeGroup.DAY
            )
            return replace(
                webapp_metric,
                config=replace(
                    webapp_metric.config, retention_window=M.TimeWindow(1, period)
                ),
            )
        return webapp_metric

    def set_default_retention_indices(
        self, webapp_metric: WM.WebappMetric
    ) -> WM.WebappMetric:

        if webapp_metric.metric_type == M.MetricType.RETENTION and (
            webapp_metric.config.retention_indices is None
            or len(webapp_metric.config.retention_indices) == 0
        ):
            default_indices = (
                CONST.RETENTION_INDICES_ALL_GROUPS
                if webapp_metric.config.time_window_chooser_config.time_group
                == M.TimeGroup.TOTAL
                else 1
            )
            return replace(
                webapp_metric,
                config=replace(
                    webapp_metric.config, retention_indices=[default_indices]
                ),
            )
        return webapp_metric

    def correct_retention_indices(
        self, webapp_metric: WM.WebappMetric, default_end_dt: datetime
    ) -> WM.WebappMetric:

        if webapp_metric.metric_type == M.MetricType.RETENTION and (
            webapp_metric.config.retention_indices is not None
        ):
            all_indices = webapp_metric.config.get_retention_indices(default_end_dt)
            all_indices.insert(0, CONST.RETENTION_INDICES_ALL_GROUPS)
            available_indices = [
                i for i in webapp_metric.config.retention_indices if i in all_indices
            ]
            return replace(
                webapp_metric,
                config=replace(
                    webapp_metric.config, retention_indices=sorted(available_indices)
                ),
            )
        return webapp_metric
