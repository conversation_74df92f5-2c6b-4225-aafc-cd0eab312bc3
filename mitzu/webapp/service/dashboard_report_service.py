import base64
from dataclasses import dataclass
from enum import Enum
from io import BytesIO
from typing import Dict, List, Optional

import markdown
import plotly.graph_objects as go
from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML

from mitzu.webapp.service.email_service import (
    get_curr_folder,
)


class DashboardReportCardType(Enum):
    CHART = "chart"
    TEXT = "text"
    DIVIDER = "divider"


@dataclass(frozen=True)
class DashboardMetricReportCard:
    def to_dict(self) -> Dict[str, Optional[str] | bool]:
        raise NotImplementedError()

    @property
    def card_type(self) -> str:
        raise NotImplementedError()


@dataclass(frozen=True)
class DashboardReportChartCard(DashboardMetricReportCard):
    name: str
    description: str
    chart_b64: str

    @property
    def card_type(self) -> str:
        return DashboardReportCardType.CHART.value

    def to_dict(self) -> Dict[str, Optional[str] | bool]:
        return {
            "name": self.name,
            "description": self.description,
            "chart_b64": self.chart_b64,
            "card_type": self.card_type,
        }


@dataclass(frozen=True)
class DashboardReportTextCard(DashboardMetricReportCard):
    text: str

    @property
    def card_type(self) -> str:
        return DashboardReportCardType.TEXT.value

    def to_dict(self) -> Dict[str, Optional[str] | bool]:
        return {
            "text": self.text,
            "card_type": self.card_type,
        }


@dataclass(frozen=True)
class DashboardReportDividerCard(DashboardMetricReportCard):
    text: str

    @property
    def card_type(self) -> str:
        return DashboardReportCardType.DIVIDER.value

    def to_dict(self) -> Dict[str, Optional[str] | bool]:
        return {
            "text": self.text,
            "card_type": self.card_type,
        }


def markdown_to_html(md: str) -> str:
    return markdown.markdown(md)


def generate_html_from_dashboard(
    dashboard_name: str, cards: List[DashboardMetricReportCard]
) -> str:
    env = Environment(loader=FileSystemLoader(f"{get_curr_folder()}/../private_assets"))
    template = env.get_template("report_template.html")
    html = template.render(
        dashboard={"name": dashboard_name}, cards=[c.to_dict() for c in cards]
    )
    return html


def generate_pdf_report(html: str) -> bytes:
    buffer = BytesIO()
    HTML(string=html, base_url="/").write_pdf(buffer)
    buffer.seek(0)
    return buffer.read()


def chart_to_base64(fig: go.Figure) -> str:
    buf = BytesIO()
    fig.write_image(buf, format="png", width=700, height=400, scale=2)
    buf.seek(0)
    image_data = base64.b64encode(buf.read()).decode("utf-8")
    return image_data
