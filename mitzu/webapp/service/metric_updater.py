from copy import copy
from dataclasses import replace
from datetime import datetime, timedelta
from enum import Enum, auto
from typing import Any, List, Optional, Tuple

import dateutil.parser as DP
from dateutil import parser

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
from mitzu.constants import EMPTY_VALUE, N_PER_A


class ExploreJourneyDirection(Enum):
    FURTHER = auto()
    PREVIOUS = auto()


class CustomEventNotFound(Exception):
    def __init__(self, custom_event_id: str):
        super().__init__(f"Custom event not found (id: {custom_event_id})")
        self.custom_event_id = custom_event_id


class CustomEventFiltersResultNoEvents(Exception):
    def __init__(self) -> None:
        super().__init__("Filtered custom event does not result any events")


class CannotApplyDashboardFilter(Exception):
    def __init__(self, event_display_name: str, field_display_name: str):
        super().__init__(
            f"{event_display_name} event cannot be filtered on {field_display_name}"
        )


class MetricUpdater:
    def __init__(
        self, metric_context: MC.MetricContext, insight_settings: M.InsightSettings
    ):
        self.metric_context = metric_context
        self.insight_settings = insight_settings

    @classmethod
    def _parse_string_values(cls, data_type: M.DataType, string_value: str) -> Any:
        if data_type == M.DataType.BOOL:
            lower_string = string_value.lower()
            if lower_string not in ["true", "false"]:
                raise ValueError(f"Cannot parse '{string_value}' as boolean")
            return lower_string == "true"
        if data_type == M.DataType.NUMBER:
            return int(string_value) if string_value.isdigit() else float(string_value)
        if data_type == M.DataType.STRING:
            return string_value
        if data_type == M.DataType.ARRAY:
            return [string_value]
        if data_type != M.DataType.DATETIME:
            raise Exception(f"Unsupported parsing for type: {data_type.name}.")
        try:
            return parser.parse(string_value)
        except parser.ParserError as exc:
            raise ValueError(f"Cannot parse '{string_value}' as datetime") from exc

    def add_or_update_segment_filter(
        self,
        segment: WM.WebappSegment,
        field_path: M.FieldPath,
        operator: M.Operator,
        value: Any,
    ) -> WM.WebappSegment:
        if isinstance(segment, WM.WebappSimpleSegment):
            mapped_field_path = self._get_matching_field_path(segment.event, field_path)
            if mapped_field_path is None:
                return copy(segment)

            fixed_value = self._fix_filter_value(field_path, operator, value)

            if (
                segment.event_filter is not None
                and segment.event_filter.left != mapped_field_path
            ):
                return WM.WebappComplexSegment(
                    group_by=segment.group_by,
                    aggregation=segment.aggregation,
                    filter=segment.filter,
                    title=segment.title,
                    left=segment.clear_top_level_configs(),
                    operator=M.BinaryOperator.AND,
                    right=replace(
                        WM.WebappSimpleSegment.create(
                            segment.event,
                            WM.WebappEventFilter(
                                mapped_field_path, operator, fixed_value
                            ),
                        ),
                        expanded_custom_event_key=segment.expanded_custom_event_key,
                    ),
                    expanded_custom_event_key=segment.expanded_custom_event_key,
                )

            return replace(
                segment,
                event_filter=WM.WebappEventFilter(
                    mapped_field_path, operator, fixed_value
                ),
            )

        if isinstance(segment, WM.WebappComplexSegment):
            if segment.operator == M.BinaryOperator.AND:
                mapped_field_path = self._get_matching_field_path(
                    next(iter(segment.get_event_name_paths())), field_path
                )
                if mapped_field_path is None:
                    return copy(segment)
                fixed_value = self._fix_filter_value(field_path, operator, value)

                return WM.WebappComplexSegment(
                    group_by=segment.group_by,
                    aggregation=segment.aggregation,
                    filter=segment.filter,
                    title=segment.title,
                    left=segment.clear_top_level_configs(),
                    operator=M.BinaryOperator.AND,
                    right=replace(
                        WM.WebappSimpleSegment.create(
                            next(iter(segment.left.get_event_name_paths())),
                            WM.WebappEventFilter(
                                mapped_field_path, operator, fixed_value
                            ),
                        ),
                        expanded_custom_event_key=segment.expanded_custom_event_key,
                    ),
                    expanded_custom_event_key=segment.expanded_custom_event_key,
                )
            elif segment.operator == M.BinaryOperator.OR:
                return WM.WebappComplexSegment(
                    group_by=segment.group_by,
                    aggregation=segment.aggregation,
                    filter=segment.filter,
                    title=segment.title,
                    left=self.add_or_update_segment_filter(
                        segment.left, field_path, operator, value
                    ),
                    operator=M.BinaryOperator.OR,
                    right=self.add_or_update_segment_filter(
                        segment.right, field_path, operator, value
                    ),
                    expanded_custom_event_key=segment.expanded_custom_event_key,
                )

            else:
                raise ValueError(f"Unknown operator: {segment.operator}")

        raise ValueError(f"Unknown segment type {segment}")

    def _get_matching_field_path(
        self, event_path: M.EventNamePath, field_path: M.FieldPath
    ) -> Optional[M.FieldPath]:
        if isinstance(field_path, M.EventFieldPath):
            return self.metric_context.get_similar_field_for_event(
                event_path, field_path
            )

        return field_path

    def _fix_filter_value(
        self, field_path: M.FieldPath, operator: M.Operator, value: Any
    ) -> Optional[Any]:
        if isinstance(field_path, (M.CollectionFieldPath, M.DimensionIDFieldPath)):
            return value

        property_meta = self.metric_context.get_property_meta(field_path)
        if value is None or operator.is_unary_operator():
            return None

        if operator.supports_multiple_operands():
            if isinstance(value, list):
                res = [
                    MetricUpdater._parse_string_values(property_meta.property_type, val)
                    for val in value
                ]
                # in case of arrays the parse string values returns an array resulting an array of arrays
                # I can't have a better fix right now :(
                if len(res) > 0 and isinstance(res[0], list):
                    return [t[0] for t in res]
                return res
            else:
                return [
                    MetricUpdater._parse_string_values(
                        property_meta.property_type, value
                    )
                ]
        else:
            if isinstance(value, list):
                if len(value) == 1:
                    return value[0]
                raise ValueError("Too many arguments for a binary operator")
            return value

    def drill_down(
        self,
        metric: WM.WebappMetric,
        custom_json: WM.TooltipCustomJson,
        selected_date: datetime,
    ) -> WM.WebappMetric:
        if metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL:
            raise ValueError("Metric with total time group cannot be drilled down")

        metric_config = self._get_drill_down_metric_config(
            metric.config, selected_date, metric.metric_type, custom_json
        )
        if metric.config.formula is not None:
            return replace(
                metric,
                config=metric_config,
                segments=copy(metric.segments),
            )

        new_segments = self._filter_segments(
            metric, custom_json, cutoff_at_current_funnel_step=False
        )

        return replace(
            metric,
            config=metric_config,
            segments=new_segments,
        )

    def _get_metric_time_window(
        self, config: WM.WebappMetricConfig
    ) -> Tuple[datetime, datetime]:
        end_dt = (
            config.time_window_chooser_config.end_dt
            or self.insight_settings.get_end_date()
        )
        if config.time_window_chooser_config.start_dt is not None:
            start_dt = config.time_window_chooser_config.start_dt
        elif config.time_window_chooser_config.lookback_days is not None:
            start_dt = (
                end_dt
                - config.time_window_chooser_config.lookback_days.to_relative_delta()
            )
        elif config.time_window_chooser_config.calendar_date_range is not None:
            (
                start_dt,
                _,
            ) = config.time_window_chooser_config.calendar_date_range.to_start_and_end_date(
                now=end_dt
            )
        elif config.time_window_chooser_config.since_date is not None:
            start_dt = config.time_window_chooser_config.since_date
            end_dt = self.insight_settings.get_end_date()
        else:
            raise ValueError(
                "Could not get start date; start_dt, lookback_days, or calendar_date_range must be set"
            )

        return start_dt, end_dt

    def _get_drill_down_metric_config(
        self,
        config: WM.WebappMetricConfig,
        selected_date: datetime,
        metric_type: M.MetricType,
        custom_json: WM.TooltipCustomJson,
    ) -> WM.WebappMetricConfig:
        start_dt = selected_date
        end_dt = (
            start_dt
            + M.TimeWindow(
                1, config.time_window_chooser_config.time_group
            ).to_relative_delta()
            - timedelta(days=1)
        )

        retention_indices = None
        if (
            metric_type == M.MetricType.RETENTION
            and custom_json.retention_index is not None
        ):
            retention_indices = [custom_json.retention_index]

        return replace(
            config,
            time_window_chooser_config=replace(
                config.time_window_chooser_config,
                start_dt=start_dt,
                end_dt=end_dt,
                time_group=M.TimeGroup.TOTAL,
            ),
            chart_type=M.SimpleChartType.BAR,
            retention_indices=retention_indices,
        )

    def _get_segments_in_order(
        self, segment: WM.WebappSegment
    ) -> List[WM.WebappSegment]:
        if isinstance(segment, WM.WebappSimpleSegment):
            return [segment]
        elif isinstance(segment, WM.WebappComplexSegment):
            if segment.operator == M.BinaryOperator.AND:
                return [segment]
            else:
                return self._get_segments_in_order(
                    segment.left
                ) + self._get_segments_in_order(segment.right)
        else:
            raise ValueError(f"Unknown segment type: {segment}")

    def _select_subsegment(
        self,
        break_down_value: str,
        segment: WM.WebappSegment,
        break_down_values: List[str],
    ) -> Tuple[WM.WebappSegment, List[str]]:
        if (
            not isinstance(segment, WM.WebappComplexSegment)
            or segment.operator != M.BinaryOperator.OR
        ):
            raise ValueError(f"Unexpected segment structure {segment}")

        _, event_index = [
            int(val)
            for val in break_down_value.replace(f"{GA.SUBSEGMENT_INDEX}_", "").split(
                "_"
            )
        ]

        segments = self._get_segments_in_order(segment)
        new_segment = segments[event_index]

        group_by_fields = []
        group_by_values = []
        if len(new_segment.get_event_name_paths()) != 1:
            raise ValueError(
                f"Unexpected amount of events found in the segment {new_segment}"
            )

        event = list(new_segment.get_event_name_paths())[0]

        for gb_field, gb_value in zip(segment.group_by, break_down_values):
            if gb_field == M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH:
                continue
            matching_field = self._get_matching_field_path(event, gb_field)
            if matching_field is not None:
                group_by_fields.append(matching_field)
                group_by_values.append(gb_value)

        return (
            replace(
                new_segment,
                aggregation=segment.aggregation,
                title=segment.title,
                filter=segment.filter,
                group_by=group_by_fields,
            ),
            group_by_values,
        )

    def _filter_on_group_by_fields(
        self, segment: WM.WebappSegment, break_down_values: List[str]
    ) -> WM.WebappSegment:
        for field, value_str in zip(segment.group_by, break_down_values):
            if field != M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH:
                continue
            segment, break_down_values = self._select_subsegment(
                value_str, segment, break_down_values
            )

        for field, value_str in zip(segment.group_by, break_down_values):
            if value_str == N_PER_A:
                value = None
                operator = M.Operator.IS_NULL
            elif value_str == EMPTY_VALUE:
                value = ""
                operator = M.Operator.ANY_OF
            else:
                operator = M.Operator.ANY_OF
                value = value_str

            segment = self.add_or_update_segment_filter(segment, field, operator, value)
        return segment

    def see_trends(
        self,
        metric: WM.WebappMetric,
        custom_json: WM.TooltipCustomJson,
    ) -> WM.WebappMetric:
        if metric.config.time_window_chooser_config.time_group != M.TimeGroup.TOTAL:
            raise ValueError(
                "Trends can be calculated only for total time group metrics"
            )

        metric_config = self._get_see_trends_metric_config(
            metric.config, metric.metric_type, custom_json.retention_index
        )

        if metric.config.formula is not None:
            return replace(metric, config=metric_config, segments=copy(metric.segments))

        new_segments = self._filter_segments(
            metric, custom_json, cutoff_at_current_funnel_step=True
        )

        return replace(metric, config=metric_config, segments=new_segments)

    def create_filtered_metric(
        self,
        metric: WM.WebappMetric,
        custom_json: WM.TooltipCustomJson,
    ) -> WM.WebappMetric:
        new_segments = self._filter_segments(
            metric, custom_json, cutoff_at_current_funnel_step=False
        )
        return replace(metric, segments=new_segments)

    def _filter_segments(
        self,
        metric: WM.WebappMetric,
        custom_json: WM.TooltipCustomJson,
        cutoff_at_current_funnel_step: bool,
    ) -> list:
        new_segments = []
        for index, selected_segment in enumerate(metric.segments):
            if custom_json.break_down and (
                (
                    index == custom_json.segment_index
                    and metric.metric_type != M.MetricType.CONVERSION
                )
                or (
                    metric.metric_type == M.MetricType.CONVERSION
                    and selected_segment.group_by
                )
                or (
                    metric.metric_type == M.MetricType.RETENTION
                    and index == 0
                    and custom_json.break_down
                )
            ):
                selected_segment = self._filter_on_group_by_fields(
                    selected_segment, break_down_values=custom_json.break_down
                )

            if metric.metric_type == M.MetricType.SEGMENTATION:
                if index == custom_json.segment_index:
                    new_segments.append(selected_segment)
            elif metric.metric_type == M.MetricType.CONVERSION:
                if index > custom_json.segment_index and cutoff_at_current_funnel_step:
                    break
                new_segments.append(selected_segment)
            elif metric.metric_type == M.MetricType.RETENTION:
                new_segments.append(selected_segment)
            else:
                raise ValueError(f"Unexpected metric type {metric.metric_type}")

        return new_segments

    def _get_see_trends_metric_config(
        self,
        config: WM.WebappMetricConfig,
        metric_type: M.MetricType,
        retention_index: Optional[int],
    ) -> WM.WebappMetricConfig:
        use_set_dates = False
        if (
            config.time_window_chooser_config.start_dt is not None
            and config.time_window_chooser_config.end_dt is not None
        ):
            use_set_dates = True
            start_dt = config.time_window_chooser_config.start_dt - timedelta(days=14)
            end_dt = config.time_window_chooser_config.end_dt + timedelta(days=14)
        else:
            start_dt, end_dt = self._get_metric_time_window(config)

        days = (end_dt - start_dt).days
        if days <= 7:
            time_group = M.TimeGroup.HOUR
        elif days <= 62:
            time_group = M.TimeGroup.DAY
        else:
            time_group = M.TimeGroup.WEEK

        retention_indices = None
        if metric_type == M.MetricType.RETENTION and retention_index is not None:
            retention_indices = [retention_index]

        return replace(
            config,
            time_window_chooser_config=replace(
                config.time_window_chooser_config,
                start_dt=start_dt if use_set_dates else None,
                end_dt=end_dt if use_set_dates else None,
                time_group=time_group,
            ),
            chart_type=M.SimpleChartType.LINE,
            retention_indices=retention_indices,
        )

    def create_cohort_metric(
        self,
        metric: WM.WebappMetric,
        custom_json: WM.TooltipCustomJson,
        selected_date: Optional[datetime],
    ) -> WM.WebappMetric:

        new_segments = []
        for index, selected_segment in enumerate(metric.segments):
            if custom_json.break_down and (
                (
                    index == custom_json.segment_index
                    and metric.metric_type != M.MetricType.CONVERSION
                )
                or (
                    metric.metric_type
                    in [M.MetricType.CONVERSION, M.MetricType.RETENTION]
                    and selected_segment.group_by
                )
            ):
                selected_segment = self._filter_on_group_by_fields(
                    selected_segment, break_down_values=custom_json.break_down
                )

            if metric.metric_type == M.MetricType.SEGMENTATION:
                if index == custom_json.segment_index:
                    new_segments.append(selected_segment)
            elif metric.metric_type == M.MetricType.CONVERSION:
                if index > custom_json.segment_index:
                    break
                new_segments.append(selected_segment)
            elif metric.metric_type == M.MetricType.RETENTION:
                new_segments.append(selected_segment)
            else:
                raise ValueError(f"Unexpected metric type {metric.metric_type}")

        if metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL:

            retention_indices = None
            if (
                metric.metric_type == M.MetricType.RETENTION
                and custom_json.retention_index is not None
            ):
                retention_indices = [custom_json.retention_index]

            metric_config = replace(
                metric.config,
                retention_indices=retention_indices,
            )

            updated_metric = replace(
                metric,
                segments=new_segments,
                config=metric_config,
            )

        else:
            if selected_date is None:
                raise ValueError("Selected date can't be none for drill down")
            updated_metric = self.drill_down(metric, custom_json, selected_date)

        segments = []
        if updated_metric.metric_type == M.MetricType.SEGMENTATION:
            if len(updated_metric.segments) != 1:
                # there should be only one segment, else the drilldown did not change the segments (eg. formula)
                raise ValueError("Transfer metric can expects only a single segment")
            for segment in updated_metric.segments:
                segments.append(
                    replace(
                        segment,
                        group_by=[],
                    )
                )
        elif updated_metric.metric_type == M.MetricType.CONVERSION:
            segments = updated_metric.segments
        elif updated_metric.metric_type == M.MetricType.RETENTION:
            segments = updated_metric.segments
        else:
            raise ValueError(
                f"Cohort creation is not supported for {updated_metric.metric_type}"
            )
        if (
            metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL
            and metric.config.time_window_chooser_config.start_dt is not None
            and metric.config.time_window_chooser_config.end_dt is not None
        ):
            start_dt: Optional[
                datetime
            ] = metric.config.time_window_chooser_config.start_dt
            end_dt: Optional[datetime] = metric.config.time_window_chooser_config.end_dt
        else:
            start_dt = updated_metric.config.time_window_chooser_config.start_dt
            end_dt = updated_metric.config.time_window_chooser_config.end_dt

        metric_config = replace(
            updated_metric.config,
            time_window_chooser_config=replace(
                updated_metric.config.time_window_chooser_config,
                start_dt=start_dt,
                end_dt=end_dt,
                time_group=M.TimeGroup.TOTAL,
            ),
        )

        return replace(
            updated_metric,
            config=metric_config,
            segments=segments,
        )

    def return_users(
        self, metric: WM.WebappMetric, is_drop_off: bool
    ) -> WM.WebappMetric:
        aggregation = metric.segments[0].aggregation or metric.config.aggregation
        if aggregation.type in [
            M.AggType.TOTAL_MRR,
            M.AggType.COUNT_SUBSCRIBERS,
            M.AggType.RETURN_SUBSCRIBERS,
        ]:
            return self.__update_aggregation_type(metric, M.AggType.RETURN_SUBSCRIBERS)
        if is_drop_off:
            return self.__update_aggregation_type(
                metric, M.AggType.RETURN_DROP_OFF_USERS
            )
        return self.__update_aggregation_type(metric, M.AggType.RETURN_UNIQUE_USERS)

    def __update_aggregation_type(
        self, metric: WM.WebappMetric, agg_type: M.AggType
    ) -> WM.WebappMetric:
        new_config = replace(
            metric.config,
            aggregation=replace(
                metric.config.aggregation,
                type=agg_type,
                conv_param=None,
                event_property_agg=None,
            ),
        )
        if metric.metric_type == M.MetricType.SEGMENTATION:
            new_segments = []
            for segment in metric.segments:
                if segment.aggregation is None:
                    new_segments.append(segment)
                else:
                    new_segments.append(
                        replace(
                            segment,
                            aggregation=replace(
                                segment.aggregation,
                                type=agg_type,
                                conv_param=None,
                                event_property_agg=None,
                            ),
                        )
                    )

            return replace(
                metric,
                config=new_config,
                segments=new_segments,
            )

        return replace(
            metric,
            config=new_config,
        )

    def filter_on(
        self,
        metric: WM.WebappMetric,
        segment_index: int,
        filter_values: List[Any],
    ) -> WM.WebappMetric:
        new_segments = []
        for index, segment in enumerate(metric.segments):
            if index == segment_index:
                new_segments.append(
                    self._filter_on_group_by_fields(segment, filter_values)
                )
            else:
                new_segments.append(segment)

        return replace(metric, segments=new_segments)

    def apply_dashboard_filters(
        self,
        metric: WM.WebappMetric,
        dfilter: WM.DashboardFilter,
    ) -> WM.WebappMetric:
        time_config = dfilter.time_config
        if time_config:
            # dashboard filters do not update the the timegroup
            config = replace(
                metric.config,
                time_window_chooser_config=replace(
                    metric.config.time_window_chooser_config,
                    start_dt=time_config.start_dt,
                    end_dt=time_config.end_dt,
                    lookback_days=time_config.lookback_days,
                    calendar_date_range=time_config.calendar_date_range,
                    since_date=time_config.since_date,
                ),
            )
        else:
            config = metric.config

        all_events = set()
        for segment in metric.segments:
            all_events.update(segment.get_event_name_paths())

        for filter in dfilter.filters:
            if not isinstance(filter.left, M.EventFieldPath):
                continue

            for event_name_path in all_events:
                matching_field = self.metric_context.get_similar_field_for_event(
                    event_name_path, filter.left
                )
                if matching_field is None:
                    raise CannotApplyDashboardFilter(
                        self.metric_context.get_event_meta_or_default_values(
                            event_name_path
                        ).display_name,
                        self.metric_context.get_display_name(filter.left),
                    )

        return replace(
            metric,
            config=config,
            global_filters=metric.global_filters + dfilter.filters,
        )

    def return_events(
        self,
        metric: WM.WebappMetric,
        fields: List[M.FieldPath],
        segment_index: int,
        selected_date_str: str,
        break_down_values: Optional[List[str]],
    ) -> WM.WebappMetric:
        if (
            selected_date_str == ""
            or metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL
        ):
            start_dt = metric.config.time_window_chooser_config.start_dt
            end_dt = metric.config.time_window_chooser_config.end_dt
        else:
            start_dt = DP.parse(selected_date_str)
            end_dt = (
                start_dt
                + M.TimeWindow(
                    1, metric.config.time_window_chooser_config.time_group
                ).to_relative_delta()
                - timedelta(days=1)
            )

        segment = metric.segments[segment_index]
        if break_down_values is not None:
            segment = self._filter_on_group_by_fields(
                segment, break_down_values=break_down_values
            )
        if segment.aggregation is not None:
            segment = replace(
                segment,
                aggregation=WM.WebappAggregationConfig(M.AggType.COUNT_EVENTS),
            )

        new_segments = [
            replace(
                segment,
                group_by=[M.EVENT_TIME_FIELD_PATH]
                + (
                    [M.SUBSEGMENT_BREAK_DOWN_FIELD_PATH]
                    if isinstance(segment, WM.WebappComplexSegment)
                    else []
                )
                + fields,
            )
        ]

        return replace(
            metric,
            config=replace(
                metric.config,
                time_window_chooser_config=replace(
                    metric.config.time_window_chooser_config,
                    time_group=M.TimeGroup.TOTAL,
                    start_dt=start_dt,
                    end_dt=end_dt,
                ),
            ),
            segments=new_segments,
        )

    def _sanitize_custom_event(
        self, custom_segment: WM.WebappSegment, filters: List[WM.WebappEventFilter]
    ) -> Optional[WM.WebappSegment]:
        if isinstance(custom_segment, WM.WebappSimpleSegment):
            if any(
                [
                    self._get_matching_field_path(custom_segment.event, filter.left)
                    is None
                    for filter in filters
                ]
            ):
                # event does not contains all filters
                return None
            return custom_segment
        elif isinstance(custom_segment, WM.WebappComplexSegment):
            if custom_segment.operator == M.BinaryOperator.AND:
                event = list(custom_segment.get_event_name_paths())[0]
                if any(
                    [
                        self._get_matching_field_path(event, filter.left) is None
                        for filter in filters
                    ]
                ):
                    # event does not contains all filters
                    return None

            left = self._sanitize_custom_event(custom_segment.left, filters)
            right = self._sanitize_custom_event(custom_segment.right, filters)

            if left is not None and right is not None:
                return replace(
                    custom_segment,
                    left=left,
                    right=right,
                )
            elif left is not None:
                return left
            return right

        else:
            raise ValueError(f"unexpeced segment type: {custom_segment}")

    def resolve_custom_events(
        self, segment: WM.WebappSegment, custom_event_key: str
    ) -> WM.WebappSegment:
        if isinstance(segment, WM.WebappSimpleSegment):
            if isinstance(segment.event, M.CustomEventNamePath):
                custom_event = self._get_custom_event(
                    segment.event.event_name, custom_event_key + "s"
                )

                if segment.event_filter is not None:
                    custom_segment = self._sanitize_custom_event(
                        custom_event.segment, [segment.event_filter]
                    )
                    if custom_segment is None:
                        raise CustomEventFiltersResultNoEvents()
                    filter = segment.event_filter
                    custom_segment = self.add_or_update_segment_filter(
                        custom_segment,
                        filter.left,
                        filter.operator,
                        filter.right,
                    )
                else:
                    custom_segment = custom_event.segment

                return replace(
                    custom_segment,
                    group_by=segment.group_by,
                    filter=segment.filter,
                    title=segment.title,
                    aggregation=segment.aggregation,
                )
            else:
                return segment
        elif isinstance(segment, WM.WebappComplexSegment):
            if segment.operator == M.BinaryOperator.AND:
                event = list(segment.get_event_name_paths())[0]
                if isinstance(event, M.CustomEventNamePath):
                    custom_event = self._get_custom_event(
                        event.event_name, custom_event_key + "a"
                    )
                    custom_segment = self._sanitize_custom_event(
                        custom_event.segment, segment.get_event_filters()
                    )
                    if custom_segment is None:
                        raise CustomEventFiltersResultNoEvents()
                    for filter in segment.get_event_filters():
                        custom_segment = self.add_or_update_segment_filter(
                            custom_segment, filter.left, filter.operator, filter.right
                        )
                    return replace(
                        custom_segment,
                        group_by=segment.group_by,
                        filter=segment.filter,
                        title=segment.title,
                        aggregation=segment.aggregation,
                    )
                else:
                    # complex segment contains multiple filters for the same not custom event
                    # nothing to be resolved, just return it as it is
                    return segment
            else:
                return replace(
                    segment,
                    left=self.resolve_custom_events(
                        segment.left, custom_event_key + "l"
                    ),
                    right=self.resolve_custom_events(
                        segment.right, custom_event_key + "r"
                    ),
                )
        else:
            raise ValueError(f"Unexpected segment type: {segment}")

    def _get_custom_event(
        self, custom_event_id: str, custom_event_key: str
    ) -> WM.CustomEvent:
        custom_event = self.metric_context.get_custom_event(custom_event_id)
        if custom_event is None:
            raise CustomEventNotFound(custom_event_id)

        def flag_complex_segments_as_custom_event(
            segment: WM.WebappSegment,
        ) -> WM.WebappSegment:
            if isinstance(segment, WM.WebappSimpleSegment):
                return replace(
                    segment,
                    expanded_custom_event_key=custom_event_key,
                )
            elif isinstance(segment, WM.WebappComplexSegment):
                return replace(
                    segment,
                    left=flag_complex_segments_as_custom_event(segment.left),
                    right=flag_complex_segments_as_custom_event(segment.right),
                    expanded_custom_event_key=custom_event_key,
                )
            else:
                raise ValueError(f"Unknown webapp segment: {segment}")

        return replace(
            custom_event,
            segment=flag_complex_segments_as_custom_event(custom_event.segment),
        )

    @staticmethod
    def fix_cohort_backwards_compatible_agg_types_for_view_insight(
        cohort_metric: WM.WebappMetric,
    ) -> WM.WebappMetric:
        # Old cohorts were saved with RETURN_UNIQUE_USERS and RETURN_SUBSCRIBERS
        # Cohort view_insight button was failing for these old cohorts
        def updated_segment_agg_type(segment: WM.WebappSegment) -> M.AggType:
            if segment.aggregation is None or segment.aggregation.type is None:
                raise ValueError("Segment aggregation is missing")
            return {
                M.AggType.RETURN_UNIQUE_USERS: M.AggType.COUNT_UNIQUE_USERS,
                M.AggType.RETURN_SUBSCRIBERS: M.AggType.COUNT_SUBSCRIBERS,
            }.get(segment.aggregation.type, M.AggType.COUNT_UNIQUE_USERS)

        if cohort_metric.metric_type == M.MetricType.SEGMENTATION:
            updated_segments = []
            for segment in cohort_metric.segments:
                if segment.aggregation is None or segment.aggregation.type is None:
                    raise ValueError("Segment aggregation is missing")
                update_segment = replace(
                    segment,
                    aggregation=replace(
                        segment.aggregation, type=updated_segment_agg_type(segment)
                    ),
                )
                updated_segments.append(update_segment)

            return replace(cohort_metric, segments=updated_segments)

        aggregation_type = {
            M.MetricType.CONVERSION: M.AggType.CONVERSION,
            M.MetricType.JOURNEY: M.AggType.CONVERSION,
            M.MetricType.RETENTION: M.AggType.RETENTION_RATE,
        }.get(cohort_metric.metric_type)

        if aggregation_type:
            return replace(
                cohort_metric,
                config=replace(
                    cohort_metric.config,
                    aggregation=WM.WebappAggregationConfig(aggregation_type),
                ),
            )

        return cohort_metric

    def explore_journey_step(
        self,
        webapp_metric: WM.WebappMetric,
        segment_index: int,
        break_down_values: List[str],
        direction: ExploreJourneyDirection,
    ) -> WM.WebappMetric:
        explore_segment = replace(
            webapp_metric.segments[segment_index],
            filter=None
            if segment_index > 0 or direction == ExploreJourneyDirection.FURTHER
            else webapp_metric.segments[0].filter,
        )
        new_segments = []
        for index, segment in enumerate(webapp_metric.segments):
            filter = (
                webapp_metric.segments[0].filter
                if index == 0
                and (segment_index > 0 or direction == ExploreJourneyDirection.FURTHER)
                else None
            )
            if index == segment_index and len(break_down_values) > 0:
                new_segments.append(
                    replace(
                        self._filter_on_group_by_fields(segment, break_down_values),
                        filter=filter,
                    )
                )
            else:
                new_segments.append(
                    replace(
                        segment,
                        filter=filter,
                    )
                )

        if direction == ExploreJourneyDirection.FURTHER:
            new_segments.insert(segment_index + 1, explore_segment)
        else:
            new_segments.insert(segment_index, explore_segment)

        return replace(webapp_metric, segments=new_segments)

    def journey_show_trends(
        self,
        webapp_metric: WM.WebappMetric,
        segment_index: int,
        break_down_values: List[str],
    ) -> WM.WebappMetric:
        metric_config = self._get_see_trends_metric_config(
            webapp_metric.config, webapp_metric.metric_type, retention_index=None
        )

        return replace(
            webapp_metric,
            metric_type=M.MetricType.CONVERSION,
            config=metric_config,
            segments=self._get_journey_funnel_steps(
                webapp_metric, segment_index, break_down_values
            ),
        )

    def _get_journey_funnel_steps(
        self,
        webapp_metric: WM.WebappMetric,
        segment_index: int,
        break_down_values: List[str],
    ) -> List[WM.WebappSegment]:
        new_segments = []
        for index, segment in enumerate(webapp_metric.segments):
            if index > segment_index:
                break
            if index == segment_index and len(break_down_values) > 0:
                new_segments.append(
                    replace(
                        self._filter_on_group_by_fields(segment, break_down_values),
                        group_by=[],
                    )
                )
            else:
                new_segments.append(replace(segment, group_by=[]))
        return new_segments

    def journey_to_funnel(
        self,
        webapp_metric: WM.WebappMetric,
        segment_index: int,
        break_down_values: List[str],
    ) -> WM.WebappMetric:
        return replace(
            webapp_metric,
            config=replace(webapp_metric.config, chart_type=M.SimpleChartType.BAR),
            metric_type=M.MetricType.CONVERSION,
            segments=self._get_journey_funnel_steps(
                webapp_metric, segment_index, break_down_values
            ),
        )
