from __future__ import annotations

import base64
import hashlib
import json
import zlib
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.constants as CONST
from mitzu import __version__

EFD_OR_ED_TYPE = Union[<PERSON><PERSON>, M.Event<PERSON>amePath]
GROUP_BY_FIELD = Union[M.EventFieldPath, List]
DIM_SEARCH_QUERY = "dim_search_query"


class DeserializationError(Exception):
    pass


class InvalidConversionTimeWindow(DeserializationError):
    pass


class InvalidRetentionTimeWindow(DeserializationError):
    pass


class InvalidUrl(DeserializationError):
    pass


class DimensionProfileConfig:
    """
    The only purpose of this empty class is to make the serialization
    logic a bit more simple
    """


def get_field_def(value: Dict[str, Any]) -> Optional[M.FieldPath]:
    field_path_str = value.get("fp")
    if field_path_str is not None:
        field_path = M.FieldPath.parse(field_path_str)
        return field_path

    source_table = value.get("st")
    event_name = value.get("en")

    if event_name == "" and source_table == "":
        return None

    if not source_table or not event_name:
        raise ValueError(
            "source_table and event_name must be defined in serialized metric"
        )

    return M.EventFieldPath(source_table, event_name, value["f"])


def component_to_dict(value: Any) -> Any:
    if value is None:
        return None
    if type(value) in (str, float, int, bool):
        return value
    if type(value) == datetime:
        return value.isoformat()
    if type(value) == list:
        return [component_to_dict(v) for v in value]
    if type(value) == tuple:
        return [component_to_dict(v) for v in value]
    if type(value) == dict:

        return {component_to_dict(k): component_to_dict(v) for k, v in value.items()}
    if isinstance(value, M.TimeGroup):
        return value.name.lower()
    if isinstance(value, M.Resolution):
        return value.name.lower()
    if isinstance(value, M.TimeWindow):
        return value.serialize()
    if isinstance(value, M.ComparisonOptions):
        return value.serialize()
    if isinstance(value, WM.YRangeConfig):
        return value.serialize()
    if isinstance(value, M.ConversionWindowType):
        return value.value
    if isinstance(value, M.ConversionAttribution):
        return value.value
    if isinstance(value, M.ConversionOrientation):
        return value.value
    if isinstance(value, M.RetentionAttributionType):
        return value.value.lower()
    if isinstance(value, M.SimpleChartType):
        return value.name.lower()
    if isinstance(value, M.Field):
        return value._get_name()
    if isinstance(value, M.SegmentFilter):
        return value.value
    if isinstance(value, M.EventPropertyAggregationType):
        return value.value
    if isinstance(value, M.Entity):
        return value.name
    if isinstance(value, WM.WebappEventPropertyAggregation):
        return {
            "at": component_to_dict(value.agg_type),
            "ef": component_to_dict(value.field_def),
        }
    if isinstance(value, WM.WebappAggregationConfig):
        res: Dict[str, Any] = {}

        if value.type is not None:
            res["at"] = value.type.to_agg_str(value.conv_param)

        if value.type == M.AggType.BY_EVENT_PROPERTY:
            res["ep"] = component_to_dict(value.event_property_agg)

        return res
    if isinstance(value, WM.WebappMetricConfig):
        res = {
            "sdt": component_to_dict(value.time_window_chooser_config.start_dt),
            "edt": component_to_dict(value.time_window_chooser_config.end_dt),
            "lbd": component_to_dict(value.time_window_chooser_config.lookback_days),
            "cdr": component_to_dict(
                value.time_window_chooser_config.calendar_date_range
            ),
            "sd": component_to_dict(value.time_window_chooser_config.since_date),
            "tg": component_to_dict(value.time_window_chooser_config.time_group),
            "mgc": component_to_dict(value.max_group_count),
            "cat": component_to_dict(value.chart_type),
            "res": component_to_dict(value.resolution),
            "es": component_to_dict(value.event_sampling),
            "cw": component_to_dict(value.conv_window),
            "cwt": component_to_dict(value.conv_window_type),
            "ca": component_to_dict(value.conv_attribution),
            "rw": component_to_dict(value.retention_window),
            "ri": component_to_dict(value.retention_indices),
            "ag": component_to_dict(value.aggregation),
            "fr": component_to_dict(value.formula),
            "cpp": component_to_dict(value.chart_post_processing),
            "rat": component_to_dict(value.retention_attribution_type),
            "uf": component_to_dict(value.unique_field),
            "cop": component_to_dict(value.comparison_options),
            "yr": component_to_dict(value.y_range_config),
            "coid": component_to_dict(value.cut_off_incomplete_data),
            "sog": component_to_dict(value.show_other_groups),
            "vm": component_to_dict(value.view_mode),
            "cor": component_to_dict(value.conv_orientation),
        }
        if len(value.custom_holding_constants) > 0:
            res["hc"] = component_to_dict(value.custom_holding_constants)

        if value.use_consistent_coloring is True:
            res["col_c"] = True
        return res
    if isinstance(value, M.EventNamePath):
        return {
            "st": value.source_table,
            "en": value.event_name,
        }
    if isinstance(value, M.EventFieldPath):
        return {
            "st": value.source_table,
            "en": value.event_name,
            "f": value.field_name,
        }
    if (
        isinstance(value, M.DimensionFieldPath)
        or isinstance(value, M.CollectionFieldPath)
        or isinstance(value, M.SubsegmentBreakDownFieldPath)
        or isinstance(value, M.DimensionIDFieldPath)
    ):
        return {
            "fp": str(value),
        }
    if isinstance(value, WM.CalendarDateRange):
        return value.value
    if isinstance(value, WM.WebappEventFilter):
        return {
            "l": component_to_dict(value.left),
            "op": value.operator.name,
            "r": component_to_dict(value.right) if value.right is not None else None,
        }
    if isinstance(value, WM.WebappSimpleSegment):

        return {
            "e": component_to_dict(value.event),
            "f": component_to_dict(value.event_filter) if value.event_filter else None,
            "gb": component_to_dict(value.group_by) if value.group_by else None,
            "ag": component_to_dict(value.aggregation) if value.aggregation else None,
            "sf": component_to_dict(value.filter) if value.filter else None,
            "t": component_to_dict(value.title) if value.title else None,
        }
    if isinstance(value, WM.WebappComplexSegment):
        return {
            "l": component_to_dict(value.left),
            "bop": value.operator.name,
            "r": component_to_dict(value.right),
            "gb": component_to_dict(value.group_by) if value.group_by else None,
            "ag": component_to_dict(value.aggregation) if value.aggregation else None,
            "sf": component_to_dict(value.filter) if value.filter else None,
            "t": component_to_dict(value.title) if value.title else None,
        }
    if isinstance(value, M.ChartPostProcessing):
        return {
            "t": value.post_processing_type.value.lower(),
            "rw": value.rolling_window_size,
        }
    if isinstance(value, (WM.CohortReference, M.Collection)):
        return {
            "cid": value.id,
        }
    if isinstance(value, WM.WebappMetric):
        return {
            "mt": value.metric_type.value,
            "segs": component_to_dict(value.segments),
            "co": component_to_dict(value.config),
            "tl": component_to_dict(value.title),
            "ds": component_to_dict(value.description),
            "gf": component_to_dict(value.global_filters)
            if len(value.global_filters) > 0
            else None,
            "lm": value.label_mapping,
        }
    if isinstance(value, WM.WebappDimensionsSearchQuery):
        return {
            "typ": DIM_SEARCH_QUERY,
            "ent": component_to_dict(value.entity),
            "txt": component_to_dict(value.search_text),
            "fts": component_to_dict(value.filters),
            "sc": component_to_dict(value.selected_columns),
        }
    if isinstance(value, WM.ViewMode):
        return value.name.lower()
    if isinstance(value, WM.DashboardFilter):
        return {
            "fl": component_to_dict(value.filters),
            "tc": component_to_dict(value.time_config),
        }
    if isinstance(value, WM.TimeWindowChooserConfig):
        return {
            "sdt": component_to_dict(value.start_dt),
            "edt": component_to_dict(value.end_dt),
            "tg": component_to_dict(value.time_group),
            "ld": component_to_dict(value.lookback_days),
            "cdr": component_to_dict(value.calendar_date_range),
            "sd": component_to_dict(value.since_date),
        }
    raise ValueError("Unsupported value type: {}".format(type(value)))


def remove_nones(dc: Dict) -> Optional[Dict]:
    res = {}
    for k, v in dc.items():
        if type(v) == dict:
            v = remove_nones(v)
        if type(v) == list:
            v = [remove_nones(i) if type(i) == dict else i for i in v if i is not None]
        if v is not None:
            res[k] = v
    if len(res) == 0:
        return None
    return res


def webapp_metric_to_dict(webapp_metric: WM.WebappMetric) -> Dict:
    res = component_to_dict(webapp_metric)
    res = remove_nones(res)
    if res is None:
        raise Exception("Serialized metric definition is empty")
    return res


def component_from_dict(
    value: Any,
    type_hint: Any,
    path: str,
    other_hint: Any = None,
) -> Any:
    if value is None and type_hint == GROUP_BY_FIELD:  # group by field should be a list
        return []
    if value is None:
        return None

    if type_hint is None:
        metric_type = value.get("mt")
        if metric_type is not None:
            if metric_type in [v.value for v in M.MetricType.__members__.values()]:
                return component_from_dict(value, WM.WebappMetric, path)

        # backward compatibility: figure out the metric type based on the set of keys
        if "conv" in value:
            return component_from_dict(value, WM.WebappMetric, path)
        if "seg" in value or "segs" in value or "rw" in value:
            return component_from_dict(value, WM.WebappMetric, path)
        if "l" in value or "e" in value:
            return component_from_dict(value, WM.WebappSegment, path)
        if value.get("typ") == DIM_SEARCH_QUERY:
            return component_from_dict(value, WM.WebappDimensionsSearchQuery, path)
        raise ValueError(
            f"Can't deserialize metric from {value} 'conv' or 'seg' expected at {path}"
        )
    if type_hint == DimensionProfileConfig:
        if value.get("did") is not None:
            entity_name = value.get("dtt", value.get("ent"))

            if entity_name == "user_table":  # backward compatibility
                entity_name = "user"
            elif entity_name == "group_table":
                entity_name = "group"

            return WM.WebappEventFilter(
                M.DimensionIDFieldPath(M.Entity(entity_name)),
                M.Operator.EQ,
                value.get("did"),
            )
        return WM.WebappEventFilter(
            M.DimensionIDFieldPath(M.USER_ENTITY), M.Operator.EQ, value.get("uid")
        )
    if type_hint == WM.WebappMetricConfig:
        aggregation = component_from_dict(
            value.get("ag"), WM.WebappAggregationConfig, path + ".ag"
        )
        if "at" in value:  # backward compatibility
            at, ap = component_from_dict(value.get("at"), M.AggType, path + ".at")
            aggregation = WM.WebappAggregationConfig(
                type=at,
                conv_param=ap,
                event_property_agg=None,
            )

        holding_constants = value.get("hc")
        custom_holding_constants: List[M.EventFieldPath] = []
        if holding_constants:
            for index, field in enumerate(holding_constants):
                custom_holding_constants.append(
                    component_from_dict(field, M.EventFieldPath, path + f".hc[{index}]")
                )

        # conversion and retention window may not exist
        conv_window = None
        if "cw" in value:  # backward compatibility
            try:
                conv_window = component_from_dict(
                    value.get("cw"), M.TimeWindow, path + ".cw"
                )
            except Exception as e:
                raise InvalidConversionTimeWindow() from e

        time_group = component_from_dict(value.get("tg"), M.TimeGroup, path + ".tg")

        retention_window = None
        if "rw" in value:  # backward compatibility
            try:
                retention_window = component_from_dict(
                    value.get("rw"), M.TimeWindow, path + ".rw"
                )
            except Exception as e:
                raise InvalidRetentionTimeWindow() from e

        retention_indices = component_from_dict(
            value.get("ri"),
            list,
            path + ".ri",
            other_hint=int,
        )
        if retention_window is not None and retention_indices is None:
            if time_group == M.TimeGroup.TOTAL:
                retention_indices = [1]
            else:
                retention_indices = [CONST.RETENTION_INDICES_ALL_GROUPS]

        retention_attribution_type = None
        if "rat" in value:  # backward compatibility
            retention_attribution_type = component_from_dict(
                value.get("rat"), M.RetentionAttributionType, path + ".rat"
            )

        conv_window_type = None
        if "cwt" in value:
            conv_window_type = component_from_dict(
                value.get("cwt"), M.ConversionWindowType, path + ".cwt"
            )

        conv_attribution = None
        if "ca" in value:
            conv_attribution = component_from_dict(
                value.get("ca"), M.ConversionAttribution, path + ".ca"
            )

        if isinstance(value.get("uf"), dict):
            unique_field = component_from_dict(
                value.get("uf"), M.EventFieldPath, path + ".uf"
            )
        elif isinstance(value.get("uf"), str):
            mapping = {
                "user_id": M.USER_ENTITY.name,
                "group_id": "group",  # backward compatibility
            }
            uf_val = value.get("uf")
            unique_field = M.Entity(mapping.get(uf_val, uf_val))
        else:
            unique_field = M.USER_ENTITY  # backward compatibility

        if "coid" in value:
            coid = component_from_dict(value.get("coid"), bool, path + ".coid")
        else:
            coid = False

        time_window_chooser_config = WM.TimeWindowChooserConfig(
            start_dt=component_from_dict(value.get("sdt"), datetime, path + ".sdt"),
            end_dt=component_from_dict(value.get("edt"), datetime, path + ".edt"),
            lookback_days=component_from_dict(
                value.get("lbd"), M.TimeWindow, path + ".lbd"
            ),
            calendar_date_range=component_from_dict(
                value.get("cdr"), WM.CalendarDateRange, path + ".cdr"
            ),
            since_date=component_from_dict(value.get("sd"), datetime, path + ".sd"),
            time_group=time_group,
        )
        return WM.WebappMetricConfig(
            time_window_chooser_config=time_window_chooser_config,
            max_group_count=component_from_dict(value.get("mgc"), int, path + ".mgc"),
            resolution=component_from_dict(
                value.get("res"), M.Resolution, path + ".res"
            ),
            event_sampling=component_from_dict(  # ensuring backwards compatibility
                value.get("es", value.get("us")), float, path + ".es"
            ),
            cut_off_incomplete_data=coid,
            chart_type=component_from_dict(
                value.get("cat"), M.SimpleChartType, path + ".cat"
            ),
            aggregation=aggregation,
            custom_holding_constants=custom_holding_constants,
            formula=value.get("fr"),
            chart_post_processing=component_from_dict(
                value.get("cpp"), M.ChartPostProcessing, path + ".cpp"
            ),
            use_consistent_coloring=value.get("col_c", False),
            conv_window=conv_window,
            retention_window=retention_window,
            retention_indices=retention_indices,
            retention_attribution_type=retention_attribution_type,
            conv_window_type=conv_window_type,
            conv_attribution=conv_attribution,
            conv_orientation=component_from_dict(
                value.get("cor"), M.ConversionOrientation, path + ".cor"
            ),
            unique_field=unique_field,
            comparison_options=component_from_dict(
                value.get("cop"), M.ComparisonOptions, path + ".cop"
            ),
            y_range_config=component_from_dict(
                value.get("yr"), WM.YRangeConfig, path + ".yr"
            ),
            show_other_groups=value.get("sog", False),
            view_mode=component_from_dict(
                value.get("vm", "edit"), WM.ViewMode, path + ".vm"
            ),
        )
    if type_hint == WM.WebappMetric:
        config = component_from_dict(
            value.get("co"), WM.WebappMetricConfig, path + ".co"
        )
        if "cw" in value:  # backward compatibility
            conv_window = component_from_dict(
                value.get("cw"), M.TimeWindow, path + ".cw"
            )
            config = config.with_conv_window(conv_window)

        if "rw" in value:  # backward compatibility
            config = config.with_retention_window(
                component_from_dict(value.get("rw"), M.TimeWindow, path + ".rw")
            )

        segments = []
        if "segs" in value:
            segments = component_from_dict(value.get("segs"), list, path + ".segs")
        elif "seg" in value:  # backward compatibility
            segments = [
                component_from_dict(value.get("seg"), WM.WebappSegment, path + ".seg")
            ]
        elif "conv" in value:  # backward compatibility
            segments = component_from_dict(
                value.get("conv").get("segs"), list, path + ".conv.segs"
            )

        for seg_key in ["seg_1", "seg_2"]:  # backward compatibility
            if seg_key in value:
                segments.append(
                    component_from_dict(
                        value.get(seg_key), WM.WebappSegment, path + seg_key
                    )
                )

        if "mt" in value:
            metric_type = M.MetricType(value.get("mt"))
        elif "seg" in value:  # backward compatibility
            metric_type = M.MetricType.SEGMENTATION
        elif "conv" in value or "segs" in value:  # backward compatibility
            metric_type = M.MetricType.CONVERSION
        elif "rw" in value:
            metric_type = M.MetricType.RETENTION
        else:
            raise ValueError(f"Could not determine metric value: {value}")

        if metric_type == M.MetricType.SEGMENTATION and config.aggregation is not None:
            segments_with_inherited_aggregation = []
            for segment in segments:
                if segment.aggregation is None:
                    segments_with_inherited_aggregation.append(
                        segment.aggregate(config.aggregation)
                    )
                else:
                    segments_with_inherited_aggregation.append(segment)
            segments = segments_with_inherited_aggregation

        if "gf" in value:
            global_filters = component_from_dict(
                value.get("gf"), list, path + "gf", WM.WebappEventFilter
            )
        elif "upc" in value:
            global_filters = [
                component_from_dict(
                    value.get("upc"),
                    DimensionProfileConfig,
                    path + "upc",
                )
            ]
        else:
            global_filters = []

        return WM.WebappMetric(
            metric_type=metric_type,
            config=config,
            segments=segments,
            title=component_from_dict(value.get("tl"), str, path + ".tl"),
            description=component_from_dict(value.get("ds"), str, path + "ds"),
            global_filters=global_filters,
            label_mapping=value.get("lm", {}),
        )
    if type_hint == WM.CohortReference:
        cid = value.get("cid")
        if cid:
            return WM.CohortReference(cid)
        return None
    if type_hint == WM.WebappSegment:
        segment_filter = component_from_dict(
            value.get("sf"),
            M.SegmentFilter,
            path + ".sf",
        )
        title = component_from_dict(value.get("t"), str, path + ".t")
        value.get("sam")
        if "bop" in value:
            return WM.WebappComplexSegment(
                left=component_from_dict(value.get("l"), WM.WebappSegment, path + ".l"),
                operator=component_from_dict(
                    value.get("bop"), M.BinaryOperator, path + ".bop"
                ),
                right=component_from_dict(
                    value.get("r"), WM.WebappSegment, path + ".r"
                ),
                group_by=component_from_dict(
                    value.get("gb"), GROUP_BY_FIELD, path + ".gb"
                ),
                aggregation=component_from_dict(
                    value.get("ag"), WM.WebappAggregationConfig, path + ".ag"
                ),
                filter=segment_filter,
                title=title,
                expanded_custom_event_key=None,
            )
        elif "op" in value:  # old simple segment with a filter
            event_or_field_path = component_from_dict(
                value.get("l"), EFD_OR_ED_TYPE, path + ".l"
            )
            if isinstance(event_or_field_path, M.EventNamePath):
                return WM.WebappSimpleSegment(
                    event=event_or_field_path,
                    event_filter=None,
                    group_by=component_from_dict(
                        value.get("gb"), GROUP_BY_FIELD, path + ".gb"
                    ),
                    filter=segment_filter,
                    title=title,
                    aggregation=None,
                    expanded_custom_event_key=None,
                )
            return WM.WebappSimpleSegment(
                event=event_or_field_path.event_name_path,
                event_filter=(
                    component_from_dict(value, WM.WebappEventFilter, path)
                    if "op" in value
                    else None
                ),
                group_by=component_from_dict(
                    value.get("gb"), GROUP_BY_FIELD, path + ".gb"
                ),
                filter=segment_filter,
                title=title,
                aggregation=None,
                expanded_custom_event_key=None,
            )
        elif "l" in value:  # old simple segment without a filter
            return WM.WebappSimpleSegment(
                event=component_from_dict(
                    value.get("l"),
                    EFD_OR_ED_TYPE,
                    path + ".l",
                ),
                group_by=component_from_dict(
                    value.get("gb"), GROUP_BY_FIELD, path + ".gb"
                ),
                filter=segment_filter,
                title=title,
                event_filter=None,
                aggregation=None,
                expanded_custom_event_key=None,
            )
        return WM.WebappSimpleSegment(
            event=component_from_dict(value.get("e"), M.EventNamePath, path + ".e"),
            event_filter=component_from_dict(
                value.get("f"), WM.WebappEventFilter, path + ".f"
            ),
            group_by=component_from_dict(value.get("gb"), GROUP_BY_FIELD, path + ".gb"),
            aggregation=component_from_dict(
                value.get("ag"), WM.WebappAggregationConfig, path + ".ag"
            ),
            filter=segment_filter,
            title=title,
            expanded_custom_event_key=None,
        )
    if type_hint == WM.WebappEventFilter:
        left = component_from_dict(value.get("l"), M.EventFieldPath, path + ".l")
        right_val = value.get("r")
        field_type_hint: type = type(Any)
        value_type_hint: type = type(Any)
        if type(right_val) == list:
            value_type_hint = list
        else:
            value_type_hint = field_type_hint

        return WM.WebappEventFilter(
            left=left,
            operator=component_from_dict(value.get("op"), M.Operator, path + ".op"),
            right=component_from_dict(
                right_val,
                value_type_hint,
                path + ".r",
                other_hint=field_type_hint,
            ),
        )
    if type_hint == GROUP_BY_FIELD:
        if type(value) == list:
            return [get_field_def(v) for v in value]
        else:
            return [get_field_def(value)]
    if type_hint == M.EventFieldPath:
        return get_field_def(value)
    if type_hint == M.DimensionFieldPath:
        return get_field_def(value)
    if type_hint == M.EventNamePath:
        source_table = value.get("st")
        event_name = value.get("en")
        # use parse function to get CustomEventNamePath on demand
        return M.EventNamePath.parse(f"{source_table}.{event_name}")
    if type_hint == WM.CalendarDateRange:
        return WM.CalendarDateRange(value)
    if type_hint == EFD_OR_ED_TYPE:
        if "f" in value:
            return component_from_dict(value, M.EventFieldPath, path)
        else:
            return component_from_dict(value, M.EventNamePath, path)
    if type_hint == M.Operator:
        return M.Operator[value.upper()]
    if type_hint == M.BinaryOperator:
        return M.BinaryOperator[value.upper()]
    if type_hint == M.ChartPostProcessing:
        return M.ChartPostProcessing(
            post_processing_type=M.ChartPostProcessingType.parse(value["t"]),
            rolling_window_size=value.get("rw", None),
        )
    if type_hint == M.TimeGroup:
        return M.TimeGroup.parse(value)
    if type_hint == M.Resolution:
        return M.Resolution.parse(value)
    if type_hint == M.AggType:
        return M.AggType.parse_agg_str(value)
    if type_hint == M.TimeWindow:
        return M.TimeWindow.parse(value)
    if type_hint == M.ConversionWindowType:
        return M.ConversionWindowType.parse(value)
    if type_hint == M.ConversionAttribution:
        return M.ConversionAttribution.parse(value)
    if type_hint == M.ConversionOrientation:
        return M.ConversionOrientation.parse(value)
    if type_hint == M.RetentionAttributionType:
        return M.RetentionAttributionType.parse(value)
    if type_hint == M.SimpleChartType:
        return M.SimpleChartType.parse(value)
    if type_hint == M.EventPropertyAggregationType:
        return M.EventPropertyAggregationType(value)
    if type_hint == M.SegmentFilter:
        return M.SegmentFilter(value)
    if type_hint == M.EventFieldPath:
        return M.EventFieldPath.parse(value)
    if type_hint == WM.WebappEventPropertyAggregation:
        return WM.WebappEventPropertyAggregation(
            field_def=component_from_dict(
                value.get("ef"), M.EventFieldPath, path + ".ef"
            ),
            agg_type=component_from_dict(
                value.get("at"), M.EventPropertyAggregationType, path + ".at"
            ),
        )
    if type_hint == WM.WebappAggregationConfig:
        at, ap = component_from_dict(value.get("at"), M.AggType, path + ".at")
        event_property_agg = (
            component_from_dict(
                value.get("ep"),
                WM.WebappEventPropertyAggregation,
                path + ".ep",
            )
            if at == M.AggType.BY_EVENT_PROPERTY
            else None
        )

        return WM.WebappAggregationConfig(
            type=at,
            conv_param=ap,
            event_property_agg=event_property_agg,
        )
    if type_hint == datetime:
        return datetime.fromisoformat(value)
    if type_hint == bool:
        return value.lower() == "true" if type(value) == str else value
    if type_hint == float:
        return float(value)
    if type_hint == list:
        return [
            component_from_dict(val, other_hint, path + f"[{i}]")
            for i, val in enumerate(value)
        ]
    if type_hint in [int, str, type(Any)]:
        if isinstance(value, dict) and "cid" in value:
            return component_from_dict(value, WM.CohortReference, path)
        return value
    if type_hint == M.ComparisonOptions:
        return M.ComparisonOptions.parse(value)
    if type_hint == WM.YRangeConfig:
        return WM.YRangeConfig.parse(value)

    if type_hint == WM.WebappDimensionsSearchQuery:
        return WM.WebappDimensionsSearchQuery(
            entity=M.Entity(value["ent"]),
            search_text=value.get("txt"),
            filters=[
                component_from_dict(val, WM.WebappEventFilter, path + f"[{i}]")
                for i, val in enumerate(value["fts"])
            ],
            selected_columns=[
                component_from_dict(val, M.DimensionFieldPath, path + f"[{i}]")
                for i, val in enumerate(value.get("sc", []))
            ],
        )
    if type_hint == WM.ViewMode:
        return WM.ViewMode(value)

    if type_hint == WM.DashboardFilter:
        return WM.DashboardFilter(
            filters=component_from_dict(
                value.get("fl", []), list, ".fl", WM.WebappEventFilter
            ),
            time_config=component_from_dict(
                value.get("tc"), WM.TimeWindowChooserConfig, ".tc"
            )
            if "tc" in value
            else None,
        )
    if type_hint == WM.TimeWindowChooserConfig:
        return WM.TimeWindowChooserConfig(
            start_dt=component_from_dict(value.get("sdt"), datetime, ".sdt"),
            end_dt=component_from_dict(value.get("edt"), datetime, ".edt"),
            time_group=component_from_dict(value.get("tg"), M.TimeGroup, ".tg"),
            lookback_days=component_from_dict(value.get("ld"), M.TimeWindow, ".td"),
            calendar_date_range=component_from_dict(
                value.get("cdr"), WM.CalendarDateRange, ".cdr"
            ),
            since_date=component_from_dict(value.get("sd"), datetime, ".sd"),
        )
    raise ValueError(f"Can't process {value} at {path} with type_hint {type_hint}")


def webapp_metric_from_dict(value: Dict) -> WM.WebappMetric:
    return component_from_dict(value, type_hint=None, path="")


def dict_to_compressed_string(metric_dict: Dict) -> str:
    # sort keys is needed to make the json deterministic, then we can use them as a key
    metric_json = json.dumps(metric_dict, sort_keys=True)
    zipped = zlib.compress(metric_json.encode("utf-8"), 9)
    return base64.urlsafe_b64encode(zipped).decode("utf-8")


def dict_from_complressed_string(compressed_str: str) -> Dict[str, Any]:
    try:
        zipped = base64.urlsafe_b64decode(compressed_str)
        unzipped = zlib.decompress(zipped)

    except Exception as exc:
        raise InvalidUrl(exc) from exc

    return json.loads(unzipped)


def to_compressed_string(metric: WM.WebappMetric) -> str:
    return dict_to_compressed_string(webapp_metric_to_dict(metric))


def from_compressed_string(compressed_str: str) -> WM.WebappMetric:
    val = dict_from_complressed_string(compressed_str)
    return webapp_metric_from_dict(val)


def dashboard_filter_to_string(dfilter: WM.DashboardFilter) -> str:
    return dict_to_compressed_string(component_to_dict(dfilter))


def dashboard_filter_from_string(compressed_str: str) -> WM.DashboardFilter:
    val = dict_from_complressed_string(compressed_str)
    return component_from_dict(val, WM.DashboardFilter, ".")


def dimension_search_query_from_string(
    compressed_str: str,
) -> WM.WebappDimensionsSearchQuery:
    try:
        zipped = base64.urlsafe_b64decode(compressed_str)
        unzipped = zlib.decompress(zipped)

    except Exception as exc:
        raise InvalidUrl(exc) from exc

    val = json.loads(unzipped)
    return search_query_from_dict(val)


def search_query_from_dict(val: Dict) -> WM.WebappDimensionsSearchQuery:
    return component_from_dict(val, type_hint=WM.WebappDimensionsSearchQuery, path="")


def search_query_to_dict(search_query: WM.WebappDimensionsSearchQuery) -> Dict:
    res = component_to_dict(search_query)
    res = remove_nones(res)
    if res is None:
        raise Exception("Serialized metric definition is empty")
    return res


def dimension_search_query_to_string(
    search_query: WM.WebappDimensionsSearchQuery,
) -> str:
    return dict_to_compressed_string(search_query_to_dict(search_query))


def data_table_to_dict(data_table: M.DataTable) -> Dict[str, str]:
    result: Dict[str, str] = {
        "table_name": str(data_table.table_name),
        "schema": str(data_table.schema),
    }
    if isinstance(data_table, M.EventDataTable):
        result.update(
            {
                "event_time_field": str(data_table.event_time_field),
                "user_id_field": str(data_table.user_id_field),
                "event_name_field": str(data_table.event_name_field),
                "date_partition_field": str(data_table.date_partition_field),
                "disabled": str(data_table.disabled),
            }
        )
    elif isinstance(data_table, M.DimensionDataTable):
        result.update(
            {
                "user_id_field": str(data_table.primary_key),
            }
        )
    return result


def insight_settings_to_dict(
    insight_settings: M.InsightSettings,
) -> Dict[str, str]:
    result: Dict[str, str] = {
        "lookback_window": str(insight_settings.lookback_window),
        "end_date_config": str(insight_settings.end_date_config),
        "custom_end_date": str(insight_settings.custom_end_date),
        "event_resolution": str(insight_settings.event_default_resolution),
        "event_auto_sampling": str(insight_settings.event_auto_sampling),
        "conversion_attribution": str(insight_settings.conversion_attribution),
        "consider_first_event_as_retained": str(
            insight_settings.consider_first_event_as_retained
        ),
    }

    return result


def revenue_settings_to_dict(
    revenue_settings: M.RevenueSettings,
) -> Dict[str, Union[str, float]]:
    return {
        "revenue_event": str(revenue_settings.revenue_event),
        "amount_field": str(revenue_settings.amount_field),
        "period_start": str(revenue_settings.period_start_field),
        "period_end": str(revenue_settings.period_end_field),
        "currency": revenue_settings.currency,
        "multiplier": revenue_settings.multiplier,
    }


def _project_to_dict(project: M.Project) -> Dict[str, str]:
    result = {}
    event_data_tables = project.event_data_tables
    dimension_data_tables = project.dimension_data_tables
    connection = project.connection
    insight_settings = project.insight_settings

    result["event_data_tables"] = json.dumps(
        [
            data_table_to_dict(item)
            for item in sorted(
                event_data_tables, key=lambda edt: f"{edt.schema}.{edt.table_name}"
            )
        ]
    )
    result["dimension_data_tables"] = json.dumps(
        [
            data_table_to_dict(item)
            for item in sorted(
                dimension_data_tables, key=lambda ddt: f"{ddt.schema}.{ddt.table_name}"
            )
        ]
    )
    result["connection"] = connection.get_hash_str()
    result["insight_settings"] = json.dumps(
        insight_settings_to_dict(insight_settings), sort_keys=True
    )
    result["revenue_settings"] = (
        json.dumps(revenue_settings_to_dict(project.revenue_settings), sort_keys=True)
        if project.revenue_settings is not None
        else "revenue-disabled"
    )

    return result


def serialize_project(project: M.Project) -> str:
    project_dict = _project_to_dict(project)
    return json.dumps(project_dict, sort_keys=True)


def get_metric_cache_key(metric: WM.WebappMetric, project: M.Project) -> str:
    metric_dict = webapp_metric_to_dict(metric)
    #  We need to remove these keys from the metric dict as changes in these shouldn't trigger re-execution
    metric_dict["co"]["mn"] = None
    metric_dict["co"]["mgc"] = None
    metric_dict["co"]["ct"] = None
    metric_dict["co"]["cat"] = None
    metric_dict["co"]["cpp"] = None
    metric_dict["co"]["col_c"] = None
    metric_dict["co"]["sog"] = None
    metric_dict["ds"] = None
    metric_dict["tl"] = None
    metric_dict["id"] = None
    metric_dict["lm"] = None
    # Removes title for each segment
    [seg.pop("t", None) for seg in metric_dict["segs"] if "t" in seg]
    # The project_id is not part of the query param, but the path.
    # However, we need to use the ID as well for caching, some project may contain the same events.
    metric_dict["prj"] = serialize_project(project)

    return (
        f"{__version__}_{project.id}_df_"
        + hashlib.md5(
            json.dumps(metric_dict, sort_keys=True).encode("ascii")
        ).hexdigest()
    )


def get_dimension_search_query_cache_key(
    search_query: WM.WebappDimensionsSearchQuery, project: M.Project
) -> str:
    search_query_dict: Dict[str, Any] = {
        "search_text": search_query.search_text,
        "used_fields": [str(field) for field in search_query.used_fields()],
        "filters": [component_to_dict(filter) for filter in search_query.filters],
    }

    search_query_dict["project_id"] = project.id
    search_query_dict["dim_tables"] = [
        data_table_to_dict(ddt)
        for ddt in sorted(
            project.dimension_data_tables,
            key=lambda ddt: f"{ddt.schema}.{ddt.table_name}",
        )
        if ddt.entity == search_query.entity
    ]

    return (
        f"{__version__}_{project.id}_sq_"
        + hashlib.md5(
            json.dumps(search_query_dict, sort_keys=True).encode("ascii")
        ).hexdigest()
    )


def serialize_scoring_output(scoring_output: WM.ScoringOutput) -> dict:
    return {
        user_id: {
            event_id: event_data.serialize() for event_id, event_data in events.items()
        }
        for user_id, events in scoring_output.items()
    }


def parse_scoring_output(data: dict) -> WM.ScoringOutput:
    """Parse a dictionary into ScoringOutput."""
    return {
        user_id: {
            event_id: WM.UserEventData.parse(event_data)
            for event_id, event_data in events.items()
        }
        for user_id, events in data.items()
    }
