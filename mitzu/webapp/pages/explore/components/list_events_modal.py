import json
from typing import List, Optional, Tuple
from urllib.parse import urlparse

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dash_table,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate

import mitzu.adapters.generic_adapter as GA
import mitzu.cache as C
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.graph_tooltips as GT
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.service.adapter_factory as AF
import mitzu.webapp.service.input_generator as IG
import mitzu.webapp.service.metric_updater as MU
import mitzu.webapp.service.tracking_service as TS
import mitzu.webapp.storage.project_storage as PS
from mitzu.logger import LOGGER
from mitzu.visualization.tables import (
    prepare_dataframe_for_event_list_table,
)
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.pages.explore.components.common import (
    create_modal_property_select,
    get_metric_context,
    please_click_button,
)
from mitzu.webapp.pages.explore.components.warning_badges import (
    create_warning_badge,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)

LIST_EVENTS_TABLE_ID = "list_events_table_id"
LIST_EVENTS_TABLE_CONTAINER = "list_events_table_container"
DOWNLOAD_LIST_EVENTS_TABLE_CSV_ID = "download_dimension_table_csv"
LIST_EVENTS_TABLE_MODAL_CLOSE = "list_events_table_modal_close"
LIST_EVENTS_TABLE_MODAL = "list_events_table_modal"
LIST_EVENTS_TABLE_MODAL_STATE = "list_events_table_modal_state"
LIST_EVENTS_TABLE_PROPERTY_SELECT = "list_events_table_dimension_select"
LIST_EVENTS_TABLE_LOADER = "list_events_table_loader"
LIST_EVENTS_TABLE_RUN_BUTTON = "list_events_table_run_button"


def create_list_events_table(
    metric_defs: M.DefinitionsForMetric,
    original_metric: WM.WebappMetric,
    segment_index: int,
    selected_date_str: str,
    group_by_values: Optional[List[str]],
    event_properties: List[M.FieldPath],
    metric_context: MC.MetricContext,
    cache: C.MitzuCache,
    project_storage: PS.ProjectScopedStorage,
    tracking_service: TS.TrackingService,
    metric_updater: MU.MetricUpdater,
    adapter_context: GA.AdapterContext,
    user_role: WM.Role,
) -> bc.Component:
    adapter = AF.create_adapter_from_project(
        metric_defs.project,
        cache,
        adapter_context,
        user_role,
    )

    metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)
    webapp_metric = metric_updater.return_events(
        original_metric,
        event_properties,
        segment_index,
        selected_date_str,
        group_by_values,
    )
    metric = metric_converter.convert_metric(webapp_metric)

    result_df = adapter.get_df(metric)
    result_df = prepare_dataframe_for_event_list_table(
        webapp_metric, result_df, metric_context
    )

    warns = []
    if result_df.shape[0] > GA.MAX_ROW_LIMIT:
        warns.append(
            create_warning_badge(
                tooltip=f"""Too many data points were returned for your insight. 
                    Please reduce the date range or introduce filters to your insight.
                    The results limit is {GA.MAX_ROW_LIMIT} rows.
                    """,
                label="Trimmed results",
                icon="mdi:warning",
                color="orange",
            )
        )

    record_count = result_df.shape[0]
    tracking_service.track_show_event_list_table_refreshed(
        record_count=record_count,
        property_count=len(result_df.columns),
        is_complex_segment=isinstance(
            webapp_metric.segments[0], WM.WebappComplexSegment
        ),
    )

    return html.Div(
        [
            dmc.Group(warns, align="start", spacing="xs", className="mb-1"),
            dash_table.DataTable(
                id=LIST_EVENTS_TABLE_ID,
                columns=[
                    {
                        "name": column_name,
                        "id": column_name,
                        "deletable": False,
                        "selectable": False,
                    }
                    for column_name in result_df.columns
                ],
                data=result_df.to_dict("records"),
                css=[
                    {
                        "selector": ".dash-table-tooltip",
                        "rule": "padding: 5px; text-decoration: none; overflow-wrap: break-word;",
                    }
                ],
                editable=False,
                sort_action="native",
                sort_mode="single",
                filter_action="native",
                export_columns="all",
                export_headers="names",
                export_format="csv",
                page_size=15,
                cell_selectable=False,
                **UIKIT.get_dcc_table_styles(),  # type: ignore[arg-type]
            ),
            dmc.Text(
                f"Showing {record_count} event{'s' if record_count > 1 else ''}.",
                size="xs",
                color="gray",
                className="w-100 text-end mt-2",
            ),
        ]
    )


def create_list_events_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=LIST_EVENTS_TABLE_MODAL,
        title="List of events",
        content=[
            html.Div(
                id=LIST_EVENTS_TABLE_MODAL_STATE,
                className="d-none",
            ),
            create_modal_property_select(
                dropdown_id=LIST_EVENTS_TABLE_PROPERTY_SELECT,
                run_id=LIST_EVENTS_TABLE_RUN_BUTTON,
                placeholder="Select event properties",
            ),
            dmc.LoadingOverlay(
                id=LIST_EVENTS_TABLE_CONTAINER,
                children=[please_click_button("to list events")],
                className="mt-2",
                style={"minHeight": "50px"},
                overlayOpacity=0.5,
                overlayBlur=3,
                loader=dmc.Loader(
                    color="gray",
                    size="md",
                    variant="oval",
                ),
                zIndex=10,
            ),
        ],
        modal_primary_button=None,
        close_button_id=LIST_EVENTS_TABLE_MODAL_CLOSE,
        config=UIKIT.DIMENSION_MODAL,
    )


@callback(
    Output(LIST_EVENTS_TABLE_PROPERTY_SELECT, "data", allow_duplicate=True),
    Input(LIST_EVENTS_TABLE_PROPERTY_SELECT, "searchValue"),
    State(LIST_EVENTS_TABLE_PROPERTY_SELECT, "value"),
    State(GT.TOOLTIP_METRIC_STATE, "children"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_property_search(
    search: Optional[str],
    selected_str: Optional[List[str]],
    tooltip_state_json: str,
    href: str,
) -> List:
    try:
        deps = DEPS.Dependencies.get()

        tooltip_state_data = json.loads(tooltip_state_json)
        tooltip_state = GT.TooltipMetricState.from_dict(tooltip_state_data)

        parse_result = urlparse(href)
        project_id = P.get_path_value(
            P.COHORT_EDIT_PATH, parse_result.path, P.PROJECT_ID_PATH_PART
        )
        catalog_service = deps.get_catalog_service(project_id)
        metric_context = get_metric_context(
            tooltip_state.webapp_metric, catalog_service
        )

        events = metric_context.resolve_custom_events_in_list(
            tooltip_state.webapp_metric.segments[
                tooltip_state.segment_index
            ].get_event_name_paths()
        )

        input_generator = IG.InputGenerator(
            project_id, metric_context, deps.get_project_storage(project_id)
        )
        options = input_generator.get_property_options_for_list_events_modal(
            list(events),
            include=[M.FieldPath.parse(val) for val in selected_str]
            if selected_str is not None
            else [],
            search=search,
        )
        return [UIKIT.render_dmc_dropdown_option(opt) for opt in options]
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to handle property search")
        return no_update


@callback(
    Output(LIST_EVENTS_TABLE_MODAL, "is_open"),
    Output(LIST_EVENTS_TABLE_PROPERTY_SELECT, "data"),
    Output(LIST_EVENTS_TABLE_PROPERTY_SELECT, "value"),
    Output(LIST_EVENTS_TABLE_MODAL_STATE, "children"),
    Input(LIST_EVENTS_TABLE_MODAL_CLOSE, "n_clicks"),
    Input(GT.SHOW_EVENTS, "n_clicks"),
    State(GT.TOOLTIP_METRIC_STATE, "children"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_open_modal(
    close_clicks: Optional[int],
    show_events: Optional[int],
    tooltip_state_json: str,
    href: str,
) -> Tuple[bool, List, None, str]:

    if ctx.triggered_id == LIST_EVENTS_TABLE_MODAL_CLOSE:
        return False, [], None, ""
    if ctx.triggered_id != GT.SHOW_EVENTS or show_events is None:
        raise PreventUpdate()

    return (
        True,
        handle_property_search(
            search=None,
            selected_str=None,
            tooltip_state_json=tooltip_state_json,
            href=href,
        ),
        None,
        tooltip_state_json,
    )


@callback(
    Output(LIST_EVENTS_TABLE_CONTAINER, "children", allow_duplicate=True),
    Input(LIST_EVENTS_TABLE_MODAL, "is_open"),
    prevent_initial_call=True,
)
def open_close_dialog(is_open: bool) -> bc.Component:
    return please_click_button("to list events")


@callback(
    Output(LIST_EVENTS_TABLE_CONTAINER, "children"),
    Input(LIST_EVENTS_TABLE_RUN_BUTTON, "n_clicks"),
    State(LIST_EVENTS_TABLE_MODAL, "is_open"),
    State(LIST_EVENTS_TABLE_PROPERTY_SELECT, "value"),
    State(LIST_EVENTS_TABLE_MODAL_STATE, "children"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
    background=True,
    interval=100,
)
def handle_event_property_selected(
    run_clicks: int,
    is_open: bool,
    select_values: List,
    tooltip_state_json: str,
    href: str,
) -> bc.Component:
    # dash update race condition
    if not is_open:
        return no_update

    try:
        tooltip_state_data = json.loads(tooltip_state_json)
        state = GT.TooltipMetricState.from_dict(tooltip_state_data)
    except Exception:
        return no_update

    try:
        deps = DEPS.Dependencies.get()
        parse_result = urlparse(href)
        project_id = P.get_path_value(
            P.COHORT_EDIT_PATH, parse_result.path, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)
        metric_defs = project_storage.get_definitions_for_metric(state.webapp_metric)
        metric_context = get_metric_context(
            state.webapp_metric, deps.get_catalog_service(project_id)
        )
        event_properties = (
            [M.FieldPath.parse(val) for val in select_values] if select_values else []
        )
        metric_updater = deps.get_metric_updater(state.webapp_metric, project_id)
        adapter_context = deps.create_adapter_context(
            parse_result.geturl(),
            metric_defs.project,
            GA.QueryType.INSIGHT,
        )
        return create_list_events_table(
            metric_defs=metric_defs,
            segment_index=state.segment_index,
            selected_date_str=state.selected_date or "",
            original_metric=state.webapp_metric,
            group_by_values=state.group_by_values,
            metric_context=metric_context,
            cache=deps.cache,
            event_properties=event_properties,
            project_storage=project_storage,
            tracking_service=deps.tracking_service,
            metric_updater=metric_updater,
            adapter_context=adapter_context,
            user_role=deps.user_role,
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update list events table")
        return dmc.Alert(
            children=str(exc)[:400],
            color="red",
            withCloseButton=True,
            title="Something went wrong",
        )
