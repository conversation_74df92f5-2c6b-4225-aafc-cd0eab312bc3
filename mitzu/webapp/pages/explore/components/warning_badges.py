import dash_mantine_components as dmc
from dash_iconify import DashIconify

from mitzu.webapp.pages import uikit


def create_warning_badge(
    tooltip: str, label: str, icon: str, color: str
) -> dmc.Tooltip:
    return uikit.add_help_tooltip(
        label=dmc.Text(children=[tooltip]),
        component=[
            dmc.Badge(
                label,
                leftSection=dmc.ThemeIcon(
                    DashIconify(icon=icon),
                    variant="transparent",
                    color=color,
                    size="md",
                ),
                size="md",
                variant="light",
                color=color,
                radius="sm",
                className="mt-1 fw-normal px-1",
            ),
        ],
    )
