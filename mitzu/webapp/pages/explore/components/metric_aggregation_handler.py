from __future__ import annotations

from typing import List

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.components.common as ECC
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.service.input_generator as IG
from mitzu.webapp.pages.explore.constants import (
    AGG_HOLDING_CONSTANT_NAME,
)


def get_measure_title(
    aggregation: WM.WebappAggregationConfig,
    holding_constants: List[M.EventFieldPath],
    metric_context: MC.MetricContext,
) -> List:
    subject = ""
    hs_str = []
    if aggregation.type == M.AggType.BY_EVENT_PROPERTY:
        event_property_agg = aggregation.event_property_agg
        if event_property_agg is None:
            subject = "Select"
        else:
            selected = (
                metric_context.get_display_name(event_property_agg.field_def)
                if event_property_agg.field_def is not None
                else "..."
            )

            subject = f"{ECC.event_property_agg_type_to_str(event_property_agg.agg_type)} {selected}"
    else:
        agg_type = (
            aggregation.type
            if aggregation.type is not None
            else M.AggType.COUNT_UNIQUE_USERS
        )
        subject = ECC.agg_type_to_str(agg_type, aggregation.conv_param)

    if holding_constants:
        hcs: List[str] = [
            metric_context.get_display_name(hc) for hc in holding_constants
        ]
        hs_str = ["- connect by ", html.B(", ".join(hcs), className="ms-1 me-1")]

    return [
        "Measure as ",
        html.B(subject, className="ms-1 me-1"),
        *hs_str,
    ]


def create_metric_options_component(
    metric: WM.WebappMetric,
    events_for_property_aggregation: List[M.EventNamePath],
    metric_context: MC.MetricContext,
    input_generator: IG.InputGenerator,
) -> bc.Component:

    if metric.metric_type == M.MetricType.SEGMENTATION:
        # TODO this shouldn't be here
        agg_type = metric.config.aggregation.type
        agg_param = metric.config.aggregation.conv_param
        if agg_type not in (
            M.AggType.COUNT_UNIQUE_USERS,
            M.AggType.COUNT_EVENTS,
            M.AggType.BY_EVENT_PROPERTY,
            M.AggType.FREQUENCY,
        ):
            agg_type = M.AggType.COUNT_UNIQUE_USERS

    elif metric.metric_type == M.MetricType.CONVERSION:
        agg_type = metric.config.aggregation.type
        agg_param = metric.config.aggregation.conv_param
        if agg_type not in (
            M.AggType.PERCENTILE_TIME_TO_CONV,
            M.AggType.AVERAGE_TIME_TO_CONV,
            M.AggType.CONVERSION,
            M.AggType.COUNT_CONVERTED_USERS,
            M.AggType.BY_EVENT_PROPERTY,
        ):
            agg_type = M.AggType.CONVERSION

    elif metric.metric_type == M.MetricType.RETENTION:
        agg_type = metric.config.aggregation.type
        agg_param = None
        if agg_type not in [M.AggType.RETENTION_RATE, M.AggType.BY_EVENT_PROPERTY]:
            agg_type = M.AggType.RETENTION_RATE

    elif metric.metric_type == M.MetricType.JOURNEY:
        agg_type = metric.config.aggregation.type
        agg_param = None
    else:
        raise ValueError(f"Unknown metric type {metric.metric_type}")

    if agg_type == M.AggType.BY_EVENT_PROPERTY:
        if metric.config.aggregation.event_property_agg is not None:
            event_property_agg = metric.config.aggregation.event_property_agg
            if event_property_agg.field_def is not None:
                event_property_agg = event_property_agg
            else:
                event_property_agg = WM.WebappEventPropertyAggregation(
                    None,
                    event_property_agg.agg_type,
                )
        else:
            event_property_agg = WM.WebappEventPropertyAggregation(
                None,
                M.EventPropertyAggregationType.COUNT_DISTINCT,
            )
    else:
        event_property_agg = None

    aggregation = WM.WebappAggregationConfig(
        type=agg_type,
        conv_param=agg_param,
        event_property_agg=event_property_agg,
    )

    class_name = (
        "d-none"
        if metric.metric_type == M.MetricType.SEGMENTATION or not metric.has_segments()
        else "visible"  # the inputs should be present to keep the dash working
    )

    comp = dmc.Menu(
        children=[
            dmc.MenuTarget(
                dmc.Button(
                    get_measure_title(
                        aggregation,
                        metric.config.custom_holding_constants,
                        metric_context,
                    ),
                    leftIcon=DashIconify(icon="ph:hash"),
                    className="mt-2 mb-3 text-start fw-normal segment-hover border rounded text-truncate",
                    fullWidth=True,
                    variant="white",
                    color="dark",
                    style={"paddingLeft": "15px"},
                ),
            ),
            dmc.MenuDropdown(
                html.Div(
                    [
                        dmc.Text(
                            "Measure options",
                            size="md",
                            color="dark",
                            className="mb-2",
                        ),
                        html.Div(
                            [
                                ECC.create_aggregation_component(
                                    aggregation,
                                    metric.metric_type,
                                    events_for_property_aggregation,
                                    input_generator=input_generator,
                                    in_config_panel=True,
                                    class_name=class_name,
                                    unique_field_used=True
                                    if metric.config.unique_field is not None
                                    else False,
                                ),
                                html.Label(
                                    "Custom holding constant",
                                ),
                                UIKIT.render_dcc_dropdown(
                                    id=AGG_HOLDING_CONSTANT_NAME,
                                    dropdown=input_generator.create_property_dropdown_for_custom_holding_constants(
                                        event_name_paths=[
                                            enp
                                            for seg in metric.segments
                                            for enp in seg.get_event_name_paths()
                                        ],
                                        selected_field_paths=metric.config.custom_holding_constants,
                                        visible=metric.metric_type
                                        in (
                                            M.MetricType.CONVERSION,
                                            M.MetricType.RETENTION,
                                            M.MetricType.JOURNEY,
                                        ),
                                    ),
                                    classnames=["w-100"],
                                ),
                            ],
                            className="mb-2 w-100",
                        ),
                    ],
                    style={"width": "350px"},
                    className="advanced-options",
                ),
                className="p-3 shadow pull-left",
            ),
        ],
        className=class_name + " w-100",
        shadow="md",
    )

    return comp
