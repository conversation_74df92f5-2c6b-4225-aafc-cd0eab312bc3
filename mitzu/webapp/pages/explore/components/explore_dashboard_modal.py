from typing import List, Optional, Tuple, Union

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    html,
    no_update,
)
from dash_iconify import DashIconify

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.constants as CONST
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
from mitzu.logger import LOGGER
from mitzu.model import TimeGroup, TimeWindow
from mitzu.webapp.helper import MITZU_LOCATION

CLOSE_ADD_TO_DASHBOARD_MODAL = "close_add_to_dashboard_modal"
ADD_TO_DASHBOARD_MODAL = "add_to_dashboard_modal"
CONFIRM_ADD_TO_DASHBOARD_BUTTON = "confirm_add_to_dashboard_button"
ADD_TO_DASHBOARD_MODAL_ERROR = "add_to_dashboard_modal_error"
ADD_TO_DASHBOARD_MODAL_BODY = "add_to_dashboard_modal_body"
ATD_INDEX_TYPE = "atd_index_type"
ADD_TO_DASHBOARD_CHIP_GROUP = "add_to_dashboard_chip_group"
ADD_TO_DASHBOARD_NEW_DASHBOARD_BUTTON = "add_to_dashboard_new_dashboard_button"
ADD_TO_DASHBOARD_NEW_DASHBOARD_NAME = "add_to_dashboard_new_dashboard_name"
NO_DASHBOARD_INFO_CONTAINER = "no_dashboard_info_container"
DASHBOARD_INFO_CONTAINER = "dashboard_info_container"
SHOW_ONLY_MY_DASHBOARDS = "show_only_my_dashboards"


def add_to_dashboard_modal_component() -> dbc.Modal:
    return UIKIT.create_modal(
        id=ADD_TO_DASHBOARD_MODAL,
        title="Add insight to dashboards",
        content=[
            html.Div(
                [],
                id=ADD_TO_DASHBOARD_MODAL_BODY,
                className="mb-3",
            ),
            html.Div(
                children=[
                    dmc.Checkbox(
                        id=SHOW_ONLY_MY_DASHBOARDS,
                        label="Show only my dashboards",
                        checked=True,
                        color="dark",
                    )
                ],
                className="mb-2",
            ),
            html.Div(
                [],
                className="mb-2",
                id=ADD_TO_DASHBOARD_MODAL_ERROR,
            ),
        ],
        modal_primary_button=UIKIT.ModalButton(
            CONFIRM_ADD_TO_DASHBOARD_BUTTON,
            "Confirm",
            "mdi:check",
        ),
        close_button_id=CLOSE_ADD_TO_DASHBOARD_MODAL,
        config=UIKIT.CONFIG_MODAL,
    )


def add_to_dashboard_chip_component(
    dashboards: List[WM.Dashboard],
    base_selected: List[str],
) -> html.Div:
    select_dashboards = dmc.Container(
        [
            dmc.Text("Select Dashboards", size="md"),
            html.Hr(),
            html.Div(
                [dmc.Text("Nothing to show.", size="md")],
                className="mt-2 " + "" if len(dashboards) == 0 else "d-none",
                id=NO_DASHBOARD_INFO_CONTAINER,
            ),
            dmc.ChipGroup(
                [
                    dmc.Chip(
                        f"{dashboard.name[:30]}{'...' if len(dashboard.name) > 30 else ''}",
                        value=dashboard.id,
                        variant="outline",
                        className="",
                    )
                    for dashboard in dashboards
                ],
                id=ADD_TO_DASHBOARD_CHIP_GROUP,
                value=base_selected,
                multiple=True,
                className="d-grid justify-content-stretch",
                style={
                    "overflowY": "auto",
                    "maxHeight": "400px",
                    "gap": "5px",
                    "justifyContent": "stretch",
                },
            ),
        ],
        className="p-3 adt-container",
        style={
            "width": "auto",
            "justify-content": "stretch",
        },
    )

    add_new_dashboard = dmc.Container(
        [
            dmc.Grid(
                children=[
                    dmc.Col(
                        dmc.TextInput(
                            placeholder="Name your new dashboard",
                            id=ADD_TO_DASHBOARD_NEW_DASHBOARD_NAME,
                            size="xs",
                        ),
                        span=7,
                    ),
                    dmc.Col(
                        UIKIT.primary_button(
                            label="Create dashboard",
                            id=ADD_TO_DASHBOARD_NEW_DASHBOARD_BUTTON,
                            icon=DashIconify(icon="icons8:plus", width=16),
                        ),
                        span="auto",
                    ),
                ],
            ),
        ],
        className="p-3 mb-3 adt-container",
        style={"width": "auto", "justifyContent": "stretch"},
    )

    comp = html.Div(
        [
            add_new_dashboard,
            select_dashboards,
        ]
    )

    return comp


clientside_callback(
    """
    function(value) {
        return value === undefined || value.trim().length === 0;
    }
    """,
    Output(ADD_TO_DASHBOARD_NEW_DASHBOARD_BUTTON, "disabled"),
    Input(ADD_TO_DASHBOARD_NEW_DASHBOARD_NAME, "value"),
    prevent_initial_callback=True,
)


@callback(
    Output(ADD_TO_DASHBOARD_MODAL_BODY, "children", allow_duplicate=True),
    Output(ADD_TO_DASHBOARD_MODAL_ERROR, "children", allow_duplicate=True),
    Input(ADD_TO_DASHBOARD_NEW_DASHBOARD_BUTTON, "n_clicks"),
    State(ADD_TO_DASHBOARD_NEW_DASHBOARD_NAME, "value"),
    State(ADD_TO_DASHBOARD_CHIP_GROUP, "value"),
    State(SHOW_ONLY_MY_DASHBOARDS, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def create_new_dashboard_add_to_dashboard(
    add_button_click: Optional[int],
    dash_name: Optional[str],
    selected_dashboard_list: List[str],
    show_only_my_dashboards: bool,
    pathname: str,
) -> Tuple[Union[bc.Component, List], Union[bc.Component, List]]:
    if add_button_click is None:
        return no_update, []

    if dash_name is None or dash_name.strip() == "":
        return (
            no_update,
            html.Div(
                [
                    dmc.Alert(
                        "The new dashboard's name can't be empty.",
                        color="red",
                        withCloseButton=True,
                    )
                ],
                className="mt-2",
            ),
        )

    try:
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        dependencies = DEPS.Dependencies.get()
        storage = dependencies.storage

        if dependencies.user is None:
            raise ValueError("User is not logged in")

        project_storage = storage.get_project_storage(project_id)
        auto_refresh = (
            project_storage.get_project().insight_settings.auto_refresh_enabled
        )
        dashboard = project_storage.create_new_dashboard(
            dash_name,
            dependencies.user.membership_id,
            auto_refresh_tw=TimeWindow(1, TimeGroup.DAY) if auto_refresh else None,
        )
        dependencies.tracking_service.track_dashboard_saved(dashboard, "explore_page")
        dashboards = project_storage.list_dashboards_infos()
        dashboards = sorted(dashboards, key=lambda x: x.name)
        new_selected_list = [dashboard.id] + selected_dashboard_list

        if show_only_my_dashboards and dependencies.user:
            current_user_membership_id = dependencies.user.membership_id
            dashboards = [
                d for d in dashboards if d.owner == current_user_membership_id
            ]

        dashboards = sorted(dashboards, key=lambda x: x.last_updated_at, reverse=True)
        comp = add_to_dashboard_chip_component(dashboards, new_selected_list)
        return comp, []

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to create new dashboard")
        return (
            no_update,
            html.Div(
                [
                    dmc.Alert(
                        "There was an error processing your request. Please try again or contact support.",
                        color="red",
                        withCloseButton=True,
                    )
                ],
                className="mt-2",
            ),
        )


@callback(
    Output(ADD_TO_DASHBOARD_MODAL, "is_open", allow_duplicate=True),
    Output(ADD_TO_DASHBOARD_MODAL_BODY, "children", allow_duplicate=True),
    Output(CONFIRM_ADD_TO_DASHBOARD_BUTTON, "className"),
    Input(ADD_TO_DASHBOARD_MODAL, "is_open"),
    Input(SHOW_ONLY_MY_DASHBOARDS, "checked"),
    State(CONST.SAVED_METRIC_ID, "children"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def add_to_dashboard_invoked(
    open: bool,
    show_only_my_dashboards: bool,
    saved_metric_id: str,
    pathname: str,
) -> Tuple[bool, Union[bc.Component, List], str]:
    if open is False:
        return False, [], ""
    deps = DEPS.Dependencies.get()
    storage = deps.storage
    project_id = P.get_path_value(
        P.PROJECTS_EXPLORE_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    project_storage = storage.get_project_storage(project_id)
    dashboards: List[WM.Dashboard] = project_storage.list_dashboards_infos()
    if show_only_my_dashboards and deps.user:
        current_user_membership_id = deps.user.membership_id
        dashboards = [d for d in dashboards if d.owner == current_user_membership_id]

    base_selected = []

    if len(dashboards) > 0:
        base_selected = [
            dashboard.id
            for dashboard in dashboards
            if saved_metric_id
            in project_storage.get_dashboard_storage(
                dashboard.id
            ).get_dashboard_saved_metrics_ids()
        ]
        dashboards = sorted(dashboards, key=lambda x: x.last_updated_at, reverse=True)

    comp = add_to_dashboard_chip_component(dashboards, base_selected)
    return True, comp, ""
