from __future__ import annotations

import json
from dataclasses import replace
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from urllib.parse import (
    parse_qs,
    quote,
    urlencode,
    urlparse,
)

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    callback,
    clientside_callback,
    ctx,
    dcc,
    html,
    no_update,
)
from dash.dependencies import ALL, Input, Output, State
from dash.exceptions import PreventUpdate
from dash_iconify import DashIconify

import mitzu.adapters.generic_adapter as GA
import mitzu.configs as C
import mitzu.helper as H
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.helper as WH
import mitzu.webapp.metric_context as WMC
import mitzu.webapp.model as WM
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.dashboards.helper as DH
import mitzu.webapp.pages.explore.components.common as ECC
import mitzu.webapp.pages.explore.components.custom_event_modal as CEM
import mitzu.webapp.pages.explore.components.delete_modal as DIM
import mitzu.webapp.pages.explore.components.explore_dashboard_modal as EDH
import mitzu.webapp.pages.explore.constants as CONST
import mitzu.webapp.pages.explore.graph_handler as GH
import mitzu.webapp.pages.explore.local_event_store as LES
import mitzu.webapp.pages.explore.metric_config_handler as MC
import mitzu.webapp.pages.explore.metric_info_handler as MIH
import mitzu.webapp.pages.explore.metric_segments_handler as MS
import mitzu.webapp.pages.explore.metric_type_handler as MTH
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.explore.toolbar_handler as DS
import mitzu.webapp.pages.explore.user_profiles.user_attributes_table as UAT
import mitzu.webapp.pages.paths as P
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.adapter_factory as AF
import mitzu.webapp.service.catalog_service as ECS
import mitzu.webapp.service.catalog_service as CS
import mitzu.webapp.service.input_generator as IG
import mitzu.webapp.service.metric_sanitizer as MSS
import mitzu.webapp.storage.storage as S
from mitzu.constants import N_PER_A
from mitzu.logger import LOGGER, PerformanceLogger
from mitzu.visualization.visualizer import Visualizer
from mitzu.webapp.constants import (
    DASHBOARD_DEF_HEIGHT,
    DASHBOARD_DEF_WIDTH,
)
from mitzu.webapp.helper import (
    MITZU_LOCATION,
    get_final_all_inputs,
    get_segment_container_id,
)
from mitzu.webapp.pages import uikit
from mitzu.webapp.pages.explore.component_update_manager import (
    get_components_to_update,
)
from mitzu.webapp.pages.explore.model import (
    OnInputsChangeResponse,
)
from mitzu.webapp.pages.explore.simple_segment_handler import (
    REFRESH_CATALOG_PROPERTY,
)
from mitzu.webapp.pages.explore.y_range_config_handler import (
    Y_RANGE_MODAL_ERROR_CONTAINER,
)
from mitzu.webapp.pages.projects.event_tables.common import (
    CUSTOM_VALUE_PREFIX,
)
from mitzu.webapp.service.adapter_service import (
    MetricOrigin,
    get_metric_result_df,
)
from mitzu.webapp.service.event_scoring_service import (
    get_event_scores_for_user,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.service.path_parser import PathParser

EXPLORE_PAGE = "explore_page"
EXPLORE_PAGE_MIRO = "explore_page_miro"
METRIC_INFO_CONTAINER = "metric_info_container"

ALL_INPUT_COMPS = {
    "all_inputs": {
        CONST.DIM_PROFILE_ID: Input(CONST.DIM_PROFILE_ID, "children"),
        CONST.EVENT_NAME_DROPDOWN: Input(
            {"type": CONST.EVENT_NAME_DROPDOWN, "index": ALL}, "value"
        ),
        CONST.PROPERTY_OPERATOR_DROPDOWN: Input(
            {"type": CONST.PROPERTY_OPERATOR_DROPDOWN, "index": ALL}, "value"
        ),
        CONST.PROPERTY_NAME_DROPDOWN: Input(
            {"type": CONST.PROPERTY_NAME_DROPDOWN, "index": ALL}, "value"
        ),
        CONST.PROPERTY_VALUE_INPUT: Input(
            {"type": CONST.PROPERTY_VALUE_INPUT, "index": ALL}, "value"
        ),
        CONST.COMPLEX_SEGMENT_GROUP_BY: Input(
            {"type": CONST.COMPLEX_SEGMENT_GROUP_BY, "index": ALL}, "value"
        ),
        CONST.TIME_GROUP_DROPDOWN: Input(CONST.TIME_GROUP_DROPDOWN, "value"),
        TWC.CUSTOM_DATE_PICKER: Input(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CUSTOM_DATE_PICKER}, "value"
        ),
        TWC.LOOKBACK_WINDOW_DROPDOWN: Input(
            {"type": CONST.EXPLORE_TWC, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"
        ),
        TWC.CALENDAR_DATE_RANGE_CHOOSER: Input(
            {"type": CONST.EXPLORE_TWC, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER},
            "value",
        ),
        TWC.SINCE_DATE_PICKER: Input(
            {"type": CONST.EXPLORE_TWC, "index": TWC.SINCE_DATE_PICKER}, "value"
        ),
        CONST.TIME_WINDOW_INTERVAL_STEPS: Input(
            CONST.TIME_WINDOW_INTERVAL_STEPS, "value"
        ),
        CONST.TIME_WINDOW_INTERVAL: Input(CONST.TIME_WINDOW_INTERVAL, "value"),
        CONST.CONVERSION_WINDOW_TYPE: Input(CONST.CONVERSION_WINDOW_TYPE, "value"),
        CONST.CONVERSION_ATTRIBUTION_TYPE: Input(
            CONST.CONVERSION_ATTRIBUTION_TYPE, "value"
        ),
        CONST.CONVERSION_ORIENTATION_TYPE: Input(
            CONST.CONVERSION_ORIENTATION_TYPE, "value"
        ),
        CONST.AGGREGATION_TYPE: Input(CONST.AGGREGATION_TYPE, "value"),
        CONST.AGG_EVENT_PROPERTY_TYPE: Input(CONST.AGG_EVENT_PROPERTY_TYPE, "value"),
        CONST.AGG_EVENT_PROPERTY_NAME: Input(CONST.AGG_EVENT_PROPERTY_NAME, "value"),
        CONST.AGG_HOLDING_CONSTANT_NAME: Input(
            CONST.AGG_HOLDING_CONSTANT_NAME, "value"
        ),
        CONST.RESOLUTION_DD: Input(CONST.RESOLUTION_DD, "value"),
        CONST.EVENT_SAMPLING_SELECT: Input(CONST.EVENT_SAMPLING_SELECT, "value"),
        CONST.RETENTION_ATTRIBUTION_TYPE: Input(
            CONST.RETENTION_ATTRIBUTION_TYPE, "value"
        ),
        CONST.RETENTION_INDICES_SELECTOR: Input(
            CONST.RETENTION_INDICES_SELECTOR, "value"
        ),
        CONST.CHART_TYPE_DD: Input(CONST.CHART_TYPE_DD, "value"),
        CONST.CHART_POST_PROCESSING_DD: Input(CONST.CHART_POST_PROCESSING_DD, "value"),
        CONST.METRIC_TYPE_CHOICE: Input(CONST.METRIC_TYPE_CHOICE, "value"),
        CONST.COMPLEX_SEGMENT_COMBINE_BUTTON: Input(
            {"type": CONST.COMPLEX_SEGMENT_COMBINE_BUTTON, "index": ALL}, "n_clicks"
        ),
        CONST.COMPLEX_SEGMENT_MOVE_DOWN: Input(
            {"type": CONST.COMPLEX_SEGMENT_MOVE_DOWN, "index": ALL}, "n_clicks"
        ),
        CONST.COMPLEX_SEGMENT_MOVE_UP: Input(
            {"type": CONST.COMPLEX_SEGMENT_MOVE_UP, "index": ALL}, "n_clicks"
        ),
        CONST.COMPLEX_SEGMENT_DUPLICATE: Input(
            {"type": CONST.COMPLEX_SEGMENT_DUPLICATE, "index": ALL}, "n_clicks"
        ),
        CONST.COMPLEX_SEGMENT_REMOVE: Input(
            {"type": CONST.COMPLEX_SEGMENT_REMOVE, "index": ALL}, "n_clicks"
        ),
        CONST.COMPLEX_SEGMENT_AGG_TYPE: Input(
            {"type": CONST.COMPLEX_SEGMENT_AGG_TYPE, "index": ALL}, "value"
        ),
        CONST.COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_NAME: Input(
            {"type": CONST.COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_NAME, "index": ALL},
            "value",
        ),
        CONST.COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_TYPE: Input(
            {"type": CONST.COMPLEX_SEGMENT_AGG_EVENT_PROPERTY_TYPE, "index": ALL},
            "value",
        ),
        CONST.SEGMENT_NAME_INPUT: Input(
            {"type": CONST.SEGMENT_NAME_INPUT, "index": ALL}, "value"
        ),
        M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW.name: Input(
            {"type": M.SegmentFilter.FIRST_EVENT_IN_TIME_WINDOW.name, "index": ALL},
            "checked",
        ),
        M.SegmentFilter.FIRST_EVENT_EVER.name: Input(
            {"type": M.SegmentFilter.FIRST_EVENT_EVER.name, "index": ALL},
            "checked",
        ),
        M.SegmentFilter.FIRST_EVENT_PER_PERIOD.name: Input(
            {"type": M.SegmentFilter.FIRST_EVENT_PER_PERIOD.name, "index": ALL},
            "checked",
        ),
        M.SegmentFilter.LAST_EVENT_IN_TIME_WINDOW.name: Input(
            {"type": M.SegmentFilter.LAST_EVENT_IN_TIME_WINDOW.name, "index": ALL},
            "checked",
        ),
        M.SegmentFilter.LAST_EVENT_EVER.name: Input(
            {"type": M.SegmentFilter.LAST_EVENT_EVER.name, "index": ALL},
            "checked",
        ),
        M.SegmentFilter.LAST_EVENT_PER_PERIOD.name: Input(
            {"type": M.SegmentFilter.LAST_EVENT_PER_PERIOD.name, "index": ALL},
            "checked",
        ),
        MIH.METRIC_LABEL_MAPPING: Input(MIH.METRIC_LABEL_MAPPING, "value"),
        CONST.FORMULA_INPUT: Input(CONST.FORMULA_INPUT, "value"),
        MIH.METRIC_CLEAR_BUTTON: Input(MIH.METRIC_CLEAR_BUTTON, "n_clicks"),
        GH.GRAPH_RUN_BUTTON: Input(GH.GRAPH_RUN_BUTTON, "n_clicks"),
        MIH.METRIC_REFRESH_BUTTON: Input(MIH.METRIC_REFRESH_BUTTON, "n_clicks"),
        CONST.UNIQUE_FIELD_CHOICE: Input(CONST.UNIQUE_FIELD_CHOICE, "value"),
        CONST.CUT_OFF_INCOMPLETE_DATA: Input(CONST.CUT_OFF_INCOMPLETE_DATA, "checked"),
        CONST.PREVIOUS_COMPARISON_OPTIONS: Input(
            CONST.PREVIOUS_COMPARISON_OPTIONS, "value"
        ),
        CONST.APPLY_Y_RANGE_BUTTON: Input(CONST.APPLY_Y_RANGE_BUTTON, "n_clicks"),
        CONST.CLEAR_LABEL_RENAMING: Input(CONST.CLEAR_LABEL_RENAMING, "n_clicks"),
        CONST.CHART_CONSISTENT_COLORING: Input(
            CONST.CHART_CONSISTENT_COLORING, "checked"
        ),
        CONST.EDIT_MODE_MENU_ITEM: Input(CONST.EDIT_MODE_MENU_ITEM, "n_clicks"),
        CONST.CHART_MODE_MENU_ITEM: Input(CONST.CHART_MODE_MENU_ITEM, "n_clicks"),
        CONST.TABLE_MODE_MENU_ITEM: Input(CONST.TABLE_MODE_MENU_ITEM, "n_clicks"),
        CONST.OTHER_GROUPS_STATE: Input(CONST.OTHER_GROUPS_STATE, "data"),
        MC.GLOBAL_FILTER_PROPERTY_TYPE: Input(
            {"type": MC.GLOBAL_FILTER_PROPERTY_TYPE, "index": ALL}, "value"
        ),
        MC.GLOBAL_FILTER_OPERATOR_TYPE: Input(
            {"type": MC.GLOBAL_FILTER_OPERATOR_TYPE, "index": ALL}, "value"
        ),
        MC.GLOBAL_FILTER_VALUE_TYPE: Input(
            {"type": MC.GLOBAL_FILTER_VALUE_TYPE, "index": ALL}, "value"
        ),
        CONST.CLEAR_GLOBAL_FILTERS_BUTTON: Input(
            {"type": CONST.CLEAR_GLOBAL_FILTERS_BUTTON, "index": ALL}, "n_clicks"
        ),
    }
}


def create_unsaved_changes_badge() -> bc.Component:
    return dmc.Badge(
        children="Unsaved changes",
        size="md",
        color="yellow",
        variant="light",
        radius=5,
    )


def create_saved_badge() -> bc.Component:
    return dmc.Badge(
        children="Saved",
        size="md",
        color="green",
        variant="light",
        leftSection=DashIconify(icon="tabler:check", width=15),
        radius=5,
    )


def create_explore_page(
    query_params: Dict[str, str],
    project_id: str,
    storage: S.OrgScopedStorage,
    catalog_service: ECS.CatalogService,
    relevance_scores: Dict[str, WM.UserEventData],
) -> bc.Component:
    saved_metric: Optional[WM.SavedMetric] = None
    path = P.create_path(P.PROJECTS_EXPLORE_PATH, project_id=project_id)
    insight_settings = storage.get_insight_settings(project_id)
    metric = WM.WebappMetric.create_empty_webapp_metric(M.MetricType.SEGMENTATION)
    try:
        if P.PROJECTS_EXPLORE_METRIC_QUERY in query_params:
            metric = SE.from_compressed_string(
                query_params[P.PROJECTS_EXPLORE_METRIC_QUERY]
            )
            url_params = "m=" + quote(SE.to_compressed_string(metric))
            if P.NO_RUN_QUERY not in query_params:
                path = f"{path}?{url_params}"

        elif P.PROJECTS_EXPLORE_SAVED_METRIC_QUERY in query_params:
            saved_metric = storage.get_project_storage(project_id).get_saved_metric(
                query_params[P.PROJECTS_EXPLORE_SAVED_METRIC_QUERY]
            )

            metric = saved_metric.metric()
            query_params[P.PROJECTS_EXPLORE_METRIC_QUERY] = saved_metric.metric_json
            path = f"{path}?" + urlencode(query_params)

    except Exception as exc:
        if isinstance(exc, M.MissingFieldException):
            LOGGER.opt(exception=exc).warning(
                "Failed to load metric on the explore page"
            )
        else:
            LOGGER.opt(exception=exc).error("Failed to load metric on the explore page")
            path = f"{path}?" + urlencode(query_params)

    metric_context = ECC.get_metric_context(
        metric,
        catalog_service,
    )
    show_filters_component = metric.has_global_filters()

    project_storage = storage.get_project_storage(project_id)
    input_generator = IG.InputGenerator(project_id, metric_context, project_storage)
    revenue_settings = project_storage.get_revenue_settings()
    metric_segments_div = MS.from_metric(
        metric,
        input_generator,
        project_id,
        revenue_settings,
        relevance_scores,
        show_filters_component,
    )
    metric_configs_div = MC.from_metric(
        project_id,
        metric,
        metric_context,
        input_generator,
        def_conv_attribution=insight_settings.retention_attribution
        if metric.metric_type == M.MetricType.RETENTION
        else insight_settings.conversion_attribution,
        show_filters_component=show_filters_component,
    )

    metric_info_children = MIH.from_metric(metric, saved_metric, project_id)
    metric_type_selector = MTH.from_metric(metric)

    is_embedded_in_miro = query_params.get("miro") is not None

    navbar = NB.get_navbar_component(
        project_id=project_id,
        hide_navbar=query_params.get("hide_navbar") is not None or is_embedded_in_miro,
        refresh_create_insight=True,
    )
    graph_container = GH.create_graph_container(query_params, metric)

    toolbar_handler = DS.from_metric(
        metric,
        show_annotations=False,
        use_consistent_coloring=metric.config.use_consistent_coloring,
        insight_settings=insight_settings,
    )  # Annotations are hidden by default

    event_local_store = LES.create_event_local_store(input_generator, relevance_scores)

    res = html.Div(
        children=[
            dcc.Interval(
                id=CONST.ICON_REVERT_INTERVAL,
                interval=1000,
                n_intervals=0,
                disabled=True,
            ),
            navbar,
            html.Div(
                [
                    event_local_store,
                    html.Div(path, id=CONST.STATE_DIV),
                    html.Div(
                        json.dumps(SE.component_to_dict(metric.global_filters))
                        if metric.global_filters and len(metric.global_filters) > 0
                        else None,
                        id=CONST.DIM_PROFILE_ID,
                    ),
                    html.Div(
                        id=CONST.SAVED_METRIC_ID,
                        children=saved_metric.id if saved_metric else None,
                    ),
                    dcc.Location(id=CONST.DIM_PROFILE_RESET, refresh="callback-nav"),
                    dcc.Location(
                        id=CONST.CATALOG_REFRESH_LOCATION, refresh="callback-nav"
                    ),
                ],
                className="d-none",
            ),
            dbc.Container(
                children=[
                    dmc.Affix(
                        id=CONST.METRIC_ERROR_TOAST,
                        position={"top": 50, "right": "50%"},
                        style={"width": "500px", "transform": "translateX(50%)"},
                        className="shadow-lg rounded-2",
                    ),
                    dbc.Row(
                        children=[
                            dbc.Col(
                                [
                                    html.Div(
                                        [
                                            dmc.TextInput(
                                                type="text",
                                                value=metric.title
                                                if metric.title
                                                else "",
                                                id=MIH.METRIC_NAME_INPUT,
                                                placeholder="+ Add title",
                                                className="name-input d-inline-block w-100 title-input rounded-2 px-2 mb-2 mt-1",
                                                debounce=400,
                                                size="sm",
                                                variant="light",
                                            ),
                                            uikit.render_hidden_input(
                                                MIH.METRIC_LABEL_MAPPING,
                                                value=json.dumps(metric.label_mapping),
                                            ),
                                            metric_type_selector,
                                        ],
                                        className="mb-1",
                                        style={
                                            "background": "#F8F9FA",
                                        },
                                    ),
                                    html.Div(
                                        [
                                            html.Div(className="pt-3"),
                                            metric_segments_div,
                                            metric_configs_div,
                                        ],
                                        className="p-2",
                                    ),
                                ],
                                className="p-0"
                                if metric.config.view_mode == WM.ViewMode.EDIT
                                else "d-none",
                                lg=2
                                if (metric.dimension_profile_config is not None)
                                else 3,
                                md=12,
                                style={
                                    "minWidth": "425px",
                                    "flexGrow": 1,
                                    "flexShrink": 0,
                                },
                                id=CONST.LEFT_PANEL_CONTAINER,
                            ),
                            dbc.Col(
                                html.Div(
                                    [
                                        dbc.Row(
                                            metric_info_children,
                                            id=METRIC_INFO_CONTAINER,
                                            justify="start",
                                            className="py-1 pb-2 ",
                                        ),
                                        toolbar_handler,
                                        html.Div(
                                            graph_container,
                                            className="pt-2 mt-1",
                                        ),
                                    ],
                                ),
                                lg=6
                                if metric.dimension_profile_config is not None
                                else 8,
                                md=12,
                                className="border-start",
                                style={
                                    "flexGrow": 1,
                                    "minWidth": "0",
                                    "minHeight": "80vh",
                                },
                            ),
                            (
                                dbc.Col(
                                    children=(
                                        UAT.dim_profile_attributes_container(
                                            metric.global_filters[0].right,
                                            entity=metric.global_filters[0].left.entity,
                                            metric_context=metric_context,
                                        )
                                    ),
                                    lg=2,
                                    md=12,
                                    className="pt-3 border-start",
                                )
                                if (
                                    metric.dimension_profile_config is not None
                                    and len(metric.global_filters) == 1
                                    and isinstance(
                                        metric.global_filters[0].left,
                                        M.DimensionIDFieldPath,
                                    )
                                    and isinstance(metric.global_filters[0].right, str)
                                )
                                else None
                            ),
                        ],
                        justify="start",
                    ),
                ],
                fluid=True,
                className="",
            ),
            EDH.add_to_dashboard_modal_component(),
            DIM.delete_insight_modal(),
            CEM.create_custom_event_modal(),
            dcc.Store(CONST.OTHER_GROUPS_STATE, data=metric.config.show_other_groups),
        ],
        className=" ".join(
            [EXPLORE_PAGE] + ([EXPLORE_PAGE_MIRO] if is_embedded_in_miro else [])
        ),
        id=EXPLORE_PAGE,
    )
    return res


def parse_global_filters(state_str: Optional[str]) -> List[WM.WebappEventFilter]:
    if state_str is None:
        return []

    dim_profile_json = json.loads(state_str)
    return SE.component_from_dict(dim_profile_json, list, ".", WM.WebappEventFilter)


def get_global_filters_from_inputs(
    properties: List[Optional[str]],
    operators: List[str],
    values: List[Any],
    triggered_type: str,
    triggered_index: int,
) -> List[WM.WebappEventFilter]:
    filters = []
    for index, property in enumerate(properties):
        if property is None:
            continue

        property_path = M.FieldPath.parse(property)
        if isinstance(property_path, M.CollectionFieldPath):
            default_operator = M.Operator.IN_COHORT
        else:
            default_operator = M.Operator.ANY_OF

        if (
            index >= len(operators)
            or triggered_type == MC.GLOBAL_FILTER_PROPERTY_TYPE
            and triggered_index == index
        ):
            operator = default_operator
        else:
            operator = M.Operator(operators[index])
        if index >= len(values) or (
            triggered_type == MC.GLOBAL_FILTER_PROPERTY_TYPE
            and triggered_index == index
        ):
            value = None
        else:
            value = values[index]

        if operator.supports_multiple_operands():
            if not isinstance(value, list):
                if value is None:
                    value = []
                else:
                    value = [value]
        elif operator.is_unary_operator():
            value = None
        else:
            if isinstance(value, list):
                if len(value) >= 1:
                    value = value[0]
                else:
                    value = None
        if isinstance(value, list):
            value = [
                item.replace(CUSTOM_VALUE_PREFIX, "") if type(item) == str else item
                for item in value
                if not item
                or not (
                    type(item) == str and item.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
                )
            ]
        elif isinstance(value, str):
            value = value.replace(CUSTOM_VALUE_PREFIX, "")

        if operator in [
            M.Operator.IN_COHORT,
            M.Operator.NOT_IN_COHORT,
        ] and isinstance(value, str):
            value = WM.CohortReference(value)
        filters.append(WM.WebappEventFilter(property_path, operator, value))

    return filters


def create_metric_from_all_inputs(
    all_inputs: Dict[str, Any],
    insight_settings: M.InsightSettings,
    catalog_service: ECS.CatalogService,
    default_end_dt: datetime,
    project: M.Project,
    view_mode_store: str,
) -> Tuple[WM.WebappMetric, WMC.MetricContext]:
    segments = MS.from_all_inputs(all_inputs, project)
    metric_type = M.MetricType[all_inputs[CONST.METRIC_TYPE_CHOICE].upper()]
    metric_config = MC.from_all_inputs(
        all_inputs, metric_type, insight_settings, view_mode_store
    )
    if isinstance(ctx.triggered_id, dict) and ctx.triggered_id["type"] in [
        MC.GLOBAL_FILTER_PROPERTY_TYPE,
        MC.GLOBAL_FILTER_OPERATOR_TYPE,
        MC.GLOBAL_FILTER_VALUE_TYPE,
    ]:
        triggered_type = ctx.triggered_id["type"]
        triggered_index = int(ctx.triggered_id["index"])
        properties = all_inputs.get(MC.GLOBAL_FILTER_PROPERTY_TYPE, [])
        operators = all_inputs.get(MC.GLOBAL_FILTER_OPERATOR_TYPE, [])
        values = all_inputs.get(MC.GLOBAL_FILTER_VALUE_TYPE, [])
        existing_filter = parse_global_filters(all_inputs.get(CONST.DIM_PROFILE_ID))
        global_filters = get_global_filters_from_inputs(
            properties, operators, values, triggered_type, triggered_index
        )
        if len(existing_filter) >= 1 and isinstance(
            existing_filter[0].left, M.DimensionIDFieldPath
        ):

            global_filters.insert(0, existing_filter[0])
    elif (
        isinstance(ctx.triggered_id, dict)
        and ctx.triggered_id["type"] == CONST.CLEAR_GLOBAL_FILTERS_BUTTON
    ):
        existing_filter = parse_global_filters(all_inputs.get(CONST.DIM_PROFILE_ID))
        new_filters = [
            f
            for f in existing_filter
            if isinstance(
                f.left, M.DimensionIDFieldPath
            )  # only keep the dimension id filters
        ]

        global_filters = new_filters
    else:
        global_filters = parse_global_filters(all_inputs.get(CONST.DIM_PROFILE_ID))
    if ctx.triggered_id in [CONST.CLEAR_LABEL_RENAMING] or (
        isinstance(ctx.triggered_id, dict)
        and ctx.triggered_id["type"]
        in [
            CONST.COMPLEX_SEGMENT_GROUP_BY,
            CONST.EVENT_NAME_DROPDOWN,
            CONST.COMPLEX_SEGMENT_MOVE_UP,
            CONST.COMPLEX_SEGMENT_MOVE_DOWN,
            CONST.COMPLEX_SEGMENT_REMOVE,
            CONST.METRIC_TYPE_CHOICE,
        ]
    ):
        label_mapping = {}
    else:
        label_mapping = json.loads(all_inputs.get(MIH.METRIC_LABEL_MAPPING, "{}"))

    webapp_metric = WM.WebappMetric(
        metric_type=metric_type,
        config=metric_config,
        segments=segments,
        title=all_inputs.get(MIH.METRIC_NAME_INPUT),
        description=all_inputs.get(MIH.METRIC_DESCRIPTION_INPUT),
        global_filters=global_filters,
        label_mapping=label_mapping,
    )

    metric_context = ECC.get_metric_context(webapp_metric, catalog_service)
    sanitizer_service = MSS.MetricSanitizer(metric_context)
    if isinstance(ctx.triggered_id, dict) and (
        ctx.triggered_id.get("type") == CONST.EVENT_NAME_DROPDOWN
        or ctx.triggered_id.get("type") == CONST.COMPLEX_SEGMENT_REMOVE
    ):
        webapp_metric = sanitizer_service.sanitize_metric_on_event_change(webapp_metric)

    webapp_metric = sanitizer_service.enforce_defaults_on_retention_metrics(
        webapp_metric, insight_settings=insight_settings
    )

    webapp_metric = sanitizer_service.enforce_defaults_on_journey_metrics(webapp_metric)

    webapp_metric = sanitizer_service.restore_default_aggregation_type(webapp_metric)

    webapp_metric = sanitizer_service.reset_time_window_interval(webapp_metric)
    webapp_metric = sanitizer_service.correct_retention_indices(
        webapp_metric, default_end_dt
    )
    webapp_metric = sanitizer_service.set_default_retention_indices(webapp_metric)
    return webapp_metric, metric_context


def handle_catalog_refresh_from_explore(
    project_id: str,
) -> Optional[OnInputsChangeResponse]:
    refresh_catalog_property = [
        item["value"]
        for item in ctx.triggered
        if (
            f'"type":"{CONST.PROPERTY_NAME_DROPDOWN}"' in item["prop_id"]
            and item["value"] is not None
            and item["value"].startswith(REFRESH_CATALOG_PROPERTY)
        )
    ]
    if len(refresh_catalog_property) > 0:
        selected_event = refresh_catalog_property[0].split(REFRESH_CATALOG_PROPERTY)[1]
        if selected_event:
            catalog_path = P.create_workspace_settings_path(
                project_id, P.WorkspaceSettingsTab.EVENT_PROPERTY_CATALOG
            )
            catalog_url = f"{catalog_path}&event={selected_event}&page=1"
            return OnInputsChangeResponse.default_instance(
                redirect_to_catalog_location=catalog_url
            )
    return None


def get_metric_with_context(
    all_inputs: Dict[str, Any],
    insight_settings: M.InsightSettings,
    catalog_service: CS.CatalogService,
    default_end_dt: datetime,
    project: M.Project,
    view_mode_store: str,
) -> Tuple[WM.WebappMetric, WMC.MetricContext]:
    if ctx.triggered_id == MIH.METRIC_CLEAR_BUTTON:
        metric_type = M.MetricType[all_inputs[CONST.METRIC_TYPE_CHOICE].upper()]
        webapp_metric_config = WM.WebappMetricConfig.from_model(M.MetricConfig())
        profile = parse_global_filters(all_inputs.get(CONST.DIM_PROFILE_ID))
        webapp_metric = WM.WebappMetric(
            metric_type=metric_type,
            config=webapp_metric_config,
            segments=[],
            title=None,
            description=None,
            global_filters=profile,
        )
        metric_context = ECC.get_metric_context(webapp_metric, catalog_service)
    else:
        webapp_metric, metric_context = create_metric_from_all_inputs(
            all_inputs,
            insight_settings,
            catalog_service,
            default_end_dt,
            project,
            view_mode_store,
        )

    return webapp_metric, metric_context


def get_triggered_input_value() -> Optional[str]:
    try:
        triggered_prop = [
            k for k, v in ctx.triggered_prop_ids.items() if v == ctx.triggered_id
        ][0]
        return ctx.inputs.get(triggered_prop)
    except Exception as exc:
        LOGGER.opt(exception=exc).warning(
            "failed to determine new value from dash context"
        )
        return None


def handle_input_changes(
    project_id: str,
    insight_settings: M.InsightSettings,
    all_inputs: Dict[str, Any],
    view_mode_store: str,
    relevance_scores: Dict[str, WM.UserEventData],
) -> OnInputsChangeResponse:
    deps = DEPS.Dependencies.get()

    refresh_catalog_from_explore = handle_catalog_refresh_from_explore(project_id)
    if refresh_catalog_from_explore is not None:
        return refresh_catalog_from_explore

    catalog_service = deps.get_catalog_service(project_id)
    project = deps.storage.get_project(project_id=project_id)

    with PerformanceLogger(deps.user, "get_metric_with_context"):
        webapp_metric, metric_context = get_metric_with_context(
            all_inputs,
            insight_settings,
            catalog_service,
            project.get_default_end_dt(),
            project,
            view_mode_store,
        )
    metric_has_segments = webapp_metric.has_segments()
    if metric_has_segments:
        if (
            webapp_metric.config.aggregation.event_property_agg is not None
            and webapp_metric.config.aggregation.event_property_agg.field_def is None
        ):
            webapp_metric = replace(
                webapp_metric,
                config=replace(
                    webapp_metric.config,
                    aggregation=replace(
                        webapp_metric.config.aggregation, event_property_agg=None
                    ),
                ),
            )

    input_generator = IG.InputGenerator(
        project_id,
        metric_context,
        deps.get_project_storage(project_id),
    )

    components_to_update = get_components_to_update(
        ctx.triggered_id, get_triggered_input_value()
    )
    if (
        isinstance(ctx.triggered_id, dict)
        and (
            ctx.triggered_id["type"] == MC.GLOBAL_FILTER_PROPERTY_TYPE
            or ctx.triggered_id["type"] == MC.GLOBAL_FILTER_OPERATOR_TYPE
            or ctx.triggered_id["type"] == MC.GLOBAL_FILTER_VALUE_TYPE
        )
        or webapp_metric.has_global_filters()
    ) and all_inputs[MC.GLOBAL_FILTER_PROPERTY_TYPE] != [None, None]:
        show_filters_component = True
    else:
        show_filters_component = False
    with PerformanceLogger(deps.user, "creating_metric_segments_ui"):
        metric_segments = [None for _ in range(0, CONST.NUMBER_OF_SEGMENTS)]
        for index in components_to_update.segments:
            if index == CONST.NUMBER_OF_SEGMENTS:
                break
            metric_segments[index] = MS.get_segment_container(
                index,
                webapp_metric,
                input_generator,
                project.revenue_settings,
                relevance_scores,
                show_filters_component,
            ).children

    with PerformanceLogger(deps.user, "creating_date_selector_ui"):
        date_selector = (
            DS.from_metric(
                webapp_metric,
                show_annotations=all_inputs[CONST.SHOW_HIDE_ANNOTATIONS],
                use_consistent_coloring=all_inputs[CONST.CHART_CONSISTENT_COLORING],
                insight_settings=insight_settings,
            ).children
            if components_to_update.date_selector
            else None
        )

    with PerformanceLogger(deps.user, "creating_metric_config_ui"):
        mc_children = (
            MC.from_metric(
                project_id=project_id,
                metric=webapp_metric,
                metric_context=metric_context,
                input_generator=input_generator,
                def_conv_attribution=(
                    insight_settings.retention_attribution
                    if webapp_metric.metric_type == M.MetricType.RETENTION
                    else insight_settings.conversion_attribution
                ),
                show_filters_component=show_filters_component,
            ).children
            if components_to_update.metric_config
            else None
        )

    url = all_inputs[MITZU_LOCATION]
    url = C.HOME_URL + P.create_explore_path(project_id, webapp_metric)
    state: Optional[str] = url

    auto_refresh = insight_settings.auto_refresh_enabled
    graph_run_style: Optional[Dict[str, str]] = None
    # This will make sure either the CANCEL or RUN buttons are correctly positioned
    # This is set to "static" by the GraphHandler. The graph Tooltip requires static positioning.
    graph_top_container: Optional[Dict[str, str]] = {"position": "relative"}
    graph_container_style: Optional[Dict[str, str]] = {"opacity": "0.3"}
    if ctx.triggered_id == MIH.METRIC_REFRESH_BUTTON and metric_has_segments:
        hash_key = SE.get_metric_cache_key(webapp_metric, project)
        deps.cache.clear(hash_key)

    if ctx.triggered_id in (
        MIH.METRIC_NAME_INPUT,
        MIH.METRIC_DESCRIPTION_INPUT,
        CONST.EDIT_MODE_MENU_ITEM,
        CONST.TABLE_MODE_MENU_ITEM,
        CONST.CHART_MODE_MENU_ITEM,
        MIH.METRIC_LABEL_MAPPING,
    ):
        #  Do not change the CANCEL or RUN button position As it is not shown.

        state = None
        graph_top_container = None
        graph_container_style = None
    elif not auto_refresh:
        # no auto-refresh case
        if (
            ctx.triggered_id not in [GH.GRAPH_RUN_BUTTON, MIH.METRIC_REFRESH_BUTTON]
            and metric_has_segments
        ):
            # Something has changed in the explore page and we need to show the RUN button
            graph_run_style = {"display": "flex"}
            state = None
        elif (
            ctx.triggered_id
            in (
                MIH.METRIC_CLEAR_BUTTON,
                MIH.METRIC_REFRESH_BUTTON,
            )
            or not metric_has_segments
        ):
            # If we clear or click refresh we should hide the run button.
            # Run button clicking is hidden in the graph handler itself
            graph_run_style = {"display": "none"}
        if ctx.triggered_id in [GH.GRAPH_RUN_BUTTON, MIH.METRIC_REFRESH_BUTTON]:
            run_response = OnInputsChangeResponse.default_instance(
                state_div=state,
                graph_run_overlay_styles=graph_run_style,
                graph_top_container_styles=graph_top_container,
                graph_container_style=graph_container_style,
                label_mapping=webapp_metric.label_mapping,
            )

            return run_response
    enable_toolbar_buttons = (
        metric_has_segments and webapp_metric.config.view_mode == WM.ViewMode.EDIT
    )
    return OnInputsChangeResponse(
        metric_segments=metric_segments,
        metric_config_container=mc_children,
        date_selector_container=date_selector,
        clipboard=url,
        mitzu_location=url,
        state_div=state,
        show_metric_save_button=not enable_toolbar_buttons or deps.is_user_read_only,
        show_metric_update_button=not enable_toolbar_buttons or deps.is_user_read_only,
        show_metric_save_as_new_button=not enable_toolbar_buttons
        or deps.is_user_read_only,
        show_metric_share_button=not enable_toolbar_buttons,
        show_metric_refresh_button=not webapp_metric.has_segments(),
        show_metric_clear_button=not enable_toolbar_buttons,
        show_metric_more_button=not enable_toolbar_buttons,
        show_metric_more_menu=not enable_toolbar_buttons,
        graph_run_overlay_styles=graph_run_style,
        graph_top_container_styles=graph_top_container,
        graph_container_style=graph_container_style,
        show_add_to_dashboard_button=not enable_toolbar_buttons
        or deps.is_user_read_only,
        show_delete_insight_button=not enable_toolbar_buttons or deps.is_user_read_only,
        redirect_to_catalog_location=None,
        y_range_config_error=None,
        y_range_min=no_update,
        y_range_max=no_update,
        unsaved_badge_container_children=create_unsaved_changes_badge(),
        label_mapping=webapp_metric.label_mapping,
        view_mode_store=webapp_metric.config.view_mode.value,
        view_mode_menu_button_icon=MIH.get_view_mode_icon(
            webapp_metric.config.view_mode
        ),
        table_container_classname="d-none"
        if webapp_metric.config.view_mode == WM.ViewMode.CHART
        else "d-flex justify-content-center align-items-center",
        graph_top_container_classname="d-none"
        if webapp_metric.config.view_mode == WM.ViewMode.TABLE
        else "",
        left_panel_container_classname="d-none"
        if webapp_metric.config.view_mode != WM.ViewMode.EDIT
        else "p-0",
        date_selector_container_classname="d-none"
        if webapp_metric.config.view_mode != WM.ViewMode.EDIT
        else "",
        view_mode_menu_button_disabled=not webapp_metric.has_enough_segments(),
        view_mode_menu=not webapp_metric.has_enough_segments(),
        dim_profile_id=json.dumps(SE.component_to_dict(webapp_metric.global_filters))
        if webapp_metric.global_filters and len(webapp_metric.global_filters) > 0
        else no_update,
    )


@callback(
    output={
        MC.METRICS_CONFIG_CONTAINER: Output(MC.METRICS_CONFIG_CONTAINER, "children"),
        CONST.DATE_SELECTOR_CONTAINER: Output(
            CONST.DATE_SELECTOR_CONTAINER, "children"
        ),
        MITZU_LOCATION: Output(MITZU_LOCATION, "href", allow_duplicate=True),
        CONST.STATE_DIV: Output(CONST.STATE_DIV, "children"),
        MIH.CLIPBOARD: Output(MIH.CLIPBOARD, "content"),
        MIH.METRIC_SAVE_BUTTON: Output(MIH.METRIC_SAVE_BUTTON, "disabled"),
        MIH.METRIC_UPDATE_BUTTON: Output(MIH.METRIC_UPDATE_BUTTON, "disabled"),
        MIH.METRIC_SAVE_AS_NEW_BUTTON: Output(
            MIH.METRIC_SAVE_AS_NEW_BUTTON, "disabled"
        ),
        MIH.METRIC_SHARE_BUTTON: Output(MIH.METRIC_SHARE_BUTTON, "disabled"),
        MIH.METRIC_REFRESH_BUTTON: Output(MIH.METRIC_REFRESH_BUTTON, "disabled"),
        MIH.METRIC_CLEAR_BUTTON: Output(MIH.METRIC_CLEAR_BUTTON, "disabled"),
        MIH.METRIC_MORE_BUTTON: Output(MIH.METRIC_MORE_BUTTON, "disabled"),
        MIH.METRIC_MORE_MENU: Output(MIH.METRIC_MORE_MENU, "disabled"),
        GH.GRAPH_RUN_OVERLAY: Output(GH.GRAPH_RUN_OVERLAY, "style"),
        GH.GRAPH_TOP_CONTAINER: Output(
            GH.GRAPH_TOP_CONTAINER, "style", allow_duplicate=True
        ),
        MIH.ADD_TO_DASHBOARD_BUTTON: Output(MIH.ADD_TO_DASHBOARD_BUTTON, "disabled"),
        MIH.DELETE_INSIGHT_BUTTON: Output(MIH.DELETE_INSIGHT_BUTTON, "disabled"),
        CONST.CATALOG_REFRESH_LOCATION: Output(CONST.CATALOG_REFRESH_LOCATION, "href"),
        GH.GRAPH_CONTAINER: Output(GH.GRAPH_CONTAINER, "style"),
        Y_RANGE_MODAL_ERROR_CONTAINER: Output(
            Y_RANGE_MODAL_ERROR_CONTAINER, "children"
        ),
        CONST.Y_RANGE_MIN: Output(CONST.Y_RANGE_MIN, "value"),
        CONST.Y_RANGE_MAX: Output(CONST.Y_RANGE_MAX, "value"),
        MIH.METRIC_SAVED_BADGE_TOOLTIP: Output(
            MIH.METRIC_SAVED_BADGE_TOOLTIP, "children"
        ),
        CONST.VIEW_MODE_STORE: Output(CONST.VIEW_MODE_STORE, "data"),
        CONST.VIEW_MODE_MENU_ICON: Output(CONST.VIEW_MODE_MENU_ICON, "icon"),
        MIH.METRIC_LABEL_MAPPING: Output(
            MIH.METRIC_LABEL_MAPPING, "value", allow_duplicate=True
        ),
        GH.GRAPH_TABLE_CONTAINER: Output(GH.GRAPH_TABLE_CONTAINER, "className"),
        CONST.GRAPH_TOP_CONTAINER_CLASS: Output(GH.GRAPH_TOP_CONTAINER, "className"),
        CONST.LEFT_PANEL_CONTAINER: Output(CONST.LEFT_PANEL_CONTAINER, "className"),
        CONST.DATE_SELECTOR_CONTAINER_CLASSNAME: Output(
            CONST.DATE_SELECTOR_CONTAINER, "className"
        ),
        CONST.VIEW_MODE_MENU_BUTTON: Output(CONST.VIEW_MODE_MENU_BUTTON, "disabled"),
        CONST.VIEW_MODE_MENU: Output(CONST.VIEW_MODE_MENU, "disabled"),
        CONST.DIM_PROFILE_ID: Output(CONST.DIM_PROFILE_ID, "children"),
        **{
            get_segment_container_id(index): Output(
                get_segment_container_id(index), "children", allow_duplicate=True
            )
            for index in range(0, CONST.NUMBER_OF_SEGMENTS)
        },
    },
    inputs={
        **ALL_INPUT_COMPS,
        "info_inputs": {
            MIH.METRIC_DESCRIPTION_INPUT: Input(MIH.METRIC_DESCRIPTION_INPUT, "value"),
            MIH.METRIC_NAME_INPUT: Input(MIH.METRIC_NAME_INPUT, "value"),
        },
    },
    state=dict(
        href=State(MITZU_LOCATION, "href"),
        y_range_min=State(CONST.Y_RANGE_MIN, "value"),
        y_range_max=State(CONST.Y_RANGE_MAX, "value"),
        show_annotations=State(CONST.SHOW_HIDE_ANNOTATIONS, "checked"),
        view_mode_store=State(CONST.VIEW_MODE_STORE, "data"),
    ),
    prevent_initial_call=True,
)
def on_inputs_change(
    all_inputs: Dict[str, Any],
    info_inputs: Dict[str, Any],
    href: str,
    y_range_min: Optional[str],
    y_range_max: Optional[str],
    show_annotations: Optional[bool],
    view_mode_store: str,
) -> Dict[str, Any]:
    try:
        if len(ctx.triggered_prop_ids) != 1:
            # Ignoring all components change as this comes from

            # page layout load and everything should be already rendered
            return OnInputsChangeResponse.default_instance().to_dict()

        url = urlparse(href)
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH, url.path, P.PROJECT_ID_PATH_PART
        )
        dependencies = DEPS.Dependencies.get()
        insight_settings = dependencies.storage.get_insight_settings(project_id)

        all_inputs = get_final_all_inputs(all_inputs, ctx.inputs_list)
        all_inputs[MIH.METRIC_NAME_INPUT] = info_inputs[MIH.METRIC_NAME_INPUT]
        all_inputs[CONST.Y_RANGE_MIN] = y_range_min
        all_inputs[CONST.Y_RANGE_MAX] = y_range_max
        all_inputs[MIH.METRIC_DESCRIPTION_INPUT] = info_inputs[
            MIH.METRIC_DESCRIPTION_INPUT
        ]
        all_inputs[MITZU_LOCATION] = href
        all_inputs[CONST.SHOW_HIDE_ANNOTATIONS] = show_annotations or False
        start_time = datetime.now()
        event_scores = get_event_scores_for_user(
            dependencies.user_id,
            dependencies.cache,
            dependencies.get_project_storage(project_id),
        )
        res = handle_input_changes(
            project_id,
            insight_settings,
            all_inputs,
            view_mode_store,
            event_scores,
        ).to_dict()
        LOGGER.bind(
            url=res[CONST.STATE_DIV],
            duration=(datetime.now() - start_time).total_seconds(),
        ).info("Explore page components updated")
        return res

    except WM.InvalidYRangeConfig as exc:
        LOGGER.opt(exception=exc).warning("Invalid y range")
        return OnInputsChangeResponse.default_instance(
            y_range_config_error=dmc.Alert(
                title="Invalid y range",
                children=str(exc),
                color="red",
                withCloseButton=True,
            ),
            y_range_min="",
            y_range_max="",
        ).to_dict()

    except Exception as exc:
        LOGGER.opt(exception=exc).bind(url=href).exception(
            "Failed to handle explore callback"
        )
        return OnInputsChangeResponse.default_instance().to_dict()


@callback(
    Input(MIH.METRIC_SAVE_AS_NEW_BUTTON, "n_clicks"),
    Input(MIH.METRIC_SAVE_BUTTON, "n_clicks"),
    Input(MIH.METRIC_UPDATE_BUTTON, "n_clicks"),
    State(MIH.METRIC_NAME_INPUT, "value"),
    State(MIH.METRIC_DESCRIPTION_INPUT, "value"),
    State(CONST.SAVED_METRIC_ID, "children"),
    State(MITZU_LOCATION, "href"),
    output={
        CONST.METRIC_ERROR_TOAST: Output(
            CONST.METRIC_ERROR_TOAST, "children", allow_duplicate=True
        ),
        CONST.SAVED_METRIC_ID: Output(CONST.SAVED_METRIC_ID, "children"),
        MIH.METRIC_SAVE_AS_NEW_BUTTON: Output(MIH.METRIC_SAVE_AS_NEW_BUTTON, "style"),
        MIH.METRIC_UPDATE_BUTTON: Output(MIH.METRIC_UPDATE_BUTTON, "style"),
        MIH.METRIC_SAVE_BUTTON: Output(MIH.METRIC_SAVE_BUTTON, "style"),
        EDH.ADD_TO_DASHBOARD_MODAL: Output(
            EDH.ADD_TO_DASHBOARD_MODAL, "is_open", allow_duplicate=True
        ),
        MIH.ADD_TO_DASHBOARD_BUTTON: Output(
            MIH.ADD_TO_DASHBOARD_BUTTON, "style", allow_duplicate=True
        ),
        MIH.DELETE_INSIGHT_BUTTON: Output(
            MIH.DELETE_INSIGHT_BUTTON, "style", allow_duplicate=True
        ),
        MIH.METRIC_SAVED_BADGE_TOOLTIP: Output(
            MIH.METRIC_SAVED_BADGE_TOOLTIP, "className"
        ),
        MIH.METRIC_SAVED_BADGE_TOOLTIP_LABEL: Output(
            MIH.METRIC_SAVED_BADGE_TOOLTIP, "label"
        ),
        MIH.METRIC_MORE_MENU: Output(
            MIH.METRIC_MORE_MENU,
            "className",
        ),
        MIH.METRIC_SAVED_BADGE_TOOLTIP_CHILDREN: Output(
            MIH.METRIC_SAVED_BADGE_TOOLTIP, "children", allow_duplicate=True
        ),
    },
    prevent_initial_call=True,
    background=True,
    interval=C.GRAPH_POLL_INTERVAL_MS,
    running=[
        (
            Output(MIH.METRIC_SAVE_BUTTON, "disabled"),
            True,
            False,
        ),
        (
            Output(MIH.METRIC_SAVE_BUTTON, "loading"),
            True,
            False,
        ),
        (
            Output(MIH.METRIC_SAVE_AS_NEW_BUTTON, "loading"),
            True,
            False,
        ),
        (
            Output(MIH.METRIC_UPDATE_BUTTON, "loading"),
            True,
            False,
        ),
        (
            Output(MIH.ADD_TO_DASHBOARD_BUTTON, "disabled"),
            True,
            False,
        ),
        (
            Output(MIH.DELETE_INSIGHT_BUTTON, "disabled"),
            True,
            False,
        ),
    ],
)
def handle_save_metric(
    save_metric_nclicks: int,
    save_metric_click: int,
    update_metric_click: int,
    metric_title: str,
    metric_description: Optional[str],
    metric_id: str,
    href: str,
) -> Dict[str, Any]:
    open_add_to_dashboard_modal = ctx.triggered_id != MIH.METRIC_UPDATE_BUTTON
    return handle_saved_metric_change(
        metric_title=metric_title,
        metric_description=metric_description,
        metric_id=metric_id,
        href=href,
        open_add_to_dashboard_modal=open_add_to_dashboard_modal,
    )


def handle_saved_metric_change(
    metric_title: str,
    metric_description: Optional[str],
    metric_id: str,
    href: str,
    open_add_to_dashboard_modal: bool = False,
) -> Dict[str, Any]:
    response: Dict[str, Any] = {
        CONST.METRIC_ERROR_TOAST: None,
        CONST.SAVED_METRIC_ID: no_update,
        MIH.METRIC_SAVE_AS_NEW_BUTTON: no_update,
        MIH.METRIC_UPDATE_BUTTON: no_update,
        MIH.METRIC_SAVE_BUTTON: no_update,
        EDH.ADD_TO_DASHBOARD_MODAL: no_update,
        MIH.ADD_TO_DASHBOARD_BUTTON: no_update,
        MIH.DELETE_INSIGHT_BUTTON: no_update,
        MIH.METRIC_SAVED_BADGE_TOOLTIP: no_update,
        MIH.METRIC_SAVED_BADGE_TOOLTIP_LABEL: no_update,
        MIH.METRIC_MORE_MENU: no_update,
        MIH.METRIC_SAVED_BADGE_TOOLTIP_CHILDREN: no_update,
    }
    error: Optional[str] = None

    if not metric_title or metric_title.strip() == "":
        error = "Please add title to your metric."
    elif len(metric_title) > 60:
        error = "Metric title must be at most 60 characters."

    if metric_description is not None and len(metric_description) > 300:
        error = "Metric description must be at most 300 characters."

    if error is not None:
        response[CONST.METRIC_ERROR_TOAST] = dmc.Alert(
            error,
            color="red",
            title="Metric cannot be saved!",
            duration=5000,
            withCloseButton=True,
        )

        return response

    metric_title = metric_title.strip()
    if metric_description is not None:
        metric_description = metric_description.strip()

        if metric_description == "":
            metric_description = None

    deps = DEPS.Dependencies.get()
    try:
        path_parser = PathParser(href)
        project_id = path_parser.project_id
        project_storage = deps.get_project_storage(project_id)
        tracking_service = deps.tracking_service
        mitzu_cache = deps.cache

        webapp_metric = path_parser.get_metric(project_storage)
        webapp_metric = replace(
            webapp_metric,
            title=metric_title,
            description=metric_description,
        )

        catalog_service = deps.get_catalog_service(project_id)
        metric_context = ECC.get_metric_context(
            webapp_metric,
            catalog_service,
        )

        metric_defs = project_storage.get_definitions_for_metric(webapp_metric)
        project = metric_defs.project
        tracking_service.set_project(project)

        metric_updater = MetricUpdater(
            metric_context,
            project.insight_settings,
        )

        metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)
        metric = metric_converter.convert_metric(webapp_metric)
        hash_key = SE.get_metric_cache_key(webapp_metric, project)
        adapter_context = deps.create_adapter_context(
            href, project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            project,
            deps.cache,
            adapter_context,
            user_role=deps.user_role,
        )
        result_df, _ = get_metric_result_df(
            adapter=adapter,
            hash_key=hash_key,
            metric=metric,
            webapp_metric=webapp_metric,
            mitzu_cache=mitzu_cache,
            metric_origin=MetricOrigin.INSIGHT,
            tracking_service=tracking_service,
            href=href,
        )
        deps.get_events_service(project_id).enrich_metric_with_event_name_alias(
            metric, project_storage
        )
        visualizer = Visualizer.create_visualizer(
            result_df, webapp_metric, project, metric_context, metric
        )
        simple_chart = visualizer.generate_chart()

        if (
            metric_id is None
            or metric_id == ""
            or ctx.triggered_id == MIH.METRIC_SAVE_AS_NEW_BUTTON
        ):
            metric_id = H.create_unique_id()

        sm = GH.store_rendered_saved_metric(
            metric_name=metric_title,
            metric_description=metric_description,
            webapp_metric=webapp_metric,
            simple_chart=simple_chart,
            metric_defs=metric_defs,
            metric_id=metric_id,
            dependencies=deps,
        )

        response[CONST.SAVED_METRIC_ID] = metric_id
        response[MIH.METRIC_SAVE_AS_NEW_BUTTON] = {"display": "inline-block"}
        response[MIH.METRIC_UPDATE_BUTTON] = {"display": "inline-block"}
        response[MIH.METRIC_SAVE_BUTTON] = {"display": "none"}
        response[EDH.ADD_TO_DASHBOARD_MODAL] = open_add_to_dashboard_modal
        response[MIH.ADD_TO_DASHBOARD_BUTTON] = {"display": "flex"}
        response[MIH.DELETE_INSIGHT_BUTTON] = {"display": "flex"}
        response[MIH.METRIC_MORE_MENU] = "d-inline-block"
        response[MIH.METRIC_SAVED_BADGE_TOOLTIP_LABEL] = MIH.get_saved_badge_label(
            sm, deps
        )
        response[MIH.METRIC_SAVED_BADGE_TOOLTIP] = "d-inline-block me-2"
        response[MIH.METRIC_SAVED_BADGE_TOOLTIP_CHILDREN] = create_saved_badge()
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to save metric")
        response[CONST.METRIC_ERROR_TOAST] = dmc.Alert(
            str(exc),
            color="red",
            title="Metric cannot be saved!",
            duration=5000,
            withCloseButton=True,
        )

    return response


@callback(
    Output(EDH.ADD_TO_DASHBOARD_MODAL, "is_open", allow_duplicate=True),
    Input(MIH.ADD_TO_DASHBOARD_BUTTON, "n_clicks"),
    State(CONST.SAVED_METRIC_ID, "children"),
    prevent_initial_call=True,
)
def add_to_dashboard_clicked(open: int, saved_metric_id: Optional[str]) -> bool:
    if saved_metric_id is not None:
        return True
    return False


clientside_callback(
    """
    function(value) {
        return false;
    }
    """,
    Output(EDH.ADD_TO_DASHBOARD_MODAL, "is_open", allow_duplicate=True),
    Input(EDH.CLOSE_ADD_TO_DASHBOARD_MODAL, "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output(EDH.ADD_TO_DASHBOARD_MODAL, "is_open", allow_duplicate=True),
    Output(EDH.ADD_TO_DASHBOARD_MODAL_ERROR, "children", allow_duplicate=True),
    Input(EDH.CONFIRM_ADD_TO_DASHBOARD_BUTTON, "n_clicks"),
    State(CONST.SAVED_METRIC_ID, "children"),
    State(EDH.ADD_TO_DASHBOARD_CHIP_GROUP, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def update_add_to_dashboard(
    confirm_button_clicks: int,
    saved_metric_id: str,
    selected_dashboard_list: List[str],
    pathname: str,
) -> Tuple[bool, Union[bc.Component, List]]:
    if ctx.triggered_id != EDH.CONFIRM_ADD_TO_DASHBOARD_BUTTON:
        return no_update, []

    try:
        deps = DEPS.Dependencies.get()
        storage = deps.storage
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = storage.get_project_storage(project_id)
        dashboards = project_storage.list_dashboards_infos()

        for dashboard in dashboards:
            dashboard_storage = project_storage.get_dashboard_storage(dashboard.id)
            dashboard = dashboard_storage.get_dashboard()
            dashboard_saved_metircs = (
                dashboard_storage.get_dashboard_saved_metrics_ids()
            )
            if (
                dashboard.id in selected_dashboard_list
                and saved_metric_id not in dashboard_saved_metircs
            ):
                saved_metric = project_storage.get_saved_metric(saved_metric_id)
                new_dm = WM.DashboardMetricItem(
                    saved_metric=saved_metric,
                    x=0,
                    y=0,  # dashboard storage will overwrite this
                    width=DASHBOARD_DEF_WIDTH,
                    height=DASHBOARD_DEF_HEIGHT,
                    id=H.create_unique_id(),
                )

                dashboard_storage.append_dashboard_metric(new_dm)
                dashboard = dashboard_storage.get_dashboard()
                event_source_page = "explore_page"
                deps.tracking_service.track_add_metric_to_dashboard(
                    dashboard, saved_metric, event_source_page
                )
                deps.tracking_service.track_dashboard_saved(
                    dashboard, event_source_page
                )
            elif (
                dashboard.id not in selected_dashboard_list
                and saved_metric_id in dashboard_saved_metircs
            ):
                dashboard_metric_id = next(
                    dashboard_metric.id
                    for dashboard_metric in dashboard.dashboard_metrics
                    if isinstance(dashboard_metric, WM.DashboardMetricItem)
                    and dashboard_metric.saved_metric.id == saved_metric_id
                )
                DH.delete_dashboard_metric(dashboard_metric_id, dashboard, deps)
        return False, []

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to add saved metric to dashboard")
        return (
            no_update,
            html.Div(
                [
                    dmc.Alert(
                        "There was an error processing your request. Please try again or contact support.",
                        color="red",
                        withCloseButton=True,
                    )
                ],
                className="mt-2",
            ),
        )


clientside_callback(
    """
    function(value) {
        return [{"display": "none"}, {"display": "flex"}];
    }
    """,
    Output(GH.GRAPH_RUN_OVERLAY, "style", allow_duplicate=True),
    Output(GH.GRAPH_CANCEL_OVERLAY, "style", allow_duplicate=True),
    Input(GH.GRAPH_RUN_BUTTON, "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output(DIM.DELETE_INSIGHT_MODAL, "is_open"),
    Input(MIH.DELETE_INSIGHT_BUTTON, "n_clicks"),
    Input(DIM.DELETE_INSIGHT_MODAL_CLOSE, "n_clicks"),
    Input(DIM.DELETE_INSIGHT_MODAL_ACCEPT, "n_clicks"),
    State(CONST.SAVED_METRIC_ID, "children"),
    prevent_initial_call=True,
)
def delete_insight_button_clicked(
    delete: int, close: int, accept: int, saved_metric_id: str
) -> bool:
    if ctx.triggered_id is None or ctx.triggered_id in [
        DIM.DELETE_INSIGHT_MODAL_CLOSE,
        DIM.DELETE_INSIGHT_MODAL_ACCEPT,
    ]:
        return False

    if ctx.triggered_id == MIH.DELETE_INSIGHT_BUTTON and saved_metric_id is not None:
        return True
    return False


@callback(
    Output(DIM.DELETE_INSIGHT_MODAL_LOCATION, "href"),
    Output(CONST.METRIC_ERROR_TOAST, "children"),
    Input(DIM.DELETE_INSIGHT_MODAL_ACCEPT, "n_clicks"),
    State(CONST.SAVED_METRIC_ID, "children"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def delete_insight_accepted(
    delete_clicked: int, saved_metric_id: Optional[str], pathname: str
) -> Tuple[str, bc.Component]:
    if saved_metric_id is None:
        return no_update, no_update

    project_id = P.get_path_value(
        P.PROJECTS_EXPLORE_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    dependencies = DEPS.Dependencies.get()
    project_storage = dependencies.get_project_storage(project_id)
    try:
        if delete_clicked and saved_metric_id is not None:
            project_storage.clear_saved_metric(saved_metric_id)
        return P.create_path(P.PROJECT_HOME_PATH, project_id=project_id), no_update

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to delete insight on the explore page.")
        return no_update, dmc.Alert(
            f"Couldn't delete saved insight. Details: {str(exc)[:300]}",
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="m-2",
        )


@callback(
    Output(CONST.DIM_PROFILE_RESET, "href", allow_duplicate=True),
    Input({"id": CONST.DIM_PROFILE_CLOSE_ID, "type": ALL}, "n_clicks"),
    State(CONST.DIM_PROFILE_RESET, "pathname"),
    prevent_initial_call=True,
)
def reset_user_profile_id(n_clicks: int, pathname: str) -> str:
    if n_clicks:
        # as an improvement we can just remove the dimension_profile_config from the metric.
        return pathname
    else:
        return no_update


@callback(
    Output(UAT.DIM_PROFILE_ATTRIBUTES, "children"),
    Output(UAT.DIM_INFO_STORE, "data"),
    Input(UAT.DIM_ATTRIBUTES_TABLE_INPUT, "children"),
    State(WH.MITZU_LOCATION, "href"),
    State(WH.MITZU_LOCATION, "search"),
    background=True,
)
def load_user_attributes(
    user_profile_id: str,
    href: str,
    search: str,
) -> Tuple[bc.Component, Any]:
    try:
        dependencies = DEPS.Dependencies.get()
        if not user_profile_id:
            return (
                dmc.Text("No user selected.", size="sm", color="gray", className="p-2"),
                {},
            )

        parse_result = urlparse(href)
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH, parse_result.path, P.PROJECT_ID_PATH_PART
        )
        storage = dependencies.get_project_storage(project_id)
        project = storage.get_project()
        adapter_context = dependencies.create_adapter_context(
            parse_result.geturl(), project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            project,
            dependencies.cache,
            adapter_context,
            user_role=dependencies.user_role,
        )

        metric_str = parse_qs(search[1:]).get(P.PROJECTS_EXPLORE_METRIC_QUERY)
        if metric_str is None or len(metric_str) != 1:
            raise PreventUpdate()

        metric = SE.from_compressed_string(metric_str[0])
        if len(metric.global_filters) != 1 or not isinstance(
            metric.global_filters[0].left, M.DimensionIDFieldPath
        ):
            raise PreventUpdate()
        dim_table_type = metric.global_filters[0].left.entity

        metric_context = dependencies.get_catalog_service(
            project_id
        ).get_metric_context()
        dim_field_defs = [
            dim_field
            for dim_field in storage.get_dimension_fields(project).values()
            if dim_field.dimension_data_table.entity == dim_table_type
        ]
        user_df = adapter.get_single_dimension_df(user_profile_id, dim_field_defs)
        user_info_dict = user_df.to_dict(orient="records")[0]
        user_info_dict.pop(GA.FINAL_DIM_PRIMARY_KEY_COL)
        user_info_dict_converted: Dict[str, str] = {
            str(key): str(value) if value is not None else N_PER_A
            for key, value in user_info_dict.items()
        }
        user_info = UAT.format_dimension_info_dict(
            metric_context, user_info_dict_converted
        )
        return (
            UAT.create_dimension_profile_attributes_table(user_info),
            user_info,
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).exception("Couldn't load dimension properties.")
        return (
            dmc.Alert("Couldn't load dimension properties.", color="red"),
            no_update,
        )
