from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Union

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import ctx, html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.components.conversion_retention_window as CRW
import mitzu.webapp.pages.explore.components.metric_aggregation_handler as MAH
import mitzu.webapp.pages.explore.constants as CONST
import mitzu.webapp.pages.explore.metric_advanced_configs as MAC
import mitzu.webapp.pages.explore.post_processing_handler as PPH
import mitzu.webapp.pages.explore.toolbar_handler as DS
import mitzu.webapp.pages.explore.unique_field_handler as UFH
import mitzu.webapp.service.input_generator as IG
from mitzu.formula import (
    FormulaSyntaxError,
    validate_formula_string,
)
from mitzu.logger import LOGGER
from mitzu.webapp.pages import uikit
from mitzu.webapp.pages.explore import (
    y_range_config_handler,
)
from mitzu.webapp.pages.explore.inputs_parser import (
    get_time_window_from_inputs,
)
from mitzu.webapp.service.input_generator.generator import (
    InputGenerator,
)

METRICS_CONFIG_CONTAINER = "metrics_config_container"
GLOBAL_FILTER_PROPERTY_TYPE = "GLOBAL_FILTER_PROPERTY"
GLOBAL_FILTER_OPERATOR_TYPE = "GLOBAL_FILTER_OPERATOR"
GLOBAL_FILTER_VALUE_TYPE = "GLOBAL_FILTER_VALUE"


def calculate_time_horizon_days(
    start_dt: Optional[datetime],
    end_dt: Optional[datetime],
    lbd: Optional[M.TimeWindow],
    calendar_date_range: Optional[WM.CalendarDateRange],
) -> int:
    if lbd is not None:
        return lbd.to_approx_days()
    if start_dt and end_dt:
        return (end_dt - start_dt).days
    if calendar_date_range is not None:
        # convert the calender date range to days
        start_date, end_date = calendar_date_range.to_start_and_end_date(
            now=datetime.now()
        )
        return (end_date - start_date).days
    if start_dt is None or end_dt is None:
        # if one of the dts is missing, we should assume the user wants to look at one day of data
        # this is because the user selected a single day in the calendar
        return 1

    raise ValueError("None of the values for time window are present")


def from_metric(
    project_id: str,
    metric: WM.WebappMetric,
    metric_context: MC.MetricContext,
    input_generator: IG.InputGenerator,
    def_conv_attribution: M.ConversionAttribution,
) -> bc.Component:

    formula_comp = get_formula_input(metric)

    divider = html.Div(className="mb-4 border-bottom")

    ret_or_conv_window = M.DEF_CONV_WINDOW
    events_for_property_aggregation: List[M.EventNamePath] = []
    if metric.has_segments():
        if metric.metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
            if metric.config.conv_window is None:
                raise ValueError("Conversion window is not set")
            ret_or_conv_window = metric.config.conv_window
            for event_name_path in metric.segments[-1].get_event_name_paths():
                events_for_property_aggregation.append(event_name_path)

        elif metric.metric_type == M.MetricType.RETENTION:
            if metric.config.retention_window is None:
                raise ValueError("Retention window is not set")
            ret_or_conv_window = metric.config.retention_window
            if len(metric.segments) > 1:
                for event_name_path in metric.segments[1].get_event_name_paths():
                    events_for_property_aggregation.append(event_name_path)

        elif metric.metric_type == M.MetricType.SEGMENTATION:
            for seg in metric.segments:
                for event_name_path in seg.get_event_name_paths():
                    events_for_property_aggregation.append(event_name_path)
    else:
        if metric.metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
            ret_or_conv_window = M.DEF_CONV_WINDOW
        elif metric.metric_type == M.MetricType.RETENTION:
            ret_or_conv_window = M.DEF_RET_WINDOW

    dependencies = DEPS.Dependencies.get()
    storage = dependencies.storage
    project = storage.get_project(project_id)
    conversion_window = CRW.create_conversion_window_component(
        metric,
        ret_or_conv_window,
        def_conv_attribution,
        project.get_default_end_dt(),
    )

    measure = MAH.create_metric_options_component(
        metric,
        events_for_property_aggregation,
        metric_context=metric_context,
        input_generator=input_generator,
    )

    advanced = MAC.from_metric(metric)

    unique_field_selector = UFH.from_metric(metric, input_generator)
    metric_events = {
        enp for seg in metric.segments for enp in seg.get_event_name_paths()
    }
    return html.Div(
        [divider]
        + formula_comp
        + [unique_field_selector, conversion_window, measure, advanced]
        + [
            render_global_filters(metric.global_filters, input_generator, metric_events)
        ],
        id=METRICS_CONFIG_CONTAINER,
        style={"position": "relative"},
    )


def from_all_inputs(
    all_inputs: Dict[str, Any],
    metric_type: M.MetricType,
    insight_settings: M.InsightSettings,
    view_mode_store: str,
) -> WM.WebappMetricConfig:
    dates_conf, comparison_options, cut_off_data = DS.from_all_inputs(all_inputs)
    time_group = dates_conf.time_group
    if time_group is None:
        raise ValueError("Time group is not set")
    formula = all_inputs.get(CONST.FORMULA_INPUT)
    unique_field_str: Optional[str] = all_inputs.get(CONST.UNIQUE_FIELD_CHOICE)
    if unique_field_str is None or unique_field_str == M.USER_ENTITY.name:
        unique_field: Union[M.Entity, M.EventFieldPath] = M.USER_ENTITY
    else:
        # once the entities should come from the project
        try:
            unique_field = M.EventFieldPath.parse(unique_field_str)
        except Exception as exc:
            LOGGER.opt(exception=exc).bind(unique_field_str=unique_field_str).warning(
                "Cannot parse unique field path, assuming it's an entity"
            )
            unique_field = M.Entity(unique_field_str)

    conversion_window: Optional[M.TimeWindow] = None
    retention_window: Optional[M.TimeWindow] = None
    conv_attribution = None

    retention_indices = get_retention_indices(
        all_inputs.get(CONST.RETENTION_INDICES_SELECTOR), metric_type, time_group
    )
    if ctx.triggered_id == CONST.METRIC_TYPE_CHOICE:
        if metric_type == M.MetricType.RETENTION:
            retention_window = M.TimeWindow(1, M.TimeGroup.WEEK)
            time_group = M.TimeGroup.DAY
            agg_type, agg_param = M.AggType.RETENTION_RATE, None
            formula = None
            cut_off_data = insight_settings.cut_off_incomplete_data
            retention_indices = [1, 2]
        elif metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
            conv_window_tg = all_inputs.get(CONST.TIME_WINDOW_INTERVAL_STEPS)
            conv_window_val = all_inputs.get(CONST.TIME_WINDOW_INTERVAL)
            if conv_window_tg and conv_window_val:
                conversion_window = M.TimeWindow(
                    conv_window_val, M.TimeGroup.parse(conv_window_tg)
                )
            else:
                conversion_window = M.TimeWindow(1, M.TimeGroup.DAY)
            agg_type, agg_param = M.AggType.CONVERSION, None
            formula = None
        elif metric_type == M.MetricType.SEGMENTATION:
            agg_type, agg_param = M.AggType.COUNT_UNIQUE_USERS, None
        else:
            raise ValueError(f"Unknown metric type {metric_type}")
    else:
        default_time_window = get_time_window_from_inputs(all_inputs)
        if metric_type == M.MetricType.RETENTION:
            retention_window = default_time_window

        elif metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
            conversion_window = default_time_window

        agg_type_input = all_inputs[CONST.AGGREGATION_TYPE]
        agg_type, agg_param = M.AggType.parse_agg_str(agg_type_input)

    event_property_agg: Optional[WM.WebappEventPropertyAggregation] = None
    if agg_type == M.AggType.BY_EVENT_PROPERTY:
        if all_inputs.get(CONST.AGG_EVENT_PROPERTY_NAME) is None:
            event_property_agg = WM.WebappEventPropertyAggregation(
                agg_type=M.EventPropertyAggregationType(
                    all_inputs.get(CONST.AGG_EVENT_PROPERTY_TYPE)
                ),
                field_def=None,
            )
        else:
            event_field_input = all_inputs[CONST.AGG_EVENT_PROPERTY_NAME]
            field_path = M.FieldPath.parse(event_field_input)
            event_property_agg = WM.WebappEventPropertyAggregation(
                agg_type=M.EventPropertyAggregationType(
                    all_inputs.get(CONST.AGG_EVENT_PROPERTY_TYPE)
                ),
                field_def=field_path,
            )
    chart_type, chart_post_processing = PPH.from_all_inputs(
        all_inputs, time_group, metric_type
    )

    if ctx.triggered_id == CONST.METRIC_TYPE_CHOICE or chart_type not in [
        M.SimpleChartType.LINE,
        M.SimpleChartType.BAR,
        M.SimpleChartType.STACKED_BAR,
        M.SimpleChartType.STACKED_AREA,
    ]:
        y_range_config = None
    else:
        y_range_config = y_range_config_handler.from_all_inputs(
            all_inputs, metric_type, chart_type
        )
    view_mode = WM.ViewMode(view_mode_store)
    if ctx.triggered_id == CONST.EDIT_MODE_MENU_ITEM:
        view_mode = WM.ViewMode.EDIT
    elif ctx.triggered_id == CONST.TABLE_MODE_MENU_ITEM:
        view_mode = WM.ViewMode.TABLE
    elif ctx.triggered_id == CONST.CHART_MODE_MENU_ITEM:
        view_mode = WM.ViewMode.CHART
    show_other_groups = all_inputs.get(CONST.OTHER_GROUPS_STATE, False)

    ret_attr_type = None
    if metric_type == M.MetricType.RETENTION:
        ret_attr_type_str = all_inputs.get(
            CONST.RETENTION_ATTRIBUTION_TYPE, M.RetentionAttributionType.RETURN_ON.value
        )
        ret_attr_type = M.RetentionAttributionType.parse(ret_attr_type_str)
    conv_window_type = None
    if metric_type in [
        M.MetricType.CONVERSION,
        M.MetricType.JOURNEY,
        M.MetricType.RETENTION,
    ]:
        conv_window_type = M.ConversionWindowType.parse(
            all_inputs.get(
                CONST.CONVERSION_WINDOW_TYPE, M.ConversionWindowType.ENTIRE_FUNNEL.value
            )
        )
        def_conv_attribution = (
            insight_settings.retention_attribution
            if metric_type == M.MetricType.RETENTION
            else insight_settings.conversion_attribution
        )
        if (
            ctx.triggered_id != CONST.METRIC_TYPE_CHOICE
            and all_inputs.get(CONST.CONVERSION_ATTRIBUTION_TYPE) is not None
        ):
            conv_attribution = M.ConversionAttribution.parse(
                all_inputs[CONST.CONVERSION_ATTRIBUTION_TYPE]
            )
        else:
            conv_attribution = def_conv_attribution

    conv_orientation = None
    if metric_type in [M.MetricType.CONVERSION, M.MetricType.JOURNEY]:
        conv_orientation = M.ConversionOrientation.parse(
            all_inputs.get(
                CONST.CONVERSION_ORIENTATION_TYPE, M.ConversionOrientation.FORWARD.value
            )
        )

    resolution, holding_consts, user_sampling = MAC.from_all_inputs(
        all_inputs,
        metric_type,
        insight_settings,
        calculate_time_horizon_days(
            dates_conf.start_dt,
            dates_conf.end_dt,
            dates_conf.lookback_days,
            dates_conf.calendar_date_range,
        ),
    )

    res_config = WM.WebappMetricConfig(
        start_dt=dates_conf.start_dt,
        end_dt=dates_conf.end_dt,
        lookback_days=dates_conf.lookback_days,
        calendar_date_range=dates_conf.calendar_date_range,
        time_group=time_group,
        since_date=dates_conf.since_date,
        max_group_count=M.DEF_MAX_GROUP_COUNT,
        chart_type=chart_type,
        chart_post_processing=chart_post_processing,
        resolution=resolution,
        event_sampling=user_sampling,
        conv_window=conversion_window,
        conv_window_type=conv_window_type,
        conv_attribution=conv_attribution,
        conv_orientation=conv_orientation,
        use_consistent_coloring=all_inputs.get(CONST.CHART_CONSISTENT_COLORING, False),
        custom_holding_constants=(
            holding_consts if metric_type != M.MetricType.SEGMENTATION else []
        ),
        retention_window=retention_window,
        retention_attribution_type=ret_attr_type,
        aggregation=WM.WebappAggregationConfig(
            type=agg_type,
            conv_param=agg_param,
            event_property_agg=event_property_agg,
        ),
        formula=formula if formula is not None and formula.strip() != "" else None,
        unique_field=unique_field,
        retention_indices=retention_indices,
        comparison_options=comparison_options,
        y_range_config=y_range_config,
        cut_off_incomplete_data=cut_off_data,
        show_other_groups=show_other_groups,
        view_mode=view_mode,
    )
    return res_config


def get_retention_indices(
    retention_indices: Optional[List[int]],
    metric_type: M.MetricType,
    time_group: M.TimeGroup,
) -> Optional[List[int]]:
    if metric_type != M.MetricType.RETENTION:
        return None
    if (
        ctx.triggered_id in [CONST.TIME_GROUP_DROPDOWN, CONST.METRIC_TYPE_CHOICE]
        or retention_indices is None
    ):
        if time_group == M.TimeGroup.TOTAL:
            retention_indices = [CONST.RETENTION_INDICES_ALL_GROUPS]
        elif (
            retention_indices == [CONST.RETENTION_INDICES_ALL_GROUPS]
            or retention_indices is None
        ):
            retention_indices = [1]
        return retention_indices

    if not retention_indices:
        # in some edge case this may happen, TODO understand why
        return [CONST.RETENTION_INDICES_ALL_GROUPS]

    if retention_indices[-1] == CONST.RETENTION_INDICES_ALL_GROUPS:
        retention_indices = [CONST.RETENTION_INDICES_ALL_GROUPS]
    elif CONST.RETENTION_INDICES_ALL_GROUPS in retention_indices:
        retention_indices.remove(CONST.RETENTION_INDICES_ALL_GROUPS)

    return retention_indices


def get_formula_input(
    metric: WM.WebappMetric,
) -> List[Union[None, dmc.TextInput, dmc.Alert]]:
    segment_count = len(metric.segments)
    should_show = segment_count > 0 and metric.metric_type == M.MetricType.SEGMENTATION
    formula = (
        metric.config.formula
        if should_show and metric.config.formula is not None
        else ""
    )
    error: Optional[str] = None
    try:
        if segment_count < 1:
            formula = ""
        if formula:
            validate_formula_string(formula, segment_count)
    except FormulaSyntaxError as exc:
        error = str(exc)

    description = ""
    if not formula:
        description = "You can write simple algebraic formulas using segment values as variables. (e.g. A+B)"
    elif error:
        description = error

    return [
        dmc.TextInput(
            id=CONST.FORMULA_INPUT,
            placeholder="Add formula expression",
            description=description,
            inputWrapperOrder=["input", "description"],
            debounce=600,
            value=formula,
            className="mb-3 segment-hover",
            style={"display": "none"} if not should_show else {},
            icon=DashIconify(icon="mynaui:hash", width=17, className="ms-2"),
        )
    ]


def render_global_filters(
    global_filters: List[WM.WebappEventFilter],
    input_generator: InputGenerator,
    metric_events: Set[M.EventNamePath],
) -> List[dmc.Group]:
    input_rows = []
    if not global_filters:
        return []
    for index, filter in enumerate(global_filters):
        if isinstance(filter.left, M.DimensionIDFieldPath):
            continue
        dropdown_component = input_generator.create_property_dropdown_for_global_filter(
            selected=filter.left,
            placeholder="+ Filter",
            event_name_paths=metric_events,
        )

        input_rows.append(
            uikit.render_property_filter_row(
                property_id={
                    "type": GLOBAL_FILTER_PROPERTY_TYPE,
                    "index": str(index),
                },
                property_dropdown=dropdown_component,
                filter=filter,
                input_generator=input_generator,
                operator_id={
                    "type": GLOBAL_FILTER_OPERATOR_TYPE,
                    "index": str(index),
                },
                value_id={
                    "type": GLOBAL_FILTER_VALUE_TYPE,
                    "index": str(index),
                },
                read_only=False,
                allow_reindex=False,
            )
        )

    property_filter_dropdown = (
        input_generator.create_property_dropdown_for_global_filter(
            selected=None,
            placeholder="+ Filter" if len(global_filters) == 0 else "+ And",
            event_name_paths=metric_events,
        )
    )
    input_rows.append(
        uikit.render_property_filter_placeholder_row(
            {
                "type": GLOBAL_FILTER_PROPERTY_TYPE,
                "index": str(len(global_filters)),
            },
            property_filter_dropdown,
        )
    )
    return html.Div(input_rows, className="d-none")
