from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import parse_qs, urlparse

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    ctx,
    dcc,
    html,
    no_update,
)
from dash._utils import AttributeDict
from dash_iconify import DashIconify

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.visualization.plot as PLT
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.explore.post_processing_handler as PPH
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.adapter_factory as AF
from mitzu.configs import get_kaleido_configs
from mitzu.helper import parse_datetime_input
from mitzu.logger import LOGGER
from mitzu.visualization.visualizer import Visualizer
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.pages.explore import (
    y_range_config_handler,
)
from mitzu.webapp.pages.explore.components.common import (
    get_metric_context,
)
from mitzu.webapp.pages.explore.constants import (
    CHART_CONSISTENT_COLORING,
    CLEAR_LABEL_RENAMING,
    CUT_OFF_INCOMPLETE_DATA,
    DATE_SELECTOR_CONTAINER,
    EXPLORE_TWC,
    METRIC_ERROR_TOAST,
    PREVIOUS_COMPARISON_OPTIONS,
    SHOW_HIDE_ANNOTATIONS,
    STATE_DIV,
    TIME_GROUP_DROPDOWN,
)
from mitzu.webapp.pages.explore.metric_info_handler import (
    METRIC_NAME_INPUT,
)
from mitzu.webapp.service.adapter_service import (
    MetricOrigin,
    get_metric_result_df,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.service.path_parser import PathParser

SQL_CONTAINER = "graph_sql_container"
SQL_AREA = "sql_area"
SHOW_SQL_BUTTON = "show_sql_button"
SQL_MODAL = "show_sql_modal"
DOWNLOAD_CHART_BUTTON = "download_chart_button"
IMAGE_DOWNLOAD = "image_download"
SQL_MODAL_CLOSE = "sql_modal_close"

COLOR_CONSISTENCY_TOOLTIP = "Colors will be assigned based on group values, ensuring consistent coloring across all charts."


def should_show_more_menu(metric: WM.WebappMetric) -> bool:
    if metric.metric_type in [M.MetricType.RETENTION, M.MetricType.JOURNEY]:
        return len(metric.segments) >= 2
    return metric.has_segments()


def create_sql_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=SQL_MODAL,
        title="Query",
        content=html.Div(id=SQL_CONTAINER),
        modal_primary_button=None,
        close_button_id=SQL_MODAL_CLOSE,
        config=UIKIT.SQL_MODAL,
    )


clientside_callback(
    """
    function(value) {
        return false;
    }
    """,
    Output(SQL_MODAL, "is_open", allow_duplicate=True),
    Input(SQL_MODAL_CLOSE, "n_clicks"),
    prevent_initial_call=True,
)


def create_sql_area(
    project: M.Project,
    metric: Optional[M.Metric],
    adapter_context: GA.AdapterContext,
) -> bc.Component:
    if metric is None:
        return html.Div(id=SQL_AREA)
    deps = DEPS.Dependencies.get()
    adapter = AF.create_adapter_from_project(
        project,
        deps.cache,
        adapter_context,
        user_role=deps.user_role,
    )

    return dmc.Prism(
        children=adapter.get_sql(metric),
        language="sql",
        id=SQL_AREA,
        className="w-100 bg-white",
    )


def get_time_group_option_label(tg: M.TimeGroup, metric_type: M.MetricType) -> str:
    if tg == M.TimeGroup.TOTAL:
        if metric_type == M.MetricType.SEGMENTATION:
            return "Overall segmentation"
        elif metric_type == M.MetricType.CONVERSION:
            return "Funnel steps"
        elif metric_type == M.MetricType.RETENTION:
            return "Overall retention"
        elif metric_type == M.MetricType.JOURNEY:
            return "Journey steps"
    else:
        if metric_type == M.MetricType.SEGMENTATION:
            return f"{tg.group_by_string()} trend"
        elif metric_type == M.MetricType.CONVERSION:
            return f"{tg.group_by_string()} funnel trend"
        elif metric_type == M.MetricType.RETENTION:
            return f"{tg.group_by_string()} retention change"
    raise ValueError(
        f"Unsupported combination for timegroup label: {tg.name}, {metric_type.name}"
    )


def get_time_group_options(metric_type: M.MetricType) -> List[Dict[str, Any]]:
    exclude = [
        M.TimeGroup.SECOND,
        M.TimeGroup.MINUTE,
        M.TimeGroup.QUARTER,
        M.TimeGroup.YEAR,
    ]
    if metric_type == M.MetricType.RETENTION:
        exclude.append(M.TimeGroup.HOUR)
    elif metric_type == M.MetricType.JOURNEY:
        exclude = [tg for tg in M.TimeGroup if tg != M.TimeGroup.TOTAL]

    return [
        {
            "label": get_time_group_option_label(tg, metric_type),
            "value": tg.value,
        }
        for tg in M.TimeGroup
        if tg not in exclude
    ]


def filter_comparison_options(option: Dict[str, Any], time_group: M.TimeGroup) -> bool:
    if option["value"] is None:
        return True

    option_time_window = M.TimeWindow.parse(option["value"])
    if time_group == M.TimeGroup.DAY:
        return True
    if time_group == M.TimeGroup.WEEK:
        return option_time_window.period in [M.TimeGroup.WEEK, M.TimeGroup.YEAR]
    if time_group == M.TimeGroup.MONTH:
        return option_time_window.period in [M.TimeGroup.MONTH, M.TimeGroup.YEAR]
    if time_group == M.TimeGroup.YEAR:
        return option_time_window.period in [M.TimeGroup.YEAR]
    if time_group == M.TimeGroup.HOUR:
        return (
            option_time_window.period in [M.TimeGroup.DAY, M.TimeGroup.WEEK]
            and option_time_window.value == 1  # restrict to 1 week
        )
    return False


def get_previous_comparison_options(
    time_group: M.TimeGroup,
) -> List[Dict[str, Optional[str]]]:
    all_options: List[Dict[str, Optional[str]]] = [
        {"label": "No comparison", "value": None},
        {
            "label": "Compare with previous day",
            "value": M.TimeWindow(1, M.TimeGroup.DAY).serialize(),
        },
        {
            "label": "Compare with previous week",
            "value": M.TimeWindow(1, M.TimeGroup.WEEK).serialize(),
        },
        {
            "label": "Compare with previous month",
            "value": M.TimeWindow(1, M.TimeGroup.MONTH).serialize(),
        },
        {
            "label": "Compare with 4 weeks ago",
            "value": M.TimeWindow(4, M.TimeGroup.WEEK).serialize(),
        },
        {
            "label": "Compare with previous year",
            "value": M.TimeWindow(1, M.TimeGroup.YEAR).serialize(),
        },
    ]

    return list(
        filter(
            lambda opt: filter_comparison_options(opt, time_group),
            all_options,
        )
    )


def match_comparison_options_with_time_group(
    select_values: List[Dict[str, Optional[str]]], time_group: M.TimeGroup
) -> Optional[str]:
    """attempts to find a matching comparison option from a list of select_values based on
    the time_group. It returns the serialized value of the first comparison option that matches the provided time_group.
    If no match is found, it returns the last value in the list"""
    return next(
        (
            item["value"]
            for item in select_values
            if item["value"] is not None
            and M.TimeWindow.parse(item["value"]).period == time_group
        ),
        select_values[-1]["value"],
    )


def create_previous_comparison_select(metric: WM.WebappMetric) -> bc.Component:
    disabled = False
    if (
        metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL
        or metric.metric_type == M.MetricType.JOURNEY
        or not metric.segments
    ):
        disabled = True
    if (
        metric.config.comparison_options
        and metric.config.comparison_options.time_window
    ):
        comparison_time_window = (
            metric.config.comparison_options.time_window.serialize()
        )
    else:
        comparison_time_window = None

    select_values = get_previous_comparison_options(metric.config.time_group)
    allwed_time_windows = [item["value"] for item in select_values]

    if (
        metric.config.comparison_options
        and metric.config.comparison_options.time_window.serialize()
        not in allwed_time_windows
    ):
        comparison_time_window = match_comparison_options_with_time_group(
            select_values, metric.config.time_group
        )
    return html.Div(
        dmc.Select(
            id=PREVIOUS_COMPARISON_OPTIONS,
            data=get_previous_comparison_options(metric.config.time_group),
            value=comparison_time_window if not disabled else None,
            icon=DashIconify(icon="mdi:calendar-clock"),
            size="xs",
            className="d-inline-block" if not disabled else "d-none",
            variant="default",
            style={"width": "225px", "height": "32px"},
        )
    )


def create_previous_comparison_buttons() -> bc.Component:
    return dmc.Group(
        [
            dmc.Button(
                DashIconify(icon="mdi:chart-line-variant", width=20, height=20),
                id="trend_button",
                size="xs",
                variant="subtle",
                color="gray",
                style={"padding": "4px", "minWidth": "auto"},
            ),
            dmc.Button(
                DashIconify(icon="mdi:percent-outline", width=20, height=20),
                id="percent_button",
                size="xs",
                variant="subtle",
                color="gray",
                style={"padding": "4px", "minWidth": "auto"},
            ),
        ],
        spacing=0,
        style={"marginLeft": "5px"},
    )


def from_metric(
    metric: WM.WebappMetric,
    show_annotations: bool,
    use_consistent_coloring: bool,
    insight_settings: M.InsightSettings,
) -> bc.Component:
    tg_val = metric.config.time_group
    y_range_modal, y_range_menu_item = y_range_config_handler.from_metric(
        metric.config.y_range_config, metric.metric_type, metric.config.chart_type
    )
    comp = dmc.Group(
        [
            create_sql_modal(),
            y_range_modal,
            TWC.create_timewindow_chooser(
                WM.TimeWindowChooserConfig.create_from_webapp_metric_config(
                    metric.config
                ),
                EXPLORE_TWC,
                insight_settings=insight_settings,
                popup_position="bottom-start",
            ),
            html.Div(
                dmc.Select(
                    id=TIME_GROUP_DROPDOWN,
                    data=get_time_group_options(metric.metric_type),
                    icon=DashIconify(
                        icon="mdi:chart-timeline-variant"
                        if tg_val != M.TimeGroup.TOTAL
                        else "mdi:chart-bar"
                    ),
                    value=tg_val.value,
                    size="xs",
                    className="d-inline-block",
                    variant="default",
                    style={"width": "215px", "height": "32px"},
                ),
            ),
            PPH.from_metric(metric),
            # add comparison value from metric
            create_previous_comparison_select(metric),
            dcc.Download(id=IMAGE_DOWNLOAD, type="image/png"),
            dmc.Menu(
                [
                    dmc.MenuTarget(
                        UIKIT.icon_button(icon="mdi:plus", size="md"),
                    ),
                    dmc.MenuDropdown(
                        [
                            UIKIT.menu_item(
                                "Show SQL",
                                icon="mdi:show-outline",
                                id=SHOW_SQL_BUTTON,
                            ),
                            UIKIT.menu_item(
                                "Download chart",
                                icon="mynaui:download",
                                id=DOWNLOAD_CHART_BUTTON,
                            ),
                            dmc.MenuItem(
                                dmc.Chip(
                                    "Global annotations",
                                    id=SHOW_HIDE_ANNOTATIONS,
                                    checked=show_annotations,
                                    className="first-event-filter",
                                    variant="subtle",
                                    color="dark",
                                    size="xs",
                                ),
                                icon=DashIconify(icon="mynaui:flag"),
                                className="d-none"
                                if metric.metric_type == M.MetricType.JOURNEY
                                else "",
                            ),
                            dmc.MenuItem(
                                UIKIT.add_help_tooltip(
                                    component=[
                                        dmc.Chip(
                                            "Consistent colors",
                                            id=CHART_CONSISTENT_COLORING,
                                            checked=use_consistent_coloring,
                                            className="first-event-filter",
                                            variant="subtle",
                                            color="dark",
                                            size="xs",
                                        ),
                                    ],
                                    label=COLOR_CONSISTENCY_TOOLTIP,
                                ),
                                icon=DashIconify(icon="fluent:color-24-regular"),
                                className="d-none"
                                if metric.metric_type == M.MetricType.JOURNEY
                                else "",
                            ),
                            dmc.MenuItem(
                                dmc.Chip(
                                    "Cut off incomplete data",
                                    id=CUT_OFF_INCOMPLETE_DATA,
                                    checked=metric.config.cut_off_incomplete_data,
                                    className="first-event-filter",
                                    variant="subtle",
                                    color="dark",
                                    size="xs",
                                ),
                                icon=DashIconify(icon="mdi:content-cut"),
                                className="d-none"
                                if metric.metric_type != M.MetricType.RETENTION
                                else "",
                            ),
                            y_range_menu_item,
                            UIKIT.menu_item(
                                "Clear label renaming",
                                icon="mdi:close",
                                id=CLEAR_LABEL_RENAMING,
                                disabled=len(metric.label_mapping) == 0,
                            ),
                        ],
                    ),
                ],
                className="" if should_show_more_menu(metric) else "d-none",
            ),
        ],
        position="left",
        align="start",
        spacing="sm",
        id=DATE_SELECTOR_CONTAINER,
        className="d-none" if metric.config.view_mode != WM.ViewMode.EDIT else "",
    )

    return comp


def from_all_inputs(
    all_inputs: Dict[str, Any]
) -> Tuple[WM.TimeWindowChooserConfig, Optional[M.ComparisonOptions], bool]:
    lookback_days: Optional[M.TimeWindow] = None
    start_dt: Optional[datetime] = None
    end_dt: Optional[datetime] = None
    calendar_date_range: Optional[WM.CalendarDateRange] = None
    comparison_options: Optional[M.ComparisonOptions] = None
    since_date: Optional[datetime] = None
    if tw_val := all_inputs.get(TWC.LOOKBACK_WINDOW_DROPDOWN):
        lookback_days = M.TimeWindow.parse(str(tw_val))
    if custom_dates := all_inputs.get(TWC.CUSTOM_DATE_PICKER, [None, None]):
        start_dt = parse_datetime_input(custom_dates[0], def_val=None)
        end_dt = parse_datetime_input(custom_dates[1], def_val=None)
    if cdr := all_inputs.get(TWC.CALENDAR_DATE_RANGE_CHOOSER):
        calendar_date_range = WM.CalendarDateRange(cdr)

    if cop := all_inputs.get(PREVIOUS_COMPARISON_OPTIONS):
        comparison_options = M.ComparisonOptions(time_window=M.TimeWindow.parse(cop))
    if sd := all_inputs.get(TWC.SINCE_DATE_PICKER):
        since_date = parse_datetime_input(sd, def_val=None)
    if type(ctx.triggered_id) == AttributeDict:
        # Handling the interactions in the modal
        triggered_id = ctx.triggered_id["index"]
        if triggered_id == TWC.LOOKBACK_WINDOW_DROPDOWN:
            start_dt = None
            end_dt = None
            calendar_date_range = None
            since_date = None

        elif triggered_id == TWC.CALENDAR_DATE_RANGE_CHOOSER:
            start_dt = None
            end_dt = None
            lookback_days = None
            since_date = None

        elif triggered_id == TWC.CUSTOM_DATE_PICKER and start_dt and end_dt:
            lookback_days = None
            calendar_date_range = None
            since_date = None
        elif triggered_id == TWC.SINCE_DATE_PICKER:
            lookback_days = None
            calendar_date_range = None
            start_dt = None
            end_dt = None
    time_group = M.TimeGroup(all_inputs.get(TIME_GROUP_DROPDOWN))

    select_values = get_previous_comparison_options(time_group)
    allwed_time_windows = [item["value"] for item in select_values]
    cut_off_incomplete_data = all_inputs.get(CUT_OFF_INCOMPLETE_DATA, False)
    if (
        comparison_options
        and comparison_options.time_window.serialize() not in allwed_time_windows
    ):
        comparison_time_window = match_comparison_options_with_time_group(
            select_values, time_group
        )
        if comparison_time_window is not None:
            comparison_options = M.ComparisonOptions(
                time_window=M.TimeWindow.parse(comparison_time_window),
                formula=M.ComparisonFormula.NO_FORMULA,
            )
    return (
        WM.TimeWindowChooserConfig(
            start_dt=start_dt,
            end_dt=end_dt,
            lookback_days=lookback_days,
            time_group=time_group,
            calendar_date_range=calendar_date_range,
            since_date=since_date,
        ),
        comparison_options,
        cut_off_incomplete_data,
    )


@callback(
    Output(SQL_CONTAINER, "children"),
    Output(SQL_MODAL, "is_open", allow_duplicate=True),
    Input(SHOW_SQL_BUTTON, "n_clicks"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_show_sql(show: int, location_href: str) -> Tuple[bc.Component, bool]:
    try:
        if not show:
            return [], False

        deps = DEPS.Dependencies.get()

        path_parser = PathParser(location_href)
        project_id = path_parser.project_id
        project_storage = deps.get_project_storage(project_id)

        webapp_metric = path_parser.get_metric(project_storage)

        metric_defs = project_storage.get_definitions_for_metric(webapp_metric)
        adapter_context = deps.create_adapter_context(
            location_href,
            metric_defs.project,
            GA.QueryType.INSIGHT,
        )
        metric_context = get_metric_context(
            webapp_metric, deps.get_catalog_service(project_id)
        )
        metric_updater = MetricUpdater(
            metric_context, project_storage.get_project().insight_settings
        )
        metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)
        sql_area = create_sql_area(
            metric_defs.project,
            metric_converter.convert_metric(webapp_metric),
            adapter_context,
        )
        return sql_area, True
    except Exception as exc:
        LOGGER.opt(exception=exc).error(str(exc))
        return dmc.Text(str(exc)), True


@callback(
    Output(IMAGE_DOWNLOAD, "data"),
    Output(METRIC_ERROR_TOAST, "children", allow_duplicate=True),
    Input(DOWNLOAD_CHART_BUTTON, "n_clicks"),
    State(STATE_DIV, "children"),
    State(METRIC_NAME_INPUT, "value"),
    State(SHOW_HIDE_ANNOTATIONS, "checked"),
    prevent_initial_call=True,
)
def download_clicked(
    n_clicks: Optional[int],
    state_href: Optional[str],
    metric_title: Optional[str],
    show_annotations: bool,
) -> Tuple[Dict[str, Any], Any]:
    if not n_clicks or not state_href:
        return no_update, no_update

    try:
        deps = DEPS.Dependencies.get()
        storage = deps.storage
        tracking_service = deps.tracking_service
        mitzu_cache = deps.cache
        start_dt = datetime.now()
        parse_result = urlparse(state_href)
        project_id = P.get_path_value(
            P.PROJECTS_EXPLORE_PATH, parse_result.path, P.PROJECT_ID_PATH_PART
        )
        project_storage = storage.get_project_storage(project_id)
        metric_v64 = parse_qs(parse_result.query).get(P.PROJECTS_EXPLORE_METRIC_QUERY)
        if metric_v64:
            webapp_metric = SE.from_compressed_string(
                metric_v64[0] if metric_v64 else ""
            )
        else:
            return no_update

        metric_defs = project_storage.get_definitions_for_metric(webapp_metric)
        project = metric_defs.project

        hash_key = SE.get_metric_cache_key(webapp_metric, project)
        adapter_context = deps.create_adapter_context(
            parse_result.geturl(), project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            project,
            deps.cache,
            adapter_context,
            user_role=deps.user_role,
        )

        catalog_service = deps.get_catalog_service(project_id)
        metric_context = get_metric_context(webapp_metric, catalog_service)
        metric_updater = MetricUpdater(metric_context, project.insight_settings)
        metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)
        metric = metric_converter.convert_metric(webapp_metric)
        result_df, _ = get_metric_result_df(
            adapter=adapter,
            hash_key=hash_key,
            metric=metric,
            webapp_metric=webapp_metric,
            mitzu_cache=mitzu_cache,
            tracking_service=tracking_service,
            metric_origin=MetricOrigin.INSIGHT,  # the only place from where you can download a chart
            href=state_href,
        )
        deps.get_events_service(project_id).enrich_metric_with_event_name_alias(
            metric, project_storage
        )
        visualizer = Visualizer(
            result_df, webapp_metric, project, metric_context, metric
        )
        simple_chart = visualizer.generate_chart()
        annotations = (
            deps.get_project_storage(project.id).list_annotations()
            if show_annotations
            else []
        )
        fig, _ = PLT.plot_chart(
            simple_chart,
            metric,
            metric_context,
            annotations,
            webapp_metric,
            metric_defs.project.get_default_end_dt(),
        )

        fig.update_layout(
            title_text=metric_title if metric_title else "",
            title_x=0.5,
            title_y=0.95,
            margin=dict(t=60),
            plot_bgcolor="white",
            paper_bgcolor="white",
            showlegend=(
                metric.type == M.MetricType.SEGMENTATION
                or (
                    len(metric.segments) >= 1
                    and any([len(segment.group_by) > 0 for segment in metric.segments])
                )
            ),
            legend=dict(
                x=0.4,
                y=-0.1,
                font=dict(family="BlinkMacSystemFont,Segoe UI", size=12),
                orientation="h",
                title=None,
                xanchor="center",
            ),
        )

        image_b64 = PLT.figure_to_base64_image(
            fig,
            1,
            kaleid_configs=get_kaleido_configs(),
            format="png",
            base64_format="",
        )
        filename = metric_title if metric_title else "chart"
        duration = (datetime.now() - start_dt).seconds
        tracking_service.track_chart_downloaded(filename, duration)
        return (
            {
                "filename": f"{filename}.png",
                "base64": True,
                "content": image_b64,
            },
            no_update,
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to create image for download")
        return no_update, dmc.Alert(
            str(exc)[:400],
            color="red",
            title="Failed to create image for download",
            duration=5000,
            withCloseButton=True,
        )


clientside_callback(
    """
    function(checked) {
        window.dash_clientside.clientside.toggle_graph_annotations(checked);
        return checked;
    }
    """,
    Output(SHOW_HIDE_ANNOTATIONS, "checked", allow_duplicate=True),
    Input(SHOW_HIDE_ANNOTATIONS, "checked"),
    prevent_initial_call=True,
)
