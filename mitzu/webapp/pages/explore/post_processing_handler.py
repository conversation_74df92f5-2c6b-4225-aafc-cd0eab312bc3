from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import ctx, dcc, html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.model as WM
from mitzu.webapp.pages.explore.constants import (
    CHART_POST_PROCESSING_DD,
    CHART_TYPE_DD,
    DIM_PROFILE_ID,
    METRIC_TYPE_CHOICE,
    TIME_GROUP_DROPDOWN,
)

CHART_TYPE_CONTAINER = "chart_type_container"
CHART_TYPE_ICONS = {
    "line": "mdi:chart-line",
    "stacked_bar": "mdi:chart-bar-stacked",
    "percentage_stacked_bar": "material-symbols:full-stacked-bar-chart-rounded",
    "bar": "mdi:chart-bar",
    "number": "mdi:hashtag",
    "stacked_area": "mdi:chart-areaspline",
    "percentage_stacked_area": "gravity-ui:chart-area-stacked-normalized",
    "heatmap": "mdi:view-grid-plus",
    "sankey": "mdi:chart-sankey-variant",
    "geo_country": "mdi:globe",
    "get_lat_long": "gravity-ui:geo-pin",
    "pie": "stash:chart-pie",
    "horizontal_bar": "ph:chart-bar-horizontal-bold",
}

ROLL_AVG_STEPS = {
    M.TimeGroup.HOUR: [2, 4, 6, 12],
    M.TimeGroup.DAY: [3, 7, 14, 21],
    M.TimeGroup.WEEK: [2, 4, 8, 16],
    M.TimeGroup.MONTH: [2, 3, 6, 12],
    M.TimeGroup.YEAR: [2, 3, 5, 10],
}


def create_chart_type_dropdown_options(
    time_group: M.TimeGroup, metric_type: M.MetricType
) -> List[M.SimpleChartType]:
    if time_group == M.TimeGroup.TOTAL:
        options = []
        if metric_type == M.MetricType.SEGMENTATION:
            options = [
                M.SimpleChartType.BAR,
                M.SimpleChartType.STACKED_BAR,
                M.SimpleChartType.HORIZONTAL_BAR,
                M.SimpleChartType.NUMBER,
                M.SimpleChartType.GEO_COUNTRY,
                M.SimpleChartType.PERCENTAGE_STACKED_BAR,
                M.SimpleChartType.PIE,
            ]

        elif metric_type == M.MetricType.CONVERSION:
            options = [
                M.SimpleChartType.BAR,
                M.SimpleChartType.GEO_COUNTRY,
            ]

        elif metric_type == M.MetricType.RETENTION:
            options = [M.SimpleChartType.BAR, M.SimpleChartType.LINE]
        elif metric_type == M.MetricType.JOURNEY:
            options = [M.SimpleChartType.SANKEY]
        else:
            raise ValueError(f"Unknown metric type: {metric_type}")
    else:
        options = [
            M.SimpleChartType.LINE,
            M.SimpleChartType.BAR,
        ]
        if metric_type == M.MetricType.SEGMENTATION:
            options.extend(
                [
                    M.SimpleChartType.STACKED_BAR,
                    M.SimpleChartType.STACKED_AREA,
                    M.SimpleChartType.PERCENTAGE_STACKED_BAR,
                    M.SimpleChartType.PERCENTAGE_STACKED_AREA,
                ]
            )
        if metric_type == M.MetricType.RETENTION:
            options.insert(1, M.SimpleChartType.HEATMAP)

        if metric_type == M.MetricType.JOURNEY:
            options = [M.SimpleChartType.SANKEY]
    return options


def create_chart_type_dropdown(metric: WM.WebappMetric) -> dcc.Dropdown:
    options = create_chart_type_dropdown_options(
        metric.config.time_window_chooser_config.time_group, metric.metric_type
    )
    if metric.config.chart_type in options:
        ct = metric.config.chart_type
    else:
        ct = options[0]

    if (
        len(metric.segments) == 0
        and metric.dimension_profile_config is not None
        and metric.metric_type == M.MetricType.SEGMENTATION
    ):
        ct = M.SimpleChartType.STACKED_BAR

    return dmc.Select(
        data=[{"label": str(o), "value": o.name} for o in options],
        value=ct.name,
        id=CHART_TYPE_DD,
        size="xs",
        style={
            "display": "inline-block",
        },
        icon=DashIconify(icon=CHART_TYPE_ICONS.get(ct.name.lower(), "")),
        variant="default",
        className="d-none" if not metric.has_segments() else "d-inline-block",
    )


def create_chart_post_processing_dropdown(metric: WM.WebappMetric) -> dcc.Dropdown:
    default_options = [{"label": "No post processing", "value": None}]
    if (
        metric.metric_type != M.MetricType.SEGMENTATION
        or metric.config.time_window_chooser_config.time_group == M.TimeGroup.TOTAL
    ):
        return dmc.Select(
            id=CHART_POST_PROCESSING_DD,
            className="d-none",
            data=default_options,
            value=None,
            size="xs",
        )

    options = default_options
    steps = ROLL_AVG_STEPS.get(
        metric.config.time_window_chooser_config.time_group, [2, 4, 8, 16]
    )
    if (
        metric.config.chart_post_processing
        and metric.config.chart_post_processing.rolling_window_size is not None
        and metric.config.chart_post_processing.rolling_window_size not in steps
    ):
        # Ensuring backwards compatibility
        steps.append(metric.config.chart_post_processing.rolling_window_size)

    for step in steps:
        options.append(
            {
                "label": f"Rolling avg. {step} {metric.config.time_window_chooser_config.time_group}",
                "value": str(
                    M.ChartPostProcessing(M.ChartPostProcessingType.ROLLING_AVG, step)
                ),
            }
        )

    options.append(
        {
            "label": "Cummulative sum",
            "value": str(
                M.ChartPostProcessing(M.ChartPostProcessingType.CUMMULATIVE_SUM)
            ),
        }
    )

    return dmc.Select(
        data=options,
        value=(
            str(metric.config.chart_post_processing)
            if metric.config.chart_post_processing
            else None
        ),
        id=CHART_POST_PROCESSING_DD,
        icon=DashIconify(icon="carbon:chart-average"),
        variant="default",
        size="xs",
        className="d-none" if not metric.has_segments() else "d-inline-block ms-3",
    )


def from_metric(metric: WM.WebappMetric) -> bc.Component:
    return html.Div(
        [
            create_chart_type_dropdown(metric),
            create_chart_post_processing_dropdown(metric),
        ],
        id=CHART_TYPE_CONTAINER,
        className="d-inline-block",
    )


def from_all_inputs(
    all_inputs: Dict[str, Any],
    time_group: M.TimeGroup,
    metric_type: M.MetricType,
) -> Tuple[M.SimpleChartType, Optional[M.ChartPostProcessing]]:
    chart_post_processing_val = all_inputs.get(CHART_POST_PROCESSING_DD, None)
    chart_type_val = all_inputs.get(CHART_TYPE_DD, None)
    chart_type = M.SimpleChartType.parse(chart_type_val)

    if ctx.triggered_id == METRIC_TYPE_CHOICE:
        if metric_type == M.MetricType.RETENTION:
            chart_type = M.SimpleChartType.LINE
        elif metric_type == M.MetricType.JOURNEY:
            chart_type = M.SimpleChartType.SANKEY
        elif metric_type == M.MetricType.CONVERSION:
            if time_group == M.TimeGroup.TOTAL:
                chart_type = M.SimpleChartType.BAR
            else:
                chart_type = M.SimpleChartType.LINE
        elif metric_type == M.MetricType.SEGMENTATION:
            if all_inputs.get(DIM_PROFILE_ID) is not None:
                chart_type = M.SimpleChartType.STACKED_BAR
            else:
                chart_type = M.SimpleChartType.LINE
    elif ctx.triggered_id == TIME_GROUP_DROPDOWN:
        if time_group == M.TimeGroup.TOTAL:
            if metric_type == M.MetricType.RETENTION:
                chart_type = M.SimpleChartType.LINE
            elif metric_type == M.MetricType.JOURNEY:
                chart_type = M.SimpleChartType.SANKEY
            else:
                chart_type = M.SimpleChartType.BAR
        else:
            if metric_type == M.MetricType.RETENTION:
                chart_type = M.SimpleChartType.LINE
            elif metric_type == M.MetricType.CONVERSION:
                chart_type = M.SimpleChartType.LINE

    chart_post_processing = None
    if (
        chart_post_processing_val
        and metric_type == M.MetricType.SEGMENTATION
        and time_group != M.TimeGroup.TOTAL
        and ctx.triggered_id != TIME_GROUP_DROPDOWN
    ):
        chart_post_processing = M.ChartPostProcessing.parse(chart_post_processing_val)

    ct_options = create_chart_type_dropdown_options(time_group, metric_type)
    if chart_type not in ct_options:
        chart_type = M.SimpleChartType.BAR

    return chart_type, chart_post_processing
