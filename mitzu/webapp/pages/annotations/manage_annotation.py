from dataclasses import replace
from datetime import datetime
from typing import Optional, <PERSON>ple

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dcc,
    html,
    no_update,
    register_page,
)
from dash_iconify import DashIconify

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
from mitzu.helper import parse_datetime_input
from mitzu.logger import LOGGER
from mitzu.visualization.common import Annotation
from mitzu.webapp.helper import (
    MITZU_LOCATION,
    approx_datetime_diff_to_str,
)
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.error_pages.not_found_page import (
    not_found_page,
)

ANNOTATION_NAME_INPUT = "annotation_name_input"
ANNOTATION_DESC_INPUT = "annotation_desc_input"
DELETE_ANNOTATION_BUTTON = "delete_annotation_button"
ANNOTATION_LOCATION = "annotation_location"
ANNOTATION_META_TABLE = "annotation_meta_table"
ANNOTATION_NOTIFICATION = "annotation_notification"

DELETE_ANNOTATION_MODAL = "delete_annotation_modal"
DELETE_ANNOTATION_MODAL_CLOSE = "delete_annotation_modal_close"
DELETE_ANNOTATION_MODAL_ACCEPT = "delete_annotation_modal_accept"
ANNOTATION_DATE_PICKER = "annotation_datetime_picker"
ANNOTATION_TIME_INPUT = "annotation_time_input"


def delete_annotation_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=DELETE_ANNOTATION_MODAL,
        title="Do you really want to delete this annotation?",
        content=None,
        modal_primary_button=UIKIT.ModalButton.delete(DELETE_ANNOTATION_MODAL_ACCEPT),
        close_button_id=DELETE_ANNOTATION_MODAL_CLOSE,
        config=UIKIT.CONFIRM_MODAL,
    )


def create_annotation_meta_table(
    annotation: Annotation, annotation_owner_email: str
) -> dmc.Table:
    dt = annotation.annotation_datetime

    picker = dmc.DatePicker(
        id=ANNOTATION_DATE_PICKER,
        value=dt,
        clearable=False,
        required=True,
        size="sm",
        className="annotation-datepicker d-inline-block",
        icon=DashIconify(icon="mdi:calendar"),
    )
    time_input = dmc.TimeInput(
        id=ANNOTATION_TIME_INPUT,
        value=annotation.annotation_datetime,
        withSeconds=True,
        size="sm",
        icon=DashIconify(icon="mdi:clock-outline"),
        className="d-inline-block",
    )

    curr_dt = datetime.now()
    meta_header = html.Tr(
        [
            html.Th("Owner", className="text-center"),
            html.Th("Created At", className="text-center"),
            html.Th("Annotation time (GMT)", className="text-center"),
        ]
    )
    meta_rows = html.Tr(
        [
            html.Td(annotation_owner_email),
            html.Td(
                approx_datetime_diff_to_str(annotation.created_at, curr_dt) + " ago"
            ),
            html.Td(dmc.Group([picker, time_input], position="center")),
        ]
    )

    return dmc.Table(
        [html.Thead(meta_header), html.Tbody(meta_rows)],
        withBorder=True,
        className="text-center",
    )


def get_annotation_owner_email(annotation: Annotation) -> str:
    user_service = DEPS.Dependencies.get().user_service
    user = user_service.get_user_by_membership_id(annotation.owner)
    return user.email if user else ""


@fallback_to_error_page("Manage annotation")
def layout(
    annotation_id: str, project_id: Optional[str], **query_params: str
) -> html.Div:
    if project_id is None:
        return not_found_page("annotations", project_id)
    dependencies = DEPS.Dependencies.get()
    storage = dependencies.storage

    try:
        annotation = storage.get_project_storage(project_id).get_annotation(
            annotation_id
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).warning(f"Failed to load annotation {annotation_id}")
        return not_found_page("annotations", project_id)

    if annotation is None:
        LOGGER.warning(f"Failed to load annotation {annotation_id}")
        return not_found_page("annotations", project_id)

    owner_email = get_annotation_owner_email(annotation)

    return html.Div(
        [
            dcc.Location(id=ANNOTATION_LOCATION, refresh="callback-nav"),
            NB.get_navbar_component(project_id=project_id),
            dbc.Container(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dmc.TextInput(
                                        type="text",
                                        id=ANNOTATION_NAME_INPUT,
                                        placeholder="+ Add name",
                                        value=annotation.annotation_text,
                                        required=True,
                                        className="name-input d-inline-block w-100 title-input rounded-2",
                                        debounce=500,
                                        size="lg",
                                        variant="light",
                                        disabled=dependencies.is_user_read_only,
                                    ),
                                ],
                                md=4,
                                sm=12,
                                className="mb-1",
                            ),
                            dbc.Col(
                                [
                                    dmc.TextInput(
                                        type="text",
                                        id=ANNOTATION_DESC_INPUT,
                                        placeholder="Annotation description",
                                        value=annotation.description,
                                        className="name-input d-inline-block w-100 rounded-2",
                                        debounce=500,
                                        size="lg",
                                        variant="light",
                                        disabled=dependencies.is_user_read_only,
                                    ),
                                ],
                                md=6,
                                sm=11,
                                className="mb-1 flex-grow-1",
                            ),
                            dbc.Col(
                                [
                                    dmc.Menu(
                                        [
                                            dmc.MenuTarget(
                                                dmc.Button(
                                                    "More",
                                                    leftIcon=DashIconify(
                                                        icon="mdi:dots-vertical",
                                                        width=20,
                                                    ),
                                                    size="xs",
                                                    variant="subtle",
                                                    color="dark",
                                                    m="auto",
                                                    className="text-center d-inline-block",
                                                ),
                                            ),
                                            dmc.MenuDropdown(
                                                [
                                                    UIKIT.menu_item(
                                                        "Delete annotation",
                                                        icon="mynaui:trash",
                                                        id=DELETE_ANNOTATION_BUTTON,
                                                        color="red",
                                                    ),
                                                ],
                                            ),
                                        ],
                                        className="d-inline-block",
                                        shadow="md",
                                        position="bottom-end",
                                    ),
                                ],
                                className="m-auto text-end",
                                md=1,
                                sm=1,
                            ),
                        ],
                        justify="start",
                        className="mb-2",
                    ),
                    html.Hr(),
                    dbc.Row(dmc.Container(id=ANNOTATION_NOTIFICATION)),
                    dbc.Row(
                        create_annotation_meta_table(annotation, owner_email),
                    ),
                ],
                className="mb-5 p-2",
            ),
            delete_annotation_modal(),
        ]
    )


@callback(
    Output(ANNOTATION_NOTIFICATION, "children", allow_duplicate=True),
    Input(ANNOTATION_NAME_INPUT, "value"),
    Input(ANNOTATION_DESC_INPUT, "value"),
    Input(ANNOTATION_DATE_PICKER, "value"),
    Input(ANNOTATION_TIME_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def annotation_updated(
    name: Optional[str],
    description: Optional[str],
    selected_date: Optional[str],
    selected_time: Optional[str],
    pathname: str,
) -> str:
    try:
        annotation_id = P.get_path_value(
            P.ANNOTATION_EDIT_PATH, pathname, P.ANNOTATION_ID
        )
        project_id = P.get_path_value(
            P.ANNOTATION_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        if name is None:
            return no_update

        deps = DEPS.Dependencies.get()
        storage = deps.storage.get_project_storage(project_id)
        annotation = storage.get_annotation(annotation_id)

        if annotation is None:
            LOGGER.error(f"Annotation not found while updating: {annotation_id} ")
            return no_update
        annotation_dt = annotation.annotation_datetime

        new_name = name or "Unnamed annotation"
        if len(new_name) > 50:
            new_name = new_name[:50]

        if description is not None and len(description) > 300:
            description = description[:300]

        sel_date = parse_datetime_input(selected_date, def_val=annotation_dt)
        sel_time = parse_datetime_input(selected_time, def_val=annotation_dt)
        if sel_date is not None and sel_time is not None:
            annotation_dt = sel_date.replace(
                hour=sel_time.hour, minute=sel_time.minute, second=sel_time.second
            )

        else:
            LOGGER.error(
                f"Invalid datetime for annotation: {selected_date} {selected_time}"
            )

        annotation = replace(
            annotation,
            annotation_text=new_name,
            description=description,
            annotation_datetime=annotation_dt,
        )
        storage.set_annotation(annotation)
        deps.tracking_service.track_annotation_set(annotation)

        return no_update

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Error updating annotation.")
        return dmc.Alert(
            f"Couldn't update annotation. Details: {str(exc)[:300]}",
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="m-2",
        )


@callback(
    Output(DELETE_ANNOTATION_MODAL, "is_open"),
    Input(DELETE_ANNOTATION_BUTTON, "n_clicks"),
    Input(DELETE_ANNOTATION_MODAL_CLOSE, "n_clicks"),
    Input(DELETE_ANNOTATION_MODAL_ACCEPT, "n_clicks"),
    prevent_initial_call=True,
)
def delete_annotation_button_clicked(delete: int, close: int, accept: int) -> bool:
    if ctx.triggered_id is None or ctx.triggered_id in [
        DELETE_ANNOTATION_MODAL_CLOSE,
        DELETE_ANNOTATION_MODAL_ACCEPT,
    ]:
        return False
    if ctx.triggered_id == DELETE_ANNOTATION_BUTTON:
        return True
    return False


@callback(
    Output(ANNOTATION_LOCATION, "href"),
    Output(ANNOTATION_NOTIFICATION, "children"),
    Input(DELETE_ANNOTATION_MODAL_ACCEPT, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def delete_annotation_accepted(
    delete_clicked: int, pathname: str
) -> Tuple[str, dmc.Alert]:
    try:
        annotation_id = P.get_path_value(
            P.ANNOTATION_EDIT_PATH, pathname, P.ANNOTATION_ID
        )
        project_id = P.get_path_value(
            P.ANNOTATION_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        dependencies = DEPS.Dependencies.get()
        storage = dependencies.storage.get_project_storage(project_id)

        if delete_clicked and annotation_id is not None:
            storage.delete_annotation(annotation_id)
        return P.create_path(P.PROJECT_HOME_PATH, project_id=project_id), no_update
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to delete annotation.")
        return no_update, dmc.Alert(
            f"Couldn't delete annotation. Details: {str(exc)[:300]}",
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="m-2",
        )


register_page(
    __name__,
    path_template=P.ANNOTATION_EDIT_PATH,
    title="Mitzu - Manage Annotation",
    layout=layout,
)
