from dataclasses import replace
from typing import Tuple

import mitzu.webapp.model as WM
import mitzu.webapp.service.input_generator as IG
from mitzu.webapp.pages.projects.event_tables.common import (
    CUSTOM_VALUE_PREFIX,
)


def handle_custom_filter_values(
    filter: WM.WebappEventFilter,
) -> Tuple[WM.WebappEventFilter, bool]:
    property_value = filter.right
    indexing = False
    if property_value is not None and isinstance(property_value, (list, tuple)):
        indexing = (
            len(
                [
                    v
                    for v in property_value
                    # it should always be a string
                    if isinstance(v, str) and v.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
                ]
            )
            > 0
        )

        property_value = [
            item.replace(CUSTOM_VALUE_PREFIX, "") if type(item) == str else item
            for item in property_value
            if isinstance(item, str)
            and not item.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
    elif isinstance(property_value, str) and property_value.startswith(
        IG.CUSTOM_LOOKUP_PROP_VALUE
    ):
        indexing = True
        property_value = None
    elif isinstance(property_value, str) and property_value.startswith(
        CUSTOM_VALUE_PREFIX
    ):
        property_value = property_value.replace(CUSTOM_VALUE_PREFIX, "")

    return replace(filter, right=property_value), indexing
