from dataclasses import replace
from typing import Any, List, Optional, Tuple

from dash import no_update

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.service.input_generator as IG
from mitzu.logger import LOGGER
from mitzu.webapp.pages.projects.event_tables.common import (
    CUSTOM_VALUE_PREFIX,
)
from mitzu.webapp.service.path_parser import PathParser


def handle_custom_filter_values(
    filter: WM.WebappEventFilter,
) -> Tuple[WM.WebappEventFilter, bool]:
    property_value = filter.right
    indexing = False
    if property_value is not None and isinstance(property_value, (list, tuple)):
        indexing = (
            len(
                [
                    v
                    for v in property_value
                    # it should always be a string
                    if isinstance(v, str) and v.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
                ]
            )
            > 0
        )

        property_value = [
            item.replace(CUSTOM_VALUE_PREFIX, "") if type(item) == str else item
            for item in property_value
            if isinstance(item, str)
            and not item.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
    elif isinstance(property_value, str) and property_value.startswith(
        IG.CUSTOM_LOOKUP_PROP_VALUE
    ):
        indexing = True
        property_value = None
    elif isinstance(property_value, str) and property_value.startswith(
        CUSTOM_VALUE_PREFIX
    ):
        property_value = property_value.replace(CUSTOM_VALUE_PREFIX, "")

    return replace(filter, right=property_value), indexing


def property_value_data_update(
    value: List[Any],
    field_path_str: Optional[str],
    href: str,
    metric: Optional[WM.WebappMetric],
) -> Tuple[List, str]:
    if value is None or field_path_str is None:
        return no_update, no_update
    try:
        search_values = [
            val.replace(IG.CUSTOM_LOOKUP_PROP_VALUE, "")
            for val in value
            if val.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
        if len(search_values) != 1:
            return no_update, no_update

        # Getting rid of the custom lookup options
        value = [
            val for val in value if not val.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
        path = PathParser(href)
        project_id = path.project_id
        deps = DEPS.Dependencies.get()
        field_path = M.FieldPath.parse(field_path_str)
        discovery_defs = deps.get_project_storage(
            project_id
        ).get_definitions_for_field_discovery([field_path])

        if metric is not None:
            default_end_dt = discovery_defs.project.get_default_end_dt()
            indexing_context = discovery_defs.project.get_indexing_context(
                start_dt=metric.config.calculate_start_dt(
                    default_end_dt=default_end_dt
                ),
                end_dt=metric.config.calculate_end_dt(default_end_dt=default_end_dt),
                keep_enums_for_high_cardinality=True,
            )
        else:
            indexing_context = discovery_defs.project.get_default_indexing_context()

        context = deps.create_adapter_context(
            href,
            discovery_defs.project,
            GA.QueryType.INDEXING,
        )
        options = deps.get_events_service(project_id).index_property_values_with_search(
            value=value,
            field_path=field_path,
            search_values=search_values,
            adapter_context=context,
            discovery_defs=discovery_defs,
            indexing_context=indexing_context,
        )
        placeholder = IG.generate_value_input_placeholder([o["label"] for o in options])
        return options, placeholder
    except Exception as exc:
        LOGGER.opt(exception=exc).error(
            "Failed to discover event properties on the explore page."
        )
        return no_update, no_update
