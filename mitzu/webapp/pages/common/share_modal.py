from typing import List, Optional, Tuple

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    html,
    no_update,
)
from dash_iconify import DashIconify

import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
from mitzu import configs
from mitzu.logger import LOGGER
from mitzu.webapp.dependencies import Dependencies
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.pages.dashboards.constants import (
    SHARE_DASHBOARD_COPY_OPTIONS,
    SHARE_DASHBOARD_TOGGLE_SHARED,
    SHARED_BUTTON_DASHBOARD,
)

SHARE_MODAL_CLOSE = "share_modal_close"


def share_modal(
    obj: Optional[WM.Shareable],
    object_type: str,
    modal_id: str,
    copy_options_component_id: str,
    toggle_shared_id: str,
) -> bc.Component:

    public_href = configs.HOME_URL + P.get_shared_path(obj) if obj else ""

    content = dmc.Stack(
        [
            dmc.Group(
                children=[
                    dmc.Checkbox(
                        label=f"Share {object_type} publicly",
                        checked=obj and obj.is_shared(),
                        id=toggle_shared_id,
                        color="dark",
                    ),
                    UIKIT.add_help_tooltip(
                        label=f"Anyone with the link can view publicly shared {object_type}s.",
                        component=[
                            dmc.ActionIcon(
                                DashIconify(
                                    icon="bi:info-circle",
                                    width=15,
                                    height=15,
                                ),
                                size="sm",
                                variant="white",
                                color="dark",
                            )
                        ],
                    ),
                ],
                align="top",
                className="px-2",
            ),
            html.Div(
                id=copy_options_component_id,
                children=create_share_options_component(public_href, object_type),
                className="d-none"
                if not obj or not obj.is_shared()
                else "d-inline-block",
            ),
        ]
    )

    return UIKIT.create_modal(
        id=modal_id,
        title=f"Sharing options for {object_type.capitalize()}",
        content=content,
        modal_primary_button=None,
        close_button_id=SHARE_MODAL_CLOSE,
        config=UIKIT.CONFIRM_MODAL,
    )


def create_share_options_component(
    public_href: str, object_type: str
) -> List[bc.Component]:
    return [
        dmc.Divider(className="mb-2"),
        html.Div(
            [
                dmc.Group(
                    children=[
                        UIKIT.add_help_tooltip(
                            label=(
                                f"Use this link if you want to embed this {object_type} in another tool."
                            ),
                            component=dmc.ActionIcon(
                                DashIconify(
                                    icon="bi:info-circle",
                                    width=15,
                                    height=15,
                                ),
                                size="sm",
                                variant="white",
                                color="dark",
                            ),
                        ),
                        dmc.Text(
                            "Embeddable link",
                            size="sm",
                            color="gray",
                            weight="bold",
                        ),
                    ],
                    align="top",
                ),
                dmc.Prism(
                    children=f"{public_href}?embed=true",
                    language="text",
                ),
            ],
            className="mb-3 p-2",
        ),
        html.Div(
            [
                dmc.Group(
                    children=[
                        UIKIT.add_help_tooltip(
                            label=f"Use this code if you want to embed this {object_type} into another website.",
                            component=dmc.ActionIcon(
                                DashIconify(
                                    icon="bi:info-circle",
                                    width=15,
                                    height=15,
                                ),
                                size="sm",
                                variant="white",
                                color="dark",
                            ),
                        ),
                        dmc.Text(
                            "IFrame",
                            size="sm",
                            color="gray",
                            weight="bold",
                        ),
                    ],
                    align="top",
                ),
                dmc.Prism(
                    children=f'<iframe \nsrc="{public_href}?embed=true">\n</iframe>',
                    language="text",
                ),
            ],
            className="mb-3 p-2",
        ),
        html.Div(
            [
                dmc.Group(
                    children=[
                        UIKIT.add_help_tooltip(
                            label=f"Use this link if you want to share the {object_type} publicly"
                            " in a standalone mode without embedding.",
                            component=dmc.ActionIcon(
                                DashIconify(
                                    icon="bi:info-circle",
                                    width=15,
                                    height=15,
                                ),
                                size="sm",
                                variant="white",
                                color="dark",
                            ),
                        ),
                        dmc.Text(
                            "Public link",
                            size="sm",
                            color="gray",
                            weight="bold",
                        ),
                    ],
                    align="top",
                ),
                dmc.Prism(
                    children=public_href,
                    language="text",
                ),
            ],
            className="mb-2 p-2",
        ),
    ]


@callback(
    Output(SHARED_BUTTON_DASHBOARD, "className"),
    Output(SHARE_DASHBOARD_COPY_OPTIONS, "className"),
    Input(SHARE_DASHBOARD_TOGGLE_SHARED, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_toggle_dashboard_sharing(shared: bool, pathname: str) -> Tuple[str, str]:
    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        dashboard_storage.set_is_shared(shared)
        shared_class = "d-inline-block" if shared else "d-none"
        deps.tracking_service.track_dashboard_published(
            dashboard_storage.get_dashboard()
        )
        return shared_class, shared_class
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to update dashboard sharing")
    return no_update, no_update
