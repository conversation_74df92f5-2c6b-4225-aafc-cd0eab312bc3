from dataclasses import dataclass
from typing import List, Optional, Set, Tuple

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import html

import mitzu.model as M
import mitzu.webapp.model as WM

COLLECTION_LIST_LIMIT = 5
ALERT_LIMIT_IN_MODAL = 3


@dataclass(frozen=True)
class CollectionUsage:
    title: str
    used_in: Set[str]


def get_collection_usage(
    cohort: WM.CohortInfo, cohort_references: Set[WM.CollectionReference]
) -> Optional[CollectionUsage]:
    if len(cohort_references) == 0:
        return None

    used_in = set()
    for ref in cohort_references:
        used_in.add(
            ref.source_name
            + (
                " (cohort/collection)"
                if ref.source_type == WM.CollectionReferenceSource.COLLECTION
                else " (saved insight)"
            )
        )

    subject = "cohort" if cohort.entity == M.USER_ENTITY else "collection"
    title = f"This {cohort.name} {subject} is used in other assets"
    return CollectionUsage(title, used_in)


def render_single_collection_usage_alert(
    collection_usage: CollectionUsage,
) -> Optional[dmc.Alert]:
    if len(collection_usage.used_in) == 0:
        return None

    list_items = []
    for ref in sorted(collection_usage.used_in)[0:COLLECTION_LIST_LIMIT]:
        list_items.append(html.Li(ref))

    return dmc.Alert(
        title=collection_usage.title,
        children=[
            html.Ul(list_items),
            dmc.Text(
                f"There are in total {len(collection_usage.used_in)} references, only the first {COLLECTION_LIST_LIMIT} is shown"
            )
            if len(collection_usage.used_in) > COLLECTION_LIST_LIMIT
            else None,
        ],
        color="yellow",
        className="mb-1",
    )


def render_collection_usage_alerts(
    modal_css: str, alerts: List[CollectionUsage]
) -> Tuple[str, html.Div]:
    class_names = [css for css in modal_css.split(" ") if css != "no-content"]
    if len(alerts) == 0:
        class_names.append("no-content")

    dmc_alerts: List[Optional[bc.Component]] = [
        render_single_collection_usage_alert(alert) for alert in alerts
    ]
    if len(alerts) > ALERT_LIMIT_IN_MODAL:
        alert_count = len(alerts)
        dmc_alerts = dmc_alerts[0:ALERT_LIMIT_IN_MODAL]
        dmc_alerts.append(
            dmc.Text(
                f"There are in total {alert_count} warnings, only the first {ALERT_LIMIT_IN_MODAL} is shown"
            )
        )

    return " ".join(class_names), html.Div(dmc_alerts)
