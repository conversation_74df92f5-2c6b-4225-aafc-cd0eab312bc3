from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, Optional
from urllib.parse import parse_qs, urlencode

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P

RECORDS_PER_PAGE = 15


def create_catalog_link_for_record(
    project_id: str,
    record: WM.EventCatalogRecord,
    tab: P.WorkspaceSettingsTab,
) -> str:
    query_params = CatalogQueryParams(
        tab=tab,
        search_string=None,
        event=record.event_name_path,
        page=1,
        records_per_page=RECORDS_PER_PAGE,
    )

    return P.create_path(
        P.MANAGE_WORKSPACE_PATH, project_id=project_id
    ) + encode_catalog_link(query_params)


@dataclass
class CatalogQueryParams:
    tab: P.WorkspaceSettingsTab
    search_string: Optional[str]
    event: Optional[M.EventNamePath]
    page: int
    records_per_page: int

    @classmethod
    def create_from_dict(cls, params: Dict) -> CatalogQueryParams:
        event = params.get("event")
        return CatalogQueryParams(
            P.WorkspaceSettingsTab(
                params.get("tab", P.WorkspaceSettingsTab.GENERIC.value)
            ),
            params.get("search"),
            M.EventNamePath.parse(event)
            if event is not None and event.strip() != ""
            else None,
            int(params.get("page", 1)),
            int(params.get("records_per_page", RECORDS_PER_PAGE)),
        )


def parse_catalog_link(query_string: str) -> CatalogQueryParams:
    if query_string == "":
        return CatalogQueryParams(
            tab=P.WorkspaceSettingsTab.GENERIC,
            search_string=None,
            event=None,
            page=1,
            records_per_page=RECORDS_PER_PAGE,
        )

    params = {
        k: v[0]
        for k, v in parse_qs(
            query_string if query_string[0] != "?" else "".join(query_string[1:])
        ).items()
    }
    return CatalogQueryParams.create_from_dict(params)


def encode_catalog_link(params: CatalogQueryParams) -> str:
    query_params = {
        "tab": params.tab.value,
        "search": params.search_string,
        "event": str(params.event) if params.event else None,
        "page": params.page,
        "records_per_page": params.records_per_page
        if params.records_per_page != RECORDS_PER_PAGE
        else None,
    }
    for key in list(query_params.keys()):
        if query_params[key] is None:
            del query_params[key]
    return "?" + urlencode(query_params, doseq=False)
