from datetime import datetime
from typing import Dict, List, Optional, Set

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import dash_table, dcc, html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
from mitzu import configs
from mitzu.webapp.helper import approx_datetime_diff_to_str
from mitzu.webapp.pages.catalog.import_modal import (
    create_import_modal,
)
from mitzu.webapp.pages.catalog.utils import (
    RECORDS_PER_PAGE,
    CatalogQueryParams,
    create_catalog_link_for_record,
    encode_catalog_link,
)

TBL_CLS = "small text mh-0 align-top"

TABLE_INDEX_TYPE = "event_catalog_table"
PROP_DISPLAY_NAME = "prop_display_name"
PROP_DESCRIPTION = "prop_description"
PROP_IS_HIDDEN = "prop_is_hidden"
LIST_PROPERTIES_BUTTON = "list_properties_link"

MAX_EVENTS_LISTED_FOR_A_PROPERTY = 5

SEARCH_INPUT = "catalog_event_search_input"
EVENT_SEARCH_DD = "catalog_event_search_dd"
RESET_SEARCH_BUTTON = "catalog_reset_search_button"

REFRESH_EVENT_PROPERTIES_BUTTON = "refresh_event_properties_button"
REFRESH_EVENT_PROPERTIES_QUERY_ID = "refresh_event_properties_query_id"
REFRESH_EVENT_PROPERTIES_CANCEL_BUTTON = "refresh_event_properties_cancel_button"
REFRESH_EVENTS_BUTTON = "refresh_events_button"
REFRESH_EVENTS_QUERY_ID = "refresh_events_query_id"
REFERSH_EVENTS_MORE_MENU = "refresh_events_more_menu"
REFERSH_EVENTS_MORE_MENU_TARGET = "refresh_events_more_menut_target"
REFRESH_EVENTS_CANCEL_BUTTON = "refresh_events_cancel_button"
REFRESH_EVENTS_INFO = "refresh_events_info"

CATALOG_EXPORT_BUTTON = "catalog_export_button"
CATALOG_IMPORT_BUTTON = "catalog_import_button"
CATALOG_EXPORT_EVENTS = "catalog_export_events"
CATALOG_EXPORT_DIMENSION_PROPERTIES = "catalog_export_dimension_properties"

MANAGE_EVENT_PROPERTY_VALUES_MODAL = "manage_event_property_values_modal"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_CLOSE = "manage_event_property_values_modal_close"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_ACCEPT = "manage_event_property_values_modal_accept"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_OPEN = "manage_event_property_values_modal_open"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_BODY = "manage_event_property_values_modal_body"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_EVENT_NAME = (
    "manage_event_property_values_modal_event_name"
)
SELECTED_EVENT_PROPERTY = "selected_event_property"
MANAGE_EVENT_PROPERTY_VALUES_MODAL_CANCEL = "manage_event_property_values_modal_cancel"
MANAGE_VALUES_MODAL_FEEDBACK_CONTAINER = "manage_values_modal_feedback_container"
MANAGE_EVENT_PROPERTY_VALUES_TYPE_SELECT = "manage_event_property_values_type_select"
REFRESH_DIMENSION_PROPERTIES_BUTTON = "refresh_dimension_properties_button"
REFRESH_DIMENSION_PROPERTIES_QUERY_ID = "refresh_dimension_properties_query_id"
REFRESH_DIMENSION_PROPERTIES_CANCEL_BUTTON = (
    "refresh_dimension_properties_cancel_button"
)


def get_export_id(index: str) -> Dict[str, str]:
    return {
        "type": CATALOG_EXPORT_BUTTON,
        "index": index,
    }


def get_import_id(index: str) -> Dict[str, str]:
    return {
        "type": CATALOG_IMPORT_BUTTON,
        "index": index,
    }


def create_search_input(
    search_value: Optional[str],
    search_event_name_path: Optional[M.EventNamePath],
    current_tab: P.WorkspaceSettingsTab,
    metric_context: MC.MetricContext,
) -> dbc.Row:
    query_params_after_reset = CatalogQueryParams(
        tab=current_tab,
        search_string=None,
        event=None,
        page=1,
        records_per_page=RECORDS_PER_PAGE,
    )

    event_options: List[Dict] = []
    event_names = metric_context.get_all_event_names()
    for event_path in event_names:
        if isinstance(event_path, M.CustomEventNamePath):
            continue
        event_options.append(
            {
                "label": metric_context.get_event_meta_or_default_values(
                    event_path
                ).display_name,
                "value": str(event_path),
            }
        )

    event_options = sorted(event_options, key=lambda x: x["label"])

    return dmc.Group(
        [
            dmc.Select(
                id=EVENT_SEARCH_DD,
                placeholder="Select event...",
                value=(
                    str(search_event_name_path)
                    if search_event_name_path is not None
                    else None
                ),
                data=event_options,
                clearable=True,
                searchable=True,
                size="xs",
                className=(
                    "d-none"
                    if current_tab == P.WorkspaceSettingsTab.DIMENSION_PROPERTY_CATALOG
                    else ""
                ),
            ),
            dmc.TextInput(
                id=SEARCH_INPUT,
                placeholder="Search...",
                size="xs",
                value=search_value,
                debounce=1000,
                style={"minWidth": "300px"},
                icon=DashIconify(icon="ic:baseline-search", width=16, height=16),
            ),
            dcc.Link(
                UIKIT.tertiary_button("Reset", RESET_SEARCH_BUTTON, size="xs"),
                href=encode_catalog_link(query_params_after_reset),
            ),
        ],
        noWrap=True,
        className="mb-2",
    )


def create_pagination(
    number_of_pages: int, current_page: int, id: str
) -> Optional[dmc.Pagination]:
    if number_of_pages < 2:
        return None

    return dmc.Pagination(
        id=id,
        total=number_of_pages,
        boundaries=2,
        page=current_page,
        className="mt-1",
        color="dark",
        style={"justifyContent": "center"},
    )


def refresh_events_toolbar(user_role: WM.Role) -> dmc.Group:
    return dmc.Group(
        [
            create_import_modal(),
            html.Div("", id=REFRESH_EVENTS_QUERY_ID, className="d-none"),
            UIKIT.secondary_button(
                "Re-index all events",
                id=REFRESH_EVENTS_BUTTON,
                size="xs",
                additional_classes="d-none" if user_role != WM.Role.ADMIN else None,
            ),
            UIKIT.create_cancel_button(REFRESH_EVENTS_CANCEL_BUTTON),
            dmc.Menu(
                [
                    dmc.MenuTarget(
                        UIKIT.tertiary_button(
                            "More",
                            icon=DashIconify(
                                icon="mdi:dots-vertical",
                                width=20,
                            ),
                            id=REFERSH_EVENTS_MORE_MENU_TARGET,
                            size="xs",
                            variant="subtle",
                        ),
                    ),
                    dmc.MenuDropdown(
                        [
                            UIKIT.menu_item(
                                "Export", id=get_export_id(CATALOG_EXPORT_EVENTS)
                            ),
                            UIKIT.menu_item(
                                "Import", id=get_import_id(CATALOG_EXPORT_EVENTS)
                            ),
                        ],
                    ),
                ],
                className="d-inline-block ms-2"
                + (" d-none" if configs.ENVIRONMENT != "local" else ""),
                id=REFERSH_EVENTS_MORE_MENU,
                shadow="md",
                position="bottom-end",
            ),
        ],
        align="center",
        position="right",
        className="mt-2",
    )


def refresh_dimension_properties_toolbar() -> dmc.Group:
    return dmc.Group(
        [
            create_import_modal(),
            html.Div("", id=REFRESH_DIMENSION_PROPERTIES_QUERY_ID, className="d-none"),
            UIKIT.secondary_button(
                "Re-index all properties",
                id=REFRESH_DIMENSION_PROPERTIES_BUTTON,
                size="xs",
            ),
            UIKIT.create_cancel_button(
                REFRESH_DIMENSION_PROPERTIES_CANCEL_BUTTON, size="xs"
            ),
            (
                dmc.Menu(
                    [
                        dmc.MenuTarget(
                            dmc.Button(
                                "More",
                                leftIcon=DashIconify(
                                    icon="mdi:dots-vertical",
                                    width=20,
                                ),
                                size="xs",
                                variant="subtle",
                                color="dark",
                                m="auto",
                                className="text-center d-inline-block",
                            ),
                        ),
                        dmc.MenuDropdown(
                            [
                                UIKIT.menu_item(
                                    "Export",
                                    id=get_export_id(
                                        CATALOG_EXPORT_DIMENSION_PROPERTIES
                                    ),
                                ),
                                UIKIT.menu_item(
                                    "Import",
                                    id=get_import_id(
                                        CATALOG_EXPORT_DIMENSION_PROPERTIES
                                    ),
                                ),
                            ],
                        ),
                    ],
                    className="d-inline-block ms-2",
                    shadow="md",
                    position="bottom-end",
                )
                if configs.ENVIRONMENT == "local"
                else None
            ),
        ],
        align="center",
        position="right",
        className="mt-2",
    )


def refresh_properties_toolbar(
    selected_event: Optional[M.EventNamePath],
) -> dmc.Group:
    classes = ""
    if selected_event is None:
        classes = "d-none"

    return dmc.Group(
        [
            html.Div("", id=REFRESH_EVENT_PROPERTIES_QUERY_ID, className="d-none"),
            UIKIT.secondary_button(
                "Re-index event properties",
                id=REFRESH_EVENT_PROPERTIES_BUTTON,
                size="xs",
                additional_classes=classes,
            ),
            UIKIT.create_cancel_button(
                REFRESH_EVENT_PROPERTIES_CANCEL_BUTTON, size="xs"
            ),
        ],
        align="center",
        position="left",
        className="mt-2",
    )


def create_event_row_from_record(
    record: WM.EventCatalogRecord,
    search_value: Optional[str],
    read_only: bool,
) -> html.Tr:
    cell_class = f"{TBL_CLS} "

    event_name_path = M.EventNamePath(record.event_source_table, record.event_name)
    curr_dt = datetime.now()
    return html.Tr(
        [
            html.Td(
                dmc.Text(
                    dmc.Highlight(
                        children=str(event_name_path), highlight=search_value
                    ),
                    size="m",
                    className="d-block mt-1",
                ),
                className=f"{cell_class} text-break",
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                dmc.TextInput(
                    id={
                        "type": PROP_DISPLAY_NAME,
                        "index": TABLE_INDEX_TYPE,
                        "event_name_path": str(event_name_path),
                    },
                    size="xs",
                    value=record.event_display_name,
                    debounce=1000,
                    required=True,
                    className=PROP_DISPLAY_NAME,
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25",
            ),
            html.Td(
                dmc.Textarea(
                    id={
                        "type": PROP_DESCRIPTION,
                        "index": TABLE_INDEX_TYPE,
                        "event_name_path": str(event_name_path),
                    },
                    value=record.event_description,
                    size="xs",
                    className=PROP_DESCRIPTION,
                    debounce=1000,
                    maxRows=0,
                    placeholder="Add event description",
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25",
            ),
            html.Td(
                dmc.Text(
                    (
                        (
                            approx_datetime_diff_to_str(
                                curr_dt, record.event_last_discovered
                            )
                            + " ago"
                        )
                        if record.event_last_discovered
                        else "Never fetched"
                    ),
                    size="xs",
                    className="d-block mt-1",
                ),
                className=cell_class,
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                dmc.Select(
                    id={
                        "type": PROP_IS_HIDDEN,
                        "index": TABLE_INDEX_TYPE,
                        "event_name_path": str(event_name_path),
                    },
                    data=[
                        {"value": False, "label": "Visible"},
                        {"value": True, "label": "Hidden"},
                    ],
                    value=record.is_hidden,
                    size="xs",
                    disabled=read_only,
                ),
                style={"maxWidth": "100px"},
            ),
            html.Td(
                [
                    dmc.Button(
                        "List properties",
                        id={
                            "type": LIST_PROPERTIES_BUTTON,
                            "index": TABLE_INDEX_TYPE,
                            "event_name_path": str(event_name_path),
                        },
                        variant="white",
                        color="indigo",
                        size="xs",
                        compact=True,
                        className="d-block fw-normal text-decoration-underline",
                    ),
                ],
                className=cell_class,
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
        ],
    )


def create_property_row_from_record(
    project_id: str,
    record: WM.PropertyCatalogRecord,
    search_value: Optional[str],
    read_only: bool,
    search_event_name_path: Optional[M.EventNamePath],
) -> html.Tr:
    cell_class = f"{TBL_CLS} "

    events = sorted(record.events, key=lambda e: e.event_display_name)
    event_links = []
    for shown_event, event in enumerate(events):
        event_links.append(
            dcc.Link(
                [
                    dmc.Text(
                        event.event_display_name,
                        size="m",
                        className="d-block mt-1 text-break",
                    ),
                ],
                href=create_catalog_link_for_record(
                    project_id, event, P.WorkspaceSettingsTab.EVENT_CATALOG
                ),
            )
        )

        if shown_event > MAX_EVENTS_LISTED_FOR_A_PROPERTY:
            break

    if len(events) >= MAX_EVENTS_LISTED_FOR_A_PROPERTY:
        event_links.append(
            dmc.Text(
                "and more",
                size="m",
                className="d-block mt-1",
            )
        )

    return html.Tr(
        [
            html.Td(
                dmc.Text(
                    dmc.Highlight(
                        children=record.property_path,
                        highlight=search_value,
                    ),
                    size="m",
                    className="d-block mt-1 text-break",
                ),
                className=cell_class,
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                dmc.TextInput(
                    id={
                        "type": PROP_DISPLAY_NAME,
                        "index": TABLE_INDEX_TYPE,
                        "property_path": record.property_path,
                    },
                    size="xs",
                    value=record.property_display_name,
                    debounce=1000,
                    required=True,
                    className=PROP_DISPLAY_NAME,
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25 ",
            ),
            html.Td(
                dmc.Textarea(
                    id={
                        "type": PROP_DESCRIPTION,
                        "index": TABLE_INDEX_TYPE,
                        "property_path": record.property_path,
                    },
                    value=record.property_description,
                    size="xs",
                    className=PROP_DESCRIPTION,
                    debounce=1000,
                    maxRows=0,
                    placeholder="Add property description",
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25",
            ),
            html.Td(
                dmc.Select(
                    id={
                        "type": PROP_IS_HIDDEN,
                        "index": TABLE_INDEX_TYPE,
                        "property_path": record.property_path,
                    },
                    data=[
                        {"value": False, "label": "Visible"},
                        {"value": True, "label": "Hidden"},
                    ],
                    value=record.is_hidden,
                    size="xs",
                    disabled=read_only,
                ),
                style={"maxWidth": "100px"},
            ),
            html.Td(
                event_links,
                className=cell_class,
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                children=[
                    dmc.Button(
                        "Manage values",
                        id={
                            "type": MANAGE_EVENT_PROPERTY_VALUES_MODAL_OPEN,
                            "index": record.property_path,
                        },
                        color="dark",
                        size="xs",
                        variant="subtle",
                        leftIcon=DashIconify(icon="tabler:settings"),
                        className="d-inline-block",
                    ),
                ],
                className=cell_class + " d-none"
                if search_event_name_path is None
                else "",
            ),
        ],
    )


def create_dimension_property_row_from_record(
    record: WM.DimensionPropertyCatalogRecord,
    search_value: Optional[str],
    read_only: bool,
    entity_meta: WM.EntityMeta,
) -> html.Tr:
    cell_class = f"{TBL_CLS} "
    curr_dt = datetime.now()
    return html.Tr(
        [
            html.Td(
                dmc.Text(
                    dmc.Highlight(
                        children=f"{record.table_name}.{record.field_name}",
                        highlight=search_value,
                    ),
                    size="xs",
                    className="d-block mt-1",
                ),
                className=cell_class,
                style={"maxWidth": "180px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                dmc.Text(
                    [
                        DashIconify(
                            icon=entity_meta.icon, className="me-1", width=16, height=16
                        ),
                        f"{entity_meta.display_name} property",
                    ],
                    size="xs",
                )
            ),
            html.Td(
                dmc.TextInput(
                    id={
                        "type": PROP_DISPLAY_NAME,
                        "index": TABLE_INDEX_TYPE,
                        "dimension_property_id": record.dimension_property_id,
                    },
                    size="xs",
                    value=record.display_name,
                    debounce=1000,
                    required=True,
                    className=PROP_DISPLAY_NAME,
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25 ",
            ),
            html.Td(
                dmc.Textarea(
                    id={
                        "type": PROP_DESCRIPTION,
                        "index": TABLE_INDEX_TYPE,
                        "dimension_property_id": record.dimension_property_id,
                    },
                    value=record.description,
                    size="xs",
                    className=PROP_DESCRIPTION,
                    debounce=1000,
                    maxRows=0,
                    placeholder="Add property description",
                    disabled=read_only,
                ),
                className=f"{TBL_CLS} w-25",
            ),
            html.Td(
                dmc.Text(
                    (
                        (
                            approx_datetime_diff_to_str(curr_dt, record.last_discovered)
                            + " ago"
                        )
                        if record.last_discovered
                        else "Never discovered"
                    ),
                    size="xs",
                    className="d-block mt-1",
                ),
                className=cell_class,
                style={"maxWidth": "220px", "overflow-wrap": "anywhere"},
            ),
            html.Td(
                dmc.Select(
                    id={
                        "type": PROP_IS_HIDDEN,
                        "index": TABLE_INDEX_TYPE,
                        "dimension_property_id": record.dimension_property_id,
                    },
                    data=[
                        {"value": False, "label": "Visible"},
                        {"value": True, "label": "Hidden"},
                    ],
                    value=record.is_hidden,
                    size="xs",
                    disabled=read_only,
                ),
                style={"maxWidth": "100px"},
            ),
        ],
    )


def manage_event_property_values_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=MANAGE_EVENT_PROPERTY_VALUES_MODAL,
        title="Manage event values",
        title_id=MANAGE_EVENT_PROPERTY_VALUES_MODAL_EVENT_NAME,
        content=[
            html.Div(
                id=SELECTED_EVENT_PROPERTY,
                hidden=True,
            ),
            html.Div(id=MANAGE_EVENT_PROPERTY_VALUES_MODAL_BODY),
            dmc.Select(
                id=MANAGE_EVENT_PROPERTY_VALUES_TYPE_SELECT,
                clearable=True,
                searchable=False,
                data=[
                    {"value": str(v.value), "label": str(v.value).title()}
                    for v in M.DataType
                    if not v.is_complex()
                ],
                size="xs",
                className="mb-2",
                label="Property data type",
            ),
            UIKIT.tertiary_button(
                "Reset values",
                id=MANAGE_EVENT_PROPERTY_VALUES_MODAL_ACCEPT,
                icon=DashIconify(icon="mynaui:refresh-alt"),
            ),
            html.Div(id=MANAGE_VALUES_MODAL_FEEDBACK_CONTAINER),
            UIKIT.create_cancel_button(
                MANAGE_EVENT_PROPERTY_VALUES_MODAL_CANCEL, size="xs"
            ),
        ],
        modal_primary_button=None,
        close_button_id=MANAGE_EVENT_PROPERTY_VALUES_MODAL_CLOSE,
        config=UIKIT.CONFIG_MODAL,
    )


def create_property_values_table(
    property_values: Set[Optional[str]],
) -> dash_table.DataTable:
    if len(property_values) == 0:
        modal_body = dmc.Text(
            "There are no values saved for the selected property.", className="mb-3"
        )
    else:
        filtered_values = sorted(
            [str(value) for value in property_values if value is not None]
        )
        modal_body = dash_table.DataTable(
            columns=[{"name": "Property values", "id": "property_values"}],
            data=[{"property_values": value} for value in filtered_values],
            editable=False,
            sort_action="native",
            sort_mode="single",
            filter_action="native",
            cell_selectable=False,
            page_size=10,
            **UIKIT.get_dcc_table_styles(),  # type: ignore[arg-type]
        )

    return modal_body
