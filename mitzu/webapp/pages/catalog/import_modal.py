import base64
from typing import Tuple
from urllib.parse import urlparse

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    html,
)

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.helper as H
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
from mitzu.logger import LOGGER
from mitzu.webapp.service.catalog_serializer import (
    InvalidCSVFile,
)
from mitzu.webapp.service.catalog_service import (
    ImportResult,
)

IMPORT_MODAL = "import_modal"
IMPORT_MODAL_TYPE = "import_modal_type"
IMPORT_BUTTON = "import_button"
IMPORT_MODAL_CLOSE = "import_modal_close"


IMPORT_FILE = "import_file"
IMPORT_FILE_NAME = "import_file_name"
IMPORT_FILE_NAME_PLACEHOLDER = "(upload a file)"
IMPORT_STATUS = "import_status"


def create_import_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=IMPORT_MODAL,
        title="Import catalog",
        content=[
            html.Div(id=IMPORT_MODAL_TYPE, className="d-none"),
            UIKIT.render_file_upload(id=IMPORT_FILE),
            UIKIT.render_static_input_value(
                IMPORT_FILE_NAME_PLACEHOLDER,
                id=IMPORT_FILE_NAME,
                with_clipboard=False,
            ),
            html.Div(id=IMPORT_STATUS),
        ],
        modal_primary_button=UIKIT.ModalButton(
            IMPORT_BUTTON,
            label="Import",
            icon="mdi:import",
        ),
        close_button_id=IMPORT_MODAL_CLOSE,
        config=UIKIT.CONFIG_MODAL,
    )


def render_error(exc: Exception) -> dmc.Alert:
    return dmc.Alert(
        f"The uploaded file is invalid. Details: {exc}"
        if isinstance(exc, InvalidCSVFile)
        else f"Something went wrong. Details: {exc}",
        title="Import failed",
        color="red",
    )


def render_import_result(result: ImportResult) -> dmc.Alert:
    if len(result.errors) > 0:

        return dmc.Alert(
            [
                f"{result.successful} entries have been imported successfully and the following errors occured:",
                html.Ul([html.Li(err) for err in result.errors[0:10]]),
                "(some erros are hidden)" if len(result.errors) > 10 else None,
            ],
            title="Import partial success",
            color="yellow",
        )

    return dmc.Alert(
        f"{result.successful} entries have been imported.",
        title="Import success",
        color="green",
    )


@callback(
    Output(IMPORT_STATUS, "children", allow_duplicate=True),
    Output(
        IMPORT_FILE, "contents"
    ),  # reset the upload content, so it will be triggered again
    Output(IMPORT_FILE_NAME, "children"),
    Input(IMPORT_BUTTON, "n_clicks"),
    State(IMPORT_FILE, "contents"),
    State(IMPORT_MODAL_TYPE, "children"),
    State(H.MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_csv_import(
    clicks: int, encoded_content: str, import_type: str, href: str
) -> Tuple[dmc.Alert, str, str]:
    try:
        if encoded_content is None or encoded_content == "":
            return (
                dmc.Alert(
                    "File is not uploaded!",
                    title="Import failed",
                    color="red",
                ),
                "",
                IMPORT_FILE_NAME_PLACEHOLDER,
            )

        if ";" not in encoded_content:
            raise ValueError("Unsupported file format!")

        header, content = encoded_content.split(";")
        if header != "data:text/csv" or not content.startswith("base64,"):
            raise ValueError("Unsupported file format!")

        decoded_content = base64.b64decode(content[7:]).decode()

        parse_result = urlparse(href)
        project_id = P.get_path_value(
            P.MANAGE_WORKSPACE_PATH, parse_result.path, "project_id"
        )
        deps = DEPS.Dependencies.get()
        catalog_service = deps.get_catalog_service(project_id)

        if import_type == "dimension_properties":

            result = catalog_service.import_dimension_properties_from_csv(
                decoded_content
            )
        elif import_type == "events":
            result = catalog_service.import_events_from_csv(decoded_content)
        else:
            raise ValueError(f"unknown import type: {import_type}")
        return render_import_result(result), "", IMPORT_FILE_NAME_PLACEHOLDER

    except Exception as exc:
        LOGGER.opt(exception=exc).warning("failed to import catalog csv")
        return render_error(exc), "", IMPORT_FILE_NAME_PLACEHOLDER


clientside_callback(
    """
    function(clicks) {
        return false;
    }
""",
    Output(IMPORT_MODAL, "is_open", allow_duplicate=True),
    Input(IMPORT_MODAL_CLOSE, "n_clicks"),
    prevent_initial_call=True,
)

clientside_callback(
    """
    function(filename) {
        return "Uploaded file: " + filename
    }
    """,
    Output(IMPORT_FILE_NAME, "children", allow_duplicate=True),
    Input(IMPORT_FILE, "filename"),
    prevent_initial_call=True,
)
