import datetime
import json
from dataclasses import replace
from typing import Any, Dict, Optional, Tuple
from urllib.parse import urlparse

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import pandas as pd
from dash import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dcc,
    html,
    no_update,
    register_page,
)
from dash_iconify import DashIconify

import mitzu.adapters.generic_adapter as GA
import mitzu.adapters.helper as AH
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.helper as WH
import mitzu.webapp.model as WM
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.cohorts.components.delete_modal as CDM
import mitzu.webapp.pages.cohorts.components.meta_table as CMT
import mitzu.webapp.pages.cohorts.components.user_table as CUT
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.adapter_factory as AF
from mitzu.logger import LOGGER
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.metric_context import MetricContext
from mitzu.webapp.pages.common.cohort_reference_alert import (
    get_collection_usage,
    render_collection_usage_alerts,
)
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.error_pages.not_found_page import (
    not_found_page,
)
from mitzu.webapp.pages.explore.components.common import (
    get_metric_context,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)

COHORT_NAME_INPUT = "cohort_name_input"
COHORT_DESC_INPUT = "cohort_desc_input"
EXPORT_COHORT_CSV = "export_cohort_csv"
EXPORT_COHORT_BUTTON = "export_cohort_button"
DELETE_COHORT_BUTTON = "delete_cohort_button"
COHORT_LOCATION = "cohort_location"
COHORT_META_TABLE = "cohort_meta_table"
COHORT_USER_TABLE = "cohort_user_table"
COHORT_NOTIFICATION = "cohort_notification"
COHORT_VIEW_LINK = "cohort_view_link"


def get_cohort_owner_email(cohort: WM.CohortInfo) -> str:
    user_service = DEPS.Dependencies.get().user_service
    user = user_service.get_user_by_membership_id(cohort.owner)
    return user.email if user else ""


def get_cohort_label(entity: M.Entity) -> str:
    if entity == M.USER_ENTITY:
        return "cohort"
    return "collection"


def create_cohort_insight_href(cohort: WM.CohortInfo, project_id: str) -> str:
    cohort_href = ""

    try:
        if cohort.type == M.CohortType.DYNAMIC:
            cohort_metric = SE.webapp_metric_from_dict(json.loads(cohort.metric_json))
            cohort_metric = replace(
                cohort_metric,
                config=replace(cohort_metric.config, chart_type=M.SimpleChartType.BAR),
            )

            cohort_metric = MetricUpdater.fix_cohort_backwards_compatible_agg_types_for_view_insight(
                cohort_metric
            )

            cohort_url_params = f"?m={SE.to_compressed_string(cohort_metric)}"
            cohort_href = f"{P.create_path(P.PROJECTS_EXPLORE_PATH, project_id=project_id)}{cohort_url_params}"
        elif cohort.type == M.CohortType.DIMENSION:
            dminesion_search_query = SE.search_query_from_dict(
                json.loads(cohort.metric_json)
            )
            lookup_page_href = P.create_dimensions_search_path(
                project_id, dminesion_search_query
            )
            return lookup_page_href
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to create cohort insight href")

    return cohort_href


@fallback_to_error_page("Manage cohort")
def layout(cohort_id: str, project_id: Optional[str], **query_params: str) -> html.Div:
    if project_id is None:
        return not_found_page("cohorts", project_id)
    dependencies = DEPS.Dependencies.get()
    project_storage = dependencies.get_project_storage(project_id)
    catalog_service = dependencies.get_catalog_service(project_id)

    try:
        cohort = project_storage.get_cohort(cohort_id)
    except Exception:
        return not_found_page("cohorts", project_id)

    cohort_owner_email = get_cohort_owner_email(cohort)
    cohort_href = create_cohort_insight_href(cohort, project_id)

    user_list = (
        project_storage.get_static_collection_entity_id_list(cohort.id)
        if cohort.type == M.CohortType.IMPORTED
        else []
    )
    label = get_cohort_label(cohort.entity)

    return html.Div(
        [
            dcc.Location(id=COHORT_LOCATION, refresh="callback-nav"),
            NB.get_navbar_component(project_id=project_id),
            dbc.Container(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dmc.TextInput(
                                        type="text",
                                        id=COHORT_NAME_INPUT,
                                        placeholder="+ Add Name",
                                        value=cohort.name,
                                        required=True,
                                        className="name-input d-inline-block w-100 title-input rounded-2",
                                        debounce=500,
                                        size="lg",
                                        variant="light",
                                        disabled=dependencies.is_user_read_only,
                                    ),
                                ],
                                md=4,
                                sm=12,
                                className="mb-1",
                            ),
                            dbc.Col(
                                [
                                    dmc.TextInput(
                                        type="text",
                                        id=COHORT_DESC_INPUT,
                                        placeholder=label.capitalize()
                                        + "  Description",
                                        value=cohort.description,
                                        className="name-input d-inline-block w-100 rounded-2",
                                        debounce=1000,
                                        size="lg",
                                        variant="light",
                                        disabled=dependencies.is_user_read_only,
                                    ),
                                ],
                                md=6,
                                sm=11,
                                className="mb-1 flex-grow-1",
                            ),
                            dbc.Col(
                                [
                                    dmc.Menu(
                                        [
                                            dmc.MenuTarget(
                                                UIKIT.tertiary_button(
                                                    "More",
                                                    icon=DashIconify(
                                                        icon="mdi:dots-vertical",
                                                        width=20,
                                                    ),
                                                    size="xs",
                                                    id="",
                                                    variant="subtle",
                                                ),
                                            ),
                                            dmc.MenuDropdown(
                                                [
                                                    UIKIT.menu_item(
                                                        "Export to CSV",
                                                        icon="mynaui:download",
                                                        id=EXPORT_COHORT_BUTTON,
                                                        disabled=dependencies.is_user_read_only,
                                                    ),
                                                    dmc.MenuDivider(),
                                                    dcc.Link(
                                                        children=[
                                                            UIKIT.menu_item(
                                                                "View Insight"
                                                                if cohort.type
                                                                != M.CohortType.DIMENSION
                                                                else "View Lookup",
                                                                icon="carbon:explore",
                                                                disabled=(
                                                                    dependencies.is_user_read_only
                                                                    or cohort.type
                                                                    == M.CohortType.IMPORTED
                                                                ),
                                                                color=(
                                                                    "gray"
                                                                    if cohort.type
                                                                    == M.CohortType.IMPORTED
                                                                    else ""
                                                                ),
                                                            ),
                                                        ],
                                                        href=cohort_href,
                                                        id=COHORT_VIEW_LINK,
                                                    ),
                                                    dmc.MenuDivider(),
                                                    UIKIT.menu_item(
                                                        f"Delete {label}",
                                                        icon="mynaui:trash",
                                                        id=DELETE_COHORT_BUTTON,
                                                        color="red",
                                                    ),
                                                ],
                                            ),
                                        ],
                                        className="d-inline-block",
                                        id="COHORT_MORE_MENU",
                                        shadow="md",
                                        position="bottom-end",
                                    ),
                                ],
                                className="m-auto text-end",
                                md=1,
                                sm=1,
                            ),
                            dcc.Download(EXPORT_COHORT_CSV),
                        ],
                        justify="start",
                        className="mb-2",
                    ),
                    html.Hr(),
                    dbc.Row(dmc.Container(id=COHORT_NOTIFICATION)),
                    dbc.Row(
                        [
                            CMT.create_cohort_meta_table(
                                cohort,
                                cohort_owner_email,
                                catalog_service,
                                dependencies.is_user_read_only,
                                False,
                            )
                        ],
                        id=COHORT_META_TABLE,
                    ),
                ],
                className="mb-5 pt-2    ",
            ),
            dbc.Container(
                [
                    dmc.Text("User List", size="xl"),
                    html.Hr(),
                    dbc.Row(
                        [CUT.create_cohort_user_table(cohort, user_list)],
                        id=COHORT_USER_TABLE,
                    ),
                ],
                className=(
                    "" + "d-none" if cohort.type != M.CohortType.IMPORTED else ""
                ),
            ),
            CDM.delete_cohort_modal(),
        ]
    )


@callback(
    Output(COHORT_NAME_INPUT, "value"),
    Input(COHORT_NAME_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def cohort_name_changed(name: Optional[str], pathname: str) -> str:
    cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
    project_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART)
    if name is None:
        return no_update
    deps = DEPS.Dependencies.get()
    storage = deps.get_project_storage(project_id)
    cohort = storage.get_cohort(cohort_id)
    if cohort is not None:
        c_name = name or "Unnamed cohort"
        cohort = replace(cohort, name=c_name)
        storage.set_cohort(cohort)
    return name


@callback(
    Output(COHORT_DESC_INPUT, "value"),
    Input(COHORT_DESC_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def cohort_description_changed(description: str, pathname: str) -> str:
    cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
    project_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART)
    description = description.strip()
    if not description:
        return no_update
    deps = DEPS.Dependencies.get()
    storage = deps.get_project_storage(project_id)
    cohort = storage.get_cohort(cohort_id)
    if cohort is not None:
        cohort = replace(cohort, description=description)
        storage.set_cohort(cohort)
    return description


@callback(
    Output(component_id=EXPORT_COHORT_CSV, component_property="data"),
    Input(component_id=EXPORT_COHORT_BUTTON, component_property="n_clicks"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
    background=True,
    running=[
        (Output(EXPORT_COHORT_BUTTON, "loading"), True, False),
    ],
)
def export_cohort_to_csv(n_clicks: int, href: str) -> Dict[str, Any]:
    parse_result = urlparse(href)
    pathname = parse_result.path
    cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
    project_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART)
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)
    project = project_storage.get_project()
    cohort = project_storage.get_cohort(cohort_id)

    if cohort.type == M.CohortType.IMPORTED:
        user_list = project_storage.get_static_collection_entity_id_list(cohort_id)
    elif cohort.type == M.CohortType.DYNAMIC:
        cohort_metric = SE.webapp_metric_from_dict(json.loads(cohort.metric_json))
        metric_defs = project_storage.get_definitions_for_metric(cohort_metric)

        adapter_context = deps.create_adapter_context(
            href, metric_defs.project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            metric_defs.project,
            deps.cache,
            adapter_context,
            user_role=deps.user_role,
        )

        metric_updater = deps.get_metric_updater(cohort_metric, project_id)
        cohort_metric = metric_updater.return_users(
            cohort_metric, False
        )  # TODO - make sure drop-off is handled
        metric_updater = MetricUpdater(
            get_metric_context(cohort_metric, deps.get_catalog_service(project.id)),
            project.insight_settings,
        )
        metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)

        pdf = adapter.get_df(metric_converter.convert_metric(cohort_metric))
        agg_value_column = AH.select_agg_value_column_for_cohort(
            cohort_metric.metric_type, cohort.type
        )
        pdf = pdf[pdf[agg_value_column].notna()]
        user_list = pdf[agg_value_column].tolist()
    elif cohort.type == M.CohortType.DIMENSION:
        search_query = SE.search_query_from_dict(json.loads(cohort.metric_json))
        metric_defs = project_storage.get_definitions_for_dimension_search_query()

        adapter_context = deps.create_adapter_context(
            href, metric_defs.project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            metric_defs.project,
            deps.cache,
            adapter_context,
            user_role=deps.user_role,
        )
        metric_updater = MetricUpdater(MetricContext.empty(), project.insight_settings)
        metric_converter = MetricConverter(metric_defs, project_storage, metric_updater)

        pdf = adapter.get_dimension_cohort_df(
            metric_converter.convert_dimension_search_query(search_query)
        )
        user_list = pdf[GA.CTE_USER_ID_ALIAS_COL].tolist()
    else:
        raise ValueError(f"Unknown cohort type: {cohort.type}")

    cohort_series = pd.Series(user_list, dtype="object")
    cohort_series.name = cohort.entity.name + "_id"

    dt = datetime.datetime.now()
    date_today = datetime.date(dt.year, dt.month, dt.day)

    # it too tricky to generate stubs for this method
    return dcc.send_data_frame(  # type: ignore[attr-defined]
        cohort_series.to_csv, f"{cohort.name}_{date_today}.csv", index=False
    )


@callback(
    Output(COHORT_META_TABLE, "children"),
    Output(COHORT_USER_TABLE, "children"),
    Output(COHORT_NOTIFICATION, "children"),
    Input(CMT.REFRESH_COHORT_BUTTON, "n_clicks"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
    background=True,
    running=[
        (
            Output(CMT.REFRESH_COHORT_BUTTON, "loading"),
            True,
            False,
        ),
    ],
)
def refresh_cohort(n_clicks: int, href: str) -> Tuple[dmc.Table, html.Div, dmc.Alert]:
    parse_result = urlparse(href)
    pathname = parse_result.path
    cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
    project_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART)

    label = "collection"
    try:
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        project = project_storage.get_project()
        cohort = project_storage.get_cohort(cohort_id)
        adapter_context = deps.create_adapter_context(
            parse_result.geturl(), project, GA.QueryType.INSIGHT
        )
        adapter = AF.create_adapter_from_project(
            project,
            deps.cache,
            adapter_context,
            user_role=deps.user_role,
        )
        label = get_cohort_label(cohort.entity)
        is_drop_off = False
        if cohort.type == M.CohortType.IMPORTED:
            return no_update, no_update, no_update
        if cohort.type == M.CohortType.DIMENSION:
            search_query_defs = (
                project_storage.get_definitions_for_dimension_search_query()
            )
            metric_updater = MetricUpdater(
                MetricContext.empty(), project.insight_settings
            )
            metric_converter = MetricConverter(
                search_query_defs, project_storage, metric_updater
            )
            webapp_dimension_search_query = SE.search_query_from_dict(
                json.loads(cohort.metric_json)
            )
            dimension_search_query = metric_converter.convert_dimension_search_query(
                webapp_dimension_search_query
            )
            dimension_collection = M.DimensionCollection(
                id=cohort_id, search_query=dimension_search_query
            )
            count = adapter.count_dimension_collection(dimension_collection)
            cohort = cohort.refresh_cohort(
                last_updated_at=datetime.datetime.now(),
                cohort_size=count,
            )

        elif cohort.type == M.CohortType.DYNAMIC:
            webapp_metric = SE.webapp_metric_from_dict(json.loads(cohort.metric_json))
            metric_defs = project_storage.get_definitions_for_metric(webapp_metric)
            metric_updater = MetricUpdater(
                get_metric_context(webapp_metric, deps.get_catalog_service(project.id)),
                project.insight_settings,
            )
            metric_converter = MetricConverter(
                metric_defs, project_storage, metric_updater
            )
            cohort_metric = metric_converter.convert_metric(webapp_metric)

            pdf = adapter.get_df(cohort_metric)

            is_static_cohort = False if cohort.type == M.CohortType.DYNAMIC else True

            if is_static_cohort:
                agg_value_column = AH.select_agg_value_column_for_cohort(
                    cohort_metric.type, cohort.type
                )
                agg_column_list = pdf[agg_value_column].tolist()
                cohort_size = 0
                if len(agg_column_list) > 0:
                    cohort_size = agg_column_list[0]
            else:
                agg_value_column = WH.select_user_count_column_for_cohort(cohort_metric)
                pdf = pdf[pdf[agg_value_column].notna()]
                agg_column_list = pdf[agg_value_column].tolist()
                if (
                    cohort_metric.config.aggregation.type
                    == M.AggType.RETURN_DROP_OFF_USERS
                ):
                    # Temporary fix for drop-off users count, this is maximum going to show 20000
                    # We should introduce a count drop-off users aggregation type
                    cohort_size = len(set(agg_column_list))
                    is_drop_off = True
                else:
                    cohort_size = int(agg_column_list[0])

            cohort = cohort.refresh_cohort(
                last_updated_at=datetime.datetime.now(),
                cohort_size=cohort_size,
            )

        project_storage.set_cohort(cohort)
        cohort_owner_email = get_cohort_owner_email(cohort)
        catalog_service = deps.get_catalog_service(project_id)

        return (
            CMT.create_cohort_meta_table(
                cohort,
                cohort_owner_email,
                catalog_service,
                deps.is_user_read_only,
                is_drop_off,
            ),
            no_update,
            no_update,
        )

    except Exception as exc:
        LOGGER.opt(exception=exc).exception(
            f"Failed to refresh cohort with ID: {cohort_id}"
        )
        return (
            no_update,
            no_update,
            dmc.Alert(
                f"Couldn't refresh {label}. Details: {str(exc)[:300]}",
                color="red",
                title="Something went wrong",
                withCloseButton=True,
                className="m-2",
            ),
        )


@callback(
    Output(CDM.DELETE_COHORT_MODAL, "is_open"),
    Output(CDM.DELETE_COHORT_MODAL, "className"),
    Output(CDM.DELETE_COHORT_MODAL_CONTENT, "children"),
    Input(DELETE_COHORT_BUTTON, "n_clicks"),
    Input(CDM.DELETE_COHORT_MODAL_CLOSE, "n_clicks"),
    Input(CDM.DELETE_COHORT_MODAL_ACCEPT, "n_clicks"),
    State(CDM.DELETE_COHORT_MODAL, "className"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def delete_cohort_button_clicked(
    delete: int, close: int, accept: int, modal_css: str, pathname: str
) -> Tuple[bool, str, Optional[html.Div]]:
    if ctx.triggered_id is None or ctx.triggered_id in [
        CDM.DELETE_COHORT_MODAL_CLOSE,
        CDM.DELETE_COHORT_MODAL_ACCEPT,
    ]:
        return False, no_update, None
    if ctx.triggered_id == DELETE_COHORT_BUTTON:
        cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
        project_id = P.get_path_value(
            P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        dependencies = DEPS.Dependencies.get()
        storage = dependencies.get_project_storage(project_id)
        cohort = storage.get_cohort(cohort_id)
        cohort_references = storage.get_collection_references(cohort_id)

        usage_alert = get_collection_usage(cohort, cohort_references)
        modal_class_names, alerts = render_collection_usage_alerts(
            modal_css, [usage_alert] if usage_alert else []
        )
        return (
            True,
            modal_class_names,
            alerts,
        )
    return False, no_update, None


@callback(
    Output(COHORT_LOCATION, "href"),
    Output(COHORT_NOTIFICATION, "children", allow_duplicate=True),
    Input(CDM.DELETE_COHORT_MODAL_ACCEPT, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def delete_cohort_accepted(delete_clicked: int, pathname: str) -> Tuple[str, dmc.Alert]:
    cohort_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.COHORT_ID)
    project_id = P.get_path_value(P.COHORT_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART)
    dependencies = DEPS.Dependencies.get()
    storage = dependencies.get_project_storage(project_id)
    try:
        if delete_clicked and cohort_id is not None:
            storage.delete_cohort(cohort_id)
        return P.create_path(P.PROJECT_HOME_PATH, project_id=project_id), no_update

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to delete cohort.")
        return no_update, dmc.Alert(
            f"Couldn't delete collection. Details: {str(exc)[:300]}",
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="m-2",
        )


register_page(
    __name__,
    path_template=P.COHORT_EDIT_PATH,
    title="Mitzu - Manage Collection",
)
