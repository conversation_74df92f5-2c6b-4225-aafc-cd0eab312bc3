from typing import List

import pandas as pd
from dash import dash_table, html

import mitzu.webapp.model as WM
from mitzu.webapp.pages.uikit import get_dcc_table_styles

USER_TABLE = "user_table"


def create_cohort_user_table(
    cohort: WM.CohortInfo, entity_id_list: List[str]
) -> html.Div:
    user_df = pd.DataFrame(entity_id_list, columns=["User ID"])

    user_table = html.Div(
        [
            dash_table.DataTable(
                id=USER_TABLE,
                columns=[{"name": i, "id": i} for i in user_df.columns],
                data=user_df.to_dict("records"),
                editable=False,
                sort_action="native",
                sort_mode="single",
                page_size=20,
                filter_action="native",
                cell_selectable=False,
                **get_dcc_table_styles(),  # type: ignore[arg-type]
            )
        ],
        className="mb-5",
    )

    return user_table
