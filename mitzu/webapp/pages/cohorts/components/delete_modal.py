import dash_bootstrap_components as dbc
from dash import html

import mitzu.webapp.pages.uikit as UIKIT

DELETE_COHORT_MODAL = "delete_cohort_modal"
DELETE_COHORT_MODAL_CLOSE = "delete_cohort_modal_close"
DELETE_COHORT_MODAL_ACCEPT = "delete_cohort_modal_accept"
DELETE_COHORT_MODAL_CONTENT = "delete_cohort_modal_content"


def delete_cohort_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=DELETE_COHORT_MODAL,
        title="Do you really want to delete this cohort?",
        content=html.Div(id=DELETE_COHORT_MODAL_CONTENT),
        modal_primary_button=UIKIT.ModalButton.delete(DELETE_COHORT_MODAL_ACCEPT),
        close_button_id=DELETE_COHORT_MODAL_CLOSE,
        config=UIKIT.CONFIRM_MODAL,
    )
