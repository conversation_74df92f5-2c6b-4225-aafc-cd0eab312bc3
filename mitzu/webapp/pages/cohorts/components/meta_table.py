import datetime

import dash_mantine_components as dmc
from dash import html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.service.catalog_service as CS
from mitzu.adapters.generic_adapter import MAX_ROW_LIMIT
from mitzu.webapp.helper import approx_datetime_diff_to_str
from mitzu.webapp.pages import uikit

REFRESH_COHORT_BUTTON = "refresh_cohort_button"


def create_cohort_meta_table(
    cohort: WM.CohortInfo,
    cohort_owner_email: str,
    catalog_service: CS.CatalogService,
    is_user_read_only: bool,
    is_drop_off: bool,
) -> dmc.Table:
    entity_meta = catalog_service.get_entity_meta(cohort.entity)
    curr_dt = datetime.datetime.now()
    cohort_size = create_cohort_size_text(cohort, is_drop_off)

    meta_header = [
        html.Thead(
            html.Tr(
                [
                    html.Th("Owner", className="text-center"),
                    html.Th(
                        children=[
                            html.Div(
                                [
                                    dmc.Text(
                                        [
                                            "Size",
                                            html.Div(
                                                uikit.add_help_tooltip(
                                                    label="The size of the cohort is just indicative. The actual "
                                                    "users in the cohort are dynamically fetched "
                                                    "from your data warehouse.",
                                                    component=[
                                                        html.I(
                                                            className="bi bi-info-circle ms-2",
                                                        ),
                                                    ],
                                                ),
                                                className="" + " d-none"
                                                if cohort.type != M.CohortType.DYNAMIC
                                                else "fw-normal",
                                            ),
                                        ],
                                        className="d-flex align-items-center me-2",
                                    ),
                                ],
                                className="d-flex align-items-center justify-content-center",
                            )
                        ],
                        className="text-center",
                    ),
                    html.Th("Entity", className="text-center"),
                    html.Th("Type", className="text-center"),
                    html.Th("Created At", className="text-center"),
                    html.Th("Last Counted At", className="text-center"),
                ]
            )
        )
    ]

    meta_rows = [
        html.Tr(
            [
                html.Td(cohort_owner_email),
                html.Td(
                    html.Div(
                        [
                            dmc.Text(
                                cohort_size,
                                className="d-flex align-items-center me-2",
                            ),
                            dmc.ActionIcon(
                                DashIconify(icon="mynaui:refresh", width=20),
                                size="md",
                                id=REFRESH_COHORT_BUTTON,
                                disabled=is_user_read_only,
                                n_clicks=0,
                                className="d-flex align-items-center" + " d-none"
                                if cohort.type == M.CohortType.IMPORTED
                                else "",
                            ),
                        ],
                        className="d-flex align-items-center justify-content-center",
                    )
                ),
                html.Td(
                    [
                        DashIconify(icon=entity_meta.icon),
                        entity_meta.display_name,
                    ]
                ),
                html.Td(cohort.type.name or ""),
                html.Td(
                    approx_datetime_diff_to_str(cohort.created_at, curr_dt) + " ago"
                ),
                html.Td(
                    approx_datetime_diff_to_str(cohort.last_updated_at, curr_dt)
                    + " ago"
                ),
            ]
        ),
    ]

    return dmc.Table(
        meta_header + [html.Tbody(meta_rows)], withBorder=True, className="text-center"
    )


def create_cohort_size_text(cohort: WM.CohortInfo, is_drop_off: bool) -> str:
    if cohort.cohort_size is None:
        cohort_size = "N/A"
    elif is_drop_off and cohort.cohort_size > MAX_ROW_LIMIT:
        cohort_size = f"more than {MAX_ROW_LIMIT}"
    else:
        cohort_size = str(cohort.cohort_size)
    return cohort_size
