import base64
import datetime
import io
from typing import Tuple

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import pandas as pd
from dash import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dcc,
    html,
    no_update,
)

import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.storage.project_storage as PS
from mitzu.helper import create_unique_id
from mitzu.logger import LOGGER
from mitzu.webapp.helper import MITZU_LOCATION

IMPORT_MODAL_BODY = "import_modal_body"
CONFIRM_IMPORT_ACCEPT = "confirm_import_accept"
CONFIRM_IMPORT_CLOSE = "confirm_import_close"
IMPORT_COHORT_MODAL = "import_cohort_modal"
IMPORT_COHORT_MODAL_LOCATION = "import_cohort_modal_location"
IMPORT_COHORT_UPLOAD_CONTAINER = "import_cohort_upload_container"
IMPORT_MODAL_ERROR = "import_modal_error"
CREATE_COHORT_CSV_BUTTON = "create_cohort_csv_button"


def import_cohort_modal() -> dbc.Modal:
    header = [html.Thead(html.Tr([html.Th("user_id")]))]

    rows = [html.Tr([html.Td(i)]) for i in ["user_id_1", "user_id_2"]]

    body = [html.Tbody(rows)]

    message = dbc.Row(
        [
            dbc.Col(
                html.Div(
                    dmc.Alert(
                        "Format Requirement: The CSV file must have a single column labeled 'user_id', "
                        "with each subsequent row containing one unique user_id.",
                        color="gray",
                    )
                ),
                width=8,
            ),
            dbc.Col(
                dmc.Table(
                    header + body,
                    verticalSpacing="xs",
                    striped=True,
                    withBorder=True,
                    withColumnBorders=True,
                ),
                width=4,
            ),
        ]
    )

    return UIKIT.create_modal(
        id=IMPORT_COHORT_MODAL,
        title="Import Cohort From CSV",
        content=[
            html.Div(
                message,
                className="mb-2",
                id=IMPORT_MODAL_BODY,
            ),
            html.Div(
                [
                    dcc.Upload(
                        id=IMPORT_COHORT_UPLOAD_CONTAINER,
                        children=html.Div(
                            [
                                "Drag and Drop or ",
                                html.A(
                                    "Select File",
                                    role="button",
                                    className="text-primary",
                                ),
                            ]
                        ),
                        className="cohort-upload-container",
                        multiple=False,
                    ),
                ]
            ),
            html.Div(
                [],
                className="mb-2",
                id=IMPORT_MODAL_ERROR,
            ),
            dcc.Location(id=IMPORT_COHORT_MODAL_LOCATION, refresh="callback-nav"),
        ],
        modal_primary_button=UIKIT.ModalButton(
            CONFIRM_IMPORT_ACCEPT,
            "Import",
            "mdi:import",
        ),
        close_button_id=CONFIRM_IMPORT_CLOSE,
        config=UIKIT.CONFIG_MODAL,
    )


@callback(
    Output(IMPORT_COHORT_MODAL, "is_open"),
    Input(CREATE_COHORT_CSV_BUTTON, "n_clicks"),
    Input(CONFIRM_IMPORT_CLOSE, "n_clicks"),
    Input(CONFIRM_IMPORT_ACCEPT, "n_clicks"),
    prevent_initial_call=True,
)
def import_button_clicked(create_button: int, close: int, accept: int) -> bool:
    if ctx.triggered_id is None or ctx.triggered_id in [CONFIRM_IMPORT_CLOSE]:
        return False
    return True


@callback(
    Output(IMPORT_COHORT_MODAL_LOCATION, "href", allow_duplicate=True),
    Output(IMPORT_MODAL_ERROR, "children"),
    Output(IMPORT_COHORT_UPLOAD_CONTAINER, "contents"),
    Input(CONFIRM_IMPORT_CLOSE, "n_clicks"),
    Input(CONFIRM_IMPORT_ACCEPT, "n_clicks"),
    Input(IMPORT_COHORT_UPLOAD_CONTAINER, "contents"),
    State(IMPORT_COHORT_UPLOAD_CONTAINER, "filename"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def create_new_cohort(
    close: int, create_button: int, contents: str, filename: str, pathname: str
) -> Tuple[str, html.Div, str]:
    project_id = P.get_path_value(P.COHORTS, pathname, P.PROJECT_ID_PATH_PART)
    dependencies = DEPS.Dependencies.get()
    storage = dependencies.storage
    if dependencies.user is None:
        raise Exception("User is not logged in")

    if ctx.triggered_id == CONFIRM_IMPORT_CLOSE:
        return no_update, html.Div(), ""
    elif ctx.triggered_id != CONFIRM_IMPORT_ACCEPT:
        return (
            no_update,
            html.Div(
                [
                    "The following file was selected: ",
                    dmc.Text(filename, weight=700, className="d-inline"),
                    " Click Import to begin the process.",
                ],
                className="mt-2",
            ),
            no_update,
        )

    try:
        project = storage.get_project(project_id)
        dependencies.tracking_service.set_project(project)

        _, content_string = contents.split(",")

        decoded = base64.b64decode(content_string)

        imported_data = pd.read_csv(
            io.StringIO(decoded.decode("utf-8")), encoding="unicode_escape"
        )

        imported_data = imported_data.drop_duplicates()

        is_series = imported_data.shape[1] == 1
        has_header = imported_data.columns[0] != 0
        has_content = not imported_data.empty

        if not has_content:
            return (
                no_update,
                html.Div(
                    [
                        dmc.Alert(
                            "The CSV is empty.",
                            color="red",
                            withCloseButton=True,
                        )
                    ],
                    className="mt-2",
                ),
                no_update,
            )

        if is_series and has_header:
            if imported_data.columns[0] != "user_id":
                return (
                    no_update,
                    html.Div(
                        [
                            dmc.Alert(
                                "The column's name is incorrect.",
                                color="red",
                                withCloseButton=True,
                            )
                        ],
                        className="mt-2",
                    ),
                    no_update,
                )

            user_list = imported_data.iloc[:, 0].tolist()
            cohort = WM.CohortInfo(
                id=create_unique_id(),
                name=filename or f"New Cohort {imported_data.shape[0]}",
                metric_json="{}",
                owner=dependencies.user.membership_id,
                type=M.CohortType.IMPORTED,
                created_at=datetime.datetime.now(),
                last_updated_at=datetime.datetime.now(),
                cohort_size=len(user_list),
                entity=M.USER_ENTITY,
            )

            project_storage = dependencies.get_project_storage(project_id)
            project_storage.set_cohort(cohort)
            project_storage.upsert_static_collection_entity_id_list(
                cohort.id, user_list
            )
            dependencies.tracking_service.track_cohort_saved(cohort)

            return (
                P.create_path(
                    P.COHORT_EDIT_PATH, cohort_id=cohort.id, project_id=project_id
                ),
                no_update,
                no_update,
            )

        else:
            return (
                no_update,
                html.Div(["The CSV does not meet the format requirements."]),
                no_update,
            )

    except PS.MaxCohortSizeReached as exc:
        LOGGER.opt(exception=exc).warning(
            "There was an error with creating a cohort from CSV."
        )
        return (
            no_update,
            html.Div(
                [
                    dmc.Alert(
                        f"{exc} Please try again.",
                        color="red",
                        withCloseButton=True,
                    )
                ],
                className="mt-2",
            ),
            no_update,
        )

    except Exception:
        return (
            no_update,
            html.Div(
                [
                    dmc.Alert(
                        "There was an error processing this file. Please try another file or review if it fits the format requirements.",
                        color="red",
                        withCloseButton=True,
                    )
                ],
                className="mt-2",
            ),
            no_update,
        )
