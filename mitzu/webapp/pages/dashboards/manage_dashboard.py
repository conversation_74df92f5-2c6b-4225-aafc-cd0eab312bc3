from dataclasses import replace
from typing import Any, Dict, List, Optional, Tuple, Union

import dash.development.base_component as bc
import dash_mantine_components as dmc
from dash import (
    ALL,
    MATCH,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    ctx,
    dcc,
    html,
    no_update,
    register_page,
)

import mitzu.helper as H
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.dashboards.components.modals.delete_dashboard_modal as DDM
import mitzu.webapp.pages.dashboards.constants as DC
import mitzu.webapp.pages.dashboards.helper as DH
import mitzu.webapp.pages.dashboards.manage_dashboards_component as MDC
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.metric_updater as MC
from mitzu.helper import (
    create_unique_id,
    parse_datetime_input,
)
from mitzu.logger import LOGGER
from mitzu.visualization.common import Annotation
from mitzu.visualization.plot import plot_chart
from mitzu.webapp.constants import (
    DASHBOARD_DEF_HEIGHT,
    DASHBOARD_DEF_WIDTH,
)
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.model import (
    DashboardHorizontalDividerItem,
    DashboardItemType,
    DashboardTextItem,
)
from mitzu.webapp.pages.common.share_modal import (
    share_modal,
)
from mitzu.webapp.pages.dashboards.components.components import (
    DashboardMetricCardModel,
    create_dashboard_horizontal_divider_container,
    create_dashboard_metric_card_model,
    create_dashboard_text_container,
    render_dashboard_metric_card,
    select_insight_component,
)
from mitzu.webapp.pages.dashboards.components.modals.duplicate_dashboard_modal import (
    DUPLICATE_DASHBOARD_MODAL,
    DUPLICATE_DASHBOARD_MODAL_CLOSE,
    DUPLICATE_DASHBOARD_MODAL_DUPLICATE_BUTTON,
    DUPLICATE_DASHBOARD_NAME_INPUT,
    DUPLICATE_DASHBOARD_WITH_INSIGHTS_CHECKBOX,
    duplicate_dashboard_modal,
)
from mitzu.webapp.pages.dashboards.components.modals.snapshot_dashboard_modal import (
    SNAPSHOT_DASHBOARD_MODAL,
    SNAPSHOT_DASHBOARD_MODAL_BUTTON,
    SNAPSHOT_DASHBOARD_MODAL_CLOSE,
    SNAPSHOT_DASHBOARD_NAME_INPUT,
    snapshot_dashboard_modal,
)
from mitzu.webapp.pages.dashboards.components.refresh_frequency_modal import (
    REFRESH_FREQUENCY_MODAL,
    REFRESH_FREQUENCY_MODAL_ACCEPT,
    REFRESH_FREQUENCY_MODAL_CLOSE,
    refresh_frequency_modal,
)
from mitzu.webapp.pages.dashboards.constants import (
    ADD_ASSET_CHIP_GROUP,
    ADD_EXISTING_INSIGHT_BUTTON,
    ADD_EXISTING_INSIGHT_MODAL,
    ADD_EXISTING_INSIGHT_MODAL_BODY,
    ADD_EXISTING_INSIGHT_MODAL_ERROR,
    CLOSE_ADD_EXISTING_INSIGHT_MODAL,
    CONFIRM_ADD_EXISTING_INSIGHT_BUTTON,
    DASHBOARD_INFO,
    DOWNLOAD_DASHBOARD_PDF,
    EXPORT_DASHBOARD_TO_PDF_BUTTON,
    SHOW_ONLY_MY_INSIGHTS,
    TEMP_TEXT_BOX_CONTAINER,
    TWC_DASHBOARD,
)
from mitzu.webapp.pages.dashboards.filter import (
    DASHBOARD_FILTER_OPERATOR_TYPE,
    DASHBOARD_FILTER_PROPERTY_TYPE,
    DASHBOARD_FILTER_VALUE_TYPE,
    get_dashboard_filter_from_inputs,
)
from mitzu.webapp.pages.dashboards.helper import (
    get_metric_context_for_dashboard,
)
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.error_pages.not_found_page import (
    not_found_page,
)
from mitzu.webapp.service.dashboard_report_service import (
    DashboardMetricReportCard,
    DashboardReportChartCard,
    DashboardReportDividerCard,
    DashboardReportTextCard,
    chart_to_base64,
    generate_html_from_dashboard,
    generate_pdf_report,
    markdown_to_html,
)
from mitzu.webapp.service.dashboard_service import (
    duplicate_dashboard,
    get_insight_result_for_dashboard,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.storage.project_storage import (
    ProjectScopedStorage,
)

DASHBOARD_LOCATION = "dashboard_location"


@fallback_to_error_page("Dashboard")
def layout(
    project_id: Optional[str], dashboard_id: Optional[str], **query_params: str
) -> bc.Component:
    if project_id is None or dashboard_id is None:
        return not_found_page("dashboard", project_id)
    dependencies = DEPS.Dependencies.get()
    if P.DASHBOARD_FILTER_QUERY in query_params:
        dfilter = SE.dashboard_filter_from_string(
            query_params[P.DASHBOARD_FILTER_QUERY]
        )
    else:
        dfilter = WM.DashboardFilter.empty()
    try:
        project_storage = dependencies.get_project_storage(project_id)
        dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()
        if dfilter.is_empty and dashboard.default_filter is not None:
            dfilter = dashboard.default_filter
        metric_context = DH.get_metric_context_for_dashboard(
            dashboard, dependencies.get_catalog_service(project_id)
        )
        project = project_storage.get_project()

        metric_updater = MC.MetricUpdater(metric_context, project.insight_settings)
        insight_results = get_insight_result_for_dashboard(
            project_storage, metric_updater, dashboard, dfilter
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).warning("failed to load dashboard page")
        return not_found_page("dashboard", project_id)

    return html.Div(
        [
            NB.get_navbar_component(project_id=project_id),
            dcc.Location(id=DASHBOARD_LOCATION, refresh="callback-nav"),
            MDC.create_manage_dashboard_container(
                dashboard,
                project_id,
                dfilter,
                is_user_read_only=dependencies.is_user_read_only,
                insight_results=insight_results,
            ),
            DDM.delete_dashboard_modal(),
            duplicate_dashboard_modal(),
            snapshot_dashboard_modal(),
            share_modal(
                obj=dashboard,
                object_type="dashboard",
                modal_id=DC.SHARE_DASHBOARD_MODAL,
                copy_options_component_id=DC.SHARE_DASHBOARD_COPY_OPTIONS,
                toggle_shared_id=DC.SHARE_DASHBOARD_TOGGLE_SHARED,
            ),
            refresh_frequency_modal(dashboard=dashboard),
        ]
    )


@callback(
    Output(DASHBOARD_LOCATION, "href"),
    Output(MDC.DASHBOARD_NOTIFICATION, "children"),
    Input(DDM.DELETE_DASHBOARD_MODAL_ACCEPT, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def delete_dashboard_accepted(
    delete_clicked: int, pathname: str
) -> Tuple[str, bc.Component]:
    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    dependencies = DEPS.Dependencies.get()
    project_storage = dependencies.get_project_storage(project_id)
    try:
        if delete_clicked and dashboard_id is not None:
            project_storage.clear_dashboard(dashboard_id)
        return P.create_path(P.PROJECT_HOME_PATH, project_id=project_id), no_update

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to delete dashboard.")
        return no_update, dmc.Alert(
            f"Couldn't delete dashboard. Details: {str(exc)[:300]}",
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="m-2",
        )


register_page(
    __name__,
    path_template=P.DASHBOARDS_EDIT_PATH,
    title="Mitzu - Dashboard",
    layout=layout,
)


@callback(
    Output(DASHBOARD_LOCATION, "pathname"),
    Output(DUPLICATE_DASHBOARD_NAME_INPUT, "error"),
    Input(DUPLICATE_DASHBOARD_MODAL_DUPLICATE_BUTTON, "n_clicks"),
    State(DUPLICATE_DASHBOARD_NAME_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    State(DUPLICATE_DASHBOARD_WITH_INSIGHTS_CHECKBOX, "checked"),
    prevent_initial_call=True,
)
def duplicate_dashboard_confirmed(
    copy_with_insights_button: Optional[int],
    input_value: str,
    pathname: str,
    checked: bool,
) -> tuple[bc.Component, bc.Component]:
    if not copy_with_insights_button:
        return no_update, no_update

    if not input_value:
        return no_update, "Dashboard name should not be empty"

    current_dashboard_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
    )
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    deps = DEPS.Dependencies.get()

    new_dash_id = duplicate_dashboard(
        deps,
        current_dashboard_id,
        project_id,
        duplicate_saved_metrics=checked,
        snapshot=False,
        new_dashboard_name=input_value,
    )

    return (
        P.create_path(
            P.DASHBOARDS_EDIT_PATH, project_id=project_id, dashboard_id=new_dash_id
        ),
        no_update,
    )


@callback(
    Output(DASHBOARD_LOCATION, "pathname", allow_duplicate=True),
    Output(SNAPSHOT_DASHBOARD_NAME_INPUT, "error"),
    Input(SNAPSHOT_DASHBOARD_MODAL_BUTTON, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    State(SNAPSHOT_DASHBOARD_NAME_INPUT, "value"),
    prevent_initial_call=True,
)
def snapshot_dashboard_confirmed(
    create_snapshot_modal_button: int, pathname: str, input_value: str
) -> Tuple[bc.Component, bc.Component]:
    if not input_value:
        return no_update, "Dashboard name should not be empty"

    current_dashboard_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
    )
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    deps = DEPS.Dependencies.get()

    new_dashboard_id = duplicate_dashboard(
        deps,
        current_dashboard_id,
        project_id,
        duplicate_saved_metrics=True,
        snapshot=True,
        new_dashboard_name=input_value,
    )

    return (
        P.create_path(
            P.DASHBOARDS_EDIT_PATH, project_id=project_id, dashboard_id=new_dashboard_id
        ),
        no_update,
    )


@callback(
    Output(TEMP_TEXT_BOX_CONTAINER, "children", allow_duplicate=True),
    Output(MDC.DASHBOARD_INFO, "children", allow_duplicate=True),
    Input(DC.ADD_TEXT_TO_DASHBOARD_BUTTON, "n_clicks"),
    Input(DC.ADD_TEXT_TO_DASHBOARD_BUTTON, "className"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def display_textarea(
    n_clicks: int, className: str, pathname: str
) -> Tuple[bc.Component, bc.Component]:
    if (
        ctx.triggered_id != DC.ADD_TEXT_TO_DASHBOARD_BUTTON
        or not n_clicks
        or len(ctx.triggered_prop_ids) > 1
    ):
        return no_update, no_update
    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        text_item = DashboardTextItem(
            x=0,
            y=0,  # storage will override this
            width=4,
            height=DASHBOARD_DEF_HEIGHT,
            text="",
            id=H.create_unique_id(),
        )

        dashboard_storage.append_dashboard_metric(text_item)
        dashboard = dashboard_storage.get_dashboard()
        card_model = DashboardMetricCardModel(
            dashboard_metric_id=text_item.id,
            name="",
            description="",
            edit_href="",
            is_valid=True,
            is_user_read_only=False,
            is_loading=False,
            is_shared=False,
            datetime_text="",
            component=create_dashboard_text_container(text_item),
            width=text_item.width,
            is_text=True,
            card_type=DashboardItemType.TEXT,
        )
        rendered_text_box = render_dashboard_metric_card(card_model)
        deps.tracking_service.track_dashboard_textbox_added(dashboard=dashboard)
        return rendered_text_box, no_update

    except Exception as exc:
        return no_update, dmc.Alert(
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="mb-3",
            children=[str(exc)[:300]],
        )


@callback(
    Output({"type": DC.DASHBOARD_TEXT_AREA, "index": MATCH}, "className"),
    Output({"type": DC.DASHBOARD_TEXT_AREA, "index": MATCH}, "error"),
    Input({"type": DC.DASHBOARD_TEXT_AREA, "index": MATCH}, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def text_updated(value: str, pathname: str) -> Tuple[bc.Component, bc.Component]:
    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)
        dashboard_metric_id = ctx.triggered_id["index"]
        dashboard_item = project_storage.get_dashboard_metric(
            dashboard_metric_id=dashboard_metric_id
        )

        if not isinstance(dashboard_item, WM.DashboardTextItem):
            raise ValueError("dashboard item does not have a text field")

        dashboard_item = replace(dashboard_item, text=value)
        project_storage.update_dashboard_metric(dashboard_item)

        return no_update, no_update

    except Exception as exc:
        return no_update, [str(exc)[:300]]


@callback(
    Output(TEMP_TEXT_BOX_CONTAINER, "children", allow_duplicate=True),
    Output(MDC.DASHBOARD_INFO, "children", allow_duplicate=True),
    Input(DC.ADD_DIVIDER_TO_DASHBOARD_BUTTON, "n_clicks"),
    Input(DC.ADD_DIVIDER_TO_DASHBOARD_BUTTON, "className"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def display_divider(
    n_clicks: int, className: str, pathname: str
) -> Tuple[bc.Component, bc.Component]:
    try:
        if (
            ctx.triggered_id != DC.ADD_DIVIDER_TO_DASHBOARD_BUTTON
            or not n_clicks
            or len(ctx.triggered_prop_ids) > 1
        ):
            return no_update, no_update

        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        dashboard_metric = DashboardHorizontalDividerItem(
            x=0,
            y=0,  # storage will overwrite this
            width=12,
            height=1,
            text="",
            id=H.create_unique_id(),
        )

        dashboard_storage.append_dashboard_metric(dashboard_metric)

        card_model = DashboardMetricCardModel(
            dashboard_metric_id=dashboard_metric.id,
            name="",
            description="",
            edit_href="",
            is_valid=True,
            is_user_read_only=False,
            is_loading=False,
            is_shared=False,
            datetime_text="",
            component=create_dashboard_horizontal_divider_container(dashboard_metric),
            width=dashboard_metric.width,
            is_text=True,
            card_type=DashboardItemType.HORIZONTAL_DIVIDER,
        )
        rendered_divider = render_dashboard_metric_card(card_model)

        return rendered_divider, no_update

    except Exception as exc:
        return no_update, dmc.Alert(
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="mb-3",
            children=[str(exc)[:300]],
        )


@callback(
    Output({"type": DC.DASHBOARD_HORIZONTAL_DIVIDER, "index": MATCH}, "className"),
    Output({"type": DC.DASHBOARD_HORIZONTAL_DIVIDER, "index": MATCH}, "error"),
    Input({"type": DC.DASHBOARD_HORIZONTAL_DIVIDER, "index": MATCH}, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def divider_updated(value: str, pathname: str) -> Tuple[bc.Component, bc.Component]:
    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)

        dashboard_metric_id = ctx.triggered_id["index"]

        dashboard_item = project_storage.get_dashboard_metric(
            dashboard_metric_id=dashboard_metric_id
        )

        if not isinstance(dashboard_item, WM.DashboardHorizontalDividerItem):
            raise ValueError("dashboard item does not have a text field")
        dashboard_item = replace(dashboard_item, text=value)

        project_storage.update_dashboard_metric(dashboard_item)

        return no_update, no_update

    except Exception as exc:
        return no_update, [str(exc)[:300]]


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="show_raw_text"),
    Output(
        {"type": DC.MARKDOWN_CONTAINER, "index": MATCH},
        "style",
        allow_duplicate=True,
    ),
    Output(
        {"type": DC.DASHBOARD_TEXT_AREA, "index": MATCH},
        "style",
        allow_duplicate=True,
    ),
    Output(
        {"type": DC.MARKDOWN_CONTAINER, "index": MATCH},
        "children",
        allow_duplicate=True,
    ),
    Input({"type": DC.RAW_TEXT_BUTTON, "index": MATCH}, "n_clicks"),
    State({"type": DC.EDIT_BUTTON_STATE, "index": MATCH}, "data"),
    State({"type": DC.DASHBOARD_TEXT_AREA, "index": MATCH}, "value"),
    prevent_initial_call=True,
)

clientside_callback(
    """
    function(n_clicks, data) {
        if (!n_clicks) {
            return [window.dash_clientside.no_update, window.dash_clientside.no_update];
        }
        if (data) {
            return [false, "dark"];
        } else {
            return [true, "indigo"];
        }
    }
    """,
    [
        Output({"type": DC.EDIT_BUTTON_STATE, "index": MATCH}, "data"),
        Output({"type": DC.RAW_TEXT_BUTTON, "index": MATCH}, "color"),
    ],
    [
        Input({"type": DC.RAW_TEXT_BUTTON, "index": MATCH}, "n_clicks"),
        State({"type": DC.EDIT_BUTTON_STATE, "index": MATCH}, "data"),
    ],
)
clientside_callback(
    """
    function(text_boxes, layout) {
        const element = layout["props"]["children"].pop();
        if (!Array.isArray(text_boxes)) {
            text_boxes = [text_boxes];
        }
        layout["props"]["children"].push(...text_boxes);
        layout["props"]["children"].push(element);
        return [[], layout];
    }
    """,
    Output(TEMP_TEXT_BOX_CONTAINER, "children"),
    Output(MDC.RESPONSIVE_GRID_LAYOUT_CONTAINER, "children", allow_duplicate=True),
    Input(TEMP_TEXT_BOX_CONTAINER, "children"),
    State(MDC.RESPONSIVE_GRID_LAYOUT_CONTAINER, "children"),
    prevent_initial_call=True,
)


# disable the error message when the user writes something in the input. The same is done for the snapshot button
clientside_callback(
    """
        function(value) {
            return ""
        }
        """,
    Output(DUPLICATE_DASHBOARD_NAME_INPUT, "error", allow_duplicate=True),
    Input(DUPLICATE_DASHBOARD_NAME_INPUT, "value"),
    prevent_initial_call=True,
)

clientside_callback(
    """
        function(value) {  
            return ""
        }
        """,
    Output(SNAPSHOT_DASHBOARD_NAME_INPUT, "error", allow_duplicate=True),
    Input(SNAPSHOT_DASHBOARD_NAME_INPUT, "value"),
    prevent_initial_call=True,
)


clientside_callback(
    ClientsideFunction(
        namespace="clientside", function_name="duplicate_dashboard_modal"
    ),
    Output(DUPLICATE_DASHBOARD_MODAL, "is_open"),
    Output(DUPLICATE_DASHBOARD_NAME_INPUT, "value", allow_duplicate=True),
    Output(DUPLICATE_DASHBOARD_NAME_INPUT, "error", allow_duplicate=True),
    Input(DC.DUPLICATE_DASHBOARD_BUTTON, "n_clicks"),
    Input(DUPLICATE_DASHBOARD_MODAL_CLOSE, "n_clicks"),
    State(DC.DASHBOARD_NAME_INPUT, "value"),
    prevent_initial_call=True,
)

clientside_callback(
    ClientsideFunction(
        namespace="clientside", function_name="snapshot_dashboard_modal"
    ),
    Output(SNAPSHOT_DASHBOARD_MODAL, "is_open"),
    Output(SNAPSHOT_DASHBOARD_NAME_INPUT, "value", allow_duplicate=True),
    Output(SNAPSHOT_DASHBOARD_NAME_INPUT, "error", allow_duplicate=True),
    Input(DC.CREATE_SNAPSHOT_BUTTON, "n_clicks"),
    Input(SNAPSHOT_DASHBOARD_MODAL_CLOSE, "n_clicks"),
    State(DC.DASHBOARD_NAME_INPUT, "value"),
    prevent_initial_call=True,
)

clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="refresh_frequency_modal"),
    Output(REFRESH_FREQUENCY_MODAL, "is_open"),
    Input(DC.DASHBOARD_AUTO_REFRESH, "n_clicks"),
    Input(REFRESH_FREQUENCY_MODAL_CLOSE, "n_clicks"),
    Input(REFRESH_FREQUENCY_MODAL_ACCEPT, "n_clicks"),
    Input(DC.AUTO_REFRESH_SETTINGS_INDICATOR, "n_clicks"),
    prevent_initial_call=True,
)

clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="delete_dashboard_modal"),
    Output(DDM.DELETE_DASHBOARD_MODAL, "is_open"),
    Input(DC.DELETE_DASHBOARD_BUTTON, "n_clicks"),
    Input(DDM.DELETE_DASHBOARD_MODAL_CLOSE, "n_clicks"),
    Input(DDM.DELETE_DASHBOARD_MODAL_ACCEPT, "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output(ADD_EXISTING_INSIGHT_MODAL, "is_open", allow_duplicate=True),
    Output(ADD_EXISTING_INSIGHT_MODAL_BODY, "children", allow_duplicate=True),
    Output(ADD_EXISTING_INSIGHT_MODAL_ERROR, "children", allow_duplicate=True),
    Input(ADD_EXISTING_INSIGHT_BUTTON, "n_clicks"),
    Input(CLOSE_ADD_EXISTING_INSIGHT_MODAL, "n_clicks"),
    Input(CONFIRM_ADD_EXISTING_INSIGHT_BUTTON, "n_clicks"),
    Input(SHOW_ONLY_MY_INSIGHTS, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_add_existing_insight_modal(
    open_clicks: Optional[int],
    close_clicks: Optional[int],
    confirm_clicks: Optional[int],
    show_only_my_insights: bool,
    pathname: str,
) -> Tuple[bool, Union[bc.Component, List], bc.Component]:
    trigger = ctx.triggered_id
    try:
        if trigger == ADD_EXISTING_INSIGHT_BUTTON or trigger == SHOW_ONLY_MY_INSIGHTS:
            deps = DEPS.Dependencies.get()
            if deps.user is None:
                raise Exception("User is not logged in")

            project_id = P.get_path_value(
                P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
            )

            project_storage = deps.storage.get_project_storage(project_id)
            dashboard = project_storage.get_dashboard_storage(
                P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
            ).get_dashboard()
            existing_saved_metric_ids = [
                dm.saved_metric.id
                for dm in dashboard.dashboard_metrics
                if isinstance(dm, WM.DashboardMetricItem)
            ]
            saved_metrics = [
                sm
                for sm in project_storage.list_saved_metrics()
                if sm.id not in existing_saved_metric_ids
            ]

            if show_only_my_insights:
                saved_metrics = [
                    sm for sm in saved_metrics if sm.owner == deps.user.membership_id
                ]

            return (
                True,
                select_insight_component(saved_metrics),
                no_update,
            )

        if trigger in [
            CLOSE_ADD_EXISTING_INSIGHT_MODAL,
            CONFIRM_ADD_EXISTING_INSIGHT_BUTTON,
        ]:
            return False, no_update, no_update

        return no_update, no_update, no_update
    except Exception as e:
        LOGGER.opt(exception=e).error("Failed to populate add existing insight modal")
        return False, no_update, dmc.Text(str(e))


@callback(
    Output(TEMP_TEXT_BOX_CONTAINER, "children", allow_duplicate=True),
    Output(DASHBOARD_INFO, "children", allow_duplicate=True),
    Input(CONFIRM_ADD_EXISTING_INSIGHT_BUTTON, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    State(ADD_ASSET_CHIP_GROUP, "value"),
    prevent_initial_call=True,
)
def confirm_add_existing_insight(
    n_clicks: Optional[int], pathname: str, selected_insights: List[str]
) -> Tuple[List[bc.Component], bc.Component]:
    if n_clicks is None or not selected_insights:
        return no_update, no_update

    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)
        dashboard = dashboard_storage.get_dashboard()
        dashboard_metrics = dashboard.dashboard_metrics

        existing_ids = {
            dm.saved_metric.id
            for dm in dashboard_metrics
            if isinstance(dm, WM.DashboardMetricItem)
        }
        if set(selected_insights) & existing_ids:
            return no_update, dmc.Alert(
                color="red",
                title="Insight already exists",
                withCloseButton=True,
                className="mb-3",
                children=[
                    "One or more selected insights are already on the dashboard."
                ],
            )
        new_cards = []
        dashboard_items: List[WM.DashboardMetricItem] = []
        for selected_insight in selected_insights:
            dashboard_item = WM.DashboardMetricItem(
                saved_metric=project_storage.get_saved_metric(selected_insight),
                x=0,
                y=0,
                width=DASHBOARD_DEF_WIDTH,
                height=DASHBOARD_DEF_HEIGHT,
                id=create_unique_id(),
            )
            dashboard_storage.append_dashboard_metric(
                dashboard_item,
            )

            dashboard_items.append(dashboard_item)

        dashboard = dashboard_storage.get_dashboard()

        metric_context = get_metric_context_for_dashboard(
            dashboard, deps.get_catalog_service(project_id)
        )

        annotations = project_storage.list_annotations()
        metric_updater = MetricUpdater(
            metric_context,
            project_storage.get_project().insight_settings,
        )

        dashboard_defs = project_storage.get_definitions_for_dashboard(
            dashboard, WM.DashboardFilter.empty()
        )

        for dashboard_item in dashboard_items:
            insight_result = project_storage.get_insight_result(
                dashboard_item.saved_metric.id, dashboard_item.saved_metric.metric_json
            )
            card_model = create_dashboard_metric_card_model(
                dashboard_item,
                loading=False,
                metric_converter=MetricConverter(
                    dashboard_defs, project_storage, metric_updater
                ),
                metric_context=metric_context,
                is_user_read_only=deps.is_user_read_only,
                annotations=annotations,
                is_shared=False,
                default_end_dt=project_storage.get_project().get_default_end_dt(),
                use_consistent_coloring=False,
                insight_result=insight_result,
                dashboard_has_filters=False,
            )

            new_cards.append(render_dashboard_metric_card(card_model))

            deps.tracking_service.track_add_metric_to_dashboard(
                dashboard=dashboard,
                saved_metric=dashboard_item.saved_metric,
                source_page="edit_dashboard_page",
            )
        return new_cards, no_update

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to add existing insight to dashboard")
        return no_update, dmc.Alert(
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="mb-3",
            children=[str(exc)[:300]],
        )


@callback(
    Output(DOWNLOAD_DASHBOARD_PDF, "data"),
    Output(DASHBOARD_INFO, "children", allow_duplicate=True),
    Input(EXPORT_DASHBOARD_TO_PDF_BUTTON, "n_clicks"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_OPERATOR_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": ALL}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.CUSTOM_DATE_PICKER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.SINCE_DATE_PICKER}, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def export_dashboard_pdf(
    n_clicks: Optional[int],
    properties: List[Optional[str]],
    operators: List[str],
    values: List[List[Any]],
    custom_dates: List[str],
    lookback_days: Optional[str],
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    pathname: str,
) -> Tuple[Dict[str, Any], bc.Component]:
    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)
        annotations = project_storage.list_annotations()

        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        dfilter = get_dashboard_filter_from_inputs(
            properties,
            operators,
            values,
            None,
            time_config=DH.get_time_window_config(
                DH.parse_custom_dates(custom_dates),
                DH.parse_time_window(lookback_days),
                None,
                WM.CalendarDateRange(calendar_date_range)
                if calendar_date_range
                else None,
                since_date=parse_datetime_input(since_date, def_val=None)
                if since_date
                else None,
            ),
        )

        dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()

        cards = create_dashboard_report_cards(
            deps, project_storage, annotations, dfilter, dashboard
        )

        html = generate_html_from_dashboard(dashboard.name, cards)
        pdf_bytes = generate_pdf_report(html)

        return (
            dcc.send_bytes(  # type: ignore[attr-defined]
                pdf_bytes, filename="dashboard_report.pdf", mimetype="application/pdf"
            ),
            no_update,
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to export dashboard to pdf")
        return no_update, dmc.Alert(
            color="red",
            title="Something went wrong",
            withCloseButton=True,
            className="mb-3",
            children=[str(exc)[:300]],
        )


def create_dashboard_report_cards(
    deps: DEPS.Dependencies,
    project_storage: ProjectScopedStorage,
    annotations: List[Annotation],
    dfilter: WM.DashboardFilter,
    dashboard: WM.Dashboard,
) -> List[DashboardMetricReportCard]:
    project = project_storage.get_project()
    dashboard_defs = project_storage.get_definitions_for_dashboard(dashboard, dfilter)
    metric_context = DH.get_metric_context_for_dashboard(
        dashboard, deps.get_catalog_service(project.id)
    )
    metric_updater = MetricUpdater(metric_context, project.insight_settings)
    metric_converter = MetricConverter(dashboard_defs, project_storage, metric_updater)
    insight_results = get_insight_result_for_dashboard(
        project_storage,
        metric_updater,
        dashboard,
        dfilter,
    )

    cards: List[DashboardMetricReportCard] = []
    for dm in sorted(dashboard.dashboard_metrics, key=lambda dm: dm.y):
        if isinstance(dm, WM.DashboardTextItem):
            cards.append(
                DashboardReportTextCard(
                    text=markdown_to_html(dm.text),
                )
            )
            continue
        elif isinstance(dm, WM.DashboardHorizontalDividerItem):
            cards.append(
                DashboardReportDividerCard(
                    text=dm.text,
                )
            )
            continue
        elif isinstance(dm, WM.DashboardMetricItem):
            webapp_metric = dm.saved_metric.metric()
            metric = metric_converter.convert_metric(webapp_metric)

            insight_result = insight_results[dm.saved_metric.id]
            if isinstance(insight_result, Exception):
                LOGGER.opt(exception=insight_result).warning(
                    "failed to get insight result for dashboard"
                )
                continue
            fig, _ = plot_chart(
                insight_result,
                metric,
                metric_context,
                annotations,
                webapp_metric,
                default_end_dt=project.get_default_end_dt(),
            )
            fig.update_layout(
                showlegend=True,
                legend=dict(
                    x=0.4,
                    y=-0.1,
                    font=dict(family="BlinkMacSystemFont,Segoe UI", size=12),
                    orientation="h",
                    title=None,
                    xanchor="center",
                ),
            )
            image_data = chart_to_base64(fig)
            cards.append(
                DashboardReportChartCard(
                    name=dm.saved_metric.name,
                    description=dm.saved_metric.description,
                    chart_b64=image_data,
                )
            )
    return cards
