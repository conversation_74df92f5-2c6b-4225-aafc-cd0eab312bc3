from datetime import datetime
from typing import List, Optional, Set, Tuple

import mitzu.model as M
import mitzu.webapp.metric_context as MC
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.service.catalog_service as CS
from mitzu.webapp import dependencies as DEPS
from mitzu.webapp import model as WM


def parse_time_window(lookback_days: Optional[str]) -> Optional[M.TimeWindow]:
    if lookback_days and lookback_days != TWC.DEFAULT_VALUE:
        return M.TimeWindow.parse(lookback_days)
    return None


def parse_custom_dates(
    custom_dates: Optional[List[str]],
) -> Optional[Tuple[datetime, datetime]]:
    if (
        custom_dates
        and len(custom_dates) == 2
        and custom_dates[0] is not None
        and custom_dates[1] is not None
    ):
        start_dt = datetime.strptime(custom_dates[0][:10], "%Y-%m-%d")
        end_dt = datetime.strptime(custom_dates[1][:10], "%Y-%m-%d")
        return (start_dt, end_dt)
    return None


def get_time_window_config(
    custom_dates: Optional[Tuple[datetime, datetime]],
    lookback_time_window: Optional[M.TimeWindow],
    time_group: Optional[M.TimeGroup],
    calendar_date_range: Optional[WM.CalendarDateRange],
    since_date: Optional[datetime],
) -> Optional[WM.TimeWindowChooserConfig]:
    if custom_dates:
        start_dt, end_dt = custom_dates
        if time_group != M.TimeGroup.TOTAL:
            time_group = M.TimeGroup.get_default_for_days((end_dt - start_dt).days)
        return WM.TimeWindowChooserConfig(
            start_dt=start_dt,
            end_dt=end_dt,
            time_group=time_group,
            lookback_days=M.DEF_LOOK_BACK_DAYS,
            calendar_date_range=None,
            since_date=since_date,
        )
    if lookback_time_window:
        delta = lookback_time_window.to_approx_days()
        if time_group != M.TimeGroup.TOTAL:
            time_group = M.TimeGroup.get_default_for_days(delta)
        return WM.TimeWindowChooserConfig(
            lookback_days=lookback_time_window,
            start_dt=None,
            end_dt=None,
            time_group=time_group,
            calendar_date_range=None,
            since_date=since_date,
        )
    if calendar_date_range is not None:
        delta = calendar_date_range.to_approx_days()
        if time_group != M.TimeGroup.TOTAL:
            time_group = M.TimeGroup.get_default_for_days(delta)
        return WM.TimeWindowChooserConfig(
            lookback_days=None,
            start_dt=None,
            end_dt=None,
            time_group=time_group,
            calendar_date_range=calendar_date_range,
            since_date=since_date,
        )
    elif since_date is not None:
        return WM.TimeWindowChooserConfig(
            lookback_days=None,
            start_dt=None,
            end_dt=None,
            time_group=time_group,
            calendar_date_range=None,
            since_date=since_date,
        )
    return None


def delete_dashboard_metric(
    dashboard_metric_id: str, dashboard: WM.Dashboard, deps: DEPS.Dependencies
) -> WM.Dashboard:
    dashboard_storage = deps.get_project_storage(
        dashboard.project_id
    ).get_dashboard_storage(dashboard.id)
    dashboard_storage.remove_dashboard_metric(dashboard_metric_id)
    dashboard = dashboard_storage.get_dashboard()
    deps.tracking_service.track_dashboard_saved(dashboard, "edit_dashboard_page")
    return dashboard


def get_metric_context_for_dashboard(
    dashboard: WM.Dashboard,
    catalog_service: CS.CatalogService,
) -> MC.MetricContext:
    selected_events: Set[M.EventNamePath] = set()
    for dashboard_metric in dashboard.dashboard_metrics:
        if not isinstance(dashboard_metric, WM.DashboardMetricItem):
            continue
        metric = dashboard_metric.saved_metric.metric()
        for seg in metric.segments:
            selected_events.update(seg.get_event_name_paths())

    return catalog_service.get_metric_context_for_events(list(selected_events))
