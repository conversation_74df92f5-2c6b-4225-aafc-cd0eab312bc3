import json
from dataclasses import replace
from datetime import datetime
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Optional,
    Tuple,
    Union,
)

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    ALL,
    MATCH,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    ctx,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from dash_iconify import DashIconify

import mitzu.visualization.common as VC
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.dashboards.constants as DC
import mitzu.webapp.pages.dashboards.helper as DH
import mitzu.webapp.pages.explore.components.dimension_table_modal as UTM
import mitzu.webapp.pages.explore.components.list_events_modal as LEM
import mitzu.webapp.pages.explore.graph_tooltips as GT
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.input_generator as IG
import mitzu.webapp.service.metric_converter as MC
from mitzu.helper import parse_datetime_input
from mitzu.logger import LOGGER
from mitzu.visualization.common import (
    CURRENT_CHART_VERSION,
    Annotation,
)
from mitzu.webapp.helper import (
    MITZU_LOCATION,
    get_error_message,
    log_exception,
)
from mitzu.webapp.metric_context import MetricContext
from mitzu.webapp.pages.common.share_modal import (
    SHARE_MODAL_CLOSE,
)
from mitzu.webapp.pages.dashboards.components.components import (
    create_responsive_grid_layout,
    get_last_updated,
    get_title_and_description_inputs_row,
    get_title_and_description_row,
)
from mitzu.webapp.pages.dashboards.constants import (
    DASHBOARD_DESC_INPUT,
    DASHBOARD_FILTER_LAST_STATE,
    DASHBOARD_GRID_ITEM,
    DASHBOARD_NAME_INPUT,
    DASHBOARD_REFRESH_BUTTON,
    DASHBOARD_REFRESH_CANCEL_BUTTON,
    DASHBOARD_SHOW_ANNOTATIONS,
    DASHBOARD_USE_CONSISTENT_COLORING,
    DOWNLOAD_DASHBOARD_PDF,
    MC_OVERWRITE_COMP,
    SHARE_DASHBOARD_MODAL,
    SHARED_BUTTON_DASHBOARD,
    SHARING_OPTIONS_BUTTON,
    TEMP_TEXT_BOX_CONTAINER,
    TWC_DASHBOARD,
)
from mitzu.webapp.pages.dashboards.filter import (
    DASHBOARD_FILTER_CONTAINER,
    DASHBOARD_FILTER_OPERATOR_TYPE,
    DASHBOARD_FILTER_PROPERTY_TYPE,
    DASHBOARD_FILTER_VALUE_TYPE,
    get_dashboard_filter_from_inputs,
    render_dashboard_filter_rows,
)
from mitzu.webapp.pages.explore.annotation_modal import (
    create_annotation_dialog,
)
from mitzu.webapp.pages.explore.cohort_modal import (
    create_cohort_dialog,
)
from mitzu.webapp.service.dashboard_service import (
    get_insight_result_for_dashboard,
    refresh_dashboard,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.service.path_parser import PathParser
from mitzu.webapp.storage.project_storage import (
    ProjectNotDiscoveredError,
)

RESPONSIVE_GRID_LAYOUT_CONTAINER = "responsive_grid_layout_container"
DASHBOARD_ITEM_PREFIX = "dashboard_metric"
DELETE_SAVED_METRICS_TYPE = "dashboard_delete_saved_metric"

DASHBOARD_LAST_UPDATED = "dashboard_last_updated"
DASHBOARD_SPINNER = "dashboard_refresh_spinner"
DASHBOARD_INFO = "dashboard_info"

DASH_ORDER_INPUT = "dashboards-order-input"
DUMMY_DIV = "dashboard_dummy_div"
DASHBOARD_NOTIFICATION = "dashboard_notification"
MANAGE_DASHBOARD_LOCATION = "manage_dashboard_location"
MANAGE_DASHBOARD_SHOW_LOCATION = (
    "manage_dashboard_show_location"  # used as a trigger to cancel the bg callback
)
PAGE_LOADED_DATA_STORE = "page_loaded_data_store"


def create_manage_dashboard_container(
    dashboard: WM.Dashboard,
    project_id: str,
    dashboard_filter: WM.DashboardFilter,
    is_user_read_only: bool,
    insight_results: Dict[str, Union[VC.SimpleChart, Exception]],
) -> bc.Component:
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)
    project = project_storage.get_project()

    metric_context = DH.get_metric_context_for_dashboard(
        dashboard, deps.get_catalog_service(project.id)
    )
    input_generator = IG.InputGenerator(project.id, metric_context, project_storage)
    return html.Div(
        [
            dcc.Store(id=PAGE_LOADED_DATA_STORE, data={"page_loaded": True}),
            # This div is used to store the Chart order after Dragula drops. See assets/dragula.js
            html.Div(children=[], id=DASH_ORDER_INPUT, className="d-none"),
            html.Div(children=[], id=DUMMY_DIV, className="d-none"),
            html.Div(
                children=SE.dashboard_filter_to_string(dashboard_filter),
                id={"type": DASHBOARD_FILTER_LAST_STATE, "index": "index"},
                className="d-none",
            ),
            dcc.Location(id=MANAGE_DASHBOARD_LOCATION, refresh=False),
            dcc.Location(id=MANAGE_DASHBOARD_SHOW_LOCATION, refresh=False),
            create_cohort_dialog(),
            create_annotation_dialog(),
            UTM.create_dimension_table_table_modal(),
            LEM.create_list_events_modal(),
            dbc.Container(
                [
                    get_title_and_description_inputs_row(dashboard, is_user_read_only),
                    dmc.Group(
                        [
                            html.Div(
                                render_dashboard_filter_rows(
                                    dashboard_filter,
                                    input_generator,
                                    dashboard.get_all_event_name_paths(),
                                ),
                                id=DASHBOARD_FILTER_CONTAINER,
                                className="uikit-property-filter-container",
                                style={"width": "100%"},
                            ),
                        ],
                        className="d-flex mt-2 w-100",
                        position="apart",
                    ),
                    dmc.Group(
                        [
                            dmc.Group(
                                [
                                    html.Span(
                                        TWC.create_timewindow_chooser(
                                            config=dashboard_filter.time_config,
                                            id_type=TWC_DASHBOARD,
                                            insight_settings=project.insight_settings,
                                            include_default=True,
                                            popup_position="bottom-start",
                                        ),
                                        id=MC_OVERWRITE_COMP,
                                    ),
                                    UIKIT.create_cancel_button(
                                        DASHBOARD_REFRESH_CANCEL_BUTTON, size="xs"
                                    ),
                                    UIKIT.primary_button(
                                        "Refresh",
                                        icon=DashIconify(icon="mdi:refresh"),
                                        size="xs",
                                        id=DASHBOARD_REFRESH_BUTTON,
                                    ),
                                ]
                            ),
                            html.Div(
                                children=get_last_updated(dashboard),
                                id=DASHBOARD_LAST_UPDATED,
                            ),
                        ],
                        spacing=10,
                        className="my-3",
                        position="left",
                    ),
                    dmc.Progress(
                        size="xs",
                        id=DASHBOARD_SPINNER,
                        value=0,
                        animate=True,
                        className="mb-3 mt-3",
                    ),
                    dbc.Row(dmc.Container(id=DASHBOARD_NOTIFICATION)),
                    html.Div(children=[], id=DASHBOARD_INFO),
                    html.Div(id=TEMP_TEXT_BOX_CONTAINER, className="d-none"),
                    dcc.Download(id=DOWNLOAD_DASHBOARD_PDF),
                    html.Div(
                        children=create_responsive_grid_layout(
                            dashboard,
                            loading=True,
                            metric_converter=None,
                            metric_context=MetricContext.empty(),  # during loading we don't need metric context
                            is_user_read_only=is_user_read_only,
                            annotations=[],  # hiding annotations initial load
                            is_shared=False,
                            default_end_dt=project.get_default_end_dt(),
                            insight_results=insight_results,
                            dashboard_has_filters=len(dashboard_filter.filters) > 0,
                        ),
                        id=RESPONSIVE_GRID_LAYOUT_CONTAINER,
                        className="loading-dashboard",
                    ),
                    *GT.create_graph_tooltips(),
                ],
                class_name="mb-10",
            ),
        ],
        className="pt-2",
    )


def create_shared_dashboard_container(
    dashboard: WM.Dashboard,
    metric_context: MetricContext,
    metric_converter: Optional[MC.MetricConverter],
    annotations: List[Annotation],
    default_end_dt: datetime,
    insight_results: Dict[str, Union[VC.SimpleChart, Exception]],
    input_generator: IG.InputGenerator,
) -> bc.Component:
    return html.Div(
        [
            UTM.create_dimension_table_table_modal(),
            LEM.create_list_events_modal(),
            html.Div(
                [
                    get_title_and_description_row(dashboard),
                    dmc.Group(
                        [
                            dmc.Group(
                                [
                                    dcc.Link(
                                        children=dmc.Button(
                                            "Open in Mitzu",
                                            size="xs",
                                            color="indigo",
                                            variant="subtle",
                                            compact=True,
                                        ),
                                        target="_blank",
                                        href=P.create_path(
                                            P.DASHBOARDS_EDIT_PATH,
                                            project_id=dashboard.project_id,
                                            dashboard_id=dashboard.id,
                                        ),
                                        className=("d-inline-block"),
                                    ),
                                    html.Div(
                                        children=get_last_updated(dashboard),
                                    ),
                                ],
                                spacing="5px",
                            ),
                        ],
                        className="d-flex mt-2",
                        position="apart",
                    ),
                    dmc.Group(
                        [
                            html.Div(
                                render_dashboard_filter_rows(
                                    dashboard.default_filter,
                                    input_generator,
                                    dashboard.get_all_event_name_paths(),
                                    read_only=True,
                                ),
                                id=DASHBOARD_FILTER_CONTAINER,
                                className="uikit-property-filter-container",
                                style={"width": "100%"},
                            ),
                        ],
                        className="d-flex mt-2 w-100",
                        position="apart",
                    )
                    if dashboard.default_filter is not None
                    else None,
                    dmc.Progress(
                        size="xs",
                        id=DASHBOARD_SPINNER,
                        value=0,
                        animate=True,
                        className="mb-3 mt-3",
                    ),
                    dbc.Row(dmc.Container(id=DASHBOARD_NOTIFICATION)),
                    html.Div(children=[], id=DASHBOARD_INFO),
                    html.Div(
                        children=create_responsive_grid_layout(
                            dashboard,
                            loading=False,
                            metric_converter=metric_converter,
                            metric_context=metric_context,  # during loading we don't need metric context
                            is_user_read_only=True,
                            annotations=annotations,
                            is_shared=True,
                            default_end_dt=default_end_dt,
                            insight_results=insight_results,
                            dashboard_has_filters=False,
                        ),
                        className="loading-dashboard",
                        id=RESPONSIVE_GRID_LAYOUT_CONTAINER,
                    ),
                    *GT.create_graph_tooltips(),
                ],
                className="mb-10 w-100",
            ),
        ],
    )


clientside_callback(
    ClientsideFunction(
        namespace="clientside", function_name="init_dashboard_drag_and_drop"
    ),
    Output(DC.RESPONSIVE_GRID_LAYOUT, "data-drag"),
    [Input(DC.RESPONSIVE_GRID_LAYOUT, "id")],
)

clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="set_dashboard_title"),
    Output(DUMMY_DIV, "children"),
    Input(DASHBOARD_NAME_INPUT, "value"),
)


@callback(
    Output(
        {"type": DC.DASHBOARD_GRID_ITEM, "index": MATCH},
        "xl",
        allow_duplicate=True,
    ),
    Input({"type": DC.RESIZE_BUTTON_TYPE, "index": MATCH, "size": ALL}, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_chart_resize(resize_click: Optional[int], pathname: str) -> int:
    if ctx.triggered_id is None or resize_click is None:
        return no_update

    resize_metric_id = ctx.triggered_id.get("index")
    if resize_metric_id is None:
        return no_update

    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    deps = DEPS.Dependencies.get()

    project_storage = deps.get_project_storage(project_id)
    dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)
    dashboard = dashboard_storage.get_dashboard()

    metrics_dict = {metric.id: metric for metric in dashboard.dashboard_metrics}
    size = int(ctx.triggered_id.get("size", "12"))
    if resize_metric_id in metrics_dict:
        dm = metrics_dict[resize_metric_id]
        dm = replace(dm, width=size)
        dashboard_storage.set_dashboard_metric(dm)
        return dm.width

    return no_update


@callback(
    Output(
        {"type": DC.DASHBOARD_GRID_ITEM, "index": MATCH},
        "className",
        allow_duplicate=True,
    ),
    Input({"type": DC.DELETE_SAVED_METRICS_TYPE, "index": MATCH}, "n_clicks"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_chart_remove(delete_click: Optional[int], pathname: str) -> str:
    if ctx.triggered_id is None or delete_click is None:
        return no_update

    delete_metric_id = ctx.triggered_id.get("index")
    if delete_metric_id is None:
        return no_update

    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)
    dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()

    metrics_dict = {
        dashboard_metric.id: dashboard_metric
        for dashboard_metric in dashboard.dashboard_metrics
    }
    if delete_metric_id in metrics_dict:
        dm = metrics_dict[delete_metric_id]
        DH.delete_dashboard_metric(dm.id, dashboard, deps)
        return "d-none"

    return no_update


@callback(
    Output(DASHBOARD_NAME_INPUT, "value"),
    Input(DASHBOARD_NAME_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def dashboard_name_changed(name: Optional[str], pathname: str) -> str:
    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    if name is None:
        return no_update
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)

    dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)
    dashboard_storage.set_name(name or "Unnamed dashboard")
    return name


@callback(
    Output(DASHBOARD_DESC_INPUT, "value"),
    Input(DASHBOARD_DESC_INPUT, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def dashboard_description_changed(description: Optional[str], pathname: str) -> str:
    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    if description is None:
        return no_update
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)

    if description is not None:
        description = description.strip()

    if description == "":
        description = None

    dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)
    dashboard_storage.set_description(description)
    return description or ""


def create_dashboard_error_alert(exc: Exception, title: str) -> dmc.Alert:
    return dmc.Alert(
        f"Details: {get_error_message(exc)}",
        color="red",
        title=title,
        withCloseButton=True,
        className="mb-3",
    )


def trial_expired_alert(project_id: str, user_role: WM.Role) -> dmc.Alert:
    if user_role != WM.Role.ADMIN:
        content = (
            "Contact your workspace administrator to get your subscription renewed."
        )
    else:
        content = dcc.Link(
            dmc.Button(
                "Manage subscription",
                size="sm",
                color="dark",
                radius=UIKIT.DEF_BUTTON_RADIUS,
                variant="filled",
            ),
            href=P.create_account_settings_path(
                project_id, P.ManageAccountTab.SUBSCRIPTION
            ),
        )

    return dmc.Alert(
        content,
        color="red",
        title="Your trial expired. Upgrade your package to continue.",
        withCloseButton=True,
        className="mb-3",
    )


@callback(
    Output(RESPONSIVE_GRID_LAYOUT_CONTAINER, "children"),
    Output(DASHBOARD_INFO, "children"),
    Output(DASHBOARD_REFRESH_BUTTON, "n_clicks"),
    Output(RESPONSIVE_GRID_LAYOUT_CONTAINER, "className"),
    Input(PAGE_LOADED_DATA_STORE, "data"),
    State({"type": TWC_DASHBOARD, "index": TWC.CUSTOM_DATE_PICKER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.SINCE_DATE_PICKER}, "value"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_OPERATOR_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": ALL}, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call="initial_duplicate",
    background=True,
    # avoid updating the RESPONSIVE_GRID_LAYOUT_CONTAINER className with the running
    # keyword since it will raise errors when the user navigates to a different page
    interval=200,
)
def show_dashboard(
    data: Dict[str, Any],
    custom_dates: List[str],
    lookback_days: str,
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    properties: List[Optional[str]],
    operators: List[str],
    values: List[List[Any]],
    pathname: str,
) -> Tuple[dbc.Row, bc.Component, int, str]:
    if not data or not data.get("page_loaded"):
        return no_update, no_update, no_update, "loaded-dashboard"
    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()
        dfilter = get_dashboard_filter_from_inputs(
            properties,
            operators,
            values,
            None,
            time_config=DH.get_time_window_config(
                DH.parse_custom_dates(custom_dates),
                DH.parse_time_window(lookback_days),
                None,
                WM.CalendarDateRange(calendar_date_range)
                if calendar_date_range
                else None,
                since_date=parse_datetime_input(since_date, def_val=None)
                if since_date
                else None,
            ),
        )
        dashboard_defs = project_storage.get_definitions_for_dashboard(
            dashboard, dfilter
        )
        project = dashboard_defs.project
        metric_context = DH.get_metric_context_for_dashboard(
            dashboard, deps.get_catalog_service(project.id)
        )
        annotations = project_storage.list_annotations()
        metric_updater = MetricUpdater(
            metric_context, project_storage.get_project().insight_settings
        )
        insight_results = get_insight_result_for_dashboard(
            project_storage,
            metric_updater,
            dashboard,
            dfilter,
        )
        return (
            create_responsive_grid_layout(
                dashboard,
                False,
                MetricConverter(dashboard_defs, project_storage, metric_updater),
                metric_context,
                deps.is_user_read_only,
                annotations=annotations,
                is_shared=False,
                default_end_dt=project.get_default_end_dt(),
                insight_results=insight_results,
                dashboard_has_filters=len(dfilter.filters) > 0,
            ),
            no_update,
            1 if dashboard.is_stale() else no_update,
            "loaded-dashboard",
        )
    except ProjectNotDiscoveredError as exc:
        LOGGER.opt(exception=exc).warning(
            "Project not discovered, dashboard refresh not possible"
        )
        return (no_update, [], no_update, "loaded-dashboard")
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to show dashboard")
        return (
            no_update,
            [
                create_dashboard_error_alert(
                    exc=exc,
                    title="Failed to show dashboard",
                ),
            ],
            no_update,
            "loaded-dashboard",
        )


clientside_callback(
    """
    function(checked) {
        window.dash_clientside.clientside.toggle_graph_annotations(checked);
        return checked;
    }
    """,
    Output(DASHBOARD_SHOW_ANNOTATIONS, "checked", allow_duplicate=True),
    Input(DASHBOARD_SHOW_ANNOTATIONS, "checked"),
    prevent_initial_call=True,
)


clientside_callback(
    """
    function(children, checked) {
        window.dash_clientside.clientside.toggle_graph_annotations(checked);
        return window.dash_clientside.no_update;
    }
    """,
    Output(DASHBOARD_SHOW_ANNOTATIONS, "checked", allow_duplicate=True),
    Input({"index": ALL, "type": DASHBOARD_GRID_ITEM}, "children"),
    State(DASHBOARD_SHOW_ANNOTATIONS, "checked"),
    prevent_initial_call=True,
)


@callback(
    Output(DASHBOARD_SHOW_ANNOTATIONS, "checked"),
    Input(DASHBOARD_SHOW_ANNOTATIONS, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_toggle_show_annotations(
    show_annotations: bool, pathname: str
) -> Tuple[str, str]:
    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        dashboard_storage.set_show_annotations(show_annotations)
        return no_update
    except Exception as exc:
        LOGGER.opt(exception=exc).error("Failed to update dashboard show annotations")
    return no_update


@callback(
    Output(DASHBOARD_USE_CONSISTENT_COLORING, "checked"),
    Input(DASHBOARD_USE_CONSISTENT_COLORING, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_toggle_use_consistent_coloring(
    use_consistent_coloring: bool, pathname: str
) -> Tuple[str, str]:
    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        dashboard_storage.set_use_consistent_coloring(use_consistent_coloring)
        return no_update
    except Exception as exc:
        LOGGER.opt(exception=exc).error(
            "Failed to update dashboard consistent coloring"
        )
    return no_update


@callback(
    Output(RESPONSIVE_GRID_LAYOUT_CONTAINER, "children", allow_duplicate=True),
    Output(DASHBOARD_LAST_UPDATED, "children"),
    Output(DASHBOARD_INFO, "children", allow_duplicate=True),
    Output({"type": DASHBOARD_FILTER_LAST_STATE, "index": "index"}, "children"),
    Input(DASHBOARD_REFRESH_BUTTON, "n_clicks"),
    Input(DASHBOARD_USE_CONSISTENT_COLORING, "checked"),
    State({"type": TWC_DASHBOARD, "index": TWC.CUSTOM_DATE_PICKER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.SINCE_DATE_PICKER}, "value"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_OPERATOR_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": ALL}, "value"),
    State(MITZU_LOCATION, "pathname"),
    running=[
        (
            Output(RESPONSIVE_GRID_LAYOUT_CONTAINER, "className"),
            "loading-dashboard",
            "loaded-dashboard",
        ),
        (
            Output(DASHBOARD_REFRESH_CANCEL_BUTTON, "className"),
            "d-inline-block",
            "d-none",
        ),
        (
            Output(DASHBOARD_REFRESH_BUTTON, "className"),
            "d-none",
            "d-inline-block",
        ),
        (
            Output(DASHBOARD_SPINNER, "value"),
            0,
            0,
        ),
    ],
    progress=Output(DASHBOARD_SPINNER, "value"),
    cancel=[
        Input(DASHBOARD_REFRESH_CANCEL_BUTTON, "n_clicks"),
        Input(MANAGE_DASHBOARD_LOCATION, "pathname"),
    ],
    background=True,
    interval=500,
    prevent_initial_call=True,
)
def refresh_dashboards(
    set_progress: Callable[[float], None],
    refresh_button_clicks: Optional[int],
    use_consistent_coloring: Optional[bool],
    custom_dates: List[str],
    lookback_days: str,
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    properties: List[Optional[str]],
    operators: List[str],
    values: List[str],
    pathname: str,
) -> Tuple[bc.Component, bc.Component, List[bc.Component], str]:
    set_progress(1)
    try:
        path = PathParser(pathname)
        project_id = path.project_id
        dashboard_id = path.dashboard_id
    except P.PathException as exc:
        # the user may have already navigated to a different page
        LOGGER.opt(exception=exc).warning("failed to parse url")
        raise PreventUpdate()

    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)

    dfilter = get_dashboard_filter_from_inputs(
        properties,
        operators,
        values,
        triggered_property_index=None,
        time_config=DH.get_time_window_config(
            DH.parse_custom_dates(custom_dates),
            DH.parse_time_window(lookback_days),
            None,
            WM.CalendarDateRange(calendar_date_range) if calendar_date_range else None,
            since_date=parse_datetime_input(since_date, def_val=None)
            if since_date
            else None,
        ),
    )

    if not deps.get_subscription_service().is_allowed_to_run_queries():
        return (
            no_update,
            no_update,
            trial_expired_alert(project_id, deps.user_role),
            no_update,
        )
    dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()
    dashboard_defs = project_storage.get_definitions_for_dashboard(dashboard, dfilter)
    if len(dashboard_defs.event_definitions) == 0:
        raise PreventUpdate()

    chart_errors: List[tuple[str, str]] = []
    if (
        not dashboard.is_stale()
        and ctx.triggered_id != DASHBOARD_REFRESH_BUTTON
        and ctx.triggered_id != DASHBOARD_USE_CONSISTENT_COLORING
    ):
        return no_update, no_update, no_update, no_update

    try:

        metric_context = DH.get_metric_context_for_dashboard(
            dashboard, deps.get_catalog_service(dashboard_defs.project.id)
        )
        if ctx.triggered_id != DASHBOARD_USE_CONSISTENT_COLORING:
            dashboard, chart_errors, metric_context = refresh_dashboard(
                dashboard,
                dashboard_defs,
                deps,
                set_progress,
                pathname,
                project_storage,
                metric_context,
                deps.get_events_service(project_id),
                dfilter,
            )
        else:
            chart_errors = []

        if use_consistent_coloring is not None:
            dashboard = replace(
                dashboard, use_consistent_coloring=use_consistent_coloring
            )

        annotations = project_storage.list_annotations()
        metric_updater = MetricUpdater(
            DH.get_metric_context_for_dashboard(
                dashboard,
                deps.get_catalog_service(project_id),
            ),
            project_storage.get_project().insight_settings,
        )
        insight_results = get_insight_result_for_dashboard(
            project_storage,
            metric_updater,
            dashboard,
            dfilter,
        )
        grid_layout = create_responsive_grid_layout(
            dashboard,
            False,
            MetricConverter(dashboard_defs, project_storage, metric_updater),
            metric_context,
            deps.is_user_read_only,
            annotations,
            is_shared=False,
            default_end_dt=dashboard_defs.project.get_default_end_dt(),
            insight_results=insight_results,
            dashboard_has_filters=len(dfilter.filters) > 0,
        )
        set_progress(0)

        return (
            grid_layout,
            get_last_updated(dashboard),
            create_chart_errors_alerts(chart_errors),
            SE.dashboard_filter_to_string(dfilter),
        )
    except Exception as exc2:
        log_exception(exc2, "failed to refresh dashboard", CURRENT_CHART_VERSION)
        return (
            no_update,
            no_update,
            [
                create_dashboard_error_alert(
                    exc=exc2, title="Failed to refresh the dashboard"
                )
            ],
            no_update,
        )


@callback(
    Output(DASHBOARD_INFO, "children", allow_duplicate=True),
    Input(DC.SAVE_DEFAULT_FILTER_BUTTON, "n_clicks"),
    State({"type": TWC_DASHBOARD, "index": TWC.CUSTOM_DATE_PICKER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER}, "value"),
    State({"type": TWC_DASHBOARD, "index": TWC.SINCE_DATE_PICKER}, "value"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_OPERATOR_TYPE, "index": ALL}, "value"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": ALL}, "value"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_save_default_filter(
    n_clicks: Optional[int],
    custom_dates: List[str],
    lookback_days: str,
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    properties: List[Optional[str]],
    operators: List[str],
    values: List[List[Any]],
    pathname: str,
) -> dmc.Alert:
    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        project_storage = deps.get_project_storage(project_id)
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        dashboard = project_storage.get_dashboard_storage(dashboard_id).get_dashboard()
        dfilter = get_dashboard_filter_from_inputs(
            properties,
            operators,
            values,
            None,
            time_config=DH.get_time_window_config(
                DH.parse_custom_dates(custom_dates),
                DH.parse_time_window(lookback_days),
                None,
                WM.CalendarDateRange(calendar_date_range)
                if calendar_date_range
                else None,
                since_date=parse_datetime_input(since_date, def_val=None)
                if since_date
                else None,
            ),
        )
        dashboard = replace(
            dashboard,
            last_updated_at=datetime.now(),
            default_filter=dfilter if not dfilter.is_empty else None,
        )
        project_storage.set_dashboard(dashboard)

        return no_update
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update default dashboard filter")
        return create_dashboard_error_alert(
            exc, title="Failed to update the default filter"
        )


def create_chart_errors_alerts(chart_errors: List[Tuple[str, str]]) -> bc.Component:
    return [
        dmc.Alert(
            desc,
            color="red",
            title=title,
            withCloseButton=True,
            className="mb-3",
        )
        for (desc, title) in chart_errors
    ]


clientside_callback(
    """function(val) {
        return 'dark';
    }""",
    Output(DASHBOARD_REFRESH_BUTTON, "color", allow_duplicate=True),
    Input(DASHBOARD_REFRESH_BUTTON, "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output(DASH_ORDER_INPUT, "children"),
    Input(DASH_ORDER_INPUT, "children"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def manage_chart_reorder(
    chart_indices: List[str],
    pathname: str,
) -> List[bc.Component]:
    if not chart_indices:
        return no_update
    dashboard_id = P.get_path_value(P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID)
    project_id = P.get_path_value(
        P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
    )
    deps = DEPS.Dependencies.get()
    project_storage = deps.get_project_storage(project_id)
    dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

    chart_order = [
        json.loads(chart)["index"]
        for chart in chart_indices
        if chart != DC.ADD_INSIGHT_CARD_ID
    ]
    dashboard_storage.reorder_dashbaord_metrics(chart_order)
    return no_update


clientside_callback(
    """function(n_clicks, n_clicks_2) {
        if (n_clicks >0 || n_clicks_2 >0) {
            return true;
        } else {
            return false;
        }
    }
    """,
    Output(SHARE_DASHBOARD_MODAL, "is_open", allow_duplicate=True),
    Input(SHARING_OPTIONS_BUTTON, "n_clicks"),
    Input(SHARED_BUTTON_DASHBOARD, "n_clicks"),
    prevent_initial_call=True,
)

clientside_callback(
    """
        function(n_clicks) {
            return false;
        }
    """,
    Output(SHARE_DASHBOARD_MODAL, "is_open"),
    Input(SHARE_MODAL_CLOSE, "n_clicks"),
)
