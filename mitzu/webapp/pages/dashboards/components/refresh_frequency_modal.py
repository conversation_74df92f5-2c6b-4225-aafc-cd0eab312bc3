from typing import List, Tuple

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    html,
    no_update,
)

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIK<PERSON>
from mitzu.logger import LOGGER
from mitzu.webapp.dependencies import Dependencies
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.pages.dashboards.constants import (
    AUTO_REFRESH_SETTINGS_INDICATOR,
)

REFRESH_FREQUENCY_MODAL = "refresh_frequency_modal"
REFRESH_FREQUENCY_MODAL_CLOSE = "refresh_frequency_modal_close"
REFRESH_FREQUENCY_MODAL_ACCEPT = "refresh_frequency_modal_accept"
REFRESH_FREQUENCY_MODAL_LOCATION = "refresh_frequency_modal_location"
REFRESH_FREQUENCY_STEP = "refresh_frequency_step"
REFRESH_FREQUENCY_TIME_GROUP = "refresh_frequency_time_group"
ENABLE_AUTO_REFRESH_CHECKBOX = "enable_auto_refresh_checkbox"
REFRESH_TIME_GROUP_AND_STEP = "refresh_time_group_and_step"
REFRESH_MODAL_ERROR_CONTAINER = "refresh_modal_error_container"


def refresh_frequency_input(value: int, id: str) -> dmc.NumberInput:
    return dmc.NumberInput(
        id=id,
        className="me-1 mb-1",
        value=value,
        debounce=300,
        hideControls=False,
        min=1,
        max=1000,
        step=1,
        precision=0,
        size="xs",
    )


def refresh_time_group_selector(
    options: List[str], default: str, id: str
) -> dmc.Select:
    return dmc.Select(
        id=id,
        clearable=False,
        size="xs",
        data=options,
        className="w-100",
        variant="default",
        value=default,
    )


def refresh_frequency_modal(dashboard: WM.Dashboard) -> dbc.Modal:
    return UIKIT.create_modal(
        id=REFRESH_FREQUENCY_MODAL,
        title="Auto refresh settings",
        content=[
            dmc.Stack(
                [
                    html.Div(children=[], id=REFRESH_MODAL_ERROR_CONTAINER),
                    dmc.Text(
                        [
                            "If enabled, this dashboard will automatically refresh when the dashboards page is opened.",
                            html.Br(),
                            html.Br(),
                            "Note: Dashboards won't refresh in the background without opening the page.",
                        ],
                        size="sm",
                    ),
                    html.Hr(),
                    dmc.Checkbox(
                        label="Enable auto refresh",
                        id=ENABLE_AUTO_REFRESH_CHECKBOX,
                        color="dark",
                        checked=dashboard.auto_refresh_time_window is not None,
                    ),
                    html.Div(
                        dmc.Grid(
                            children=[
                                dmc.Col(
                                    dmc.Text("Refresh every: ", size="md"),
                                    span=3,
                                ),
                                dmc.Col(
                                    refresh_frequency_input(
                                        dashboard.auto_refresh_time_window.value
                                        if dashboard.auto_refresh_time_window
                                        is not None
                                        else 1,
                                        REFRESH_FREQUENCY_STEP,
                                    ),
                                    span=3,
                                ),
                                dmc.Col(
                                    refresh_time_group_selector(
                                        options=[
                                            tg.name.lower().title()
                                            for tg in [
                                                M.TimeGroup.HOUR,
                                                M.TimeGroup.DAY,
                                                M.TimeGroup.WEEK,
                                                M.TimeGroup.MONTH,
                                            ]
                                        ],
                                        default=dashboard.auto_refresh_time_window.period.name.lower().title()
                                        if dashboard.auto_refresh_time_window
                                        is not None
                                        else M.TimeGroup.DAY.name.lower().title(),
                                        id=REFRESH_FREQUENCY_TIME_GROUP,
                                    ),
                                    span="auto",
                                ),
                            ],
                        ),
                        id=REFRESH_TIME_GROUP_AND_STEP,
                        className="d-inline-block"
                        if dashboard.auto_refresh_time_window is not None
                        else "d-none",
                    ),
                ],
                spacing="xs",
            ),
        ],
        modal_primary_button=UIKIT.ModalButton(
            REFRESH_FREQUENCY_MODAL_ACCEPT,
            "Confirm",
            "mdi:save",
        ),
        close_button_id=REFRESH_FREQUENCY_MODAL_CLOSE,
        callback_nav_id=REFRESH_FREQUENCY_MODAL_LOCATION,
        config=UIKIT.CONFIG_MODAL,
    )


@callback(
    Output(REFRESH_MODAL_ERROR_CONTAINER, "children"),
    Output(AUTO_REFRESH_SETTINGS_INDICATOR, "className"),
    Output(AUTO_REFRESH_SETTINGS_INDICATOR, "children"),
    Input(REFRESH_FREQUENCY_MODAL_ACCEPT, "n_clicks"),
    State(REFRESH_FREQUENCY_TIME_GROUP, "value"),
    State(REFRESH_FREQUENCY_STEP, "value"),
    State(ENABLE_AUTO_REFRESH_CHECKBOX, "checked"),
    State(MITZU_LOCATION, "pathname"),
    prevent_initial_call=True,
)
def handle_refresh_frequency_change(
    refresh_frequency_modal_accept: int,
    refresh_frequency_time_group: str,
    refresh_frequency_step: int,
    checked: bool,
    pathname: str,
) -> Tuple[bc.Component, bc.Component, bc.Component]:
    try:
        dashboard_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.DASHBOARD_ID
        )
        project_id = P.get_path_value(
            P.DASHBOARDS_EDIT_PATH, pathname, P.PROJECT_ID_PATH_PART
        )
        deps = Dependencies.get()
        project_storage = deps.get_project_storage(project_id)
        dashboard_storage = project_storage.get_dashboard_storage(dashboard_id)

        time_group = M.TimeGroup.parse(refresh_frequency_time_group)
        time_window = (
            M.TimeWindow(period=time_group, value=refresh_frequency_step)
            if checked
            else None
        )

        dashboard_storage.set_auto_refresh_time_window(time_window)

        refresh_settings_as_text = f"Refreshed every {str(time_window)}"
        auto_refresh_settings_button_display = (
            "d-inline-block" if time_window is not None else "d-none"
        )
    except Exception as e:
        LOGGER.error(f"Error while setting auto refresh: {str(e)[:300]}")
        return (
            dmc.Alert(
                children=dmc.Text("Error while setting auto refresh", size="sm"),
                color="red",
                title="Error while setting auto refresh",
                className="mt-3",
                variant="light",
            ),
            no_update,
            no_update,
        )
    return no_update, auto_refresh_settings_button_display, refresh_settings_as_text


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="toggle_auto_refresh"),
    Output(REFRESH_TIME_GROUP_AND_STEP, "className", allow_duplicate=True),
    Input(ENABLE_AUTO_REFRESH_CHECKBOX, "checked"),
    prevent_initial_call=True,
)
