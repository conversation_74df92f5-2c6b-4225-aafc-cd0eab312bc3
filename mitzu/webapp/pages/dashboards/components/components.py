from dataclasses import dataclass, replace
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import dcc, html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.visualization.common as VC
import mitzu.webapp.helper as WH
import mitzu.webapp.metric_context as MC
import mitzu.webapp.model as WM
import mitzu.webapp.pages.dashboards.constants as DC
import mitzu.webapp.pages.paths as P
from mitzu import helper as H
from mitzu.visualization import plot as PLT
from mitzu.webapp.constants import DASHBOARD_DEF_HEIGHT
from mitzu.webapp.helper import (
    get_error_message,
    log_exception,
)
from mitzu.webapp.pages import uikit
from mitzu.webapp.pages.dashboards.constants import (
    ADD_ASSET_CHIP_GROUP,
    ADD_DIVIDER_TO_DASHBOARD_BUTTON,
    ADD_EXISTING_INSIGHT_BUTTON,
    ADD_EXISTING_INSIGHT_MODAL,
    ADD_EXISTING_INSIGHT_MODAL_BODY,
    ADD_EXISTING_INSIGHT_MODAL_ERROR,
    ADD_INSIGHT_CARD_ID,
    ADD_NEW_INSIGHT_BUTTON,
    ADD_TEXT_TO_DASHBOARD_BUTTON,
    AUTO_REFRESH_SETTINGS_INDICATOR,
    CLOSE_ADD_EXISTING_INSIGHT_MODAL,
    CONFIRM_ADD_EXISTING_INSIGHT_BUTTON,
    CREATE_NEW_INSIGHT_CONTAINER,
    CREATE_SNAPSHOT_BUTTON,
    DASHBOARD_AUTO_REFRESH,
    DASHBOARD_DESC_INPUT,
    DASHBOARD_GRID_ITEM,
    DASHBOARD_HORIZONTAL_DIVIDER,
    DASHBOARD_NAME_INPUT,
    DASHBOARD_SHOW_ANNOTATIONS,
    DASHBOARD_TEXT_AREA,
    DASHBOARD_USE_CONSISTENT_COLORING,
    DELETE_DASHBOARD_BUTTON,
    DELETE_SAVED_METRICS_TYPE,
    DT_TEXT_ID,
    DUPLICATE_DASHBOARD_BUTTON,
    EDIT_BUTTON_STATE,
    MARKDOWN_CONTAINER,
    METRIC_CARD_DROPDOWN_MENU,
    METRIC_CARD_STACK,
    NO_ASSETS_INFO_CONTAINER,
    RAW_TEXT_BUTTON,
    RESIZE_BUTTON_TYPE,
    RESPONSIVE_GRID_LAYOUT,
    SAVE_DEFAULT_FILTER_BUTTON,
    SHARING_OPTIONS_BUTTON,
    SHOW_ONLY_MY_INSIGHTS,
)
from mitzu.webapp.pages.explore.constants import (
    GRAPH_ID_TYPE,
)
from mitzu.webapp.pages.explore.graph_legend import (
    create_graph_legend,
)
from mitzu.webapp.service.metric_converter import (
    MetricConverter,
)
from mitzu.webapp.service.metric_updater import (
    CannotApplyDashboardFilter,
)

MARKDOWN_PLACEHOLDER = """Add your markdown text here

# H1 title

## H2 title

### H3 title

- bullet list item
- bullet list item

1. numbered list item
2. numbered list item
"""


@dataclass(frozen=True)
class DashboardMetricCardModel:
    dashboard_metric_id: str
    name: str
    description: Optional[str]
    edit_href: str
    is_valid: bool
    is_user_read_only: bool
    is_loading: bool
    is_shared: bool
    width: int
    datetime_text: str
    component: bc.Component
    is_text: bool
    card_type: Optional[WM.DashboardItemType]


def get_title_and_description_inputs_row(
    dashboard: WM.Dashboard,
    is_user_read_only: bool,
) -> bc.Component:
    return dmc.Group(
        [
            html.Div(
                [
                    dmc.TextInput(
                        type="text",
                        id=DASHBOARD_NAME_INPUT,
                        placeholder="+ Add Name",
                        value=dashboard.name,
                        required=True,
                        className="name-input   rounded-2",
                        debounce=500,
                        size="lg",
                        variant="light",
                        disabled=is_user_read_only,
                    ),
                    dmc.TextInput(
                        type="text",
                        id=DASHBOARD_DESC_INPUT,
                        placeholder="+ Add Description",
                        value=dashboard.description,
                        className="name-input  rounded-2",
                        debounce=1000,
                        size="sm",
                        variant="light",
                        disabled=is_user_read_only,
                    ),
                ],
                className="mb-1",
                style={
                    "width": "100%",
                    "display": "flex",
                    "flexDirection": "column",
                    "minWidth": "150px",
                    "flexGrow": 1,
                },
            ),
            dmc.Group(
                children=[
                    uikit.add_help_tooltip(
                        component=dmc.Button(
                            "Published",
                            leftIcon=DashIconify(icon="weui:eyes-on-outlined"),
                            color="gray",
                            size="xs",
                            variant="subtle",
                            id=DC.SHARED_BUTTON_DASHBOARD,
                            className="d-inline-block"
                            if dashboard.shared
                            else "d-none",
                        ),
                        label="This dashboard is publicly shared",
                        classname="d-inline-block",
                    ),
                    dmc.Button(
                        f"Refreshed every {str(dashboard.auto_refresh_time_window)}"
                        if dashboard.auto_refresh_time_window is not None
                        else "",
                        id=AUTO_REFRESH_SETTINGS_INDICATOR,
                        className="d-none d-lg-block"
                        if dashboard.auto_refresh_time_window is not None
                        else "d-none",
                        color="gray",
                        size="xs",
                        variant="subtle",
                        leftIcon=DashIconify(icon="ic:baseline-autorenew"),
                    ),
                    dmc.Menu(
                        [
                            dmc.MenuTarget(
                                uikit.icon_button(
                                    icon="mdi:dots-vertical",
                                    size="md",
                                    variant="subtle",
                                ),
                            ),
                            dmc.MenuDropdown(
                                [
                                    uikit.menu_item(
                                        "Auto refresh",
                                        icon="ic:baseline-autorenew",
                                        id=DASHBOARD_AUTO_REFRESH,
                                    ),
                                    dmc.Divider(),
                                    uikit.menu_item_with_chip(
                                        "Global annotations",
                                        icon="mynaui:flag",
                                        id=DASHBOARD_SHOW_ANNOTATIONS,
                                        checked=dashboard.show_annotations,
                                        class_name="first-event-filter",
                                    ),
                                    uikit.menu_item_with_chip(
                                        "Consistent colors",
                                        icon="fluent:color-24-regular",
                                        id=DASHBOARD_USE_CONSISTENT_COLORING,
                                        checked=dashboard.use_consistent_coloring,
                                        class_name="first-event-filter",
                                    ),
                                    uikit.menu_item(
                                        "Publishing",
                                        icon="weui:eyes-on-outlined",
                                        id=SHARING_OPTIONS_BUTTON,
                                    ),
                                    uikit.menu_item(
                                        "Duplicate dashboard",
                                        icon="weui:photo-wall-outlined",
                                        id=DUPLICATE_DASHBOARD_BUTTON,
                                    ),
                                    uikit.menu_item_with_tooltip(
                                        "Create snapshot",
                                        "Create a copy of the dashboard with the current time range",
                                        icon="weui:camera-outlined",
                                        id=CREATE_SNAPSHOT_BUTTON,
                                    ),
                                    uikit.menu_item(
                                        "Update default filter",
                                        icon="mynaui:filter",
                                        id=SAVE_DEFAULT_FILTER_BUTTON,
                                    ),
                                    dmc.Divider(),
                                    uikit.menu_item(
                                        "Delete dashboard",
                                        icon="mynaui:trash",
                                        color="red",
                                        id=DELETE_DASHBOARD_BUTTON,
                                    ),
                                ],
                            ),
                        ],
                        id="COHORT_MORE_MENU",
                        shadow="md",
                    ),
                ],
                spacing="xs",
                noWrap=True,
            ),
        ],
        className="w-100 text-left",
        position="apart",
        align="start",
        noWrap=True,
    )


def get_title_and_description_row(dashboard: WM.Dashboard) -> dbc.Row:
    return dbc.Row(
        [
            dbc.Col(
                [
                    dmc.Text(
                        dashboard.name,
                        className="d-inline-block w-100",
                        size="lg",
                    ),
                ],
                md=4,
                sm=12,
                className="mb-1",
            ),
            dbc.Col(
                [
                    dmc.Text(
                        dashboard.description,
                        className="d-inline-block w-100",
                        size="lg",
                    ),
                ],
                md=4,
                sm=12,
                className="mb-1 flex-grow-1",
            ),
        ]
    )


def get_last_updated(dashboard: WM.Dashboard) -> bc.Component:
    curr_dt = datetime.now()
    warn_refresh = abs(curr_dt - dashboard.last_updated_at) > timedelta(days=1)

    last_refreshed_text = [
        "Refreshed "
        + (
            WH.approx_datetime_diff_to_str(curr_dt, dashboard.last_updated_at) + " ago"
            if dashboard.last_updated_at
            else "never"
        ),
    ]
    return dmc.Text(
        children=last_refreshed_text,
        color="red" if warn_refresh else "dark",
        className="fw-normal mx-1",
        size="xs",
    )


def create_dashboard_horizontal_divider_container(
    dashboard_metric: WM.DashboardHorizontalDividerItem, is_shared: bool = False
) -> dmc.TextInput:
    return dmc.TextInput(
        id={
            "type": DASHBOARD_HORIZONTAL_DIVIDER,
            "index": dashboard_metric.id,
        },
        placeholder="Add a divider text here",
        debounce=500,
        value=dashboard_metric.text,
        size="lg",
        className="dashboard-divider"
        if not is_shared
        else "dashboard-divider read-only",
    )


def create_responsive_grid_layout(
    dashboard: WM.Dashboard,
    loading: bool,
    metric_converter: Optional[MetricConverter],
    metric_context: MC.MetricContext,
    is_user_read_only: bool,
    annotations: List[VC.Annotation],
    is_shared: bool,
    default_end_dt: datetime,
    insight_results: Dict[str, Union[VC.SimpleChart, Exception]],
) -> dbc.Row:
    card_models: List[DashboardMetricCardModel] = []
    dms = dashboard.dashboard_metrics.copy()
    for dm in sorted(
        dms, key=lambda dm: dm.y * DASHBOARD_DEF_HEIGHT + dm.x
    ):  # this ordering makes it backwards compatible
        card_models.append(
            create_dashboard_metric_card_model(
                dm,
                loading,
                metric_converter,
                metric_context,
                is_user_read_only,
                annotations,
                is_shared,
                default_end_dt,
                dashboard.use_consistent_coloring,
                insight_result=insight_results.get(dm.saved_metric.id)
                if isinstance(dm, WM.DashboardMetricItem)
                else None,
            )
        )

    return dbc.Row(
        id=RESPONSIVE_GRID_LAYOUT
        if not is_shared
        else "",  # disable all callbacks like ordering
        children=[add_existing_insight_modal()]
        + [render_dashboard_metric_card(card_model) for card_model in card_models]
        + (
            [create_dashboard_add_insight_card(dashboard.project_id)]
            if not is_shared
            else []
        ),
        className="",
    )


def create_dashboard_metric_card_model(
    dm: WM.DashboardItem,
    loading: bool,
    metric_converter: Optional[MetricConverter],
    metric_context: MC.MetricContext,
    is_user_read_only: bool,
    annotations: List[VC.Annotation],
    is_shared: bool,
    default_end_dt: datetime,
    use_consistent_coloring: bool,
    insight_result: Union[None, VC.SimpleChart, Exception],
) -> DashboardMetricCardModel:
    is_text = False
    invalid = False
    datetime_text = ""
    edit_href = ""
    name = ""
    desc = ""
    dataframe_version = VC.CURRENT_CHART_VERSION
    try:
        if isinstance(dm, WM.DashboardHorizontalDividerItem):
            component = create_dashboard_horizontal_divider_container(
                dm, is_shared=is_shared
            )
            is_text = True

        elif isinstance(dm, WM.DashboardTextItem):
            component = create_dashboard_text_container(dm, is_shared=is_shared)
            is_text = True
        elif isinstance(dm, WM.DashboardMetricItem):
            name = dm.saved_metric.name
            path = P.create_path(
                P.PROJECTS_EXPLORE_PATH, project_id=dm.saved_metric.project_id
            )
            edit_href = f"{path}?sm={dm.saved_metric.id}"
            webapp_metric = dm.saved_metric.metric()

            if use_consistent_coloring:
                webapp_metric = replace(
                    webapp_metric,
                    config=replace(
                        webapp_metric.config,
                        use_consistent_coloring=use_consistent_coloring,
                    ),
                )

            if loading is False and metric_converter is not None:
                if insight_result is None:
                    component = create_saved_metric_not_cached()
                elif isinstance(insight_result, Exception):
                    raise insight_result
                else:
                    metric = metric_converter.convert_metric(webapp_metric)
                    dataframe_version = insight_result.dataframe_version
                    component = create_card_from_dashboard_metric(
                        metric,
                        metric_context,
                        annotations,
                        is_shared,
                        dm.saved_metric.id,
                        webapp_metric,
                        default_end_dt,
                        insight_result,
                    )
                    if (
                        webapp_metric.config
                        and webapp_metric.config.calendar_date_range
                    ):
                        datetime_text = (
                            f"{webapp_metric.config.calendar_date_range.to_string()},"
                            f" {metric.config.time_group.group_by_string()}"
                        )
                    else:
                        datetime_text = (
                            f"{H.get_metric_timewindow_as_str(metric, default_end_dt)}, "
                            f"{metric.config.time_group.group_by_string()}"
                        )
                    desc = dm.saved_metric.description

            else:
                component = create_empty_card()
                datetime_text = ""
                desc = dm.saved_metric.description
        else:
            raise ValueError("Invalid card type")
    except Exception as exc:
        log_exception(exc, "failed to create dashboard metric card", dataframe_version)
        invalid = True
        component = create_saved_metric_error_card(
            edit_href, exc, is_user_read_only, dataframe_version
        )

    return DashboardMetricCardModel(
        dashboard_metric_id=dm.id,
        name=name,
        description=desc,
        edit_href=edit_href,
        is_valid=not invalid,
        is_user_read_only=is_user_read_only,
        is_loading=loading,
        is_shared=is_shared,
        datetime_text=datetime_text,
        component=component,
        width=dm.width,
        is_text=is_text,
        card_type=dm.get_card_type(),
    )


def create_resize_button(
    card: DashboardMetricCardModel, label: str, size: str, icon: str
) -> Optional[dmc.MenuItem]:
    return (
        uikit.menu_item(
            label,
            icon=icon,
            id={
                "type": RESIZE_BUTTON_TYPE,
                "index": card.dashboard_metric_id,
                "size": size,
            },
            disabled=card.is_user_read_only,
        )
        if card.card_type != WM.DashboardItemType.HORIZONTAL_DIVIDER
        else None
    )


def render_dashboard_metric_card(card: DashboardMetricCardModel) -> dbc.Col:
    return dbc.Col(
        children=dmc.Stack(
            id=METRIC_CARD_STACK,
            spacing="xs",
            children=[
                html.Div(
                    children=[html.B(className="bi bi-three-dots drag-handler")],
                    className="justify-content-center d-flex drag-handler",
                )
                if not card.is_shared
                and card.card_type != WM.DashboardItemType.HORIZONTAL_DIVIDER
                else None,
                dmc.Group(
                    [
                        html.Div(
                            children=[
                                html.B(className="bi bi-three-dots drag-handler")
                            ],
                            className="justify-content-center d-flex flex-grow-1 drag-handler rounded",
                        )
                        if not card.is_shared
                        and card.card_type == WM.DashboardItemType.HORIZONTAL_DIVIDER
                        else None,
                        dcc.Link(
                            [
                                dmc.Text(
                                    [
                                        html.B(
                                            className="bi bi-box-arrow-up-right me-1"
                                        ),
                                        card.name,
                                    ],
                                    size="lg",
                                    className="saved-metric-link",
                                    color="dark",
                                ),
                            ],
                            href=card.edit_href,
                            target="_blank" if card.is_shared else None,
                            className="text-truncate",
                        )
                        if not card.is_text
                        else None,
                        html.Div(style={"flexGrow": 1})
                        if card.card_type != WM.DashboardItemType.HORIZONTAL_DIVIDER
                        or card.is_shared
                        else None,
                        dmc.Loader(
                            size="sm", color="gray", className="dashboard-card-spinner"
                        )
                        if not card.is_text
                        else None,
                        dmc.ActionIcon(
                            DashIconify(icon="bi:pencil-square", width=20),
                            size="xs",
                            color="dark",
                            id={
                                "type": RAW_TEXT_BUTTON,
                                "index": card.dashboard_metric_id,
                            },
                            className="d-none"
                            if not card.card_type == WM.DashboardItemType.TEXT
                            else "",
                        ),
                        dcc.Store(
                            id={
                                "type": EDIT_BUTTON_STATE,
                                "index": card.dashboard_metric_id,
                            },
                            data=False,
                        ),
                        dmc.Menu(
                            className="d-none" if card.is_shared else "",
                            children=[
                                dmc.MenuTarget(
                                    dmc.ActionIcon(
                                        DashIconify(
                                            icon="heroicons-solid:dots-vertical"
                                        ),
                                        size="md",
                                        variant="subtle",
                                        color="gray",
                                        className="text-dark",
                                    ),
                                ),
                                dmc.MenuDropdown(
                                    [
                                        uikit.menu_item(
                                            "Explore",
                                            icon="mdi:arrow-right",
                                            href=card.edit_href,
                                            disabled=not card.is_valid,
                                            target="_self"
                                            if not card.is_shared
                                            else "_blank",
                                        )
                                        if not card.is_text
                                        else None,
                                        dmc.MenuDivider() if not card.is_text else None,
                                    ]
                                    + (
                                        [
                                            create_resize_button(
                                                card,
                                                "SM size",
                                                "4",
                                                "fluent:layout-column-one-third-left-16-regular",
                                            ),
                                            create_resize_button(
                                                card,
                                                "MD size",
                                                "6",
                                                "fluent:layout-column-two-16-regular",
                                            ),
                                            create_resize_button(
                                                card,
                                                "LG size",
                                                "8",
                                                "fluent:layout-column-one-third-right-16-regular",
                                            ),
                                            create_resize_button(
                                                card,
                                                "XL size",
                                                "12",
                                                "fluent:rectangle-landscape-16-regular",
                                            ),
                                            dmc.MenuDivider()
                                            if card.card_type
                                            != WM.DashboardItemType.HORIZONTAL_DIVIDER
                                            else None,
                                            uikit.menu_item(
                                                "Remove",
                                                icon="tabler:trash",
                                                color="red",
                                                id={
                                                    "type": DELETE_SAVED_METRICS_TYPE,
                                                    "index": card.dashboard_metric_id,
                                                },
                                                disabled=card.is_user_read_only,
                                            ),
                                        ]
                                        if not card.is_shared
                                        else []
                                    ),
                                    id=METRIC_CARD_DROPDOWN_MENU,
                                ),
                            ],
                            position="bottom-start",
                            shadow="md",
                        ),
                    ],
                    noWrap=True,
                    className="ps-3 pe-1 w-100 d-flex flex-nowrap"
                    + ("border-bottom" if not card.is_shared else ""),
                    style={"minHeight": "30px"},
                ),
                html.Div(
                    [
                        html.Div(
                            children=[
                                dmc.Text(
                                    card.datetime_text
                                    + (
                                        " | " + card.description
                                        if card.description
                                        else ""
                                    ),
                                    className="text-truncate",
                                    size="sm",
                                    color="gray",
                                    id=DT_TEXT_ID,
                                ),
                            ],
                            title=card.description,
                            className="px-3",
                        ),
                        html.Div(children=card.component, className="graph p-1 mb-1"),
                    ],
                    style={"height": "460px"}
                    if card.card_type != WM.DashboardItemType.HORIZONTAL_DIVIDER
                    else {"height": "auto"},
                    className="pb-1",
                ),
            ],
            className="w-100 d-inline-block shadow-sm bg-white rounded border",
        ),
        id={"type": DASHBOARD_GRID_ITEM, "index": card.dashboard_metric_id},
        className="p-2",
        xl=card.width,
        xs=12,
    )


def create_dashboard_add_insight_card(project_id: str) -> dbc.Col:

    component = dmc.Container(
        [
            dmc.Stack(
                children=[
                    dcc.Link(
                        [
                            dmc.Button(
                                "Add new insight",
                                leftIcon=[
                                    DashIconify(icon="mdi:plus", width=20),
                                ],
                                variant="subtle",
                                color="indigo",
                                style={"width": "200px"},
                                id=ADD_NEW_INSIGHT_BUTTON,
                                className="text-start text-dark",
                            )
                        ],
                        href=P.create_path(
                            P.PROJECTS_EXPLORE_PATH,
                            project_id=project_id,
                        ),
                    ),
                    dmc.Button(
                        "Add existing insight",
                        leftIcon=[
                            DashIconify(icon="ic:baseline-addchart", width=20),
                        ],
                        color="indigo",
                        variant="subtle",
                        style={"width": "200px"},
                        id=ADD_EXISTING_INSIGHT_BUTTON,
                        className="text-start text-dark",
                    ),
                    dmc.Button(
                        "Add text",
                        leftIcon=[
                            DashIconify(icon="fluent:text-t-24-filled", width=18),
                        ],
                        variant="subtle",
                        color="indigo",
                        style={"width": "200px"},
                        id=ADD_TEXT_TO_DASHBOARD_BUTTON,
                        className="text-start text-dark",
                    ),
                    dmc.Button(
                        "Add divider",
                        leftIcon=[
                            DashIconify(icon="mdi:minus", width=18),
                        ],
                        variant="subtle",
                        color="indigo",
                        style={"width": "200px"},
                        id=ADD_DIVIDER_TO_DASHBOARD_BUTTON,
                        className="text-start text-dark",
                    ),
                ],
                spacing="xs",
                align="center",
            ),
        ],
        id=CREATE_NEW_INSIGHT_CONTAINER,
        className="text-center",
    )

    return dbc.Col(
        children=dmc.Stack(
            children=[
                html.Div(children=component),
            ],
            justify="center",
            align="stretch",
            className="w-100 border rounded bg-white shadow-sm",
            style={"minHeight": "511px"},
        ),
        className="p-2",
        xl=4,
        xs=12,
        id=ADD_INSIGHT_CARD_ID,
    )


def create_saved_metric_error_card(
    edit_href: str,
    exc: Exception,
    is_user_read_only: bool,
    dataframe_version: int,
) -> dmc.Stack:
    if isinstance(exc, CannotApplyDashboardFilter):
        title = "Insight cannot be filtered"
        message = get_error_message(exc)
        color = "yellow"
        show_button = False
    elif dataframe_version != VC.CURRENT_CHART_VERSION:
        title = "Cache is outdated"
        message = "Cached saved insight result is outdated. Refresh the dashboard!"
        show_button = False
        color = "yellow"
    else:
        title = "This chart is broken"
        message = get_error_message(exc)
        show_button = True
        color = "red"

    return dmc.Stack(
        [
            dmc.ThemeIcon(
                DashIconify(icon="mynaui:danger", height=30),
                variant="filled",
                color=color,
                radius=uikit.DEF_BUTTON_RADIUS,
                size="xl",
            ),
            dmc.Text(title, size="md", className="fw-bold"),
            dmc.Text(message, size="sm", className="text-center ms-3 me-3"),
            dcc.Link(
                [dmc.Button("Fix the issue", variant="outline", color="dark")],
                href=edit_href,
            )
            if not is_user_read_only and show_button
            else None,
        ],
        align="center",
        justify="center",
        className="m-5",
    )


def create_saved_metric_not_cached() -> dmc.Stack:
    return dmc.Stack(
        [
            dmc.ThemeIcon(
                DashIconify(icon="mynaui:danger", height=30),
                variant="filled",
                color="yellow",
                radius=uikit.DEF_BUTTON_RADIUS,
                size="xl",
            ),
            dmc.Text(
                "Results are missing or not up to date.", size="md", className="fw-bold"
            ),
            dmc.Text(
                "Click the Refresh button to show the insight.",
                size="sm",
                className="text-center ms-3 me-3",
            ),
        ],
        align="center",
        justify="center",
        className="m-5",
    )


def create_empty_card() -> html.Div:
    return html.Div(
        dmc.Skeleton(height="100%", className="skeleton"),
        style={"height": "100%", "opacity": 0.5},
        className="p-3",
    )


def create_card_from_dashboard_metric(
    metric: M.Metric,
    metric_context: MC.MetricContext,
    annotations: List[VC.Annotation],
    is_shared: bool,
    saved_metric_id: str,
    webapp_metric: WM.WebappMetric,
    default_end_dt: datetime,
    insight_result: VC.SimpleChart,
) -> html.Div:
    fig, _ = PLT.plot_chart(
        insight_result,
        metric,
        metric_context,
        annotations,
        webapp_metric,
        default_end_dt,
    )
    legend = create_graph_legend(insight_result, webapp_metric)
    fig.update_layout(height=400)
    return html.Div(
        [
            dcc.Graph(
                id={
                    "type": GRAPH_ID_TYPE,
                    "index": saved_metric_id,
                    "shared": str(is_shared),
                },
                className=f"w-100 chart-type-{insight_result.chart_type.name.lower()}",
                figure=fig,
                config={"displayModeBar": False},
                clear_on_unhover=True,
            ),
            html.Div(legend, className="px-3"),
        ],
        # sometimes dash fails to update the dcc.Graph classnames
        className=f"w-100 chart-type-{insight_result.chart_type.name.lower()}",
    )


def create_dashboard_text_container(
    dashboard_metric: WM.DashboardTextItem, is_shared: bool = False
) -> html.Div:
    is_dashboard_metric_empty = dashboard_metric.text and not str.isspace(
        dashboard_metric.text
    )
    return html.Div(
        children=[
            dmc.Textarea(
                id={
                    "type": DASHBOARD_TEXT_AREA,
                    "index": dashboard_metric.id,
                },
                autosize=True,
                placeholder=MARKDOWN_PLACEHOLDER,
                debounce=500,
                value=dashboard_metric.text,
                maxRows=14,
                minRows=14,
                size="lg",
                className="text-box" if not is_shared else "text-box read-only",
                style={"display": "none"},
            ),
            dcc.Markdown(
                dashboard_metric.text
                if is_dashboard_metric_empty
                else "Click on the edit button to add your text",
                id={
                    "type": MARKDOWN_CONTAINER,
                    "index": dashboard_metric.id,
                },
                style={
                    "max-height": "400px",
                    "overflow-y": "auto",
                    "padding": "10px",
                    "white-space": "pre-wrap",
                    "font-size": "18px",
                    "opacity": "0.5 " if not is_dashboard_metric_empty else "1",
                },
            ),
        ]
    )


def select_insight_component(
    saved_metrics: List[WM.SavedMetricInfo],
) -> html.Div:
    select_dashboards = dmc.Container(
        [
            dmc.Text("Select Insights", size="md"),
            html.Hr(),
            html.Div(
                [dmc.Text("Nothing to show.", size="md")],
                className="mt-2 " + "" if len(saved_metrics) == 0 else "d-none",
                id=NO_ASSETS_INFO_CONTAINER,
            ),
            dmc.ChipGroup(
                [
                    dmc.Chip(
                        sm.name,
                        value=sm.id,
                        variant="outline",
                        className="text-truncate",
                    )
                    for sm in saved_metrics
                ],
                id=ADD_ASSET_CHIP_GROUP,
                multiple=False,
                className="d-grid justify-content-stretch",
                style={
                    "overflowY": "auto",
                    "maxHeight": "400px",
                    "gap": "5px",
                    "justifyContent": "stretch",
                },
            ),
        ],
        className="p-3 adt-container",
        style={
            "width": "auto",
            "justify-content": "stretch",
        },
    )

    return select_dashboards


def add_existing_insight_modal() -> dbc.Modal:
    return uikit.create_modal(
        id=ADD_EXISTING_INSIGHT_MODAL,
        title="Add insight to dashboards",
        content=[
            html.Div(
                [
                    select_insight_component([]),
                ],
                id=ADD_EXISTING_INSIGHT_MODAL_BODY,
                className="mb-3",
            ),
            html.Div(
                children=[
                    dmc.Checkbox(
                        id=SHOW_ONLY_MY_INSIGHTS,
                        label="Show only my insights",
                        checked=True,
                        color="dark",
                    )
                ],
                className="mb-2",
            ),
            html.Div(
                [],
                className="mb-2",
                id=ADD_EXISTING_INSIGHT_MODAL_ERROR,
            ),
        ],
        modal_primary_button=uikit.ModalButton(
            CONFIRM_ADD_EXISTING_INSIGHT_BUTTON,
            "Confirm",
            "mdi:check",
        ),
        close_button_id=CLOSE_ADD_EXISTING_INSIGHT_MODAL,
        config=uikit.CONFIG_MODAL,
    )
