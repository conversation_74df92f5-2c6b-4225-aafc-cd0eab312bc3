import dash_bootstrap_components as dbc
import dash_mantine_components as dmc

import mitzu.webapp.pages.uikit as UIKIT

SNAPSHOT_DASHBOARD_NAME_INPUT = "snapshot_dashboard_name_input"
SNAPSHOT_DASHBOARD_MODAL_CLOSE = "snapshot_dashboard_modal_close"
SNAPSHOT_DASHBOARD_MODAL_BUTTON = "snapshot_dashboard_modal_button"
SNAPSHOT_DASHBOARD_MODAL = "snapshot_dashboard_modal"


def snapshot_dashboard_modal() -> dbc.Modal:
    return UIKIT.create_modal(
        id=SNAPSHOT_DASHBOARD_MODAL,
        title="Create snapshot",
        content=dmc.Group(
            children=[
                dmc.TextInput(
                    type="text",
                    id=SNAPSHOT_DASHBOARD_NAME_INPUT,
                    className="mb-3",
                    debounce=500,
                    size="md",
                    label="Dashboard name",
                ),
            ],
            grow=True,
            position="center",
        ),
        modal_primary_button=UIKIT.ModalButton(
            SNAPSHOT_DASHBOARD_MODAL_BUTTON, "Create snapshot", "weui:camera-outlined"
        ),
        close_button_id=SNAPSHOT_DASHBOARD_MODAL_CLOSE,
        config=UIKIT.CONFIG_MODAL,
    )
