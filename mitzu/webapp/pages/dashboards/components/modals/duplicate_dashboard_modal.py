import dash.development.base_component as bc
import dash_mantine_components as dmc

import mitzu.webapp.pages.uikit as UIKIT

DUPLICATE_DASHBOARD_MODAL = "duplicate_dashboard_modal"
DUPLICATE_DASHBOARD_MODAL_DUPLICATE_BUTTON = (
    "duplicate_dashboard_modal_duplicate_button"
)
DUPLICATE_DASHBOARD_MODAL_CLOSE = "duplicate_dashboard_modal_close"
DUPLICATE_DASHBOARD_WITH_INSIGHTS_CHECKBOX = (
    "duplicate_dashboard_with_insights_checkbox"
)

DUPLICATE_DASHBOARD_NAME_INPUT = "duplicate_dashboard_name_input"


def duplicate_dashboard_modal() -> bc.Component:
    return UIKIT.create_modal(
        id=DUPLICATE_DASHBOARD_MODAL,
        title="Duplicate dashboard",
        content=dmc.Stack(
            spacing="md",
            children=[
                dmc.TextInput(
                    type="text",
                    id=DUPLICATE_DASHBOARD_NAME_INPUT,
                    className="mb-3",
                    debounce=500,
                    size="md",
                    label="Dashboard name",
                ),
                dmc.Group(
                    children=[
                        dmc.Checkbox(
                            label="Also duplicate insights",
                            id=DUPLICATE_DASHBOARD_WITH_INSIGHTS_CHECKBOX,
                            color="dark",
                        ),
                    ],
                    grow=True,
                    position="center",
                ),
            ],
        ),
        modal_primary_button=UIKIT.ModalButton(
            DUPLICATE_DASHBOARD_MODAL_DUPLICATE_BUTTON,
            "Duplicate",
            "weui:photo-wall-outlined",
        ),
        close_button_id=DUPLICATE_DASHBOARD_MODAL_CLOSE,
        config=UIKIT.CONFIG_MODAL,
    )
