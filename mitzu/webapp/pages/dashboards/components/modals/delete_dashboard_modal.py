import dash.development.base_component as bc

import mitzu.webapp.pages.uikit as UIKIT

DELETE_DASHBOARD_MODAL = "delete_dashboard_modal"
DELETE_DASHBOARD_MODAL_ACCEPT = "delete_dashboard_modal_accept"
DELETE_DASHBOARD_MODAL_CLOSE = "delete_dashboard_modal_close"


def delete_dashboard_modal() -> bc.Component:
    return UIKIT.create_modal(
        id=DELETE_DASHBOARD_MODAL,
        title="Do you really want to delete this dashboard?",
        content=None,
        modal_primary_button=UIKIT.ModalButton.delete(DELETE_DASHBOARD_MODAL_ACCEPT),
        close_button_id=DELETE_DASHBOARD_MODAL_CLOSE,
        config=UIKIT.CONFIRM_MODAL,
    )
