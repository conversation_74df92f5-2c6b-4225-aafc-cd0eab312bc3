from typing import Any, Callable, Dict, Optional, Tuple

import dash.development.base_component as bc
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    Input,
    Output,
    State,
    callback,
    dcc,
    html,
    no_update,
    register_page,
)

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.dashboards.manage_dashboards_component as MDC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.service.catalog_service as CS
import mitzu.webapp.service.input_generator as IG
import mitzu.webapp.service.metric_converter as MC
import mitzu.webapp.service.metric_updater as MU
import mitzu.webapp.service.org_manager as OM
import mitzu.webapp.storage.project_storage as PS
from mitzu.logger import LOGGER
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.model import DashboardFilter, Role
from mitzu.webapp.pages.dashboards.constants import (
    DASHBOARD_SHOW_ANNOTATIONS,
)
from mitzu.webapp.pages.dashboards.helper import (
    get_metric_context_for_dashboard,
)
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.errors import FeedbackType
from mitzu.webapp.service.dashboard_service import (
    get_insight_result_for_dashboard,
    refresh_dashboard,
)
from mitzu.webapp.service.events_service import (
    EventsService,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)
from mitzu.webapp.storage.project_discovery_storage import (
    ProjectDiscoveryStorage,
)

DASHBOARD_LOCATION = "dashboard_location"
SHARED_DASHBOARD_PAGE_LOADED_DATA_STORE = "shared_dashboard_page_loaded data store"
SHARED_DASHBOARD_CONTAINER_WRAPPER = "shared_dashboard_container_wrapper"


def dashboard_not_found() -> html.Div:
    return html.Div(
        [
            dmc.Image(
                src=FeedbackType.get_image_path(FeedbackType.INVALID_STATE),
                style={
                    "width": "240px",
                    "height": "200px",
                    "margin": "auto",
                },
            ),
            dmc.Alert(
                "The requested public dashboard does not exist.",
                title="Public dashboard not found",
                color="yellow",
                className="mt-3",
            ),
        ],
        style={
            "maxWidth": "350px",
            "margin": "auto",
            "paddingTop": "40px",
        },
    )


@fallback_to_error_page("Dashboard")
def layout(dashboard_id: Optional[str], **query_params: str) -> bc.Component:
    embed = query_params.get("embed") is not None
    if dashboard_id is None:
        LOGGER.bind(dashboard_id=dashboard_id, embed=embed).warning(
            "shared dashboard not found"
        )
        return dashboard_not_found()
    dependencies = DEPS.Dependencies.get()
    try:
        org_manager = OM.OrgManager.get()
        if org_manager.is_dashboard_shared(dashboard_id):
            dashboard_storage = org_manager.get_dashboard_stoage(dashboard_id)
            dashboard = dashboard_storage.get_dashboard()

            org_id = org_manager.get_dashboard_org_id(dashboard_id)

            project_storage = PS.ProjectScopedStorage(
                org_id=org_id,
                project_id=dashboard.project_id,
                session_manager=dashboard_storage._session_manager,
                read_only=True,
            )
            project = project_storage.get_project()

            catalog_service = CS.CatalogService(project_storage, dependencies.cache)
            metric_context = get_metric_context_for_dashboard(
                dashboard,
                catalog_service,
            )
            metric_updater = MU.MetricUpdater(metric_context, project.insight_settings)
            insight_results = get_insight_result_for_dashboard(
                project_storage,
                metric_updater,
                dashboard,
                dashboard.default_filter or DashboardFilter.empty(),
            )
            input_generator = IG.InputGenerator(
                project.id, metric_context, project_storage
            )
        else:
            LOGGER.bind(dashboard_id=dashboard_id, embed=embed).warning(
                "shared dashboard not found"
            )
            return dashboard_not_found()

    except Exception as exc:
        LOGGER.opt(exception=exc).bind(dashboard_id=dashboard_id, embed=embed).warning(
            "shared dashboard not found"
        )
        return dashboard_not_found()

    dependencies.tracking_service.track_shared_dashboard_opened(dashboard, org_id)

    return html.Div(
        [
            dcc.Store(
                id=SHARED_DASHBOARD_PAGE_LOADED_DATA_STORE, data={"page_loaded": True}
            ),
            NB.get_navbar_component(
                project_id=None,
                hide_workspaces_button=True,
                hide_navbar=embed,
            ),
            dmc.Chip(
                "",
                id=DASHBOARD_SHOW_ANNOTATIONS,
                checked=dashboard.show_annotations,
                className="d-none",
            ),
            dbc.Container(
                children=[
                    MDC.create_shared_dashboard_container(
                        dashboard=dashboard,
                        metric_context=metric_context,
                        metric_converter=None,
                        annotations=project_storage.list_annotations()
                        if dashboard.show_annotations
                        else [],
                        default_end_dt=project.get_default_end_dt(),
                        insight_results=insight_results,
                        input_generator=input_generator,
                    ),
                ],
                fluid=embed,
                id=SHARED_DASHBOARD_CONTAINER_WRAPPER,
            ),
        ]
    )


register_page(
    __name__,
    path_template=P.SHARED_DASHBOARD_PATH,
    title="Mitzu - Dashboard",
    layout=layout,
)


@callback(
    Output(SHARED_DASHBOARD_CONTAINER_WRAPPER, "children"),
    Output(MDC.RESPONSIVE_GRID_LAYOUT_CONTAINER, "className", allow_duplicate=True),
    Input(SHARED_DASHBOARD_PAGE_LOADED_DATA_STORE, "data"),
    State(MITZU_LOCATION, "pathname"),
    background=True,
    progress=Output(MDC.DASHBOARD_SPINNER, "value"),
    interval=200,
    prevent_initial_call="initial_duplicate",
)
def handle_shared_dashboard_page_loaded(
    set_progress: Callable[[float], None], data: Dict[str, Any], pathname: str
) -> Tuple[bc.Component, str]:
    try:
        set_progress(1)
        dependencies = DEPS.Dependencies.get()
        dashboard_id = P.get_path_value(
            P.SHARED_DASHBOARD_PATH, pathname, P.DASHBOARD_ID
        )
        org_manager = OM.OrgManager.get()
        if not org_manager.is_dashboard_shared(dashboard_id):
            return no_update, "loaded-dashboard"

        dashboard_storage = org_manager.get_dashboard_stoage(dashboard_id)
        dashboard = dashboard_storage.get_dashboard()
        project_storage = PS.ProjectScopedStorage(
            org_id="shared-dashboard",  # won't be used, can be anything
            project_id=dashboard.project_id,
            session_manager=dashboard_storage._session_manager,
            read_only=False,
        )
        dashboard_defs = project_storage.get_definitions_for_dashboard(
            dashboard, DashboardFilter.empty()
        )

        catalog_service = CS.CatalogService(project_storage, dependencies.cache)
        metric_context = get_metric_context_for_dashboard(
            dashboard,
            catalog_service,
        )
        metric_updater = MetricUpdater(
            metric_context,
            project_storage.get_project().insight_settings,
        )

        metric_converter = MC.MetricConverter(
            dashboard_defs, project_storage, metric_updater
        )
        dfilter = dashboard.default_filter or DashboardFilter.empty()

        if dashboard.is_stale():
            project_discovery_storage = ProjectDiscoveryStorage(
                "shared-dashboard",
                dashboard.project_id,
                dashboard_storage._session_manager,
            )
            events_service = EventsService(
                project_discovery_storage,
                dependencies.cache,
                dependencies.tracking_service,
                Role.READ_ONLY,
            )
            dashboard, _, metric_context = refresh_dashboard(
                dashboard,
                dashboard_defs=dashboard_defs,
                deps=dependencies,
                set_progress=set_progress,
                pathname=P.create_path(
                    P.SHARED_DASHBOARD_PATH,
                    dashboard_id=dashboard_id,
                ),
                project_storage=project_storage,
                metric_context=metric_context,
                events_service=events_service,
                dfilter=dfilter,
            )

        insight_results = get_insight_result_for_dashboard(
            project_storage, metric_updater, dashboard, dfilter
        )

        input_generator = IG.InputGenerator(
            dashboard.project_id, metric_context, project_storage
        )

        return (
            MDC.create_shared_dashboard_container(
                dashboard=dashboard,
                metric_context=metric_context,
                metric_converter=metric_converter,
                annotations=project_storage.list_annotations()
                if dashboard.show_annotations
                else [],
                default_end_dt=dashboard_defs.project.get_default_end_dt(),
                insight_results=insight_results,
                input_generator=input_generator,
            ),
            "loaded-dashboard",
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to refresh shared dashboard")
        return no_update, "loaded-dashboard"
