from __future__ import annotations

from typing import Any, List, Optional, Set, Tuple

import dash_mantine_components as dmc
from dash import (
    ALL,
    MATCH,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    ctx,
    no_update,
)
from dash.exceptions import PreventUpdate

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.pages.dashboards.helper as DH
import mitzu.webapp.pages.explore.time_window_chooser as TWC
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.serialization as SE
import mitzu.webapp.service.input_generator as IG
from mitzu.helper import parse_datetime_input
from mitzu.logger import LOGGER
from mitzu.webapp.helper import MITZU_LOCATION
from mitzu.webapp.pages.dashboards.constants import (
    DASHBOARD_REFRESH_BUTTON,
    MC_OVERWRITE_COMP,
    TWC_DASHBOARD,
)
from mitzu.webapp.pages.dashboards.helper import (
    get_metric_context_for_dashboard,
)
from mitzu.webapp.pages.dimensions.constants import (
    DEF_FILTER_VALUE_CLASS,
)
from mitzu.webapp.service.input_generator import (
    InputGenerator,
)
from mitzu.webapp.service.path_parser import PathParser

DASHBOARD_FILTER_CONTAINER = "dashboard-filter-container"
DASHBOARD_FILTER_PROPERTY_TYPE = "dashboard-filter-property-type"
DASHBOARD_FILTER_OPERATOR_TYPE = "dashboard-filter-operator-type"
DASHBOARD_FILTER_VALUE_TYPE = "dashboard-filter-value-type"
DASHBOARD_FILTER_READ_ONLY_INDEX = "dfilter_readonly"


def get_dashboard_filter_from_inputs(
    properties: List[Optional[str]],
    operators: List[str],
    values: List[Any],
    triggered_property_index: Optional[int],
    time_config: Optional[WM.TimeWindowChooserConfig],
) -> WM.DashboardFilter:
    filters = []
    for index, property in enumerate(properties):
        if property is None:
            continue

        property_path = M.FieldPath.parse(property)
        if isinstance(property_path, M.CollectionFieldPath):
            default_operator = M.Operator.IN_COHORT
        else:
            default_operator = M.Operator.ANY_OF

        operator = (
            M.Operator(operators[index])
            if len(operators) > index and index != triggered_property_index
            else default_operator
        )
        value = (
            values[index]
            if len(values) > index and index != triggered_property_index
            else None
        )

        if operator.supports_multiple_operands():
            if not isinstance(value, list):
                if value is None:
                    value = []
                else:
                    value = [value]
        elif operator.is_unary_operator():
            value = None
        else:
            if isinstance(value, list):
                if len(value) == 1:
                    value = value[0]
                else:
                    value = None

        if operator in [
            M.Operator.IN_COHORT,
            M.Operator.NOT_IN_COHORT,
        ] and isinstance(value, str):
            value = WM.CohortReference(value)
        filters.append(WM.WebappEventFilter(property_path, operator, value))

    return WM.DashboardFilter(filters, time_config)


def render_dashboard_filter_rows(
    dfilter: WM.DashboardFilter,
    input_generator: InputGenerator,
    dashboard_events: Set[M.EventNamePath],
    read_only: bool = False,
) -> List[dmc.Group]:
    input_rows = []

    for index, filter in enumerate(dfilter.filters):
        dropdown_component = input_generator.create_property_dropdown_for_global_filter(
            selected=filter.left,
            placeholder="+ Filter",
            event_name_paths=dashboard_events,
        )

        input_rows.append(
            UIKIT.render_property_filter_row(
                property_id={
                    "type": DASHBOARD_FILTER_READ_ONLY_INDEX
                    if read_only
                    else DASHBOARD_FILTER_PROPERTY_TYPE,
                    "index": str(index),
                },
                property_dropdown=dropdown_component,
                filter=filter,
                input_generator=input_generator,
                operator_id={
                    "type": DASHBOARD_FILTER_READ_ONLY_INDEX
                    if read_only
                    else DASHBOARD_FILTER_OPERATOR_TYPE,
                    "index": str(index),
                },
                value_id={
                    "type": DASHBOARD_FILTER_READ_ONLY_INDEX
                    if read_only
                    else DASHBOARD_FILTER_VALUE_TYPE,
                    "index": str(index),
                },
                read_only=read_only,
                allow_reindex=False,
            )
        )

    if not read_only:
        property_filter_dropdown = (
            input_generator.create_property_dropdown_for_global_filter(
                selected=None,
                placeholder="+ Filter" if len(dfilter.filters) == 0 else "+ And",
                event_name_paths=dashboard_events,
            )
        )
        input_rows.append(
            UIKIT.render_property_filter_placeholder_row(
                {
                    "type": DASHBOARD_FILTER_PROPERTY_TYPE,
                    "index": str(len(dfilter.filters)),
                },
                property_filter_dropdown,
            )
        )
    return input_rows


@callback(
    Output(DASHBOARD_FILTER_CONTAINER, "children"),
    Output(MC_OVERWRITE_COMP, "children"),
    Output(DASHBOARD_REFRESH_BUTTON, "color", allow_duplicate=True),
    Output(MITZU_LOCATION, "search", allow_duplicate=True),
    Input({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": ALL}, "value"),
    Input({"type": DASHBOARD_FILTER_OPERATOR_TYPE, "index": ALL}, "value"),
    Input({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": ALL}, "value"),
    Input({"type": TWC_DASHBOARD, "index": TWC.CUSTOM_DATE_PICKER}, "value"),
    Input({"type": TWC_DASHBOARD, "index": TWC.LOOKBACK_WINDOW_DROPDOWN}, "value"),
    Input({"type": TWC_DASHBOARD, "index": TWC.CALENDAR_DATE_RANGE_CHOOSER}, "value"),
    Input({"type": TWC_DASHBOARD, "index": TWC.SINCE_DATE_PICKER}, "value"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_property_filter_change(
    properties: List[Optional[str]],
    operators: List[str],
    values: List[List[Any]],
    custom_dates: List[str],
    lookback_days: Optional[str],
    calendar_date_range: Optional[str],
    since_date: Optional[str],
    href: str,
) -> Tuple[List[dmc.Group], dmc.Menu, str, str]:
    try:
        path = PathParser(href)
        dfilter_query_param = path.get_query_value(P.DASHBOARD_FILTER_QUERY)
        encoded_dfilter = (
            SE.dashboard_filter_from_string(dfilter_query_param)
            if dfilter_query_param
            else WM.DashboardFilter.empty()
        )
        deps = DEPS.Dependencies.get()
        project_storage = deps.get_project_storage(path.project_id)
        project = project_storage.get_project()

        dashboard = project_storage.get_dashboard_storage(
            path.dashboard_id
        ).get_dashboard()
        if encoded_dfilter.empty() and dashboard.default_filter is not None:
            encoded_dfilter = dashboard.default_filter

        triggered_property_index: Optional[int] = None
        if (
            isinstance(ctx.triggered_id, dict)
            and ctx.triggered_id["type"] == DASHBOARD_FILTER_PROPERTY_TYPE
        ):
            index = int(ctx.triggered_id["index"])
            # do not reset the index when it's the same property field
            if (
                index >= len(encoded_dfilter.filters)
                or str(encoded_dfilter.filters[index].left) != properties[index]
            ):
                triggered_property_index = index

        if ctx.triggered_id["index"] == TWC.LOOKBACK_WINDOW_DROPDOWN:
            custom_dates = []
            calendar_date_range = None
            since_date = None

        elif ctx.triggered_id["index"] == TWC.CUSTOM_DATE_PICKER:
            lookback_days = None
            calendar_date_range = None
            since_date = None

        elif ctx.triggered_id["index"] == TWC.CALENDAR_DATE_RANGE_CHOOSER:
            custom_dates = []
            lookback_days = None
            since_date = None
        elif ctx.triggered_id["index"] == TWC.SINCE_DATE_PICKER:
            custom_dates = []
            lookback_days = None
            calendar_date_range = None

        time_config = DH.get_time_window_config(
            DH.parse_custom_dates(custom_dates),
            DH.parse_time_window(lookback_days),
            M.TimeGroup.TOTAL,
            calendar_date_range=WM.CalendarDateRange(calendar_date_range)
            if calendar_date_range is not None
            else None,
            since_date=parse_datetime_input(since_date, def_val=None)
            if since_date
            else None,
        )

        dfilter = get_dashboard_filter_from_inputs(
            properties, operators, values, triggered_property_index, time_config
        )
        metric_context = get_metric_context_for_dashboard(
            dashboard, deps.get_catalog_service(path.project_id)
        )
        input_generator = IG.InputGenerator(
            path.project_id, metric_context, project_storage
        )
        return (
            render_dashboard_filter_rows(
                dfilter, input_generator, dashboard.get_all_event_name_paths()
            ),
            TWC.create_timewindow_chooser(
                time_config,
                id_type=TWC_DASHBOARD,
                insight_settings=project.insight_settings,
                include_default=True,
                popup_position="bottom-start",
            ),
            "dark" if dfilter.is_empty else "indigo",
            f"?{P.DASHBOARD_FILTER_QUERY}={SE.dashboard_filter_to_string(dfilter)}"
            if not dfilter.is_empty
            else "",
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update dashboard filter inputs")
        raise PreventUpdate() from exc


clientside_callback(
    ClientsideFunction(
        namespace="clientside", function_name="set_custom_event_property_value"
    ),
    Output(
        {"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH},
        "data",
        allow_duplicate=True,
    ),
    Input({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "value"),
    Input({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "searchValue"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "data"),
    State({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "value"),
    prevent_initial_call=True,
)


@callback(
    Output({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "data"),
    Output({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "className"),
    Output({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "placeholder"),
    Input({"type": DASHBOARD_FILTER_VALUE_TYPE, "index": MATCH}, "value"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": MATCH}, "value"),
    State(MITZU_LOCATION, "href"),
    background=True,
    interval=100,
    prevent_initial_call=True,
)
def property_value_data_update(
    value: List[Any],
    field_path_str: Optional[str],
    href: str,
) -> Tuple[List, str, str]:
    if value is None or field_path_str is None:
        return no_update, DEF_FILTER_VALUE_CLASS, no_update
    try:
        search_values = [
            val.replace(IG.CUSTOM_LOOKUP_PROP_VALUE, "")
            for val in value
            if val.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
        if len(search_values) != 1:
            return no_update, DEF_FILTER_VALUE_CLASS, no_update

        # Getting rid of the custom lookup options
        value = [
            val for val in value if not val.startswith(IG.CUSTOM_LOOKUP_PROP_VALUE)
        ]
        path = PathParser(href)
        project_id = path.project_id
        deps = DEPS.Dependencies.get()
        field_path = M.FieldPath.parse(field_path_str)
        discovery_defs = deps.get_project_storage(
            project_id
        ).get_definitions_for_field_discovery([field_path])
        context = deps.create_adapter_context(
            href,
            discovery_defs.project,
            GA.QueryType.INDEXING,
        )
        options = deps.get_events_service(project_id).index_property_values_with_search(
            value=value,
            field_path=field_path,
            search_values=search_values,
            adapter_context=context,
            discovery_defs=discovery_defs,
            indexing_context=discovery_defs.project.get_default_indexing_context(),
        )
        placeholder = IG.generate_value_input_placeholder([o["label"] for o in options])
        return options, DEF_FILTER_VALUE_CLASS, placeholder
    except Exception as exc:
        LOGGER.opt(exception=exc).error(
            "Failed to discover event properties on the explore page."
        )
        return no_update, DEF_FILTER_VALUE_CLASS, no_update


@callback(
    Output(
        {"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": MATCH},
        "options",
        allow_duplicate=True,
    ),
    Input({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": MATCH}, "search_value"),
    State({"type": DASHBOARD_FILTER_PROPERTY_TYPE, "index": MATCH}, "value"),
    State(MITZU_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_property_search(
    search: Optional[str],
    selected_str: Optional[str],
    href: str,
) -> List:
    try:
        path = PathParser(href)
        deps = DEPS.Dependencies.get()
        project_id = path.project_id
        project_storage = deps.get_project_storage(project_id)

        dashboard = project_storage.get_dashboard_storage(
            path.dashboard_id
        ).get_dashboard()
        metric_context = get_metric_context_for_dashboard(
            dashboard, deps.get_catalog_service(path.project_id)
        )
        input_generator = IG.InputGenerator(
            path.project_id, metric_context, project_storage
        )
        events = dashboard.get_all_event_name_paths()
        options = input_generator.get_property_options_for_dashboard_filter(
            events,
            include=M.FieldPath.parse(selected_str)
            if selected_str is not None
            else None,
            search=search,
        )
        return [UIKIT.render_dcc_dropdown_option(opt) for opt in options]
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to handle dasboard property search")
        return no_update
