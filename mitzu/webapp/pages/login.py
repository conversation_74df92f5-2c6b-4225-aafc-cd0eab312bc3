import os
from typing import List, Optional, Union

import boto3
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from boto3 import client as boto3_client
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    register_page,
)
from dash_iconify import DashIconify

import mitzu.configs as configs
import mitzu.webapp.auth.cloud_authorizer as CA
import mitzu.webapp.pages.paths as P
import mitzu.webapp.service.org_manager as OM
from mitzu.logger import LOGGER
from mitzu.webapp.auth.auth_config_manager import (
    AuthConfigManager,
)
from mitzu.webapp.auth.model import UnauthorizedReason
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.uikit import DEF_BUTTON_RADIUS
from scripts.export_cognito_users import REGION

LOCATION = "login_location"
ENTERPRISE_LOGIN_BUTTON = "login_enterprise_button"
EMAIL_ADDRESS = "email_address_input"
EMAIL_ADDRESS_INFO = "email_address_info"

GRADIENT_STYLE = {
    "backgroundImage": "linear-gradient(to right, #fb4b6e, #f57480)",
}

PASSWORD_INPUT = "password_input"
LOGIN_BUTTON = "login_button"
LOGIN_ERROR_CONTAINER = "login_error_container"

# browser tests provide a way better coverage on this than unit tests
@fallback_to_error_page("Login")
def layout(**query_params: str) -> dbc.Container:  # pragma: no cover
    cloud_authorizer = CA.CloudAuthorizer.get()
    request_context = cloud_authorizer.get_request_context()

    auth_config_manager = AuthConfigManager.create(
        cloud_authorizer.default_oauth,
        cloud_authorizer._org_manager._cloud_storage,
        cloud_authorizer._cache,
    )

    error_message = None
    error_code = query_params.get("errcode")
    if error_code == UnauthorizedReason.SESSION_EXPIRED.value:
        error_message = "Your session has been expired. Log in again."

    org_name: Optional[str] = None
    if request_context.last_org_id is None:
        idp_id: Optional[str] = None
    else:
        idp_id = auth_config_manager.get_idp_for_org_id(request_context.last_org_id)
        org_manager = OM.OrgManager.get()
        org_name = org_manager.get_org_name(request_context.last_org_id)

    return dbc.Container(
        children=[
            dcc.Location(id=LOCATION, refresh=True),
            dmc.Grid(
                children=[
                    dmc.Col(
                        [
                            dmc.Stack(
                                [
                                    html.Img(
                                        src=configs.WHITE_LOGO_PATH,
                                        style={"width": "150px"},
                                    ),
                                    dmc.Text(
                                        "Log in to your account",
                                        size="xl",
                                        weight=500,
                                        color="white",
                                    ),
                                ],
                                align="center",
                                justify="center",
                                spacing="xl",
                                className="vh-100",
                            ),
                        ],
                        span=3,
                        className="mitzu-left-panel d-none d-sm-block",
                    ),
                    dmc.Col(
                        [] + create_login_card(idp_id, org_name, error_message),
                        span="auto",
                        className="mobile-padding sm-bg-melon",
                    ),
                ],
                gutter="xl",
            ),
        ],
        className="m-0",
        fluid=True,
    )


def create_login_card(
    idp_id: Optional[str],
    last_org_name: Optional[str],
    error_message: Optional[str],
) -> List[dmc.Stack]:
    children = [
        html.Div(id=LOGIN_ERROR_CONTAINER),
        dmc.Text(
            "You are logged out",
            size="md",
            transform="uppercase",
            weight=300,
            className="mb-2 text-center",
        ),
        get_cognito_login_buttons()
        if idp_id is None
        else get_enterprise_login_button(idp_id, last_org_name),
    ]

    if idp_id is None:
        children.extend(
            [
                html.Hr(),
            ]
            + get_enterprise_login_form()
        )
    else:
        children.append(
            dcc.Link(
                dmc.Text(
                    "Log in with basic authentication"
                    if last_org_name is None
                    else "Log in with a different account",
                    size="xs",
                    color="gray",
                    weight=300,
                    className="mb-2 text-center text-decoration-underline",
                ),
                href=configs.HOME_URL + P.SIGN_OUT_URL,
                refresh=True,
            ),
        )

    return [
        dmc.Stack(
            [
                dmc.Alert(
                    error_message,
                    title="Permission denied",
                    color="yellow",
                    style={
                        "maxWidth": "407px",
                    },
                )
                if error_message
                else None,
                dmc.Card(
                    children=[
                        dmc.Container(children),
                    ],
                    withBorder=True,
                    shadow="sm",
                    radius=DEF_BUTTON_RADIUS,
                    style={
                        "width": "auto",
                        "maxWidth": "407px",
                    },
                ),
            ],
            align="center",
            justify="center",
            spacing="xl",
            className="vh-100",
        ),
    ]


def get_login_with_email_component():
    return dmc.Stack(
        [
            dmc.Text(
                "Log in with email",
                size="xs",
                color="gray",
                weight=300,
                className="mb-2 text-center",
            ),
            dmc.TextInput(
                placeholder="email",
                id=EMAIL_ADDRESS,
                style={
                    "width": "300px",
                },
                className="w-100",
            ),
            dmc.TextInput(
                placeholder="password",
                id=PASSWORD_INPUT,
                type="password",
                style={
                    "width": "300px",
                },
                className="w-100",
            ),
            dmc.Button(
                "Log in",
                id=LOGIN_BUTTON,
                color="indigo",
                variant="filled",
                size="sm",
                fullWidth=True,
                style={
                    "borderColor": "rgb(206, 212, 218)",
                },
            ),
        ],
        className="" if os.getenv("ENVIRONMENT", "local") else "d-none",
    )


def get_cognito_login_buttons() -> dmc.Stack:
    return dmc.Stack(
        [
            get_login_with_email_component(),
            dmc.Group(
                dcc.Link(
                    dmc.Button(
                        "Log in with email",
                        leftIcon=DashIconify(icon="mdi:email-outline"),
                        color="dark",
                        size="sm",
                        fullWidth=True,
                    ),
                    href=P.REDIRECT_TO_LOGIN_URL,
                    refresh=True,
                ),
                position="apart",
                grow=True,
                className="d-none" if os.getenv("ENVIRONMENT", "local") else "",
            ),
            dmc.Group(
                dcc.Link(
                    dmc.Button(
                        "Log in with Google",
                        leftIcon=DashIconify(icon="logos:google-icon"),
                        variant="outline",
                        color="dark",
                        size="sm",
                        fullWidth=True,
                        style={
                            "borderColor": "rgb(206, 212, 218)",
                        },
                    ),
                    href=P.REDIRECT_TO_GOOGLE_LOGIN_URL,
                    refresh=True,
                ),
                position="apart",
                grow=True,
            ),
        ],
        mb="xs",
    )


def get_enterprise_login_button(idp_id: str, last_org_name: Optional[str]) -> dmc.Stack:
    return dmc.Stack(
        [
            dmc.Text(["Last time you were logged in to ", html.Strong(last_org_name)]),
            dmc.Group(
                dcc.Link(
                    dmc.Button(
                        "Enterprise login" if last_org_name is None else "Log back in",
                        leftIcon=DashIconify(icon="mdi:email-outline"),
                        color="dark",
                        mt="xs",
                        size="sm",
                        fullWidth=True,
                        style=GRADIENT_STYLE,
                    ),
                    href=P.create_path(P.REDIRECT_TO_LOGIN_WITH_IDP_URL, idp_id=idp_id),
                    refresh=True,
                ),
                position="apart",
                grow=True,
                mb="xs",
            ),
        ],
        spacing="xs",
    )


def get_enterprise_login_form() -> List[Union[dmc.Text, dmc.Stack]]:
    return [
        dmc.Text(
            "Enterprise single sign-on",
            size="xs",
            color="gray",
            weight=300,
            className="mb-2 text-center ",
        ),
        dmc.Stack(
            [
                dmc.TextInput(
                    placeholder="Company email",
                    id=EMAIL_ADDRESS,
                    style={
                        "width": "300px",
                    },
                    className="w-100",
                ),
                dmc.Text(
                    "",
                    size="xs",
                    color="red",
                    weight=300,
                    className="text-center",
                    id=EMAIL_ADDRESS_INFO,
                    style={"display": "none"},
                ),
                dmc.Button(
                    "Log in with company SSO",
                    leftIcon=DashIconify(icon="mdi:cloud-outline"),
                    id=ENTERPRISE_LOGIN_BUTTON,
                    color="dark",
                    size="sm",
                    fullWidth=True,
                    style=GRADIENT_STYLE,
                ),
                dmc.Text(
                    [
                        "Enterprise single sign-on must be enabled for your organisation.",
                        html.Br(),
                        " Contact Mitzu support for help.",
                    ],
                    size="xs",
                    color="gray",
                    className="text-center w-100",
                ),
            ],
            mb="xs",
        ),
    ]


# user is not authorized at this point avoid calling the backend
clientside_callback(
    f"""
    async function(enterprise_login_clicks, email, css) {{
        error = ['Invalid email address or not configured with SSO', {{}}];
        if (email === '') {{
            return error;
        }} else {{
            const response = await fetch('{P.ENTERPRISE_LOGIN}', {{
                method: 'POST',
                headers: {{
                'Accept': 'application/json',
                'Content-Type': 'application/json'
                }},
                body: JSON.stringify({{"email": email}})
            }});
            if (response.status === 200) {{
                const data = await response.json();
                window.location.href = data['login_url'];
                return 'Redirecting soon ...', {{}};
            }}
            window.location.href = '{P.REDIRECT_TO_LOGIN_URL}';
            return "Redirecting to basic login ...", {{}};
        }}
    }}
    """,
    Output(EMAIL_ADDRESS_INFO, "children"),
    Output(EMAIL_ADDRESS_INFO, "style"),
    Input(ENTERPRISE_LOGIN_BUTTON, "n_clicks"),
    State(EMAIL_ADDRESS, "value"),
    State(EMAIL_ADDRESS_INFO, "style"),
    prevent_initial_call=True,
)


@callback(
    Output(LOGIN_ERROR_CONTAINER, "children"),
    Input(LOGIN_BUTTON, "n_clicks"),
    State(EMAIL_ADDRESS, "value"),
    State(PASSWORD_INPUT, "value"),
    prevent_initial_call=True,
)
def handle_login_with_email(n_clicks: int, email: str, password: str) -> dmc.Alert:
    print("hi")
    verify_login_info(email, password)
    return None


def verify_login_info(email: str, password: str) -> bool:
    client = boto3.client("cognito-idp", REGION)
    response = client.initiate_auth(
        authFlow="USER_PASSWORD_AUTH",
        authParameters={"USERNAME": email, "PASSWORD": password},
    )
    LOGGER.info(response)
    return None


register_page(__name__, path=P.UNAUTHORIZED_URL, title="Mitzu", layout=layout)
