from typing import Optional

import dash_mantine_components as dmc
from dash import html

from mitzu.webapp.pages.error_pages.page_container import (
    page_container,
)


def not_found_page(resource_name: str, project_id: Optional[str]) -> html.Div:
    return page_container(
        title=f"{resource_name.capitalize()} not found",
        content=[
            dmc.Text(
                f"""
                    The requested {resource_name} is not found. If you've wanted to open
                    a previously bookmarked page then you may need to find that workspace,
                    dashboard, saved insight etc. again and update your bookmark.
                """,
                size="lg",
            ),
            dmc.Text(
                """
                    If you can't find that resource then please contact us!
                """,
                size="lg",
            ),
        ],
        project_id=project_id,
    )
