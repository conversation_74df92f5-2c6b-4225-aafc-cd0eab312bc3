from typing import Optional

import dash.development.base_component as bc
import dash_mantine_components as dmc

from mitzu.webapp.pages.error_pages.page_container import (
    page_container,
)


def error_page(exc: Exception, project_id: Optional[str]) -> bc.Component:
    return page_container(
        title="Ooops, something went wrong!",
        content=[
            dmc.Text(
                """
                    We were unable to fulfill your request, and we are terribly sorry for this
                    inconvenience. Our support team has been notified and will begin working
                        on the issue shortly.
                """,
                size="lg",
                className="mb-3",
            ),
            dmc.Alert(
                f"{str(exc)[:1000]}",
                title="Details error message:",
                color="red",
            ),
        ],
        project_id=project_id,
    )
