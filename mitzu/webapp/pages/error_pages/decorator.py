import functools
from typing import Callable, <PERSON>m<PERSON><PERSON>, TypeVar, cast

from mitzu.logger import LOGGER
from mitzu.webapp.pages.error_pages.error_page import (
    error_page,
)

P = ParamSpec("P")
T = TypeVar("T")


def fallback_to_error_page(
    page_name: str,
) -> Callable[[Callable[P, T]], Callable[P, T]]:
    def handle_call(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            try:
                return func(*args, **kwargs)
            except Exception as exc:
                project_id = cast(str, kwargs.get("project_id", None))
                LOGGER.opt(exception=exc).error(
                    f"Failed to create page[{project_id}]: {page_name}"
                )
                return error_page(exc, project_id)

        return wrapper

    return handle_call
