from typing import Dict, List

import dash_mantine_components as dmc
import pandas as pd
from dash import dash_table, dcc, html
from dash_iconify import DashIconify

import mitzu.model as M
import mitzu.webapp.model as WM
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.service.catalog_service as CS
import mitzu.webapp.service.input_generator as IG
from mitzu.adapters.generic_adapter import (
    FINAL_DIM_PRIMARY_KEY_COL,
)
from mitzu.model import (
    DIMENSION_FIELD_PATH_PREFIX,
    DimensionFieldPath,
    Project,
)
from mitzu.webapp.metric_context import (
    DimensionPropertyMeta,
)
from mitzu.webapp.pages import uikit
from mitzu.webapp.pages.dimensions.constants import (
    BLUR_OPACITY,
    SEARCH_QUERY_STATE,
)
from mitzu.webapp.pages.errors import no_dimensions_message

DIM_COLUMN_CHOOSER = "dim_column_chooser"
DIM_TABLE_CONTAINER = "dim_table_container"
DIM_ID_COL = " ID "  # spaces are necessary, as dash_table already has a default column with ID, we need to name it differently.
DIM_TABLE = "dim_tabler"
CLEAR_DIM_SEARCH = "clear_dim_search"
DIM_PAGE_TABS = "dim_page_tabs"
DIM_SEARCH_INPUT = "dim_search_input"
DIM_FILTER_CONTAINER = "dim_filter_container"
DIM_FILTER_PROPERTY = "dim_filter_property"
DIM_FILTER_OPERATOR = "dim_filter_operator"
DIM_FILTER_VALUE = "dim_filter_value"
DIM_PAGE_TABS_HORIZONTAL = "dim_page_tabs_horizontal"

DIM_DATA_CONTAINER = "dim_data_container"  # DATA_TOP_CONTAINER

DIM_RUN_BUTTON = "dim_run_button"
DIM_RUN_OVERLAY = "dim_run_overlay"
DIM_CANCEL_BUTTON = "dim_cancel_button"
DIM_CANCEL_OVERLAY = "dim_cancel_overlay"
DIM_CLUSTER_INFO = "dim_cluster_info"

MAX_COLUMN_COUNT = 30


def create_dim_column_chooser(
    dim_catalog: Dict[DimensionFieldPath, DimensionPropertyMeta],
    search_query: WM.WebappDimensionsSearchQuery,
) -> dmc.MultiSelect:

    data = [
        {
            "label": meta.display_name,
            "value": str(field_path),
        }
        for field_path, meta in dim_catalog.items()
        if meta.entity == search_query.entity
    ]
    data = sorted(data, key=lambda val: val["label"])

    return dmc.MultiSelect(
        id=DIM_COLUMN_CHOOSER,
        data=data,
        searchable=True,
        value=[str(field) for field in search_query.selected_columns],
        creatable=False,
        icon=DashIconify(icon="fluent:text-column-two-left-20-filled"),
        placeholder="Select properties",
        size="xs",
        clearable=True,
        style={"zIndex": 250, "width": "300px"},
    )


def create_def_component(entity_meta: WM.EntityMeta) -> dmc.Stack:
    empty_state_image = "/assets/state_images/empty_state-users.svg"
    subject = entity_meta.display_name.lower() + "s"
    if subject.endswith("ss"):
        subject = subject[:-1]
    return dmc.Stack(
        [
            dmc.Image(src=empty_state_image, width=240, height=200),
            dmc.Text(
                f"Start searching for {subject} based on their properties.",
                size="md",
            ),
            dmc.Text(
                [
                    f"Read about {entity_meta.display_name} search in our ",
                    dcc.Link(
                        dmc.Text("documentation", size="sm", className="d-inline"),
                        href="https://docs.mitzu.io/user-and-group-lookup",
                        refresh=True,
                        target="_blank",
                    ),
                ],
                color="gray",
                size="sm",
            ),
        ],
        className="w-100 text-center p-3",
        align="center",
        spacing="xs",
    )


def create_no_results_comp() -> dmc.Stack:
    return dmc.Stack(
        [
            dmc.Center(
                dmc.Image(
                    src="/assets/state_images/empty_state-no_data.svg",
                    width="200px",
                    height="200px",
                ),
            ),
            dmc.Text(
                "Nothing to show. Try refining your search.",
                className="w-100 text-center p-3",
                size="lg",
            ),
        ]
    )


def filter_df_columns_by_search_tokens(
    df: pd.DataFrame,
    search_query: WM.WebappDimensionsSearchQuery,
) -> pd.DataFrame:
    selected_columns = ["id"] + [str(path) for path in search_query.selected_columns]

    for filter in search_query.filters:
        column_name = str(filter.left)
        if column_name not in selected_columns:
            selected_columns.append(column_name)

    if search_query.search_text:
        search_tokens = [s.lower() for s in search_query.search_text.split(" ")]
        matching_columns = df.columns[
            df.apply(
                lambda col: col.astype(str)
                .str.lower()
                .str.contains("|".join(search_tokens).lower(), na=False)
                .any()
            )
        ].tolist()

        for col in matching_columns:
            if col not in selected_columns:
                selected_columns.append(col)

    selected_columns = ([FINAL_DIM_PRIMARY_KEY_COL] + selected_columns)[
        0:MAX_COLUMN_COUNT
    ]
    return df[selected_columns]


def rename_columns(
    df: pd.DataFrame, dim_catalog: Dict[DimensionFieldPath, DimensionPropertyMeta]
) -> pd.DataFrame:
    df.rename(
        columns={
            FINAL_DIM_PRIMARY_KEY_COL: DIM_ID_COL,
            **{
                col: dim_catalog[DimensionFieldPath.parse(col)].display_name
                for col in df.columns
                if col.startswith(DIMENSION_FIELD_PATH_PREFIX + ":")
            },
        },
        inplace=True,
    )

    return df


def reorder_email_columns(dim_df: pd.DataFrame) -> pd.DataFrame:
    def contains_email(column_name: str) -> bool:
        clean_name = column_name.replace(":", "").replace(".", "")
        return "email" in clean_name.lower()

    email_columns = [col for col in dim_df.columns if contains_email(col)]
    other_columns = [col for col in dim_df.columns if not contains_email(col)]
    ordered_columns = email_columns + other_columns
    return dim_df[ordered_columns]


def create_dimensions_table(
    dim_df: pd.DataFrame,
    dim_catalog: Dict[DimensionFieldPath, DimensionPropertyMeta],
    search_query: WM.WebappDimensionsSearchQuery,
) -> dash_table.DataTable:
    # moving the dim_id column

    dim_df = reorder_email_columns(dim_df)
    col = dim_df.pop(FINAL_DIM_PRIMARY_KEY_COL)
    dim_df.insert(0, FINAL_DIM_PRIMARY_KEY_COL, col)
    dim_df["id"] = dim_df[FINAL_DIM_PRIMARY_KEY_COL]
    filtered_dim_df = filter_df_columns_by_search_tokens(dim_df, search_query)
    filtered_dim_df = rename_columns(filtered_dim_df, dim_catalog)
    rows = filtered_dim_df.to_dict("records")
    return dash_table.DataTable(
        id=DIM_TABLE,
        data=rows,
        columns=[{"id": c, "name": c} for c in filtered_dim_df.columns if c != "id"],
        style_as_list_view=True,
        fill_width=True,
        editable=False,
        sort_action="native",
        sort_mode="single",
        page_size=20,
        filter_action="native",
        **UIKIT.get_dcc_table_styles(),  # type: ignore[arg-type]
    )


def get_tabs(
    search_query: WM.WebappDimensionsSearchQuery,
    project: M.Project,
    catalog_service: CS.CatalogService,
    disabled: bool,
) -> dmc.Tabs:
    metric_context = catalog_service.get_metric_context_with_events_only(
        global_filters=[]
    )
    dim_catalog = catalog_service.get_dimension_property_catalog()

    tabs: List[UIKIT.TabPanelElement] = [
        UIKIT.TabDivider(
            label="Entities",
        ),
    ]
    for em in sorted(
        metric_context._entity_meta.values(), key=lambda e: e.display_name
    ):
        content = (
            create_search_tab_content(
                project,
                disabled,
                em,
                dim_catalog,
                search_query,
                IG.InputGenerator(project.id, metric_context, catalog_service._storage),
            )
            if em.entity == search_query.entity
            else UIKIT.tab_spinner()
        )

        tabs.append(
            UIKIT.TabContent(
                label=em.display_name,
                component=content,
                value=em.entity.name,
                icon=em.icon,
            )
        )

    return UIKIT.create_mobile_friendly_tab_layout(
        tabs,
        selected_tab=search_query.entity.name,
        tab_id=DIM_PAGE_TABS,
        horizontal_bar_id=DIM_PAGE_TABS_HORIZONTAL,
    )


def create_search_tab_content(
    project: Project,
    disabled: bool,
    entity_meta: WM.EntityMeta,
    dim_catalog: Dict[DimensionFieldPath, DimensionPropertyMeta],
    search_query: WM.WebappDimensionsSearchQuery,
    input_generator: IG.InputGenerator,
) -> List[html.Div]:

    has_dimension_table_configured = False
    for dim_table in project.dimension_data_tables:
        if (
            dim_table.entity == entity_meta.entity
            and dim_table.last_indexed is not None
        ):
            has_dimension_table_configured = True
            break

    table_type_txt = entity_meta.display_name
    example = f"""Search for anything {table_type_txt} property. Example: 'john doe'.
        The search will query every {table_type_txt} property for the keywords 'john' and 'doe'."""

    content = html.Div(
        [
            html.Div(
                children=[
                    html.Div(
                        "",  # empty to prevent start on the page load
                        id=SEARCH_QUERY_STATE,
                        className="d-none",
                    ),
                    dmc.Text(
                        [
                            DashIconify(
                                icon=entity_meta.icon,
                                width=30,
                                height=30,
                                className="me-2 pb-1",
                            ),
                            f"Lookup {table_type_txt}"
                            + ("s" if not table_type_txt.endswith("s") else ""),
                        ],
                        className="fw-normal",
                        size="xl",
                        style={"minWidth": "300px"},
                    ),
                    dmc.Space(h="xl"),
                    dmc.Stack(
                        children=[
                            dmc.Group(
                                children=[
                                    dmc.Group(
                                        [
                                            html.Div(
                                                render_property_filter(
                                                    search_query, input_generator
                                                ),
                                                id=DIM_FILTER_CONTAINER,
                                                className="uikit-property-filter-container",
                                                style={"width": "100%"},
                                            ),
                                        ],
                                        position="bottom",
                                        align="start",
                                        noWrap=False,
                                        className="w-100 mt-3",
                                    ),
                                    dmc.Group(
                                        [
                                            dmc.Group(
                                                [
                                                    dmc.TextInput(
                                                        placeholder=f"Start typing to search for {table_type_txt}"
                                                        + (
                                                            "s"
                                                            if not table_type_txt.endswith(
                                                                "s"
                                                            )
                                                            else ""
                                                        ),
                                                        icon=DashIconify(
                                                            icon="mynaui:search",
                                                            color="dark",
                                                        ),
                                                        rightSection=dmc.ActionIcon(
                                                            DashIconify(
                                                                icon="mynaui:x"
                                                            ),
                                                            variant="transparent",
                                                            id=CLEAR_DIM_SEARCH,
                                                        ),
                                                        className="w-50",
                                                        id=DIM_SEARCH_INPUT,
                                                        value=search_query.search_text,
                                                        disabled=disabled,
                                                        size="xs",
                                                        style={"minWidth": "300px"},
                                                    ),
                                                    uikit.add_help_tooltip(
                                                        label=example,
                                                        component=dmc.ThemeIcon(
                                                            children=DashIconify(
                                                                icon="mdi:question-mark-circle-outline",
                                                                width=20,
                                                                height=20,
                                                            ),
                                                            size="md",
                                                            variant="transparent",
                                                            color="gray",
                                                        ),
                                                    ),
                                                ],
                                                noWrap=True,
                                            ),
                                            create_dim_column_chooser(
                                                dim_catalog, search_query
                                            ),
                                        ],
                                        position="apart",
                                        className="w-100",
                                    ),
                                ],
                                position="bottom",
                                noWrap=False,
                            ),
                        ],
                        className="w-100",
                    ),
                ],
            ),
            dmc.Group(
                position="apart",
                align="center",
                className="mt-5 mb-3",
                children=[
                    uikit.primary_button(
                        "Run",
                        id=DIM_RUN_BUTTON,
                        icon=DashIconify(icon="mdi:play"),
                        color="violet",
                        disabled=search_query.is_empty(),
                    ),
                ],
            ),
            dmc.Divider(className="mb-5"),
            html.Div(
                html.Div(
                    [
                        html.Div(
                            create_def_component(entity_meta),
                            id=DIM_TABLE_CONTAINER,
                            style=BLUR_OPACITY
                            if not search_query.is_empty()
                            else {"opacity": 1},
                        ),
                        UIKIT.cancel_button_overlay(
                            overlay_id=DIM_CANCEL_OVERLAY,
                            cancel_button_id=DIM_CANCEL_BUTTON,
                            cluster_info_id=DIM_CLUSTER_INFO,
                        ),
                        html.Div(
                            [],
                            id=DIM_RUN_OVERLAY,
                            className="run-button-overlay",
                        ),
                    ],
                    style={
                        "position": "relative",
                    },
                    id=DIM_DATA_CONTAINER,
                ),
            ),
        ],
        className="m-3",
    )
    if not has_dimension_table_configured:
        return [
            html.Div(content, className="d-none"),
            no_dimensions_message(project.id, table_type_txt),
        ]

    else:
        return content


def render_property_filter(
    search_query: WM.WebappDimensionsSearchQuery, input_generator: IG.InputGenerator
) -> html.Div:
    input_rows = []

    for index, filter in enumerate(search_query.filters):
        dropdown_component = (
            input_generator.create_property_dropdown_for_dimension_search_filter(
                entity=search_query.entity,
                placeholder="",
                selected=filter.left,
                search=None,
            )
        )

        input_rows.append(
            UIKIT.render_property_filter_row(
                property_id={
                    "type": DIM_FILTER_PROPERTY,
                    "index": str(index),
                },
                property_dropdown=dropdown_component,
                filter=filter,
                input_generator=input_generator,
                operator_id={
                    "type": DIM_FILTER_OPERATOR,
                    "index": str(index),
                },
                value_id={
                    "type": DIM_FILTER_VALUE,
                    "index": str(index),
                },
                read_only=False,
                allow_reindex=True,
                size="sm",
            )
        )

    dropdown_component = (
        input_generator.create_property_dropdown_for_dimension_search_filter(
            entity=search_query.entity,
            placeholder="+ Filter" if len(search_query.filters) == 0 else "+ And",
            selected=None,
            search=None,
        )
    )

    input_rows.append(
        UIKIT.render_property_filter_placeholder_row(
            property_id={
                "type": DIM_FILTER_PROPERTY,
                "index": str(len(search_query.filters)),
            },
            property_dropdown=dropdown_component,
        )
    )
    return html.Div(input_rows)
