from dataclasses import replace
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Optional,
    Tuple,
    Union,
)

import dash_mantine_components as dmc
from dash import (
    ALL,
    MATCH,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    ctx,
    dcc,
    html,
    no_update,
    register_page,
)
from dash.exceptions import PreventUpdate
from dash_iconify import DashIconify

import mitzu.adapters.generic_adapter as GA
import mitzu.model as M
import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.model as WM
import mitzu.webapp.navbar as NB
import mitzu.webapp.pages.paths as P
import mitzu.webapp.pages.uikit as UIKIT
import mitzu.webapp.service.adapter_factory as AF
import mitzu.webapp.service.input_generator as IG
import mitzu.webapp.service.metric_converter as MC
from mitzu import configs
from mitzu.logger import LOGGER
from mitzu.model import Entity
from mitzu.webapp.model import WebappMetric
from mitzu.webapp.pages.common.property_filter import (
    property_value_data_update,
)
from mitzu.webapp.pages.dimensions.components import (
    CLEAR_DIM_SEARCH,
    DIM_CANCEL_BUTTON,
    DIM_CANCEL_OVERLAY,
    DIM_COLUMN_CHOOSER,
    DIM_DATA_CONTAINER,
    DIM_FILTER_CONTAINER,
    DIM_FILTER_OPERATOR,
    DIM_FILTER_PROPERTY,
    DIM_FILTER_VALUE,
    DIM_PAGE_TABS,
    DIM_PAGE_TABS_HORIZONTAL,
    DIM_RUN_BUTTON,
    DIM_RUN_OVERLAY,
    DIM_SEARCH_INPUT,
    DIM_TABLE,
    DIM_TABLE_CONTAINER,
    create_def_component,
    create_dimensions_table,
    create_no_results_comp,
    get_tabs,
    render_property_filter,
)
from mitzu.webapp.pages.dimensions.constants import (
    BLUR_OPACITY,
    DEF_FILTER_VALUE_CLASS,
    DIM_CREATE_COHORT,
    SEARCH_QUERY_STATE,
)
from mitzu.webapp.pages.dimensions.dimension_cohort_modal import (
    create_cohort_dialog,
)
from mitzu.webapp.pages.error_pages.decorator import (
    fallback_to_error_page,
)
from mitzu.webapp.pages.errors import no_connection_message
from mitzu.webapp.pages.explore.components.warning_badges import (
    create_warning_badge,
)
from mitzu.webapp.pages.projects.utils import cancel_query
from mitzu.webapp.serialization import (
    dimension_search_query_from_string,
    dimension_search_query_to_string,
    get_dimension_search_query_cache_key,
)
from mitzu.webapp.service.metric_updater import (
    MetricUpdater,
)

DIM_PAGE_LOCATION = "dim_page_location"
DIM_SEARCH_LOCATION = "dim_search_location"
SHOW_ALL_PROPERTIES = "show_all_properties"
DIM_PAGE_TABS_CONTAINER = "dim_page_tabs_container"
DIM_QUERY_ID = "dim_query_id"
DIM_REFRESH_BUTTON = "dim_refresh_button"


@fallback_to_error_page("Users Page")
def layout(project_id: str, **query_params: str) -> html.Div:
    deps = DEPS.Dependencies.get()
    project = deps.storage.get_project(project_id)

    dim_tables = project.dimension_data_tables
    disabled = len(dim_tables) == 0

    catalog_service = deps.get_catalog_service(project_id)

    if not project.is_connection_configured:
        content = [no_connection_message(project.id)]
    else:
        if "qs" in query_params:
            search_query = dimension_search_query_from_string(query_params["qs"])
        else:
            search_query = WM.WebappDimensionsSearchQuery(
                M.USER_ENTITY,
                search_text=None,
                filters=[],
                selected_columns=[],
            )
        content = [
            dcc.Location(id=DIM_PAGE_LOCATION, refresh="callback-nav"),
            dcc.Location(id=DIM_SEARCH_LOCATION, refresh=False),
            create_cohort_dialog(),
            html.Div(
                "",
                id=DIM_QUERY_ID,
                className="d-none",
            ),
            html.Div(
                get_tabs(
                    search_query,
                    project,
                    catalog_service,
                    disabled,
                ),
                id=DIM_PAGE_TABS_CONTAINER,
            ),
        ]
    return html.Div(
        [
            NB.get_navbar_component(project_id=project_id),
            dmc.Container(
                children=content,
                fluid=True,
                className="px-0",
                style={"overflow": "scroll"},
            ),
        ]
    )


def get_search_query_from_inputs(
    entity: M.Entity, input_data: List[Union[List, Dict]]
) -> WM.WebappDimensionsSearchQuery:
    search_input = None
    filters: Dict[int, Dict] = {}
    selected_columns = []
    for input_group in input_data:
        if isinstance(input_group, list):
            input_list = input_group
        else:
            input_list = [input_group]

        for input in input_list:
            if (
                input["id"] == DIM_SEARCH_INPUT
                and input["value"] is not None
                and input["value"].strip() != ""
            ):
                search_input = input["value"]

            if input["id"] == DIM_COLUMN_CHOOSER and isinstance(input["value"], list):
                selected_columns = [
                    M.DimensionFieldPath.parse(val) for val in input["value"]
                ]

            if not isinstance(input["id"], dict):
                continue

            if "index" not in input["id"].keys():
                continue

            order = int(input["id"]["index"])
            if order not in filters.keys():
                filters[order] = {
                    "left": None,
                    "operator": None,
                    "right": None,
                }

            if (
                input["id"]["type"] == DIM_FILTER_PROPERTY
                and input["value"] is not None
            ):
                filters[order]["left"] = M.FieldPath.parse(input["value"])

            if (
                isinstance(ctx.triggered_id, dict)
                and ctx.triggered_id.get("type") == DIM_FILTER_PROPERTY
                and input["id"]["index"] == ctx.triggered_id.get("index")
            ):
                continue

            if (
                input["id"]["type"] == DIM_FILTER_OPERATOR
                and input["value"] is not None
            ):
                filters[order]["operator"] = M.Operator(input["value"])

            if input["id"]["type"] == DIM_FILTER_VALUE and input["value"] is not None:
                filters[order]["right"] = input["value"]

    event_filters = []
    for _, f in sorted(filters.items(), key=lambda t: t[0]):
        if f["left"] is None:
            continue

        operator = f["operator"] or M.Operator.ANY_OF

        value = f["right"]
        if operator.is_unary_operator():
            value = None

        if operator.supports_multiple_operands():
            if not isinstance(value, list):
                if value is None:
                    value = []
                else:
                    value = [value]
        else:
            if isinstance(value, list):
                if len(value) == 1:
                    value = value[0]
                else:
                    value = None

        event_filters.append(WM.WebappEventFilter(f["left"], operator, value))

    return WM.WebappDimensionsSearchQuery(
        entity,
        search_input,
        filters=event_filters,
        selected_columns=selected_columns,
    )


def get_search_query_as_query_string(
    search_query: WM.WebappDimensionsSearchQuery,
) -> str:
    return "?qs=" + dimension_search_query_to_string(search_query)


@callback(
    Output(DIM_PAGE_TABS_CONTAINER, "children"),
    Output(DIM_SEARCH_LOCATION, "search", allow_duplicate=True),
    Input(DIM_PAGE_TABS, "value"),
    Input(DIM_PAGE_TABS_HORIZONTAL, "value"),
    State(DIM_SEARCH_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_tab_change(
    vertical_tab: str,
    horizontal_tab: str,
    href: str,
) -> Tuple[dmc.Tabs, str]:
    deps = DEPS.Dependencies.get()
    project_id = P.get_project_id(href)
    if ctx.triggered_id == DIM_PAGE_TABS_HORIZONTAL:
        tab = horizontal_tab
    else:
        tab = vertical_tab
    search_query = WM.WebappDimensionsSearchQuery(
        M.Entity(tab),
        search_text=None,
        filters=[],
        selected_columns=[],
    )
    project_storage = deps.get_project_storage(project_id)
    project = project_storage.get_project()
    catalog_service = deps.get_catalog_service(project_id)
    return get_tabs(
        search_query,
        project,
        catalog_service,
        disabled=len(project.dimension_data_tables) == 0,
    ), get_search_query_as_query_string(search_query)


@callback(
    Input(DIM_SEARCH_INPUT, "value"),
    Input(DIM_COLUMN_CHOOSER, "value"),
    Input({"type": DIM_FILTER_PROPERTY, "index": ALL}, "value"),
    Input({"type": DIM_FILTER_OPERATOR, "index": ALL}, "value"),
    Input({"type": DIM_FILTER_VALUE, "index": ALL}, "value"),
    State(DIM_SEARCH_LOCATION, "href"),
    State(DIM_SEARCH_INPUT, "disabled"),
    State(DIM_PAGE_TABS, "value"),
    output={
        DIM_SEARCH_LOCATION: Output(DIM_SEARCH_LOCATION, "search"),
        DIM_FILTER_CONTAINER: Output(DIM_FILTER_CONTAINER, "children"),
        DIM_RUN_OVERLAY: Output(DIM_RUN_OVERLAY, "style", allow_duplicate=True),
        DIM_TABLE_CONTAINER: Output(DIM_TABLE_CONTAINER, "style", allow_duplicate=True),
        DIM_RUN_BUTTON: Output(DIM_RUN_BUTTON, "disabled"),
    },
    prevent_initial_call=True,
)
def handle_filter_change(
    search_value: str,
    extra_properties: List[str],
    properties: List,
    operators: List,
    values: List,
    href: str,
    disabled: bool,
    entity_str: Optional[str],
) -> Dict[str, Union[html.Div, str, Dict[str, str], Dict[str, float], bool]]:
    if not entity_str:
        raise PreventUpdate()

    try:
        deps = DEPS.Dependencies.get()
        project_id = P.get_project_id(href)
        project_storage = deps.get_project_storage(project_id)
        entity = M.Entity(entity_str)
        search_query = get_search_query_from_inputs(entity, ctx.inputs_list)

        catalog_service = deps.get_catalog_service(project_id)
        metric_context = catalog_service.get_metric_context_with_dimensions_only()

        input_generator = IG.InputGenerator(project_id, metric_context, project_storage)

        return {
            DIM_SEARCH_LOCATION: get_search_query_as_query_string(search_query),
            DIM_FILTER_CONTAINER: render_property_filter(search_query, input_generator),
            DIM_RUN_OVERLAY: {"display": "flex"}
            if not search_query.is_empty()
            else {"display": "none"},
            DIM_TABLE_CONTAINER: BLUR_OPACITY if not search_query.is_empty() else {},
            DIM_RUN_BUTTON: False if not search_query.is_empty() else True,
        }

    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update lookup page")
        raise PreventUpdate() from exc


@callback(
    Output({"type": DIM_FILTER_PROPERTY, "index": MATCH}, "options"),
    Input({"type": DIM_FILTER_PROPERTY, "index": MATCH}, "search_value"),
    State({"type": DIM_FILTER_PROPERTY, "index": MATCH}, "value"),
    State(DIM_PAGE_TABS, "value"),
    State(DIM_SEARCH_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_property_input_search(
    search_value: Optional[str], value: Optional[str], entity_str: str, href: str
) -> List[Dict]:
    try:
        project_id = P.get_project_id(href)
        deps = DEPS.Dependencies.get()

        catalog_service = deps.get_catalog_service(project_id)
        metric_context = catalog_service.get_metric_context_with_dimensions_only()
        input_generator = IG.InputGenerator(
            project_id, metric_context, catalog_service._storage
        )

        dropdown = input_generator.create_property_dropdown_for_dimension_search_filter(
            entity=M.Entity(entity_str),
            placeholder="",
            selected=M.FieldPath.parse(value) if value is not None else None,
            search=search_value,
        )
        return [UIKIT.render_dcc_dropdown_option(opt) for opt in dropdown.options]

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Error while indexing with search for property")
        return no_update


clientside_callback(
    """
    function(value) {
        return [{"display": "none"}, {"display": "flex"}];
    }
    """,
    Output(DIM_RUN_OVERLAY, "style", allow_duplicate=True),
    Output(DIM_CANCEL_OVERLAY, "style", allow_duplicate=True),
    Input(DIM_RUN_BUTTON, "n_clicks"),
    prevent_initial_call=True,
)

clientside_callback(
    """
    async function(clicks) {
        return [{"position": "static"}, {"display": "none"}]
    }
    """,
    Output(DIM_DATA_CONTAINER, "style", allow_duplicate=True),
    Output(DIM_CANCEL_OVERLAY, "style", allow_duplicate=True),
    Input(DIM_CANCEL_BUTTON, "n_clicks"),
    prevent_initial_call=True,
)


@callback(
    Output(SEARCH_QUERY_STATE, "children"),
    Input(DIM_RUN_BUTTON, "n_clicks"),
    State(DIM_SEARCH_INPUT, "value"),
    State(DIM_COLUMN_CHOOSER, "value"),
    State({"type": DIM_FILTER_PROPERTY, "index": ALL}, "value"),
    State({"type": DIM_FILTER_OPERATOR, "index": ALL}, "value"),
    State({"type": DIM_FILTER_VALUE, "index": ALL}, "value"),
    State(DIM_PAGE_TABS, "value"),
    State(DIM_SEARCH_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_run_button(
    n_clicks: int,
    search_text: str,
    columns: List,
    properties: List,
    operators: List,
    values: List,
    entity_str: str,
    href: str,
) -> str:
    try:
        entity = M.Entity(entity_str)
        search_query = get_search_query_from_inputs(entity, ctx.states_list)

        return dimension_search_query_to_string(search_query)
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to update lookup page")
        raise PreventUpdate() from exc


@callback(
    output=[
        Output(DIM_QUERY_ID, "children", allow_duplicate=True),
        Output(DIM_TABLE_CONTAINER, "children", allow_duplicate=True),
        # add cluster info support here
    ],
    inputs=[
        Input(SEARCH_QUERY_STATE, "children"),
        Input({"type": DIM_REFRESH_BUTTON, "index": ALL}, "n_clicks"),
    ],
    state=[
        State(DIM_COLUMN_CHOOSER, "value"),
        State(DIM_PAGE_TABS, "value"),
        State(DIM_QUERY_ID, "children"),
        State(DIM_SEARCH_LOCATION, "href"),
    ],
    background=True,
    running=[
        (
            Output(DIM_TABLE_CONTAINER, "style"),
            BLUR_OPACITY,
            {"opacity": 1},
        ),
        (
            Output(DIM_CANCEL_OVERLAY, "style"),
            {"display": "flex"},
            {"display": "none"},
        ),
    ],
    cancel=[
        Input(DIM_CANCEL_BUTTON, "n_clicks"),
        Input(DIM_PAGE_LOCATION, "pathname"),
        Input(
            DIM_RUN_OVERLAY, "style"
        ),  # if the run button appears, we should cancel the background job
    ],
    progress=[
        Output(DIM_QUERY_ID, "children"),
        # add cluster info support here
    ],
    prevent_initial_call=True,
)
def handle_query_state_change(
    set_progress: Callable[[str], None],
    state_str: str,
    refresh_button_click: Optional[int],
    extra_properties: List[str],
    entity_str: str,
    running_query_id: str,
    href: str,
) -> Tuple[str, Union[dmc.Alert, dmc.Stack, html.Div]]:
    try:
        deps = DEPS.Dependencies.get()
        entity = Entity(entity_str)
        project_id = P.get_project_id(href)
        project_storage = deps.get_project_storage(project_id)

        catalog_service = deps.get_catalog_service(project_id)
        metric_context = catalog_service.get_metric_context_with_events_only([])

        cancel_query(href, running_query_id)  # kill previous query

        if not state_str:
            return no_update, no_update

        webapp_search_query = dimension_search_query_from_string(state_str)
        if webapp_search_query.is_empty():
            return (
                "",
                create_def_component(metric_context.get_entity_meta(entity)),
            )

        search_query_defs = project_storage.get_definitions_for_dimension_search_query()
        project = search_query_defs.project

        metric_updater = MetricUpdater(metric_context, project.insight_settings)

        metric_converter = MC.MetricConverter(
            search_query_defs, project_storage, metric_updater
        )
        query = metric_converter.convert_dimension_search_query(webapp_search_query)

        dim_catalog = deps.get_catalog_service(
            project_id
        ).get_dimension_property_catalog()
        adapter_context = deps.create_adapter_context(
            href, project, GA.QueryType.INSIGHT
        )
        set_progress(adapter_context.query_id)
        adapter = AF.create_adapter_from_project(
            project,
            cache=deps.cache,
            adapter_context=adapter_context,
            user_role=deps.user_role,
        )

        hash_key = get_dimension_search_query_cache_key(webapp_search_query, project)
        result_df = deps.cache.get(hash_key)
        from_cache = True
        if result_df is None or (
            isinstance(ctx.triggered_id, dict)
            and ctx.triggered_id.get("type") == DIM_REFRESH_BUTTON
        ):
            from_cache = False
            result_df = adapter.search_dimensions_df(query)
            deps.cache.put(hash_key, result_df, expire=120)

        result_size = result_df.shape[0]
        deps.tracking_service.track_search_dimensions(
            search_query=webapp_search_query,
            result_count=result_size,
            from_cache=from_cache,
            table_type=entity,
        )
        if result_size == 0:
            return (
                "",
                create_no_results_comp(),
            )

        if entity == M.USER_ENTITY:
            label = "Create cohort"
            icon = DashIconify(icon="bi:person-lines-fill")
        else:
            entity_meta = metric_context.get_entity_meta(entity)
            label = f"Create {entity_meta.display_name.lower()} collection"
            icon = DashIconify(icon=entity_meta.icon)

        return (
            "",
            html.Div(
                [
                    dmc.Group(
                        [
                            UIKIT.icon_button_with_tooltip(
                                id={"type": DIM_REFRESH_BUTTON, "index": "1"},
                                tooltip_label="Refresh data",
                                icon="mdi:refresh",
                                class_name="text-center d-inline-block",
                            ),
                            UIKIT.primary_button(
                                label,
                                id=DIM_CREATE_COHORT,
                                icon=icon,
                            )
                            if entity == M.USER_ENTITY or configs.COLLECTIONS_ENABLED
                            else None,
                            create_warning_badge(
                                tooltip=f"""Too many data points were returned for your insight. 
                                    Please reduce the date range or introduce filters to your insight.
                                    The results limit is {GA.MAX_ROW_LIMIT} rows.
                                    """,
                                label="Trimmed results",
                                icon="mdi:warning",
                                color="orange",
                            )
                            if result_size > GA.MAX_ROW_LIMIT
                            else None,
                            create_warning_badge(
                                tooltip=(
                                    "Click on the ↻ refresh data button to get the latest results."
                                ),
                                label="Cached results",
                                icon="material-symbols:info-outline",
                                color="gray",
                            )
                            if from_cache
                            else None,
                        ],
                        position="left",
                        className="mb-3",
                    ),
                    create_dimensions_table(
                        result_df,
                        dim_catalog,
                        webapp_search_query,
                    ),
                ]
            ),
        )

    except Exception as exc:
        LOGGER.opt(exception=exc).error("Couldn't search users")
        return (
            "",
            dmc.Alert(
                title="Couldn't run search",
                children=dmc.Text(str(exc), color="gray", size="xs"),
                color="red",
                closeButtonLabel="close",
                radius=UIKIT.DEF_BUTTON_RADIUS,
            ),
        )


@callback(
    Output(DIM_QUERY_ID, "children", allow_duplicate=True),
    Input(DIM_CANCEL_BUTTON, "n_clicks"),
    State(DIM_QUERY_ID, "children"),
    State(DIM_PAGE_LOCATION, "href"),
    prevent_initial_call=True,
)
def handle_cancel_query(
    nclicks: Optional[int],
    running_query_id: Optional[str],
    href: str,
) -> str:
    if nclicks is None or running_query_id is None or running_query_id == "":
        return ""

    cancel_query(href, running_query_id)
    return ""


@callback(
    Output(DIM_PAGE_LOCATION, "href"),
    Input(DIM_TABLE, "active_cell"),
    Input(DIM_TABLE, "derived_virtual_data"),
    State(DIM_SEARCH_LOCATION, "href"),
    State(DIM_PAGE_TABS, "value"),
    prevent_initial_call=True,
)
def handle_active_cell_click(
    active_cells: Union[Dict[str, str], List[Dict[str, str]]],
    dim_data: List,
    href: str,
    tab: str,
) -> str:
    if isinstance(active_cells, List):
        if len(active_cells) == 0:
            return no_update
        active_cell = active_cells[0]
    else:
        active_cell = active_cells

    if not tab:
        return no_update

    if not active_cell:
        return no_update

    try:
        dim_id = active_cell["row_id"]
        if not dim_id:
            return no_update

        project_id = P.get_project_id(href)
        metric = WebappMetric.create_empty_webapp_metric(M.MetricType.SEGMENTATION)
        metric = replace(
            metric,
            global_filters=[
                WM.WebappEventFilter(
                    left=M.DimensionIDFieldPath(Entity(tab)),
                    operator=M.Operator.EQ,
                    right=dim_id,
                )
            ],
        )
        url = configs.HOME_URL + P.create_explore_path(
            project_id, metric, ignore_empty_segments=True
        )
        return url
    except Exception as exc:
        LOGGER.opt(exception=exc).error(
            "Couldn't retrieve dimension data on search dimensions page."
        )
        return no_update


clientside_callback(
    ClientsideFunction(
        namespace="clientside", function_name="set_custom_event_property_value"
    ),
    Output({"type": DIM_FILTER_VALUE, "index": MATCH}, "data", allow_duplicate=True),
    Input({"type": DIM_FILTER_VALUE, "index": MATCH}, "value"),
    Input({"type": DIM_FILTER_VALUE, "index": MATCH}, "searchValue"),
    State({"type": DIM_FILTER_VALUE, "index": MATCH}, "data"),
    State({"type": DIM_FILTER_VALUE, "index": MATCH}, "value"),
    prevent_initial_call=True,
)


@callback(
    Output({"type": DIM_FILTER_VALUE, "index": MATCH}, "data"),
    Output({"type": DIM_FILTER_VALUE, "index": MATCH}, "className"),
    Output({"type": DIM_FILTER_VALUE, "index": MATCH}, "placeholder"),
    Input({"type": DIM_FILTER_VALUE, "index": MATCH}, "value"),
    State({"type": DIM_FILTER_PROPERTY, "index": MATCH}, "value"),
    State(DIM_PAGE_LOCATION, "href"),
    background=True,
    interval=100,
    prevent_initial_call=True,
)
def handle_property_value_data_update(
    value: List[Any],
    field_path_str: Optional[str],
    href: str,
) -> Tuple[List, str, str]:
    options, placeholder = property_value_data_update(
        value, field_path_str, href, metric=None
    )
    return options, DEF_FILTER_VALUE_CLASS, placeholder


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="clear_search_input"),
    Output(DIM_SEARCH_INPUT, "value"),
    Input(CLEAR_DIM_SEARCH, "n_clicks"),
    prevent_initial_call=True,
)


register_page(
    __name__,
    path_template=P.DIMENSIONS_SEARCH,
    title="Mitzu - Lookup",
    layout=layout,
)
