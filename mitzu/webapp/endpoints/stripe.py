from datetime import timedelta

import flask

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.pages.paths as P
from mitzu import configs
from mitzu.logger import LOGGER
from mitzu.webapp.auth.model import (
    LAST_WORKSPACE_ID_COOKIE_NAME,
    ResponseType,
)
from mitzu.webapp.service.stripe_service import (
    StripeService,
)


def handle_stripe_event(request: flask.Request) -> ResponseType:
    stripe_service = StripeService()
    event = stripe_service.get_event_from_request(request.data, request.headers)
    response = flask.Response('{"status": "ok"}', status=200)

    # ignore parsing errors
    if event is None:
        LOGGER.error("stripe event is empty")
        return response

    customer_id = event.data.object.get("customer")
    logger_with_context = LOGGER.bind(
        event_id=event.id,
        customer_id=customer_id,
        event_type=event.type,
    )
    logger_with_context.info("stripe event received")

    # nothing to do with customer.created event
    if event.type == "customer.created":
        return response

    try:
        # this is stripe issue, the first customer.update does not have a customer_id
        if customer_id is None and event.type == "customer.updated":
            logger_with_context.info("customer is updated without having an id")
            return response

        if customer_id is None:
            raise ValueError("customer id is empty")

        customer = stripe_service.get_customer(customer_id)

        if customer.subscription is None:
            return response

        deps = DEPS.Dependencies.get()
        stripe_storage = deps.get_subscription_storage(customer_id)
        org_sub_expire = customer.subscription.expires_at + timedelta(
            days=configs.STRIPE_GRACE_PERIOD_DAYS
        )
        updated_org = stripe_storage.update_subscription_expiration_date(org_sub_expire)
        LOGGER.bind(
            event_id=event.id,
            customer_id=customer_id,
            event_type=event.type,
            org_id=updated_org.org_id,
            org_name=updated_org.name,
            stripe_subscription_expire_date=customer.subscription.expires_at,
            org_subscription_expire_date=org_sub_expire,
        ).info("subscription updated based on stripe event")

    except Exception:
        logger_with_context.error("failed to process stripe event")

    return response


def redirect_back_to_billing(request: flask.Request) -> ResponseType:
    last_workspace_id = request.cookies.get(LAST_WORKSPACE_ID_COOKIE_NAME)
    if last_workspace_id is None:
        return flask.redirect(code=307, location=configs.HOME_URL)

    return flask.redirect(
        code=307,
        location=configs.HOME_URL
        + P.create_account_settings_path(
            last_workspace_id, P.ManageAccountTab.SUBSCRIPTION
        ),
    )
