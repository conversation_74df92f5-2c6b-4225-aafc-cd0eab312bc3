from typing import Dict, cast

import flask

import mitzu.webapp.pages.paths as P
import mitzu.webapp.service.org_manager as OM
from mitzu import __version__
from mitzu.logger import LOGGER
from mitzu.webapp.auth.cloud_authorizer import (
    CloudAuthorizer,
)


def handle_ui_error(
    request: flask.Request,
) -> Dict[str, str]:
    response = __version__
    try:
        json_body = cast(Dict[str, str], request.json)
        ui_version = json_body.get("ui_version")

        authorizer = CloudAuthorizer.get()
        auth_token = authorizer.get_auth_token(request)
        if auth_token is None or authorizer.parse_token(auth_token) is None:
            pathname = json_body.get("pathname")
            if pathname and P.path_template_matches(P.SHARED_DASHBOARD_PATH, pathname):
                dashboard_id = P.get_path_value(
                    P.SHARED_DASHBOARD_PATH, pathname, P.DASHBOARD_ID
                )
                org_manager = OM.OrgManager.get()
                if not org_manager.is_dashboard_shared(dashboard_id):
                    response = "unreachable_dashboard"
                    LOGGER.bind(
                        ui_error_type=json_body.get("error"),
                        ui_error_message=json_body.get("message"),
                        pathname=json_body.get("pathname"),
                        ui_version=ui_version,
                    ).warning("dashboard-unreachable")
            elif pathname and P.path_template_matches(P.SHARED_INSIGHT_PATH, pathname):
                insight_id = P.get_path_value(
                    P.SHARED_INSIGHT_PATH, pathname, P.INSIGHT_ID
                )
                org_manager = OM.OrgManager.get()
                if not org_manager.is_insight_shared(insight_id):
                    response = "unreachable_insight"
                    LOGGER.bind(
                        ui_error_type=json_body.get("error"),
                        ui_error_message=json_body.get("message"),
                        pathname=json_body.get("pathname"),
                        ui_version=ui_version,
                    ).warning("insight-unreachable")
            else:
                response = "unauthorized"
                LOGGER.bind(
                    ui_error_type=json_body.get("error"),
                    ui_error_message=json_body.get("message"),
                    pathname=json_body.get("pathname"),
                    ui_version=ui_version,
                ).warning("user-is-logged-out")
        else:
            logger_with_binds = LOGGER.bind(
                ui_error_type=json_body.get("error"),
                ui_error_message=json_body.get("message"),
                pathname=json_body.get("pathname"),
                ui_version=ui_version,
            )
            if ui_version == __version__:
                logger_with_binds.error("error-from-ui")
            else:
                logger_with_binds.warning("error-from-ui")
    except Exception as exc:
        LOGGER.opt(exception=exc).bind(request_body=request.data).error(
            "UI error parsing failed"
        )

    return {"backend_version": response}
