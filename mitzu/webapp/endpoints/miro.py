from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, replace
from datetime import datetime
from typing import Dict, Optional

import flask
from dataclasses_json import DataClassJsonMixin
from flask import json
from werkzeug.exceptions import BadRequest

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.pages.paths as P
import mitzu.webapp.storage.cloud_storage as CS
import mitzu.webapp.storage.storage_model as SM
import mitzu.webapp.task_scheduler.model as TSM
from mitzu import configs
from mitzu.cache import MitzuCache, RedisMitzuCache
from mitzu.helper import create_unique_id
from mitzu.logger import LOGGER
from mitzu.model import MetricType
from mitzu.webapp.auth.cloud_authorizer import (
    CloudAuthorizer,
)
from mitzu.webapp.auth.model import ResponseType
from mitzu.webapp.auth.response_generator import (
    ResponseGenerator,
)
from mitzu.webapp.integrations.miro import MiroIntegration
from mitzu.webapp.model import (
    SavedMetric,
    User,
    WebappMetric,
)
from mitzu.webapp.service.miro import MiroService
from mitzu.webapp.service.path_parser import PathParser
from mitzu.webapp.service.svg_updater import (
    get_error_image,
    update_svg_for_miro,
)
from mitzu.webapp.storage.project_storage import (
    ObjectDoesNotExist,
)
from mitzu.webapp.storage.task_storage import TaskStorage
from mitzu.webapp.task_scheduler.runner import TaskRunner


@dataclass
class MiroInsightResponse(DataClassJsonMixin):
    org_id: Optional[str]
    project_id: Optional[str]
    insight_id: Optional[str]
    insight_title: Optional[str]
    insight_image: str
    error: Optional[str]


@dataclass
class MiroTaskResponse(DataClassJsonMixin):
    task_id: Optional[str]
    state: Optional[str]
    error: Optional[str]


def serve_insight_details_for_miro(request: flask.Request) -> ResponseType:
    try:
        path_parser = PathParser(request.full_path)
        saved_metric_id = path_parser.get_query_value("sm")
        if saved_metric_id is None:
            raise ValueError("Insight id is not set")

        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User is not logged in")

        project_id = deps.storage.get_saved_metric_project_id(saved_metric_id)
        if project_id is None:
            raise ObjectDoesNotExist("project id not found for saved metric")

        project_storage = deps.get_project_storage(project_id)
        saved_metric = project_storage.get_saved_metric(saved_metric_id)
        response = MiroInsightResponse(
            org_id=deps.user.org_id,
            project_id=project_id,
            insight_id=saved_metric.id,
            insight_title=saved_metric.name,
            insight_image=update_svg_for_miro(
                saved_metric.image_base64,
                saved_metric.last_updated_at,
                title=saved_metric.name,
                description=saved_metric.description,
            ),
            error=None,
        )
        deps.tracking_service.track_miro_insight_updated(saved_metric)
    except ObjectDoesNotExist as exc:
        LOGGER.opt(exception=exc).warning("failed to server miro insight")
        response = MiroInsightResponse(
            org_id=None,
            project_id=None,
            insight_id=None,
            insight_title=None,
            insight_image=get_error_image("Insight not found"),
            error="Insight not found",
        )
    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to serve miro insight")
        response = MiroInsightResponse(
            org_id=None,
            project_id=None,
            insight_id=None,
            insight_title=None,
            insight_image=get_error_image(str(exc))[0:300],
            error=str(exc)[0:300],
        )

    return flask.Response(
        response.to_json(), status=200, content_type="application/json"
    )


def add_miro_team_to_project(
    request: flask.Request,
) -> ResponseType:
    url = P.HOME_PATH
    try:
        path_parser = PathParser(request.full_path)
        auth_code = path_parser.get_query_value("code")
        project_id = path_parser.get_query_value("state")

        if auth_code is None:
            raise ValueError("auth code is missing")

        if project_id is None:
            raise ValueError("project is is missing")

        deps = DEPS.Dependencies.get()

        project_config_storage = deps.get_project_config_storage(project_id)

        miro = MiroService(project_config_storage)

        miro.add_team_to_project_by_auth_code(auth_code)
        url = P.create_workspace_settings_path(project_id, P.WorkspaceSettingsTab.MIRO)

    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to add miro team to project")

    return flask.redirect(url, code=307)


def embed_insight_in_miro(request: flask.Request) -> ResponseType:
    insight_id = "notfound"
    try:
        path_parser = PathParser(request.full_path)
        insight_id_arg = path_parser.get_query_value("insight_id")
        if insight_id_arg is None:
            raise BadRequest("Insight id is not set")
        insight_id = insight_id_arg

        access_token = path_parser.get_query_value("access_token")
        if access_token is None:
            raise BadRequest("Access token is not set")

        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise BadRequest("User not logged in")

        project_id = deps.storage.get_saved_metric_project_id(insight_id)
        if project_id is None:
            raise BadRequest("project id not found for saved metric")

        url = (
            P.create_path(
                P.PROJECTS_EXPLORE_PATH,
                project_id=project_id,
            )
            + f"?sm={insight_id}&miro=true&access_token={access_token}"
        )
        deps.tracking_service.track_miro_insight_opened_in_modal(
            project_id,
            insight_id,
        )
    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to embed insight in miro")
        else:
            LOGGER.opt(exception=exc).error("failed to embed insight in miro")
        url = (
            P.create_path(P.SHARED_INSIGHT_PATH, insight_id="notfound") + "?embed=true"
        )

    return flask.redirect(url, code=307)


def authorize_miro_app() -> ResponseType:
    """
    This is called when the user first opens the app and we redirect him to the
    Miro's authorization page
    """
    url = (
        f"https://miro.com/oauth/authorize?response_type=code&client_id={configs.MIRO_CLIENT_ID}"
        f"&redirect_uri={configs.HOME_URL}{P.MIRO_APP_AUTHORIZE_FINISH}"
    )
    return flask.Response(url, status=200)


def authorize_miro_app_finish(request: flask.Request) -> ResponseType:
    """
    This is called when the user authorizes the app and grants permissions to the app.
    We need to exchange the oauth code to a token, store it in the redis cache and then return
    the cache key as a 'auth token'.

    Setting cookies in a redirection is may not supported by all browsers, so we show a small
    HTML page which will do the final redirection in a javascript call.
    """
    try:
        path_parser = PathParser(request.full_path)
        auth_code = path_parser.get_query_value("code")
        if auth_code is None:
            raise BadRequest("Auth code is not set")

        tokens = MiroService.exchange_auth_code_to_token(
            auth_code, configs.HOME_URL + P.MIRO_APP_AUTHORIZE_FINISH
        )

        token_info = MiroService.get_token_info(tokens["access_token"])
        token_cache_key = MiroService.store_miro_token_details(
            RedisMitzuCache(), token_info
        )

        team_id = tokens["team_id"]

        uri = f"https://miro.com/app-install-completed/?client_id={configs.MIRO_CLIENT_ID}&team_id={team_id}"
        content = f"""
        <html>
            <body>
                <h2>Auth successful! This window will close itself.</h2>
                <script>
                    window.location = "{uri}";
                </script>
            </body>
        </html>"""
        response = flask.Response(content, status=200, content_type="text/html")
        ResponseGenerator().set_miro_auth_cookie(
            response, "mitzu-token-key", token_cache_key
        )
        return response

    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to start miro session")
        else:
            LOGGER.opt(exception=exc).error("failed to start miro session")

        content = f"""
<h2>Failed to authenticate the user</h2>
{exc}
"""
        return flask.Response(content, status=200, content_type="text/html")


def start_session_from_miro(
    request: flask.Request,
    cache: MitzuCache,
    cloud_storage: CS.CloudStorage,
    cloud_authorizer: CloudAuthorizer,
) -> ResponseType:
    try:
        path_parser = PathParser(request.full_path)
        email = path_parser.get_query_value("email")
        token_cache_key = path_parser.get_query_value("token_cache_key")
        if email is None:
            raise BadRequest("Email is not set")

        if token_cache_key is None:
            raise BadRequest("Token cache key is not set")

        token: Optional[Dict[str, Dict[str, str]]] = cache.get(token_cache_key)
        if token is None:
            return flask.Response("", status=204)

        with cloud_storage._new_db_session() as session:
            integration_rec = (
                session.query(SM.MiroIntegrationStorageRecord)
                .filter(
                    SM.MiroIntegrationStorageRecord.miro_team_id == token["team"]["id"]
                )
                .one_or_none()
            )
            if integration_rec is None:
                raise BadRequest("Team is not assigned to Mitzu project")

            integration: MiroIntegration = integration_rec.as_model_instance()

            user = (
                session.query(SM.UserStorageRecord)
                .filter(SM.UserStorageRecord.email == email)
                .one_or_none()
            )
            if user is None:
                raise BadRequest("User not found")

            membership_rec, org_rec = (
                session.query(
                    SM.UserOrgMembershipStorageRecord, SM.OrganisationStorageRecord
                )
                .join(
                    SM.OrganisationStorageRecord,
                    SM.OrganisationStorageRecord.org_id
                    == SM.UserOrgMembershipStorageRecord.org_id,
                )
                .join(
                    SM.ProjectStorageRecord,
                    SM.ProjectStorageRecord.org_id
                    == SM.OrganisationStorageRecord.org_id,
                )
                .filter(SM.ProjectStorageRecord.project_id == integration.project_id)
                .filter(SM.UserOrgMembershipStorageRecord.user_id == user.user_id)
                .one()
            )

            membership: User = membership_rec.as_model_instance(user)

            mitzu_token = cloud_authorizer._generate_new_token_with_claims(
                {"sub": membership.id, "org": membership.org_id}
            )

        response = flask.Response(
            json.dumps(
                {
                    "email": membership.email,
                    "org_name": org_rec.name,
                    "access_token": mitzu_token,
                }
            ),
            status=200,
            content_type="application/json",
        )
    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("faield to start miro session")
        else:
            LOGGER.opt(exception=exc).error("faield to start miro session")
        response = flask.Response(str(exc), status=400)

    return response


def get_post_message_html_response(response_data: Dict) -> ResponseType:
    content = f"""
    <html>
        <body>
            <h2>"Mitzu session details sent. This window will close itself."</h2>
            <script>
                window.opener.postMessage({json.dumps(response_data)}, '*');
                setTimeout(() => {{
                    window.close();
                }}, 3000);
            </script>
        </body>
    </html>"""
    return flask.Response(
        content,
        status=200,
        content_type="text/html",
    )


def miro_login(
    request: flask.Request, cloud_authorizer: CloudAuthorizer
) -> ResponseType:
    try:
        token = cloud_authorizer.get_auth_token(request)
        if token is None:
            return get_post_message_html_response(
                {"error": "First login at app.mitzu.io"}
            )

        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User not logged in into Mitzu")

        org = deps.storage.get_organisation()
        response_data = {
            "access_token": token,
            "email": deps.user.email,
            "org_name": org.name,
            "projects": [
                {"id": project.id, "name": project.name}
                for project in deps.storage.list_projects()
            ],
        }
        deps.tracking_service.track_miro_login()

    except Exception as exc:
        LOGGER.opt(exception=exc).error("failed to login into miro")
        response_data = {"error": str(exc)}

    return get_post_message_html_response(response_data)


def miro_create_new_insight(request: flask.Request) -> ResponseType:
    try:
        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User is not logged in into Mitzu")

        path = PathParser(request.full_path)
        project_id = path.get_query_value("project_id")
        metric_type = path.get_query_value("metric_type")

        if project_id is None or metric_type is None:
            raise BadRequest("project id or metric type is not set")

        webapp_metric = WebappMetric.create_empty_webapp_metric(
            MetricType.parse(metric_type)
        )

        project_storage = deps.get_project_storage(project_id)
        saved_metric = SavedMetric(
            name="New Miro insight",
            description=None,
            image_base64=get_error_image("Empty insight"),
            small_base64=get_error_image("Empty insight"),
            owner=deps.user.membership_id,
            project_id=project_id,
            webapp_metric=webapp_metric,
        )
        project_storage.set_saved_metric(saved_metric)

        response = MiroInsightResponse(
            org_id=deps.user.org_id,
            project_id=project_id,
            insight_id=saved_metric.id,
            insight_title=saved_metric.name,
            insight_image=update_svg_for_miro(
                saved_metric.image_base64,
                saved_metric.last_updated_at,
                title=saved_metric.name,
                description=saved_metric.description,
            ),
            error=None,
        )
        deps.tracking_service.track_miro_insight_created(
            saved_metric, is_duplication=False
        )

    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to create new insight from miro")
        else:
            LOGGER.opt(exception=exc).error("failed to create new insight from miro")

        response = MiroInsightResponse(
            org_id=None,
            project_id=None,
            insight_id=None,
            insight_title=None,
            insight_image=get_error_image(str(exc)),
            error=str(exc),
        )

    return flask.Response(
        response.to_json(), status=200, content_type="application/json"
    )


def miro_duplicate_insight(request: flask.Request) -> ResponseType:
    try:
        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User is not logged in into Mitzu")

        path = PathParser(request.full_path)
        insight_id = path.get_query_value("insight_id")

        if insight_id is None:
            raise BadRequest("insight id is not set")

        project_id = deps.storage.get_saved_metric_project_id(insight_id)
        if project_id is None:
            raise ValueError("project not found for insight")

        project_storage = deps.get_project_storage(project_id)
        saved_metric = project_storage.get_saved_metric(insight_id)

        now = datetime.now()
        duplicated_metric = replace(
            saved_metric,
            id=create_unique_id(),
            name=saved_metric.name + " (copy)",
            created_at=now,
            last_updated_at=now,
        )

        project_storage.set_saved_metric(duplicated_metric)

        response = MiroInsightResponse(
            org_id=deps.user.org_id,
            project_id=project_id,
            insight_id=duplicated_metric.id,
            insight_title=duplicated_metric.name,
            insight_image=update_svg_for_miro(
                duplicated_metric.image_base64,
                duplicated_metric.last_updated_at,
                title=duplicated_metric.name,
                description=duplicated_metric.description,
            ),
            error=None,
        )
        deps.tracking_service.track_miro_insight_created(
            duplicated_metric, is_duplication=True
        )

    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to duplicate insight from miro")
        else:
            LOGGER.opt(exception=exc).error("failed to duplicate insight from miro")

        response = MiroInsightResponse(
            org_id=None,
            project_id=None,
            insight_id=None,
            insight_title=None,
            insight_image=get_error_image(str(exc)),
            error=str(exc),
        )

    return flask.Response(
        response.to_json(), status=200, content_type="application/json"
    )


def miro_refresh_insight(
    request: flask.Request, worker_pool: ThreadPoolExecutor
) -> ResponseType:
    try:
        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User is not logged in into Mitzu")

        insight_id = request.get_json(force=True).get("insight_id")
        if insight_id is None:
            raise BadRequest("insight id is not set")

        project_id = deps.storage.get_saved_metric_project_id(insight_id)
        if project_id is None:
            raise ValueError("project not found for insight")

        refresh_insight_task = TSM.RefreshInsightTask(
            task_id=create_unique_id(),
            source=TSM.TaskSource(
                org_id=deps.org_id or "",
                project_id=project_id,
                user_id=deps.user.id,
                user_email=deps.user.email,
                user_role=deps.user.role,
                org_name=deps.org_name or "",
                url=request.path,
                tracking_context=deps.get_tracking_context(),
            ),
            insight_id=insight_id,
            state=TSM.TaskState.QUEUED,
            created_at=datetime.now(),
            error=None,
        )
        deps.storage.submit_new_task(refresh_insight_task)

        task_runner = TaskRunner(deps.storage._session_manager, deps.cache)
        worker_pool.submit(
            task_runner.run_task, refresh_insight_task.task_id, lambda *arg: None
        )

        response = MiroTaskResponse(
            task_id=refresh_insight_task.task_id,
            state=refresh_insight_task.state.value,
            error=None,
        )
    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to refresh insight from miro")
        else:
            LOGGER.opt(exception=exc).error("failed to refresh insight from miro")

        response = MiroTaskResponse(
            task_id=None,
            state=None,
            error=str(exc),
        )

    return flask.Response(
        response.to_json(), status=200, content_type="application/json"
    )


def miro_task_status(request: flask.Request) -> ResponseType:
    try:
        deps = DEPS.Dependencies.get()
        if deps.user is None:
            raise ValueError("User is not logged in into Mitzu")

        path = PathParser(request.full_path)
        task_id = path.get_query_value("task_id")

        if task_id is None:
            raise BadRequest("task id is not set")

        task_storage = TaskStorage(task_id, deps.storage._session_manager)
        task = task_storage.get_task()

        response = MiroTaskResponse(
            task_id=task_id, state=task.state.value, error=task.error
        )
    except Exception as exc:
        if isinstance(exc, BadRequest):
            LOGGER.opt(exception=exc).warning("failed to serve task status to miro")
        else:
            LOGGER.opt(exception=exc).error("failed to serve task status to miro")

        response = MiroTaskResponse(
            task_id=None,
            state=None,
            error=str(exc),
        )

    return flask.Response(
        response.to_json(), status=200, content_type="application/json"
    )
