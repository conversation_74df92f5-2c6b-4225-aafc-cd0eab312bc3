from typing import Dict, cast

import flask
from werkzeug.exceptions import BadRequest

import mitzu.webapp.dependencies as DEPS
import mitzu.webapp.pages.paths as P
import mitzu.webapp.service.org_manager as OM
from mitzu import configs
from mitzu.logger import LOGGER
from mitzu.webapp.auth.auth_config_manager import (
    AuthConfigManager,
)
from mitzu.webapp.auth.model import ResponseType


def reload_auth_config() -> ResponseType:
    deps = DEPS.Dependencies.get()
    if deps.user is None:
        return flask.redirect(code=307, location=configs.HOME_URL)

    if "@mitzu.io" not in deps.user.email:
        return flask.redirect(code=307, location=configs.HOME_URL)

    AuthConfigManager.clear_cached_config(deps.cache)
    return flask.Response('{"status": "ok"}', status=200)


def handle_enterprise_login(
    request: flask.Request,
    org_manager: OM.OrgManager,
) -> Dict[str, str]:
    email = None
    try:
        json_body = cast(Dict[str, str], request.json)
        email = json_body.get("email")
        if email is None:
            raise BadRequest("Email is missing")

        email_parts = str(email).split("@")
        if len(email_parts) != 2:
            raise BadRequest("Malformed email address")

        auth_config = org_manager.get_oauth_config_for_email_domain(email_parts[1])
        if auth_config is None:
            raise BadRequest("SSO not configured")

        if auth_config.identity_provider_id is not None:
            return {
                "login_url": P.create_path(
                    P.REDIRECT_TO_LOGIN_WITH_IDP_URL,
                    idp_id=auth_config.identity_provider_id,
                ),
            }
        else:
            return {
                "login_url": configs.HOME_URL + P.REDIRECT_TO_LOGIN_URL,
            }
    except Exception as exc:
        LOGGER.opt(exception=exc).bind(email=email).warning("Enterprise login failed")
        raise exc
