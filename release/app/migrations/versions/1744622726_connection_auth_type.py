"""Connection auth type

Revision ID: b604662c0a77
Revises: f1677b0e0ad7
Create Date: 2025-04-14 09:25:26.931851+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b604662c0a77"
down_revision = "f1677b0e0ad7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.batch_alter_table("connections", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "auth_type", sa.String(), nullable=False, server_default="password"
            )
        )


def downgrade() -> None:
    with op.batch_alter_table("connections", schema=None) as batch_op:
        batch_op.drop_column("auth_type")
