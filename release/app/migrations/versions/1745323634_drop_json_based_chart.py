"""Drop json based charts

Revision ID: 6734111eaba6
Revises: c285d4d5b3b2
Create Date: 2025-04-22 12:07:14.364004+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6734111eaba6"
down_revision = "c285d4d5b3b2"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("saved_metrics", schema=None) as batch_op:
        batch_op.drop_column("chart")

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("saved_metrics", schema=None) as batch_op:
        batch_op.add_column(sa.Column("chart", sa.VARCHAR(), nullable=True))

    # ### end Alembic commands ###
