"""Add maximum sample size

Revision ID: f1677b0e0ad7
Revises: 203e65159416
Create Date: 2025-04-14 13:57:14.819833+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f1677b0e0ad7"
down_revision = "203e65159416"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.batch_alter_table("indexing_settings", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "maximum_sample_size",
                sa.Integer(),
                nullable=False,
                server_default="20000",
            )
        )

    with op.batch_alter_table("indexing_settings", schema=None) as batch_op:
        batch_op.alter_column("maximum_sample_size", server_default=None)


def downgrade() -> None:
    with op.batch_alter_table("indexing_settings", schema=None) as batch_op:
        batch_op.drop_column("maximum_sample_size")
