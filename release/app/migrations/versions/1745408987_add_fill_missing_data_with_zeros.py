"""Add fill missing data with zeros

Revision ID: c285d4d5b3b2
Revises: 45f454067f79
Create Date: 2025-04-23 11:49:47.910906+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "c285d4d5b3b2"
down_revision = "45f454067f79"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("insight_settings", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "fill_missing_values_with_zero",
                sa.<PERSON>(),
                nullable=False,
                server_default="false",
            )
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("insight_settings", schema=None) as batch_op:
        batch_op.drop_column("fill_missing_values_with_zero")

    # ### end Alembic commands ###
