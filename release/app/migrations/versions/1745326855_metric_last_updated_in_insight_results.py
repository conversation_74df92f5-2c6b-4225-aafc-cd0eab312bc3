"""<PERSON><PERSON> json and last updated at in insight results

Revision ID: b7b0119b3540
Revises: 6734111eaba6
Create Date: 2025-04-22 13:00:55.414123+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b7b0119b3540"
down_revision = "6734111eaba6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.batch_alter_table("insight_results", schema=None) as batch_op:
        batch_op.add_column(sa.Column("metric_json", sa.String(), nullable=True))
        batch_op.add_column(sa.Column("last_updated_at", sa.DateTime(), nullable=True))
        batch_op.create_index(
            batch_op.f("ix_insight_results_metric_json"), ["metric_json"], unique=False
        )

    op.execute(
        """
        WITH data AS (
            SELECT
                last_updated_at as lua,
                metric_json as mj,
                saved_metric_id as smi
            FROM saved_metrics
        )
        UPDATE insight_results SET
            last_updated_at = (SELECT lua FROM data WHERE saved_metric_id = smi),
            metric_json = (SELECT mj FROM data WHERE saved_metric_id = smi)
        """
    )

    with op.batch_alter_table("insight_results", schema=None) as batch_op:
        batch_op.alter_column("metric_json", nullable=False)
        batch_op.alter_column("last_updated_at", nullable=False)

    with op.batch_alter_table("insight_results", schema=None) as batch_op:
        if op.get_context().dialect.name != "sqlite":
            batch_op.drop_constraint("insight_results_pkey")
        batch_op.create_primary_key(
            "insight_results_pkey", columns=["saved_metric_id", "metric_json"]
        )


def downgrade() -> None:
    with op.batch_alter_table("insight_results", schema=None) as batch_op:
        if op.get_context().dialect.name != "sqlite":
            batch_op.drop_constraint("insight_results_pkey")
        batch_op.create_primary_key("insight_results_pkey", columns=["saved_metric_id"])

    with op.batch_alter_table("insight_results", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_insight_results_metric_json"))
        batch_op.drop_column("last_updated_at")
        batch_op.drop_column("metric_json")
