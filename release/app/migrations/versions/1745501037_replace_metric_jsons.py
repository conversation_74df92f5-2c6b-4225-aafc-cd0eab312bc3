"""Replace metric jsons with deterministic jsons

Revision ID: 5d77265e024d
Revises: b7b0119b3540
Create Date: 2025-04-24 13:23:57.066203+00:00

"""
import sqlalchemy as sa
from alembic import op

import mitzu.webapp.serialization as SE
from mitzu.logger import LOGGER

# revision identifiers, used by Alembic.
revision = "5d77265e024d"
down_revision = "b7b0119b3540"
branch_labels = None
depends_on = None


def upgrade() -> None:
    saved_metrics = (
        op.get_bind()
        .execute(sa.text("""SELECT saved_metric_id, metric_json FROM saved_metrics"""))
        .all()
    )

    total_saved_metrics = len(saved_metrics)
    stored_metric_jsons = set([sm["metric_json"] for sm in saved_metrics])
    for index, saved_metric in enumerate(saved_metrics):
        webapp_metric = SE.from_compressed_string(saved_metric["metric_json"])
        new_metric_json = SE.to_compressed_string(webapp_metric)
        if new_metric_json not in stored_metric_jsons:
            op.execute(
                sa.text(
                    f"""
                UPDATE saved_metrics
                SET metric_json='{new_metric_json}'
                WHERE saved_metric_id='{saved_metric["saved_metric_id"]}'
            """
                )
            )
            stored_metric_jsons.add(new_metric_json)

        if index % 100 == 0:
            LOGGER.bind(total_saved_metrics=total_saved_metrics, progress=index).info(
                "updating saved metrics"
            )

    insight_results = (
        op.get_bind()
        .execute(
            sa.text("""SELECT saved_metric_id, metric_json FROM insight_results""")
        )
        .all()
    )

    total_insight_results = len(insight_results)
    stored_metric_jsons = set([ir["metric_json"] for ir in insight_results])
    for index, insight_result in enumerate(insight_results):
        webapp_metric = SE.from_compressed_string(insight_result["metric_json"])
        new_metric_json = SE.to_compressed_string(webapp_metric)
        if new_metric_json not in stored_metric_jsons:
            op.execute(
                sa.text(
                    f"""
                UPDATE insight_results
                SET metric_json='{new_metric_json}'
                WHERE saved_metric_id='{insight_result["saved_metric_id"]}'
                    AND metric_json='{insight_result["metric_json"]}'
            """
                )
            )
            stored_metric_jsons.add(new_metric_json)

        if index % 100 == 0:
            LOGGER.bind(
                total_insight_results=total_insight_results, progress=index
            ).info("updating insight results")


def downgrade() -> None:
    pass
