"""Insight results

Revision ID: 45f454067f79
Revises: b604662c0a77
Create Date: 2025-04-17 11:09:08.269881+00:00

"""
import json

import sqlalchemy as sa
from alembic import op

import mitzu.model as M

# revision identifiers, used by Alembic.
revision = "45f454067f79"
down_revision = "b604662c0a77"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "insight_results",
        sa.Column("saved_metric_id", sa.String(), nullable=False),
        sa.Column("x_axis_label", sa.String(), nullable=False),
        sa.Column("y_axis_label", sa.String(), nullable=False),
        sa.Column("color_label", sa.String(), nullable=False),
        sa.Column("yaxis_ticksuffix", sa.String(), nullable=False),
        sa.Column("hover_mode", sa.String(), nullable=False),
        sa.Column("chart_type", sa.String(), nullable=False),
        sa.Column("dataframe", sa.String(), nullable=False),
        sa.Column("dataframe_version", sa.Integer(), nullable=False),
        sa.Column("all_groups", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["saved_metric_id"],
            ["saved_metrics.saved_metric_id"],
            name="insight_results_saved_metric_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("saved_metric_id"),
    )

    saved_metrics = (
        op.get_bind()
        .execute(sa.text("SELECT saved_metric_id, chart FROM saved_metrics"))
        .all()
    )

    chart_types = [
        M.SimpleChartType.BAR,
        M.SimpleChartType.STACKED_BAR,
        M.SimpleChartType.LINE,
        M.SimpleChartType.STACKED_AREA,
        M.SimpleChartType.HEATMAP,
        M.SimpleChartType.GEO_COUNTRY,
        M.SimpleChartType.PERCENTAGE_STACKED_BAR,
        M.SimpleChartType.PERCENTAGE_STACKED_AREA,
        M.SimpleChartType.SANKEY,
        M.SimpleChartType.NUMBER,
        M.SimpleChartType.PIE,
        M.SimpleChartType.HORIZONTAL_BAR,
    ]

    for saved_metric_id, chart_dict_str in saved_metrics:
        data = json.loads(chart_dict_str)
        op.execute(
            sa.text(
                f"""
                INSERT INTO insight_results (
                    saved_metric_id, x_axis_label, y_axis_label, color_label, yaxis_ticksuffix,
                    hover_mode,chart_type, dataframe, dataframe_version, all_groups)
                VALUES (
                    '{saved_metric_id}',
                    '{data["x_axis_label"]}',
                    '{data["y_axis_label"]}',
                    '{data["color_label"]}',
                    '{data["yaxis_ticksuffix"]}',
                    '{data["hover_mode"]}',
                    '{chart_types[data["chart_type"]-1].value}',
                    '{data["dataframe"]}',
                    {data.get("dataframe_version", 1)},
                    {data.get("all_groups", 0)}
                )
            """
            )
        )

    with op.batch_alter_table("saved_metrics", schema=None) as batch_op:
        batch_op.alter_column("chart", nullable=True)


def downgrade() -> None:
    with op.batch_alter_table("saved_metrics", schema=None) as batch_op:
        batch_op.alter_column("chart", nullable=False)

    op.drop_table("insight_results")
