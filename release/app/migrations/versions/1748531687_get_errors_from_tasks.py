"""Get indexing errors from the tasks table

Revision ID: ce850353399e
Revises: 49d89f5d7641
Create Date: 2025-05-29 15:14:47.317456+00:00

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ce850353399e"
down_revision = "49d89f5d7641"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("event_data_tables", schema=None) as batch_op:
        batch_op.add_column(sa.Column("last_index_task_id", sa.String(), nullable=True))
        batch_op.create_foreign_key(
            "event_data_tables_task_id_fkey",
            "tasks",
            ["last_index_task_id"],
            ["task_id"],
        )

    is_sqlite = "sqlite" in op.get_bind().engine.url
    with op.batch_alter_table("tasks", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("resource_id", sa.String(), nullable=not is_sqlite)
        )

    op.execute(
        sa.text(
            """
            INSERT INTO tasks (
                task_id, org_id, project_id, user_id, user_email,
                user_role, org_name, url, tracking_context,
                task_type, task_details, state, created_at, error, resource_id)
            SELECT
                'migration-' || event_data_table_id,
                org.org_id,
                proj.project_id,
                users.user_id,
                users.email,
                uom.role,
                org.name,
                'migration',
                '{}',
                'edt_indexing',
                '{"event_data_table_id": "' || event_data_table_id || '", "event_data_table_hash": "' || last_indexed_hash || '"}',
                'finished',
                edt.last_indexed,
                edt.last_indexing_error,
                event_data_table_id
            FROM event_data_tables edt
            JOIN projects proj USING(project_id)
            JOIN organisations org USING(org_id)
            JOIN user_org_memberships uom ON uom.org_id = org.org_id AND uom.is_org_owner IS TRUE
            JOIN users using(user_id)
            WHERE edt.last_indexed IS NOT NULL
            """
        )
    )
    op.execute(
        sa.text(
            """
            UPDATE event_data_tables
            SET last_index_task_id = 'migration-' || event_data_table_id
            WHERE last_indexed IS NOT NULL
            """
        )
    )

    with op.batch_alter_table("event_data_tables", schema=None) as batch_op:
        batch_op.drop_column("last_indexed")
        batch_op.drop_column("last_indexed_hash")
        batch_op.drop_column("last_indexing_error")

    if not is_sqlite:
        with op.batch_alter_table("tasks", schema=None) as batch_op:
            batch_op.alter_column("resource_id", nullable=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table("event_data_tables", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("last_indexing_error", sa.VARCHAR(), nullable=True)
        )
        batch_op.add_column(sa.Column("last_indexed_hash", sa.VARCHAR(), nullable=True))
        batch_op.add_column(sa.Column("last_indexed", sa.DateTime(), nullable=True))
        batch_op.drop_constraint("event_data_tables_task_id_fkey", type_="foreignkey")
        batch_op.drop_column("last_index_task_id")

    with op.batch_alter_table("tasks", schema=None) as batch_op:
        batch_op.drop_column("resource_id")
    # ### end Alembic commands ###
