@startuml Auth
participant Browser as browser
participant "Flask\n@before_request" as before_request
participant "Dash\n(restricted decorator)" as dash
participant "Flask\n@after_request" as after_request
participant IDP as idp


== First requests / loging out ==

browser -> before_request: GET / (no token or invalid token)
before_request -> browser: Redirect to /auth/unauthorized

browser -> before_request: GET|POST /_dash_callback (no token or invalid token)
before_request -> dash: forward request as it is
dash -> browser: 204 No Content\n(used as unauthorized to keep the react working)


== Authorized requests ==

browser -> before_request: GET / (valid token)
before_request -> dash: forward request as it is
note right of dash: callback is executed
dash -> after_request: http response
note right of after_request
    maintains the auth token:
     - refresh tokens close to expire
     - clears auth token when org membership is removed
end note
after_request -> browser: http response

== Login with IDP ==
browser -> before_request: GET /auth/redirect-to-login
before_request -> browser: Redirect to IDP + url encoded redirection URL
browser -> idp: login with url encoded redirection URL
note across: 3rd party auth is performed by the IDP
idp -> browser: redirect to redirection URL + auth code
browser -> before_request: GET /auth/oauth?code=xxxxx
note right of before_request
    see the 'Login with a wrong IDP' flow if the email belongs to an account with a different IDP
end note
before_request -> idp: get user details for code xxxxx
idp -> before_request: user details (including the email address)
note right of before_request
    sets the auth token having the email address
    (at this point we don't know if the user is part of any orgs or not)
end note
before_request -> after_request: redirect to / with an auth token
note right of after_request
    puts the user into the org based on the SSO config
    updates the auth token:
     - with an org scoped auth token if the user is member of any orgs
end note
after_request -> browser: redirect to / (with org scoped auth token)


== Login with a wrong IDP ==
note across
    if an email address belongs to an SSO account then the login flow must be done through the IDP linked to the account
end note
browser -> before_request: GET /auth/redirect-to-login
before_request -> browser: Redirect to IDP + url encoded redirection URL
browser -> idp: login with url encoded redirection URL
note across: 3rd party auth is performed by the IDP
idp -> browser: redirect to redirection URL + auth code
browser -> before_request: GET /auth/oauth?code=xxxxx
before_request -> idp: get user details for code xxxxx
idp -> before_request: user details (including the email address)
note right of before_request
    the SSO account existence and the IDP is checked
    if
      - account does not exists or
      - IDP is not configured or
      - IDP is the same as configured
    then the login flow ends like the 'Login with IDP' flow
    else this the user is redirected to the login flow of the real IDP
end note
before_request -> browser: GET <home url of the configured IDP>/auth/redirect-to-login
note across
    the 'Login with IDP' flow starts here
end note


@enduml