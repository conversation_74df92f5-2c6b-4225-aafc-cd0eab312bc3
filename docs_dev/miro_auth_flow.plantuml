@startuml Auth
participant User as user
participant "Miro Webapp" as miro_webapp
participant "Miro backend" as miro_backend
participant "Mitzu app (in Miro)" as mitzu_app
participant "Mitzu backend" as mitzu_backend
participant "Mitzu Redis" as mitzu_redis


== App auth on Miro Board ==
user -> miro_webapp: Opens the app for the first time
miro_webapp -> user: Shows the app auth modal in an iframe
user -> miro_webapp: Authorizes the app
miro_webapp -> mitzu_backend: redirects to .../authorize-app-finish?OAuth code=...
mitzu_backend -> miro_backend: POST: exchange OAuth code to miro token
miro_backend -> mitzu_backend: access token
mitzu_backend -> mitzu_redis: store access token
mitzu_backend -> miro_webapp: serves a dummy HTML + redis key as an auth cookie
miro_webapp -> miro_webapp: dummy HTML redirects to miro.com/app-install-completed
note across: at this point Mitzu Miro App is installed and gets initialized in a hidden iframe

mitzu_app -> mitzu_backend: POST /api/miro/start-session?email + the auth cookie (redis key)
mitzu_backend -> mitzu_redis: fetch the original Miro token
mitzu_redis -> mitzu_backend: original Miro token (if exists)
mitzu_backend -> mitzu_backend: performs authorization
mitzu_backend -> mitzu_app: returns valid Mitzu access token
note across: at this point Mitzu Miro App can call Mitzu backend
mitzu_app -> mitzu_app: opens the side panel


== App opened second time ==
user -> miro_webapp: Opens the app for the second time
miro_webapp -> mitzu_backend: opens the .../authorize-app-finish without oauth code
mitzu_backend -> miro_webapp: empty response
note across: at this point Mitzu Miro App gets initialized in a hidden iframe and inits the Mitzu session in the similar way


== Embed Mitzu app ==
user -> mitzu_app: clicks the open insight in modal button
mitzu_app -> miro_webapp: add iframe with the .../api/miro/embed-insight?insight_id=...&access_token=...
miro_webapp -> mitzu_backend: loads the .../api/miro/embed-insight?insight_id=...&access_token=...
mitzu_backend -> user: redirects to shared insight url or to explore page
user -> mitzu_backend: follows redirection
mitzu_backend -> mitzu_backend: auth flow fallbacks to use the access_token\n query param,and sets it as the auth cookie
user -> mitzu_app: closes the iframe
mitzu_app -> mitzu_backend: calls .../api/miro/insight?insight_id=...&access_token=...
mitzu_backend -> mitzu_app: latest details of the insight (title, image)
mitzu_app -> miro_webapp: update picture

@enduml