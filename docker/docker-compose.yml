version: "3.8"
services:
  postgres:
    image: postgres
    restart: always
    hostname: postgres
    environment:
      POSTGRES_PASSWORD: test
      POSTGRES_USER: test
    ports:
      - "5432:5432"
    expose:
      - "5432"
    volumes:
      - ./.dbs/postgres:/var/lib/postgresql/data
      - ./postgres_dump.sql.gz:/docker-entrypoint-initdb.d/init.sql.gz
    networks:
      - mitzu-network

  redis:
    image: redis:6.2-alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --save 20 1 --loglevel warning
    networks:
      - mitzu-network

  clickhouse-server:
    image: clickhouse/clickhouse-server:*********-alpine
    restart: always
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse-data:/var/lib/clickhouse
    environment:
      - TZ=UTC
      - CLICKHOUSE_DB=test
      - CLICKHOUSE_PASSWORD=test
      - CLICKHOUSE_USER=test
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    networks:
      - mitzu-network

  trino-server:
    image: trinodb/trino:402 # <PERSON><PERSON>'s version
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./trino/etc:/etc/trino
    networks:
      - mitzu-network

volumes:
  clickhouse-data:


networks:
  mitzu-network:
    driver: bridge
